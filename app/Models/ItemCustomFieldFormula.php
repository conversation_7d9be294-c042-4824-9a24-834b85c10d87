<?php

namespace App\Models;

use App\Traits\HasCompany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ItemCustomFieldFormula extends Model
{
    use HasFactory, HasCompany;

    protected $table = 'item_custom_field_formula';

    protected $fillable = [
        'company_id',
        'custom_field_id',
        'item_id',
        'formula',
        'is_system_field',
        'system_field_name',
        'used_cf_ids_for_formula',
    ];

    public $casts = [
        'is_system_field' => 'boolean',
        'used_cf_ids_for_formula' => 'array',
    ];

    public function customField()
    {
        return $this->belongsTo(ItemCustomField::class, 'custom_field_id');
    }
}
