<?php

namespace App\Models;

use App\Traits\HasCompany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransactionCustomField extends Model
{
    use HasFactory, HasCompany;

    protected $table = 'transaction_custom_field';

    protected $fillable = [
        'label_name',
        'input_type',
        'company_id',
    ];

    public const INPUT_TYPE_TEXT = 'text';

    public const INPUT_TYPE_NUMBER = 'number';

    public const INPUT_TYPE_DATE = 'date';

    public const INPUT_TYPE_DATETIME = 'datetime';

    public const INPUT_TYPE_TEXTAREA = 'textarea';

    public const INPUT_TYPE_SELECT = 'select';

    public const INPUT_TYPE_RADIO = 'radio';


    public const SALE = 1;

    public const SALE_RETURN = 2;

    public const INCOME_DEBIT_NOTE = 3;

    public const INCOME_CREDIT_NOTE = 4;

    public const PURCHASE = 5;

    public const PURCHASE_RETURN = 6;

    public const EXPENSE_DEBIT_NOTE = 7;

    public const EXPENSE_CREDIT_NOTE = 8;

    public const SALE_REPORT = 9;

    public const PURCHASE_REPORT = 10;

    public const INCOME_ESTIMATE_QUOTE = 15;

    public const DELIVERY_CHALLAN = 16;

    public const PURCHASE_ORDER = 17;

    public const RECURRING = 18;

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function customFieldOptions()
    {
        return $this->hasMany(TransactionCustomFieldOption::class, 'custom_field_id');
    }
}
