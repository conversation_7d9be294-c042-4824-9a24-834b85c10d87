<?php

namespace App\Models;

use App\Traits\HasCompany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Gstr2bSummaryTransaction extends Model
{
    use HasCompany, HasFactory;

    protected $table = 'gstr2b_summary_transactions';

    protected $fillable = [
        'company_id',
        'return_period',
        'transaction_type',
        'gstin',
        'invoice_number',
        'taxable_value',
        'igst',
        'cgst',
        'sgst',
        'cess',
        'total_tax',
        'invoice_amount',
        'invoice_date',
    ];
}
