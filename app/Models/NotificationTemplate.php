<?php

namespace App\Models;

use App\Models\Contracts\JsonResourceful;
use App\Traits\HasCompany;
use App\Traits\HasJsonResourcefulData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\NotificationTemplate
 *
 * @property int $id
 * @property int $company_id
 * @property string $template_name
 * @property string $subject
 * @property string $body
 * @property string|null $regards
 * @property string|null $dynamic_variable
 * @property int $is_attachment
 * @property int $use_for_whatsapp
 * @property int $use_for_email
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|NotificationTemplate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|NotificationTemplate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|NotificationTemplate query()
 * @method static \Illuminate\Database\Eloquent\Builder|NotificationTemplate whereBody($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NotificationTemplate whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NotificationTemplate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NotificationTemplate whereDynamicVariable($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NotificationTemplate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NotificationTemplate whereIsAttachment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NotificationTemplate whereRegards($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NotificationTemplate whereSubject($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NotificationTemplate whereTemplateName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NotificationTemplate whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NotificationTemplate whereUseForEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NotificationTemplate whereUseForWhatsapp($value)
 *
 * @mixin \Eloquent
 */
class NotificationTemplate extends Model implements JsonResourceful
{
    use HasCompany, HasFactory, HasJsonResourcefulData;

    public $table = 'notification_templates';

    public $fillable = [
        'company_id',
        'template_name',
        'subject',
        'body',
        'whatsapp_body',
        'regards',
        'dynamic_variable',
        'is_attachment',
        'use_for_whatsapp',
        'use_for_email',
        'enabled_for_one_one_za',
        'one_one_za_wp_body',
        'one_one_za_template',
        'one_one_za_attachment_pdf',
    ];

    public const SALE = 'Sale';

    public const SALE_RETURN = 'Sale Return';

    public const INCOME_DEBIT_NOTE = 'Debit Note - Income';

    public const INCOME_CREDIT_NOTE = 'Credit Note - Income';

    public const PURCHASE = 'Purchase';

    public const PURCHASE_RETURN = 'PurchaseReturn';

    public const EXPENSE_DEBIT_NOTE = 'ExpenseDebitNote';

    public const EXPENSE_CREDIT_NOTE = 'ExpenseCreditNote';

    public const RECEIPT = 'Receipt';

    public const PAYMENT = 'Payment';

    public const INCOME_ESTIMATE_QUOTE = 'Estimate / Quote';

    public const DELIVERY_CHALLAN = 'Delivery Challan';

    public const PURCHASE_ORDER = 'Purchase Order';

    public const CUSTOMER_SUMMARY_REPORT = 'Customer Summary Report';

    public const SUPPLIER_SUMMARY_REPORT = 'Supplier Summary Report';

    public const RECURRING_INVOICE = 'Recurring Invoice';

    public const ONE_ONE_ZA_AUTO_PAYMENT_REMINDER = 'Auto Payment Reminder';

    public const GSTR2B_RECONCILIATION = 'GSTR2B Reconciliation';

    public function getDynamicVariableAttribute($value)
    {
        if (is_string($value)) {
            return explode(',', $value);
        }

        return $value;
    }

    public function prepareLinks(): array
    {
        return [
            //
        ];
    }

    public function relationLinks(): array
    {
        return [
            //
        ];
    }

    public function prepareAttributes(): array
    {
        return [
            'template_name' => $this->template_name,
            'subject' => $this->subject,
            'body' => $this->body,
            'whatsapp_body' => $this->whatsapp_body,
            'regards' => $this->regards,
            'is_attachment' => $this->is_attachment,
        ];
    }
}
