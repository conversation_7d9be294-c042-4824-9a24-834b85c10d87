<?php

use App\Actions\CommonAction\GetStepForDecimalPlaceAction;
use App\Http\Controllers\TrialBalanceController;
use App\Models\Account;
use App\Models\AccountSetting;
use App\Models\AdditionalChargesForExpenseCreditNoteTransaction;
use App\Models\AdditionalChargesForExpenseDebitNoteTransaction;
use App\Models\AdditionalChargesForIncomeCreditNoteTransaction;
use App\Models\AdditionalChargesForIncomeDebitNoteTransaction;
use App\Models\AdditionalChargesForIncomeEstimateQuoteTransaction;
use App\Models\AdditionalChargesForPurchaseOrderTransaction;
use App\Models\AdditionalChargesForPurchaseReturnTransaction;
use App\Models\AdditionalChargesForPurchaseTransaction;
use App\Models\AdditionalChargesForSalesReturnTransaction;
use App\Models\AdditionalChargesForSalesTransaction;
use App\Models\Address;
use App\Models\Analytic;
use App\Models\Bank;
use App\Models\BusinessCategory;
use App\Models\CessRate;
use App\Models\City;
use App\Models\Company;
use App\Models\CompanyDashboardButton;
use App\Models\CompanyFilter;
use App\Models\CompanyGroup;
use App\Models\CompanyRazorpayCredential;
use App\Models\CompanySetting;
use App\Models\CompanyTeamManagement;
use App\Models\CompanyTile;
use App\Models\Configuration\ExpenseCreditNote;
use App\Models\Configuration\ExpenseDebitNote;
use App\Models\Configuration\IncomeCreditNote;
use App\Models\Configuration\IncomeDebitNote;
use App\Models\Configuration\IncomeEstimateQuoteConfiguration;
use App\Models\Configuration\IncomeSalesReturn;
use App\Models\Configuration\PurchaseConfiguration;
use App\Models\Configuration\PurchaseOrderConfiguration;
use App\Models\Configuration\PurchaseReturnConfiguration;
use App\Models\Configuration\SaleConfiguration;
use App\Models\ConsultantTeamManagement;
use App\Models\Country;
use App\Models\DeliveryChallanTransaction;
use App\Models\DeliveryChallanTransactionItem;
use App\Models\DeliveryChallanTransactionMaster;
use App\Models\EInvAPICredentials;
use App\Models\EInvoice;
use App\Models\EntityType;
use App\Models\EstimateQuoteTitle;
use App\Models\EwayBillAPICredentials;
use App\Models\ExpenseCreditNoteItemTransaction;
use App\Models\ExpenseCreditNoteLedgerTransaction;
use App\Models\ExpenseCreditNoteTransaction;
use App\Models\ExpenseDebitNoteItemTransaction;
use App\Models\ExpenseDebitNoteLedgerTransaction;
use App\Models\ExpenseDebitNoteTransaction;
use App\Models\FinancialYearOpeningBalance;
use App\Models\Franchise;
use App\Models\GstDashboardData;
use App\Models\GstFiling;
use App\Models\GstrLogin;
use App\Models\GstTax;
use App\Models\IncomeCreditNoteItemTransaction;
use App\Models\IncomeCreditNoteLedgerTransaction;
use App\Models\IncomeCreditNoteTransaction;
use App\Models\IncomeDebitNoteItemTransaction;
use App\Models\IncomeDebitNoteLedgerTransaction;
use App\Models\IncomeDebitNoteTransaction;
use App\Models\IncomeEstimateQuoteItemInvoice;
use App\Models\IncomeEstimateQuoteTransaction;
use App\Models\InvoicesLabel;
use App\Models\ItemStockClosingBalance;
use App\Models\JournalCreditCustomerTransaction;
use App\Models\JournalDebitCreditTransaction;
use App\Models\JournalDebitSupplierTransaction;
use App\Models\JournalTransaction;
use App\Models\Ledger;
use App\Models\Location;
use App\Models\LockTransaction;
use App\Models\Master;
use App\Models\Master\Customer;
use App\Models\Master\ExpenseCreditNoteTransactionMaster;
use App\Models\Master\ExpenseDebitNoteTransactionMaster;
use App\Models\Master\ExpenseReturnTransactionMaster;
use App\Models\Master\ExpenseTransactionMaster;
use App\Models\Master\IncomeCreditNoteTransactionMaster;
use App\Models\Master\IncomeDebitNoteTransactionMaster;
use App\Models\Master\IncomeEstimateQuoteTransactionMaster;
use App\Models\Master\IncomeReturnTransactionMaster;
use App\Models\Master\IncomeTransactionMaster;
use App\Models\Master\ItemMaster;
use App\Models\Master\ItemMasterGoods;
use App\Models\Master\ItemMasterService;
use App\Models\Master\JournalTransactionMaster;
use App\Models\Master\ProfitAndLoss;
use App\Models\Master\PurchaseOrderTransactionMaster;
use App\Models\Master\StockInHand;
use App\Models\Master\Supplier;
use App\Models\Master\Transport;
use App\Models\Notification;
use App\Models\PaymentMode;
use App\Models\PaymentTransaction;
use App\Models\PaymentTransactionItem;
use App\Models\Plan;
use App\Models\PurchaseItemTransaction;
use App\Models\PurchaseLedgerTransaction;
use App\Models\PurchaseOrderItemInvoice;
use App\Models\PurchaseOrderTitle;
use App\Models\PurchaseOrderTransaction;
use App\Models\PurchaseReturnItemTransaction;
use App\Models\PurchaseReturnLedgerTransaction;
use App\Models\PurchaseReturnTransaction;
use App\Models\PurchaseTransaction;
use App\Models\ReceiptTransaction;
use App\Models\ReceiptTransactionItem;
use App\Models\RecurringInvoice;
use App\Models\Role;
use App\Models\SadminSetting;
use App\Models\SaleReturnItemTransaction;
use App\Models\SaleReturnLedgerTransaction;
use App\Models\SaleReturnTransaction;
use App\Models\SaleTransaction;
use App\Models\SaleTransactionItem;
use App\Models\SaleTransactionLedger;
use App\Models\State;
use App\Models\Subscription;
use App\Models\TaxClassificationDetails;
use App\Models\TcsRate;
use App\Models\TdsRate;
use App\Models\TeamManagement;
use App\Models\ThirdPartyAPIKey;
use App\Models\Transaction;
use App\Models\UnitOfMeasurement;
use App\Models\User;
use App\Models\VastraDeliveryChallan;
use App\Models\WhatsappTransaction;
use App\Repositories\TrialBalanceRepository;
use App\Scopes\CompanyScope;
use App\Scopes\RecurringScope;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\HigherOrderBuilderProxy as HigherOrderBuilderProxyAlias;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Response as HttpResponse;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

if (! function_exists('getLoginUser')) {
    /**
     * @return User|Authenticatable|null
     */
    function getLoginUser()
    {
        /** @var User $authUser */
        $authUser = null;

        if ($authUser === null) {
            $authUser = Auth::user();
        }

        if (str_contains(request()->url(), url('/react-api')) && request()->hasHeader('user')) {
            $userId = request()->header('user');
            $authUser = User::findOrFail($userId);
        }

        if (str_contains(request()->url(), url('/third-party')) && request()->hasHeader('ApiKey')) {
            $apiKey = request()->header('ApiKey');
            $keyCheck = ThirdPartyAPIKey::where('app_key', $apiKey)->where('expires_at', '>', now())->where('status', true)->first();
            $currentCompany = Company::findOrFail($keyCheck->company_id);
            $authUser = User::where('id', $currentCompany->user_id)->first();
        }

        return $authUser;
    }
}

if (! function_exists('getLoggedInEmail')) {
    /**
     * @return User|Authenticatable|null
     */
    function getLoggedInEmail()
    {
        return Auth::user() ? Auth::user()->email : null;
    }
}

if (! function_exists('getAccount')) {
    /**
     * @return Account
     */
    function getAccount()
    {
        /** @var Account $account */
        static $account;

        if ($account === null || (App::environment('testing'))) {
            $account = Account::where('id', getLoginUser()->account_id)->first();
        }

        if (str_contains(request()->url(), url('/react-api')) && request()->hasHeader('account')) {
            $accountId = request()->header('account');
            $account = Account::findOrFail($accountId);
        }

        if (str_contains(request()->url(), url('/third-party')) && request()->hasHeader('ApiKey')) {
            $apiKey = request()->header('ApiKey');
            $keyCheck = ThirdPartyAPIKey::where('app_key', $apiKey)->where('expires_at', '>', now())->where('status', true)->first();
            $currentCompany = Company::findOrFail($keyCheck->company_id);
            $account = Account::where('id', $currentCompany->account_id)->first();
        }

        return $account;
    }
}

if (! function_exists('getRedirectUri')) {

    function getRedirectUri(): string
    {
        /** @var User $user */
        $user = getLoginUser();

        if ($user->hasRole(Role::SALES_USER)) {
            return route('admin.company-list.index');
        }

        if ($user->hasRole(Role::CHANNEL_PARTNER)) {
            return route('channel-partner.dashboard');
        }

        if ($user->hasRole([Role::HO_ADMIN, Role::HO_ASSOCIATE, Role::HO_ASSOCIATE_PRINCIPAL])) {

            return route('admin.dashboard');
        }

        if ($user->hasRole([Role::FRANCHISE_ADMIN, Role::FRANCHISE_SENIOR_MANAGER])) {

            return route('franchise.dashboard');
        }

        if ($user->hasRole([
            Role::FRANCHISE_JUNIOR_ACCOUNT_EXECUTIVE,
            Role::FRANCHISE_SENIOR_ACCOUNT_EXECUTIVE,
            Role::FRANCHISE_MANAGER,
            Role::FRANCHISE_OFFICE_EXECUTIVE,
            Role::FRANCHISE_SALES_PERSON,
        ])) {

            return route('franchise.clients.index');
        }

        if ($user->hasRole([Role::HO_USER])) {
            if ($user->can('company_view_dashboard')) {
                return route('admin.dashboard');
            } elseif ($user->can('admin_view_franchise')) {
                return route('admin.franchises.index');
            } elseif ($user->can('admin_view_companies')) {
                return route('admin.company-list.index');
            } elseif ($user->can('admin_view_plan')) {
                return route('admin.plans.index');
            } elseif ($user->can('admin_view_subscription')) {
                return route('admin.subscription.index');
            } elseif ($user->can('admin_view_add_company_to_franchise')) {
                return route('admin.add-company-into-franchise.index');
            } elseif ($user->can('admin_view_tutorial_video')) {
                return route('admin.tutorial.index');
            } elseif ($user->can('admin_view_business_category')) {
                return route('admin.business-category.index');
            } elseif ($user->can('admin_view_user')) {
                return route('sadmin-team-management.index');
            } elseif ($user->can('admin_view_global_search')) {
                return route('admin.global-search.index');
            } elseif ($user->can('admin_view_setting')) {
                return route('admin.sadmin-setting.index');
            } elseif ($user->can('admin_view_whatsapp_transaction')) {
                return route('admin.whatsapp-transaction.index');
            } elseif ($user->can('admin_view_banner')) {
                return route('admin.banner.index');
            } elseif ($user->can('admin_view_notification')) {
                return route('admin.notification.index');
            } elseif ($user->can('admin_view_sales_person')) {
                return route('admin.sale-person.index');
            } elseif ($user->can('admin_view_lead_source')) {
                return route('admin.lead-source.index');
            }

        }

        if ($user->hasRole([Role::CLIENT_ADMIN, Role::CLIENT_USER, Role::CONSULTANT, Role::CONSULTANT_USER])) {
            // if (! getCurrentCompany()) {
            //     return route('company.companies.index');
            // }

            if (Request()->rdt == 'dlt') {
                return route('delete-company');
            }

            if (empty(session('current_company'))) {

                if ($user->hasRole(Role::CONSULTANT_USER)) {
                    $companiesCount = ConsultantTeamManagement::whereUserId($user->id)->get();
                } else {
                    $companiesCount = CompanyTeamManagement::whereUserId($user->id)->get();
                }

                if (count($companiesCount) == 0) {
                    return route('company.companies.index');
                }

                $freshCompany = Company::whereId($companiesCount[0]->company_id)->first();
                if (count($companiesCount) == 1 && ! empty($companiesCount[0]->company_id) && ! empty($freshCompany)) {
                    return route('company.set-company', $companiesCount[0]->company_id);
                } else {
                    $lastUsedCompanyId = User::whereId($user->id)->value('last_used_company_id');
                    $companyExists = Company::whereId($lastUsedCompanyId)->exists();

                    if (! empty($lastUsedCompanyId) && $companyExists) {
                        return route('company.set-company', $lastUsedCompanyId);
                    }

                    return route('company.companies.index');
                }
            }

            if ($user->hasRole([Role::CLIENT_USER, Role::CONSULTANT, Role::CONSULTANT_USER])) {
                setPermissionsTeamId(getCurrentCompany()->id);
            }

            if (! getCurrentCompany() && empty(getCurrentCompany()->franchise_id)) {
                return route('company.companies.index');
            }
            if ($user->can('company_view_dashboard')) {
                return route('company.dashboard');
            } elseif ($user->can('company_view_income') || $user->can('company_summery_income_transactions')) {
                return route('company.sales.index');
            } elseif ($user->can('company_view_expense') || $user->can('company_summery_expense_transactions')) {
                return route('company.purchases.index');
            } elseif ($user->can('company_view_receipt') || $user->can('company_summery_receipt_transactions')) {
                return route('company.transaction-receipt.index');
            } elseif ($user->can('company_view_payment') || $user->can('company_summery_payment_transactions')) {
                return route('company.transaction-payment.index');
            } elseif ($user->can('company_view_journal') || $user->can('company_summery_journal_transactions')) {
                return route('company.transaction-journal.index');
            } elseif ($user->can('company_view_groups') || $user->can('company_summery_groups_master')) {
                return route('company.groups.index');
            } elseif ($user->can('company_view_ledgers') || $user->can('company_summery_ledgers_master')) {
                return route('company.ledgers.index');
            } elseif ($user->can('company_view_transaction_masters') || $user->can('company_summery_transaction_masters')) {
                return route('company.income-transaction.index');
            } elseif ($user->can('company_view_item_masters') || $user->can('company_summery_item_masters')) {
                return route('company.item-masters.index');
            } elseif ($user->can('company_view_broker_masters') || $user->can('company_summery_broker_masters')) {
                return route('company.broker-master.index');
            } elseif ($user->can('company_view_transport_masters') || $user->can('company_summery_transport_masters')) {
                return route('company.transport-master.index');
            }
            // elseif ($user->can('company_view_cess_rate') || $user->can('company_summery_cess_rate')) {
            //     return route('company.cess-rates.index');
            // }
            elseif (
                $user->can('company_view_ledger_report') || $user->can('company_view_outstanding_report') || $user->can('company_view_ageing_report') ||
                $user->can('company_view_sale_report') || $user->can('company_view_purchase_report') || $user->can('company_view_day_book_report') ||
                $user->can('company_view_cash_bank_report') || $user->can('company_view_stock_report') || $user->can('company_view_broker_report') ||
                $user->can('company_view_trial_balance_report') || $user->can('company_view_trading_profit_loss_report') || $user->can('company_view_balance_sheet_report') ||
                $user->can('company_view_cash_flow_report') || $user->can('company_view_item_stock') || $user->can('company_summery_item_stocks')
            ) {

                return route('company.reports-menu', ['section' => 'financial-reports']);
            } elseif (
                $user->can('company_view_eway_bill_report') || $user->can('company_view_einvoice_report') || $user->can('company_view_gstr_1_report') ||
                $user->can('company_view_gstr_3b_report') || $user->can('company_view_input_tax_report') || $user->can('company_view_output_tax_report') ||
                $user->can('company_view_hsn_outward_report') || $user->can('company_view_hsn_inward_report') || $user->can('company_view_tds_liability_report') ||
                $user->can('company_view_tds_return_report') || $user->can('company_view_tcs_liability_report') || $user->can('company_view_tcs_return_report')
            ) {

                return route('company.reports-menu', ['section' => 'tax-reports']);
            }
        }

        abort(HttpResponse::HTTP_FORBIDDEN);
    }
}

if (! function_exists('getSuperAdmin')) {
    /**
     * @return string|void
     */
    function getSuperAdmin()
    {
        static $superAdmin;
        if ($superAdmin === null) {
            $superAdmin = User::role(Role::HO_ADMIN)->first();
        }

        return $superAdmin;
    }
}

if (! function_exists('getAppLogo')) {

    function getAppLogo(): string
    {
        return asset('assets/images/hisab_kitab_logo.png');
    }
}

if (! function_exists('getAppName')) {

    function getAppName(): string
    {
        return config('app.name');
    }
}

if (! function_exists('getFaviconName')) {

    function getFaviconName(): string
    {
        return asset('assets/images/hisab_kitab_favicon.png');
    }
}

if (! function_exists('getCountries')) {
    function getCountries(): mixed
    {
        static $countries;
        if (empty($countries)) {
            $countries = Country::orderBy('name', 'ASC')->toBase()->pluck('name', 'id');
        }

        return $countries;
    }
}

if (! function_exists('getStates')) {
    function getStates($countryId): array
    {
        return State::whereCountryId($countryId)->orderBy('name', 'ASC')->toBase()
            ->pluck('name', 'id')->toArray();
    }
}

if (! function_exists('getStateNameAscending')) {
    function getStateNameAscending($countryId): array
    {
        return State::whereCountryId($countryId)->orderBy('name', 'ASC')->toBase()
            ->pluck('id', 'name')->toArray();
    }
}

if (! function_exists('getCities')) {
    function getCities($stateId): array
    {
        return City::whereStateId($stateId)->orderBy('name', 'ASC')->toBase()
            ->pluck('name', 'id')->toArray();
    }
}

if (! function_exists('getLoginUserProfile')) {

    function getLoginUserProfile(): ?string
    {
        /** @var User $user */
        $user = getLoginUser();

        if ($user->hasRole(Role::CLIENT_ADMIN) || $user->hasRole(Role::HO_ADMIN) || $user->hasRole(Role::CLIENT_USER)) {
            return $user->admin_profile_image ?? null;
        }

        return $user->profile_image ?? null;
    }
}

if (! function_exists('getCurrentCompany')) {
    /**
     * @return Company|Builder|null
     */
    function getCurrentCompany()
    {
        /** @var Company $currentCompany */
        $currentCompany = null;
        if (str_contains(request()->url(), url('/api'))) {

            if (request()->hasHeader('company')) {
                $companyId = request()->header('company');

                $currentCompany = Company::findOrFail($companyId);
            } else {
                return Session::get('current_company');
                // throw new Exception('Company not found.');
            }
        }

        if (str_contains(request()->url(), url('/react-api')) && request()->hasHeader('company')) {
            $companyId = request()->header('company');
            $currentCompany = Company::findOrFail($companyId);
        }

        // if (str_contains(request()->url(), url('/third-party')) && request()->hasHeader('company')) {
        //     $companyId = request()->header('company');
        //     $currentCompany = Company::findOrFail($companyId);
        // }

        if (str_contains(request()->url(), url('/third-party')) && request()->hasHeader('ApiKey')) {
            $apiKey = request()->header('ApiKey');
            $keyCheck = ThirdPartyAPIKey::where('app_key', $apiKey)->where('expires_at', '>', now())->where('status', true)->first();
            $currentCompany = Company::findOrFail($keyCheck->company_id);
        }

        if (Session::exists('company') && request()->segment(1) == 'invoice') {
            return Session::get('company');
        }

        if (Session::exists('current_company')) {
            $currentCompany = Session::get('current_company');
        }

        return $currentCompany;
    }
}

if (! function_exists('getCurrentFranchise')) {
    /**
     * @return Company|Franchise|null
     */
    function getCurrentFranchise()
    {
        /** @var Franchise $currentFranchise */
        static $currentFranchise;
        if ($currentFranchise === null) {
            $currentFranchise = getLoginUser()->franchise;
        }

        if (empty($currentFranchise)) {
            $currentFranchise = getLoginUser()->teamManagement?->franchise;
        }

        return $currentFranchise;
    }
}

if (! function_exists('generateFranchiseUniqCode')) {
    /**
     * @throws Exception
     */
    function generateFranchiseUniqCode($stateId): string
    {
        $stateCodePrefix = State::whereId($stateId)->first()->state_code;
        $franchiseLastCode = Franchise::whereStateId($stateId)->orderBy('unique_code', 'DESC')->first();
        if ($franchiseLastCode) {
            $code = substr($franchiseLastCode->unique_code, 2, 3);
            $franchiseUniqueCode = $stateCodePrefix.($code + 1);
        } else {
            $codeNumber = 101;
            $franchiseUniqueCode = $stateCodePrefix.$codeNumber;
        }
        $uniqueCodeExists = Franchise::whereUniqueCode($franchiseUniqueCode)->exists();

        if ($uniqueCodeExists) {
            return generateFranchiseUniqCode($stateId);
        }

        return $franchiseUniqueCode;
    }
}

if (! function_exists('generateCompanyUniqueCode')) {

    /**
     * @throws Exception
     */
    function generateCompanyUniqueCode($franchiseId = null): string
    {
        if (empty($franchiseId)) {
            $franchiseId = getFranchiseId();
        }
        $franchiseRecord = Franchise::where('id', $franchiseId)->first();
        //        $franchiseCode = getCurrentFranchise()->unique_code;
        $franchiseCode = $franchiseRecord->unique_code;
        $alphaValue = 65;
        $lastFranchiseCompanyCode = Company::whereFranchiseId($franchiseId)
            ->orderBy('unique_code', 'DESC')->first();
        if ($lastFranchiseCompanyCode) {
            $lastCompanyCodeCharacter = $lastFranchiseCompanyCode->unique_code[5];
            $getCompanyCodeLastNumber = substr($lastFranchiseCompanyCode->unique_code, 6, 2);
            if ($getCompanyCodeLastNumber == 99) {
                $changeCharterAsciValue = ord($lastCompanyCodeCharacter) + 1;
                $alphaValue = chr($changeCharterAsciValue); // change alpha prefix
                $companyCode = $franchiseCode.$alphaValue.'01';
            } else {
                $changeCharterAsciValue = ord($lastCompanyCodeCharacter);
                $alphaValue = chr($changeCharterAsciValue); // change alpha prefix
                if ($lastFranchiseCompanyCode->unique_code[6] == 0 && $getCompanyCodeLastNumber != '09') {
                    $companyCode = $franchiseCode.$alphaValue.'0'.($getCompanyCodeLastNumber + 01);
                } else {
                    $companyCode = $franchiseCode.$alphaValue.($getCompanyCodeLastNumber + 01);
                }
            }
        } else {
            $companyCode = $franchiseCode.chr($alphaValue).'01';
        }

        return $companyCode;
    }
}

if (! function_exists('getParentGroup')) {
    /**
     * @return mixed
     */
    function getParentGroup($companyGroup)
    {
        $checkParentGroup = $companyGroup->with('parentGroup')->where('id', $companyGroup->id)->first();
        if (! empty($checkParentGroup->parentGroup)) {
            return getParentGroup($checkParentGroup->parentGroup);
        }

        return $checkParentGroup;
    }
}

if (! function_exists('getParentGroupName')) {
    /**
     * @return mixed
     */
    function getParentGroupName($companyGroup, &$groups)
    {
        $checkParentGroup = $companyGroup->with('parentGroup')->where('id', $companyGroup->id)->first();
        if (! empty($checkParentGroup->parentGroup)) {

            getParentGroupName($checkParentGroup->parentGroup, $groups);
        }
        $groups[] = $checkParentGroup->name;

        return $checkParentGroup;
    }
}

if (! function_exists('getGroup')) {
    /**
     * @return HigherOrderBuilderProxyAlias|mixed|string
     */
    function getGroup($groupId)
    {
        $companyGroup = CompanyGroup::whereCompanyId(getCurrentCompany()->id)->whereId($groupId)->with('parentGroup')->firstOrFail();
        $companyGroupDetails = $companyGroup;

        $groupName = [];
        $groupName[] = $companyGroup->name;
        $checkParentGroup = $companyGroup->with('parentGroup')->where('id', $companyGroup->id)->first();
        if (! empty($checkParentGroup->parentGroup)) {
            getParentGroupName($checkParentGroup->parentGroup, $groupName);
        }

        if (! in_array('TDS Receivable', $groupName) && ! in_array('TCS Receivable', $groupName)) {
            if (! empty($companyGroup->parentGroup)) {
                $companyGroupDetails = getParentGroup($companyGroup->parentGroup);
            }
        } else {
            if (in_array('TDS Receivable', $groupName)) {
                $companyGroupDetails = CompanyGroup::whereCompanyId(getCurrentCompany()->id)->whereName(Ledger::TDS_RECEIVABLE)->firstOrFail();
            } elseif (in_array('TCS Receivable', $groupName)) {
                $companyGroupDetails = CompanyGroup::whereCompanyId(getCurrentCompany()->id)->whereName(Ledger::TCS_RECEIVABLE)->firstOrFail();
            }
        }

        return $companyGroupDetails;
    }
}
if (! function_exists('getPanCardEntityType')) {

    function getPanCardEntityType($entityId): Collection|string|array
    {
        if (! empty($entityId) && $entityId) {
            $companyPanEntityCharacter = EntityType::whereId($entityId)->first()->character;

            return EntityType::whereCharacter($companyPanEntityCharacter)->get();
        }

        return '';
    }
}
if (! function_exists('isImage')) {

    function isImage($file): bool
    {
        return in_array($file->mime_type, ['image/png', 'image/jpeg']);
    }
}

if (! function_exists('returnAttachmentLink')) {
    /**
     * @return string
     */
    function returnAttachmentLink($file)
    {
        if (str_contains($file->mime_type, 'video/')) {
            return asset('images/video.png');
        }

        return returnFileThumbnail($file);
    }
}

if (! function_exists('returnFileThumbnail')) {

    function returnFileThumbnail($file): string
    {
        $extension = $file->mime_type;

        return match ($extension) {
            'application/zip' => asset('assets/images/zip.png'),
            'application/pdf' => asset('assets/images/pdf.png'),
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => asset('assets/images/xls.png'),
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => asset('assets/images/docx.png'),
            'application/vnd.ms-excel' => asset('assets/images/xlse.png'),
            'application/vnd.ms-powerpoint' => asset('assets/images/ppt.png'),
            default => $file->getFullUrl(),
        };
    }
}

if (! function_exists('removeCommaSeparator')) {
    /**
     * @return array|string|string[]|null
     */
    function removeCommaSeparator($number)
    {
        if ($number) {
            return str_replace(',', '', $number);
        }

        return null;
    }
}

if (! function_exists('getCurrencySymbol')) {

    function getCurrencySymbol(): string
    {
        if (getCurrentCompany()) {
            $companySettings = getCompanySettings();
        }

        if (! isset($companySettings['show_currency'])) {
            return '₹';
        }

        if (! $companySettings['show_currency']) {
            return '';
        }

        $currencyOptions = getCurrencyOptions();

        return getCurrencyIcons()[$companySettings['default_currency'] ?? $currencyOptions['in']] ?? $currencyOptions['in'];
    }
}

if (! function_exists('getCurrencyOptions')) {
    function getCurrencyOptions(): array
    {
        return getCurrencyIcons();
    }
}

if (! function_exists('getCurrencyIcons')) {
    function getCurrencyIcons(): array
    {
        return [
            'in' => '₹',    // Indian Rupee
            // 'af' => '؋',   // Afghanistan Afghani
            'al' => 'L ',   // Albanian Lek
            'dz' => 'دج',  // Algerian Dinar
            'ao' => 'Kz ',  // Angolan Kwanza
            // 'ar' => '$',   // Argentine Peso
            // 'am' => '֏',   // Armenian Dram
            // 'au' => '$',   // Australian Dollar
            'at' => '€',   // Euro
            // 'az' => '₼',   // Azerbaijani Manat
            // 'bs' => '$',   // Bahamian Dollar
            'bh' => '.د.ب', // Bahraini Dinar
            // 'bd' => '৳',   // Bangladeshi Taka
            // 'bb' => '$',   // Barbadian Dollar
            'by' => 'Br ',  // Belarusian Ruble
            // 'be' => '€',   // Euro
            // 'bz' => '$',   // Belize Dollar
            // 'bj' => 'CFA', // West African CFA Franc
            'bt' => 'Nu. ', // Bhutanese Ngultrum
            'bo' => 'Bs. ', // Bolivian Boliviano
            'ba' => 'KM ',  // Bosnian Convertible Mark
            'bw' => 'P ',   // Botswanan Pula
            'br' => 'R$',  // Brazilian Real
            // 'bn' => '$',   // Brunei Dollar
            'bg' => 'лв',  // Bulgarian Lev
            // 'bf' => 'CFA', // West African CFA Franc
            'bi' => 'FBu ', // Burundian Franc
            // 'kh' => '៛',   // Cambodian Riel
            // 'cm' => 'CFA', // Central African CFA Franc
            // 'ca' => '$',   // Canadian Dollar
            // 'cv' => '$',   // Cape Verdean Escudo
            // 'cf' => 'CFA', // Central African CFA Franc
            // 'td' => 'CFA', // Central African CFA Franc
            // 'cl' => '$',   // Chilean Peso
            'cn' => '¥',   // Chinese Yuan
            // 'co' => '$',   // Colombian Peso
            'km' => 'CF ',  // Comorian Franc
            'cd' => 'FC ',  // Congolese Franc
            // 'cg' => 'CFA', // Central African CFA Franc
            'cr' => '₡',   // Costa Rican Colón
            // 'ci' => 'CFA', // West African CFA Franc
            'hr' => 'kn ',  // Croatian Kuna
            // 'cu' => '$',   // Cuban Peso
            // 'cy' => '€',   // Euro
            'cz' => 'Kč',  // Czech Koruna
            'dk' => 'kr ',  // Danish Krone
            'dj' => 'Fdj ', // Djiboutian Franc
            // 'dm' => '$',   // East Caribbean Dollar
            // 'do' => '$',   // Dominican Peso
            // 'ec' => '$',   // United States Dollar
            'eg' => '£',   // Egyptian Pound
            'us' => '$',   // United States Dollar
            // 'gq' => 'CFA', // Central African CFA Franc
            'er' => 'Nfk ', // Eritrean Nakfa
            // 'ee' => '€',   // Euro
            'sz' => 'E ',   // Swazi Lilangeni
            'et' => 'Br ',  // Ethiopian Birr
            // 'eu' => '€',   // Euro
            // 'fj' => '$',   // Fijian Dollar
            // 'fi' => '€',   // Euro
            // 'fr' => '€',   // Euro
            'ga' => 'CFA ', // Central African CFA Franc
            'gm' => 'D ',   // Gambian Dalasi
            // 'ge' => '₾',   // Georgian Lari
            // 'de' => '€',   // Euro
            'gh' => '₵',   // Ghanaian Cedi
            // 'gr' => '€',   // Euro
            // 'gd' => '$',   // East Caribbean Dollar
            'gt' => 'Q ',   // Guatemalan Quetzal
            'gn' => 'FG ',  // Guinean Franc
            // 'gw' => 'CFA', // West African CFA Franc
            // 'gy' => '$',   // Guyanese Dollar
            'ht' => 'G ',   // Haitian Gourde
            'hn' => 'L ',   // Honduran Lempira
            // 'hk' => '$',   // Hong Kong Dollar
            'hu' => 'Ft ',  // Hungarian Forint
            'is' => 'kr ',  // Icelandic Króna
            'id' => 'Rp ',  // Indonesian Rupiah
            // 'ir' => '﷼',   // Iranian Rial
            'iq' => 'ع.د', // Iraqi Dinar
            // 'ie' => '€',   // Euro
            'il' => '₪',   // Israeli New Shekel
            // 'it' => '€',   // Euro
            // 'jm' => '$',   // Jamaican Dollar
            'jp' => '¥',   // Japanese Yen
            'jo' => 'د.ا', // Jordanian Dinar
            'kz' => '₸',   // Kazakhstani Tenge
            'ke' => 'Sh ',  // Kenyan Shilling
            // 'ki' => '$',   // Australian Dollar
            'kp' => '₩',   // North Korean Won
            'kr' => '₩',   // South Korean Won
            'kw' => 'د.ك', // Kuwaiti Dinar
            'kg' => 'с',   // Kyrgyzstani Som
            'la' => '₭',   // Lao Kip
            // 'lv' => '€',   // Euro
            'lb' => 'ل.ل', // Lebanese Pound
            'ls' => 'L ',   // Lesotho Loti
            // 'lr' => '$',   // Liberian Dollar
            'ly' => 'ل.د', // Libyan Dinar
            'li' => 'CHF ', // Swiss Franc
            // 'lt' => '€',   // Euro
            // 'lu' => '€',   // Euro
            'mg' => 'Ar ',  // Malagasy Ariary
            'mw' => 'MK ',  // Malawian Kwacha
            'my' => 'RM ',  // Malaysian Ringgit
            'mv' => 'Rf ',  // Maldivian Rufiyaa
            // 'ml' => 'CFA', // West African CFA Franc
            // 'mh' => '$',   // United States Dollar
            'mr' => 'UM ',  // Mauritanian Ouguiya
            // 'mu' => '₨',   // Mauritian Rupee
            // 'mx' => '$',   // Mexican Peso
            // 'fm' => '$',   // United States Dollar
            'md' => 'L ',   // Moldovan Leu
            'mn' => '₮',   // Mongolian Tögrög
            'ma' => 'د.م.', // Moroccan Dirham
            'mz' => 'MT ',  // Mozambican Metical
            'mm' => 'K ',   // Burmese Kyat
            // 'na' => '$',   // Namibian Dollar
            // 'nr' => '$',   // Australian Dollar
            'np' => '₨',   // Nepalese Rupee
            // 'nz' => '$',   // New Zealand Dollar
            'ni' => 'C$',  // Nicaraguan Córdoba
            // 'ne' => 'CFA', // West African CFA Franc
            'ng' => '₦',   // Nigerian Naira
            'no' => 'kr ',  // Norwegian Krone
            'om' => 'ر.ع.', // Omani Rial
            'pk' => '₨',   // Pakistani Rupee
            // 'pw' => '$',   // United States Dollar
            // 'pa' => '$',   // Panamanian Balboa
            'pg' => 'K ',   // Papua New Guinean Kina
            'py' => '₲',   // Paraguayan Guaraní
            'pe' => 'S/',  // Peruvian Sol
            'ph' => '₱',   // Philippine Peso
            'pl' => 'zł',  // Polish Złoty
            'aed' => 'AED ',  // Arab Emirates Dirham (AED)
        ];
    }
}

if (! function_exists('getCurrencyInWords')) {
    function getCurrencyInWords(): array
    {
        return [
            'in' => ['name' => 'Rupees', 'paise' => 'Paise'],
            'al' => ['name' => 'Albanian Lek', 'paise' => 'Qindarka'],
            'dz' => ['name' => 'Algerian Dinar', 'paise' => 'Centime'],
            'ao' => ['name' => 'Angolan Kwanza', 'paise' => 'Cêntimos'],
            'at' => ['name' => 'Euro', 'paise' => 'Euro Cents'],
            'bh' => ['name' => 'Bahraini Dinar', 'paise' => 'Fils'],
            'by' => ['name' => 'Belarusian Ruble', 'paise' => 'Kopecks'],
            'bt' => ['name' => 'Bhutanese Ngultrum', 'paise' => 'Chetrum'],
            'bo' => ['name' => 'Bolivian Boliviano', 'paise' => 'Centavos'],
            'ba' => ['name' => 'Bosnia and Herzegovina Convertible Mark', 'paise' => 'Fening'],
            'bw' => ['name' => 'Botswana Pula', 'paise' => 'Thebe'],
            'br' => ['name' => 'Brazilian Real', 'paise' => 'Centavos'],
            'bg' => ['name' => 'Bulgarian Lev', 'paise' => 'Stotinki'],
            'bi' => ['name' => 'Burundian Franc', 'paise' => 'Centimes'],
            'cn' => ['name' => 'Chinese Yuan', 'paise' => 'Fen'],
            'km' => ['name' => 'Comorian Franc', 'paise' => 'Centimes'],
            'cd' => ['name' => 'Congolese Franc', 'paise' => 'Centimes'],
            'cr' => ['name' => 'Costa Rican Colón', 'paise' => 'Centimos'],
            'hr' => ['name' => 'Croatian Kuna', 'paise' => 'Lipa'],
            'cz' => ['name' => 'Czech Koruna', 'paise' => 'Haler'],
            'dk' => ['name' => 'Danish Krone', 'paise' => 'Øre'],
            'dj' => ['name' => 'Djiboutian Franc', 'paise' => 'Centimes'],
            'eg' => ['name' => 'Egyptian Pound', 'paise' => 'Piastres'],
            'sv' => ['name' => 'Salvadoran Dollar', 'paise' => 'Centavos'],
            'er' => ['name' => 'Eritrean Nakfa', 'paise' => 'Cents'],
            'sz' => ['name' => 'Swazi Lilangeni', 'paise' => 'Cents'],
            'et' => ['name' => 'Ethiopian Birr', 'paise' => 'Santim'],
            'ga' => ['name' => 'Central African CFA Franc', 'paise' => 'Centimes'],
            'gm' => ['name' => 'Gambian Dalasi', 'paise' => 'Bututs'],
            'gh' => ['name' => 'Ghanaian Cedi', 'paise' => 'Pesewas'],
            'gt' => ['name' => 'Guatemalan Quetzal', 'paise' => 'Centavos'],
            'gn' => ['name' => 'Guinean Franc', 'paise' => 'Centimes'],
            'ht' => ['name' => 'Haitian Gourde', 'paise' => 'Centimes'],
            'hn' => ['name' => 'Honduran Lempira', 'paise' => 'Centavos'],
            'hu' => ['name' => 'Hungarian Forint', 'paise' => 'Fillér'],
            'is' => ['name' => 'Icelandic Krona', 'paise' => 'Auror'],
            'id' => ['name' => 'Indonesian Rupiah', 'paise' => 'Sen'],
            'iq' => ['name' => 'Iraqi Dinar', 'paise' => 'Fils'],
            'il' => ['name' => 'Israeli Shekel', 'paise' => 'Agora'],
            'jp' => ['name' => 'Japanese Yen', 'paise' => 'Sen'],
            'jo' => ['name' => 'Jordanian Dinar', 'paise' => 'Piastres'],
            'kz' => ['name' => 'Kazakhstani Tenge', 'paise' => 'Tïın'],
            'ke' => ['name' => 'Kenyan Shilling', 'paise' => 'Cents'],
            'kp' => ['name' => 'North Korean Won', 'paise' => 'Chŏn'],
            'kr' => ['name' => 'South Korean Won', 'paise' => 'Jeon'],
            'kw' => ['name' => 'Kuwaiti Dinar', 'paise' => 'Fils'],
            'kg' => ['name' => 'Kyrgyzstani Som', 'paise' => 'Tyin'],
            'la' => ['name' => 'Lao Kip', 'paise' => 'Att'],
            'lb' => ['name' => 'Lebanese Pound', 'paise' => 'Piastres'],
            'ls' => ['name' => 'Lesotho Loti', 'paise' => 'Sente'],
            'ly' => ['name' => 'Libyan Dinar', 'paise' => 'Dirhams'],
            'li' => ['name' => 'Liechtenstein Franc', 'paise' => 'Rappen'],
            'mg' => ['name' => 'Malagasy Ariary', 'paise' => 'Iraimbilanja'],
            'mw' => ['name' => 'Malawian Kwacha', 'paise' => 'Tambala'],
            'my' => ['name' => 'Malaysian Ringgit', 'paise' => 'Sen'],
            'mv' => ['name' => 'Maldivian Rufiyaa', 'paise' => 'Laari'],
            'mr' => ['name' => 'Mauritanian Ouguiya', 'paise' => 'Khoums'],
            'md' => ['name' => 'Moldovan Leu', 'paise' => 'Bani'],
            'mn' => ['name' => 'Mongolian Tögrög', 'paise' => 'Möngö'],
            'ma' => ['name' => 'Moroccan Dirham', 'paise' => 'Centimes'],
            'mz' => ['name' => 'Mozambican Metical', 'paise' => 'Centavos'],
            'mm' => ['name' => 'Myanmar Kyat', 'paise' => 'Pya'],
            'np' => ['name' => 'Nepalese Rupees', 'paise' => 'Paisa'],
            'ni' => ['name' => 'Nicaraguan Córdoba', 'paise' => 'Centavos'],
            'ng' => ['name' => 'Nigerian Naira', 'paise' => 'Kobo'],
            'no' => ['name' => 'Norwegian Krone', 'paise' => 'Øre'],
            'om' => ['name' => 'Omani Rial', 'paise' => 'Baisa'],
            'pk' => ['name' => 'Pakistani Rupees', 'paise' => 'Paisa'],
            'pg' => ['name' => 'Papua New Guinean Kina', 'paise' => 'Toea'],
            'py' => ['name' => 'Paraguayan Guaraní', 'paise' => 'Céntimos'],
            'pe' => ['name' => 'Peruvian Sol', 'paise' => 'Céntimos'],
            'ph' => ['name' => 'Philippine Peso', 'paise' => 'Centavos'],
            'pl' => ['name' => 'Polish Zloty', 'paise' => 'Groszy'],
            'aed' => ['name' => 'United Arab Emirates Dirham', 'paise' => 'Fils'],
            'us' => ['name' => 'Dollar', 'paise' => 'Cents'],
        ];
    }
}

if (! function_exists('getCompanySettings')) {
    /**
     * @return array
     */
    function getCompanySettings()
    {
        /** @var CompanySetting $companySettings */
        static $companySettings;

        if (empty($companySettings)) {
            $companySettingCacheKey = generateCacheKey('company_settings');
            Cache::forget($companySettingCacheKey);
            $companySettings = Cache::get($companySettingCacheKey);
            $companySettings = CompanySetting::all()->pluck('value', 'key')->toArray();
            Cache::put($companySettingCacheKey, $companySettings);
        }

        return $companySettings;
    }
}

if (! function_exists('getCurrencyFormat')) {

    function getCurrencyFormat($number): ?string
    {
        $settingDecimal = getCompanyFixedDigitNumber() ?? 2;
        $isNegativeAmount = false;

        if ($number < '0') {
            $number = abs($number);
            $isNegativeAmount = true;
        }

        if ($number == null) {
            $number = abs($number);
            $isNegativeAmount = false;
        }

        $number = (string) $number;

        $decimal = '.'.(explode('.', $number)[1] ?? '0000');

        $money = (int) explode('.', $number)[0] ?? $number;
        $length = strlen($money);
        $delimiter = '';
        $money = strrev($money);
        for ($i = 0; $i < $length; $i++) {
            if (($i == 3 || ($i > 3 && ($i - 1) % 2 == 0))) {
                $delimiter .= ',';
            }
            $delimiter .= $money[$i];
        }
        $result = strrev($delimiter);
        $decimal = preg_replace("/0\./i", '.', $decimal);
        $decimal = substr($decimal, 0, $settingDecimal + 1);
        if ($decimal != '0') {
            if (strlen($decimal) != $settingDecimal + 1) {
                $decimal = $decimal.''.str_repeat('0', $settingDecimal + 1 - strlen($decimal));
            }
            $result .= $decimal;
        }
        //check point value
        if (! strpos($result, '.')) {
            $result .= '.'.str_repeat('0', $settingDecimal);
        }
        if ($isNegativeAmount) {
            $result = '-'.$result;
        }

        return $result;
    }
}

if (! function_exists('getCurrencyFormatFor3digit')) {

    function getCurrencyFormatFor3digit($number): ?string
    {

        $fixDigit = (strlen(substr(strrchr($number, '.'), 1)) > 1) ? strlen(substr(strrchr($number, '.'), 1)) : 2;

        $settingDecimal = $fixDigit;
        $isNegativeAmount = false;

        if ($number < '0') {
            $number = abs($number);
            $isNegativeAmount = true;
        }

        if ($number == null) {
            $number = abs($number);
            $isNegativeAmount = false;
        }

        $number = (string) $number;

        $decimal = '.'.(explode('.', $number)[1] ?? '0000');

        $money = (int) explode('.', $number)[0] ?? $number;
        $length = strlen($money);
        $delimiter = '';
        $money = strrev($money);
        for ($i = 0; $i < $length; $i++) {
            if (($i == 3 || ($i > 3 && ($i - 1) % 2 == 0))) {
                $delimiter .= ',';
            }
            $delimiter .= $money[$i];
        }
        $result = strrev($delimiter);
        $decimal = preg_replace("/0\./i", '.', $decimal);
        $decimal = substr($decimal, 0, $settingDecimal + 1);
        if ($decimal != '0') {
            if (strlen($decimal) != $settingDecimal + 1) {
                $decimal = $decimal.''.str_repeat('0', $settingDecimal + 1 - strlen($decimal));
            }
            $result .= $decimal;
        }
        //check point value
        if (! strpos($result, '.')) {
            $result .= '.'.str_repeat('0', $settingDecimal);
        }
        if ($isNegativeAmount) {
            $result = '-'.$result;
        }

        return $result;
    }
}

if (! function_exists('deleteOldRecord')) {

    function deleteOldRecord($ledger)
    {
        $ledger->model?->delete();
    }
}

if (! function_exists('groupArrayList')) {

    function groupArrayList(): array
    {
        return [
            'Fixed Asset' => 'fixed_asset',
            'Loan and Advance (Asset)' => 'loan_and_advance_asset',
            'Investment' => 'investment',
            'Misc Assets' => 'misc_assets',
            'Cash' => 'cash',
            'Bank' => 'bank',
            'Capital' => 'capital',
            'Reserve and Surplus' => 'reserve_and_surplus',
            'Secured Loan' => 'secured_loan',
            'Unsecured Loan' => 'unsecured_loan',
            'Taxes - GST' => 'taxes_gst',
            'Taxes - TDS' => 'taxes_tds',
            'Taxes - TCS' => 'taxes_tcs',
            'Provisions' => 'provision',
            'Income' => 'income',
            'Expense' => 'expense',
            'Income Transaction' => 'income-transaction',
            'Expense Transaction' => 'expense-transaction',
            'Receipt Transaction' => 'receipt-transaction',
            'Payment Transaction' => 'payment-transaction',
            'Journal Transaction' => 'journal-transaction',
            'Transport Master' => 'transport-master',
            'Broker Master' => 'broker-master',
            'Stock in hand' => 'stock_in_hand',
            'Item Master - Goods - GST Not Enabled' => 'item-master-goods-not-enabled',
            'Item Master - Goods - GST Enabled' => 'item-master-goods-enabled',
            'Item Master - Service - GST Not Enabled' => 'item-master-not-enabled',
            'Item Master - Service - GST Enabled' => 'item-master-enabled',
            'Customers' => 'customer',
            'Supplier' => 'supplier',
            'Profit & Loss A/c' => 'profit-and-loss',
            'Other Current Liabilities' => 'other_current_liabilities',
            'Other Current Assets' => 'other_current_assets',
            'TDS Receivable' => 'taxes_tds',
            'TCS Receivable' => 'taxes_tcs',
        ];
    }
}

if (! function_exists('isCompanyGstApplicable')) {

    function isCompanyGstApplicable(): mixed
    {
        return getCurrentCompany()->is_gst_applicable;
    }
}

if (! function_exists('getGstTaxes')) {
    /**
     * @return mixed
     */
    function getGstTaxes()
    {
        static $gstTaxes;

        $gstTaxesArr = [
            'NA',
            'Exempt',
            '0%',
            '0.1%',
            '0.25%',
            '1%',
            '1.5%',
            '3%',
            '5%',
            '6%',
            '7.5%',
            '12%',
            '13.8%',
            '18%',
            '28%',
        ];
        if (empty($gstTaxes)) {
            $gstTaxes = GstTax::whereIn('name', $gstTaxesArr)
                ->orderByRaw('FIELD (name, "'.implode('","', $gstTaxesArr).'")')
                ->toBase()->pluck('name', 'id')->toArray();
        }

        //        if(empty($gstTaxes)) {
        //            $gstTaxes = GstTax::orderBy('tax_rate', 'ASC')->toBase()->pluck('name', 'id')->toArray();
        //        }

        return $gstTaxes;
    }
}

if (! function_exists('getParentGroupsValue')) {
    /**
     * @return array
     */
    function getParentGroupsValue($ledgerName)
    {
        $currentCompanyId = getCurrentCompany()->id;

        $finalGroups = [];

        /** @var CompanyGroup $mainParentCompany */
        $mainParentCompany = CompanyGroup::whereName($ledgerName)->whereCompanyId($currentCompanyId)
            ->with('childGroups')
            ->first();

        if (! empty($mainParentCompany)) {
            $finalGroups[$mainParentCompany->id] = $mainParentCompany->name;

            foreach ($mainParentCompany->childGroups as $childGroup) {
                $finalGroups[$childGroup->id] = $childGroup->name;

                if ($childGroup->childGroups) {
                    getChildGroupsValue($childGroup->id, $finalGroups, $currentCompanyId);
                }
            }
        }

        return $finalGroups;
    }
}

if (! function_exists('getChildGroupsValue')) {

    function getChildGroupsValue($parentId, &$finalGroups, $currentCompanyId = null)
    {
        $currentCompanyId = $currentCompanyId ?? getCurrentCompany()->id;

        /** @var CompanyGroup $mainParentCompanies */
        $mainParentCompanies = CompanyGroup::whereParentId($parentId)->whereCompanyId($currentCompanyId)
            ->with('childGroups')
            ->get();

        foreach ($mainParentCompanies as $mainParentCompany) {
            $finalGroups[$mainParentCompany->id] = $mainParentCompany->name;
            foreach ($mainParentCompany->childGroups as $childGroup) {
                $finalGroups[$childGroup->id] = $childGroup->name;

                if ($childGroup->childGroups) {
                    getChildGroupsValue($childGroup->id, $finalGroups, $currentCompanyId);
                }
            }
        }
    }
}

if (! function_exists('getGroupLists')) {

    function getGroupLists(): array
    {
        static $companyGroups;

        $currentCompany = getCurrentCompany();
        $defaultGroups = CompanyGroup::with(['childGroups'])
            ->whereCompanyId($currentCompany->id)
            ->whereIn('name', [Ledger::ITEM_DEFAULT_GROUP, Ledger::PROFIT_AND_LOSS])
            ->get();
        $result = [];
        foreach ($defaultGroups as $group) {
            $result[$group->id] = $group->name;
            if (! empty($group->childGroups)) {
                foreach ($group->childGroups as $childGroup) {
                    $result[$childGroup->id] = $childGroup->name;
                    getChildGroupsValue($childGroup->id, $result, getCurrentCompany()->id);
                }
            }
        }

        if (empty($companyGroups)) {
            $companyGroups = CompanyGroup::whereCompanyId(getCurrentCompany()->id)
                ->whereNotIn('name', $result)
                ->orderBy('name', 'ASC')->pluck('name', 'id')->toArray();

        }

        return $companyGroups;
    }
}

if (! function_exists('getIncomeEstimateQuoteTransactionConfigurationValue')) {

    /**
     * @return false|HigherOrderBuilderProxyAlias|mixed
     */
    function getIncomeEstimateQuoteTransactionConfigurationValue($field)
    {
        if ($field) {
            return IncomeEstimateQuoteConfiguration::whereCompanyId(getCurrentCompany()->id)->first()->$field;
        }

        return false;
    }
}

if (! function_exists('getPurchaseOrderTransactionConfigurationValue')) {

    function getPurchaseOrderTransactionConfigurationValue($field)
    {
        if ($field) {
            return PurchaseOrderConfiguration::whereCompanyId(getCurrentCompany()->id)->first()->$field;
        }

        return false;
    }
}

if (! function_exists('getIncomeTransactionConfigurationValue')) {

    /**
     * @return false|HigherOrderBuilderProxyAlias|mixed
     */
    function getIncomeTransactionConfigurationValue($field)
    {
        if ($field) {
            return SaleConfiguration::whereCompanyId(getCurrentCompany()->id)->first()->$field;
        }

        return false;
    }
}

if (! function_exists('getIncomeDebitNoteTransactionConfigurationValue')) {

    /**
     * @return false|HigherOrderBuilderProxyAlias|mixed
     */
    function getIncomeDebitNoteTransactionConfigurationValue($field)
    {
        if ($field) {
            return IncomeDebitNote::whereCompanyId(getCurrentCompany()->id)->first()->$field;
        }

        return false;
    }
}

if (! function_exists('getExpenseTransactionConfigurationValue')) {

    /**
     * @return false|HigherOrderBuilderProxyAlias|mixed
     */
    function getExpenseTransactionConfigurationValue($field)
    {
        if ($field) {
            return PurchaseConfiguration::whereCompanyId(getCurrentCompany()->id)->first()->$field;
        }

        return false;
    }
}

if (! function_exists('getExpenseCreditNoteTransactionConfigurationValue')) {

    /**
     * @return false|HigherOrderBuilderProxyAlias|mixed
     */
    function getExpenseCreditNoteTransactionConfigurationValue($field)
    {
        if ($field) {
            return ExpenseCreditNote::whereCompanyId(getCurrentCompany()->id)->first()->$field;
        }

        return false;
    }
}

if (! function_exists('getPurchaseReturnTransactionConfigurationValue')) {

    /**
     * @return false|HigherOrderBuilderProxyAlias|mixed
     */
    function getPurchaseReturnTransactionConfigurationValue($field)
    {
        if ($field) {
            return PurchaseReturnConfiguration::whereCompanyId(getCurrentCompany()->id)->first()->$field;
        }

        return false;
    }
}

if (! function_exists('getIncomeSaleReturnTransactionConfigurationValue')) {

    /**
     * @return false|HigherOrderBuilderProxyAlias|mixed
     */
    function getIncomeSaleReturnTransactionConfigurationValue($field)
    {
        if ($field) {
            return IncomeSalesReturn::whereCompanyId(getCurrentCompany()->id)->first()->$field;
        }

        return false;
    }
}

if (! function_exists('getExpenseDebitNoteTransactionConfigurationValue')) {

    /**
     * @return false|HigherOrderBuilderProxyAlias|mixed
     */
    function getExpenseDebitNoteTransactionConfigurationValue($field)
    {
        if ($field) {
            return ExpenseDebitNote::whereCompanyId(getCurrentCompany()->id)->first()->$field;
        }

        return false;
    }
}

if (! function_exists('getIncomeCreditNoteTransactionConfigurationValue')) {

    /**
     * @return false|HigherOrderBuilderProxyAlias|mixed
     */
    function getIncomeCreditNoteTransactionConfigurationValue($field)
    {
        if ($field) {
            return IncomeCreditNote::whereCompanyId(getCurrentCompany()->id)->first()->$field;
        }

        return false;
    }
}

if (! function_exists('getGstRate')) {
    function getGstRate(): array
    {
        $gstRate = [];

        foreach (getGstTaxes() as $key => $item) {
            $gstRate[] = [
                'key' => $key,
                'value' => $item,
            ];
        }

        return $gstRate;
    }
}

if (! function_exists('getBrokerMasters')) {
    function getBrokerMasters(): array
    {
        static $brokerMasters;
        if (empty($brokerMasters) || App::environment('testing')) {
            $brokerMasters = Master\Broker::whereCompanyId(getCurrentCompany()->id)
                ->orderBy('broker_name', 'ASC')->toBase()->pluck('broker_name', 'id')->toArray();
        }

        return $brokerMasters;
    }
}

if (! function_exists('getTransportMasters')) {
    function getTransportMasters(): array
    {
        /** @var Master\Transport $transportMasters */
        static $transportMasters;

        if (empty($transportMasters)) {
            $transportMasters = Master\Transport::whereCompanyId(getCurrentCompany()->id)
                ->orderBy('transporter_name', 'ASC')->toBase()
                ->pluck('transporter_name', 'id')->toArray();
        }

        return $transportMasters;
    }
}

if (! function_exists('getCompanyItemLedger')) {
    function getCompanyItemLedger(): array
    {
        static $companyItemLedgers;
        if (empty($companyItemLedgers)) {
            $finalGroups = [];
            $groups = CompanyGroup::whereCompanyId(getCurrentCompany()->id)->orderBy('name', 'ASC')
                ->whereIn('name', [Ledger::INCOME, Ledger::EXPENSE, Ledger::FIXED_ASSET])->get();

            foreach ($groups as $group) {
                $finalGroups[$group->id] = $group->name;
                foreach ($group->childGroups as $childGroup) {
                    $finalGroups[$childGroup->id] = $childGroup->name;

                    if ($childGroup->childGroups) {
                        getChildGroupsValue($childGroup->id, $finalGroups);
                    }
                }
            }

            $companyItemLedgers = Ledger::whereIn('group_id', array_keys($finalGroups))->orderBy('name', 'ASC')->toBase()
                ->pluck('name', 'id')->toArray();

        }

        return $companyItemLedgers;
    }
}

if (! function_exists('getItemMasters')) {
    function getItemMasters(): array
    {
        static $itemMasters;
        if (empty($itemMasters)) {
            $itemMasters = Master\ItemMaster::whereCompanyId(getCurrentCompany()->id)
                ->orderBy('item_name', 'ASC')
                ->toBase()
                ->pluck(DB::raw("CONCAT(item_name, IFNULL(CONCAT(' - ', sku), ''))"), 'id')
                ->toArray();
        }

        return $itemMasters;
    }
}

if (! function_exists('getItemMasterUnit')) {
    function getItemMasterUnit(): array
    {
        static $itemMasterUnits;
        if (empty($itemMasterUnits)) {
            $itemMasterUnits = UnitOfMeasurement::select('unit_of_measurements.*')
                ->where(function ($query) {
                    $query->whereNull('company_id')
                        ->orWhere('company_id', getCurrentCompany()->id);
                })->get()->sortBy('name')->pluck('full_name', 'id')->toArray();
        }

        return $itemMasterUnits;
    }
}

if (! function_exists('getDecimalPlace')) {
    function getDecimalPlace($decimal)
    {
        return GetStepForDecimalPlaceAction::run($decimal);
    }
}

if (! function_exists('getDefaultItemUnit')) {
    function getDefaultItemUnit(): array
    {
        $defaultGroups = UnitOfMeasurement::whereNull('for_gst_use_name')->get()->sortBy('name')->pluck('full_name', 'id')->toArray();

        return $defaultGroups;
    }
}

if (! function_exists('getForGSTItemUnit')) {
    function getForGSTItemUnit($unitCode)
    {
        $code = UnitOfMeasurement::where('code', $unitCode)->first()->for_gst_use_name;
        if (! empty($code)) {
            return UnitOfMeasurement::where('name', $code)->first()->code;
        }

        return $unitCode;
    }
}

if (! function_exists('getLedgerList')) {
    /**
     * @param  null  $groupId
     * @param  null  $groupName
     * @return \Illuminate\Support\Collection
     */
    function getLedgerList($groupId = null, $groupName = null)
    {

        if (! empty($groupName)) {
            /** @var CompanyGroup $companyGroup */
            $companyGroup = CompanyGroup::whereCompanyId(getCurrentCompany()->id)->whereName($groupName)
                ->select('id')->toBase()->first();

            $groupId = $companyGroup?->id;
        }

        return Ledger::whereGroupId($groupId)->orderBy('name', 'ASC')->pluck('name', 'id');
    }
}

if (! function_exists('getCompanyBank')) {

    function getCompanyBank()
    {
        $currentCompany = getCurrentCompany();

        return Bank::whereModelId($currentCompany?->id)->whereModelType(get_class($currentCompany))
            ->pluck('bank_name', 'id')->toArray();
    }
}

if (! function_exists('getTransactionLedgerLists')) {

    function getTransactionLedgerLists($modelType): array
    {
        return Ledger::whereModelType($modelType)->orderBy('name', 'ASC')->pluck('name', 'id')->toArray();
    }
}
if (! function_exists('getAllChildTransactionLedgers')) {

    function getAllChildTransactionLedgers($modelType): array
    {
        return Ledger::whereIn('model_type', $modelType)->orderBy('name', 'ASC')->pluck('name', 'id')->toArray();
    }
}

if (! function_exists('getAccountInvoiceLedgerLists')) {
    function getAccountInvoiceLedgerLists(): array
    {
        static $accountInvoiceLedgerLists;

        if (App::environment('testing')) {
            $accountInvoiceLedgerLists = [];
        }

        if (empty($accountInvoiceLedgerLists)) {
            $accountInvoiceLedgerLists = Ledger::whereNotIn('model_type', [
                Customer::class,
                Supplier::class,
                Master\UnsecuredLoan::class,
                Master\SecuredLoan::class,
                Master\Capital::class,
                Master\ReserveAndSurplus::class,
                Master\LoanAndAdvanceAsset::class,
                Master\Cash::class,
                Master\Bank::class,
                Master\Investment::class,
                ProfitAndLoss::class,
                StockInHand::class,
                Master\OtherCurrentAssets::class,
            ])->pluck('name', 'id')->toArray();
        }

        return $accountInvoiceLedgerLists;
    }
}

if (! function_exists('getSupplierAndCustomerLedgers')) {
    /**
     * @return \Illuminate\Support\Collection
     */
    function getSupplierAndCustomerLedgers()
    {
        static $supplierAndCustomerLedgers;
        if (empty($supplierAndCustomerLedgers)) {
            $supplierAndCustomerLedgers = Ledger::whereIn('model_type', [Customer::class, Supplier::class])->orderBy('name', 'ASC')->pluck('name', 'id');
        }

        return $supplierAndCustomerLedgers;
    }
}

if (! function_exists('getOpeningBalanceType')) {

    function getOpeningBalanceType($string): int
    {
        if ($string === 'Dr' || $string === 'DR' || $string === 'dr') {
            return Ledger::DR;
        }

        return Ledger::CR;
    }
}

if (! function_exists('getLocationOfAssetId')) {

    function getLocationOfAssetId($locationName): mixed
    {
        $location = Location::whereName($locationName)->first();

        return $location->id ?? Location::first()?->id;
    }
}

if (! function_exists('getCountryId')) {
    /**
     * @return int|null
     */
    function getCountryId($countryName)
    {
        if (empty($countryName)) {
            return null;
        }

        return Country::where('name', 'like', '%'.trim($countryName))->first()->id ?? null;
    }
}

if (! function_exists('getStateId')) {
    /**
     * @return HigherOrderBuilderProxyAlias|int|mixed|null
     */
    function getStateId($stateName, $countryId)
    {
        return State::whereCountryId($countryId)->where('name', 'like', '%'.trim($stateName))->first()->id ?? null;
    }
}

if (! function_exists('getCityId')) {
    /**
     * @return HigherOrderBuilderProxyAlias|int|mixed|null
     */
    function getCityId($cityName, $stateId)
    {
        return City::whereStateId($stateId)->where('name', 'like', '%'.trim($cityName))->first()->id ?? null;
    }
}

if (! function_exists('getEntityTypeId')) {
    /**
     * @return HigherOrderBuilderProxyAlias|int|mixed|null
     */
    function getEntityTypeId($entityTypeName)
    {
        return EntityType::whereEntityName($entityTypeName)->first()->id ?? null;
    }
}

if (! function_exists('getBrokerMasterId')) {
    function getBrokerMasterId($brokerMasterName)
    {
        return Master\Broker::whereCompanyId(getCurrentCompany()->id)->whereBrokerName($brokerMasterName)->first()->id ?? null;
    }
}

if (! function_exists('getTransportMasterId')) {
    function getTransportMasterId($transportMasterName)
    {
        return Transport::whereTransporterName($transportMasterName)->first()->id ?? null;
    }
}

if (! function_exists('getCompanyLedger')) {

    function getCompanyLedger(): array
    {
        static $companyLedgers;

        if (empty($companyLedgers)) {
            return Ledger::whereNotIn('model_type', [StockInHand::class, ProfitAndLoss::class])->orderBy('name', 'ASC')->toBase()->pluck('name', 'id')->toArray();
        }

        return $companyLedgers;
    }
}

if (! function_exists('getCompanyJVLedger')) {
    /**
     * Ledger for journal transactions, ledger contain profit and loss ledger
     */
    function getCompanyJVLedger(): array
    {
        static $companyJVLedgers;

        if (empty($companyJVLedgers)) {
            return Ledger::whereNotIn('model_type', [StockInHand::class])->orderBy('name', 'ASC')->toBase()->pluck('name', 'id')->toArray();
        }

        return $companyJVLedgers;
    }
}

if (! function_exists('getTaxClassificationDetails')) {

    function getTaxClassificationDetails($type): array
    {
        return TaxClassificationDetails::whereTaxClassificationType($type)->toBase()->pluck('name', 'id')->toArray();
    }
}

if (! function_exists('getTdsRate')) {

    function getTdsRate(): array
    {
        static $tdsRates;
        if (empty($tdsRates)) {
            $record = TdsRate::where('section_name', TdsRate::SALARY_TAX_TYPE_SECTION)->get()->pluck('full_name', 'id')->toArray();
            $tdsRates = TdsRate::where('section_name', '!=', TdsRate::SALARY_TAX_TYPE_SECTION)->get()->pluck('full_name', 'id')->toArray();

            if (! empty($record)) {
                $firstKey = array_key_first($record);

                $tdsRates = [$firstKey => $record[$firstKey]] + $tdsRates;
            }

            return $tdsRates;
        }

        return $tdsRates;
    }
}

if (! function_exists('getTdsRateSectionName')) {
    /**
     * @return HigherOrderBuilderProxyAlias|mixed|string
     */
    function getTdsRateSectionName($id)
    {
        return TdsRate::whereId($id)->first()->section_name;
    }
}

if (! function_exists('getTcsRateSectionName')) {
    /**
     * @return HigherOrderBuilderProxyAlias|mixed|string
     */
    function getTcsRateSectionName($id)
    {
        return TcsRate::whereId($id)->first()->section_name;
    }
}

if (! function_exists('getSaleInvoiceGstEnabled')) {

    function getSaleInvoiceGstEnabled($saleId): bool|int
    {
        $isGstEnabled = getCurrentCompany()->is_gst_applicable;

        $saleTransaction = SaleTransaction::find($saleId);

        if (! empty($saleTransaction)) {
            $isGstEnabled = $saleTransaction->is_gst_enabled;
        }

        return $isGstEnabled;
    }
}

if (! function_exists('getSaleReturnInvoiceGstEnabled')) {

    function getSaleReturnInvoiceGstEnabled($saleReturnId): bool|int
    {
        $isGstEnabled = getCurrentCompany()->is_gst_applicable;

        $saleReturnTransaction = SaleReturnTransaction::find($saleReturnId);

        if (! empty($saleReturnTransaction)) {
            $isGstEnabled = $saleReturnTransaction->is_gst_enabled;
        }

        return $isGstEnabled;
    }
}

if (! function_exists('getIncomeDebitNoteInvoiceGstEnabled')) {

    function getIncomeDebitNoteInvoiceGstEnabled($incomeDebitNoteId): bool|int
    {
        $isGstEnabled = getCurrentCompany()->is_gst_applicable;

        $incomeDebitNoteTransaction = IncomeDebitNoteTransaction::find($incomeDebitNoteId);

        if (! empty($incomeDebitNoteTransaction)) {
            $isGstEnabled = $incomeDebitNoteTransaction->is_gst_enabled;
        }

        return $isGstEnabled;
    }
}

if (! function_exists('getIncomeCreditNoteInvoiceGstEnabled')) {

    function getIncomeCreditNoteInvoiceGstEnabled($incomeCreditNoteId): bool|int
    {
        $isGstEnabled = getCurrentCompany()->is_gst_applicable;

        $incomeCreditNoteTransaction = incomeCreditNoteTransaction::find($incomeCreditNoteId);

        if (! empty($incomeCreditNoteTransaction)) {
            $isGstEnabled = $incomeCreditNoteTransaction->is_gst_enabled;
        }

        return $isGstEnabled;
    }
}

if (! function_exists('getPurchaseInvoiceGstEnabled')) {

    function getPurchaseInvoiceGstEnabled($purchaseId): bool|int
    {
        $isGstEnabled = getCurrentCompany()->is_gst_applicable;

        $purchaseTransaction = PurchaseTransaction::find($purchaseId);

        if (! empty($purchaseTransaction)) {
            $isGstEnabled = $purchaseTransaction->is_gst_enabled;
        }

        return $isGstEnabled;
    }
}

if (! function_exists('getPurchaseReturnInvoiceGstEnabled')) {

    function getPurchaseReturnInvoiceGstEnabled($purchaseReturnId): bool|int
    {
        $isGstEnabled = getCurrentCompany()->is_gst_applicable;

        $purchaseReturnTransaction = PurchaseReturnTransaction::find($purchaseReturnId);

        if (! empty($purchaseReturnTransaction)) {
            $isGstEnabled = $purchaseReturnTransaction->is_gst_enabled;
        }

        return $isGstEnabled;
    }
}

if (! function_exists('getExpenseDebitNoteInvoiceGstEnabled')) {

    function getExpenseDebitNoteInvoiceGstEnabled($debitNoteNumber): bool|int
    {
        $isGstEnabled = getCurrentCompany()->is_gst_applicable;

        $debitNoteTransaction = ExpenseDebitNoteTransaction::find($debitNoteNumber);

        if (! empty($debitNoteTransaction)) {
            $isGstEnabled = $debitNoteTransaction->is_gst_enabled;
        }

        return $isGstEnabled;
    }
}

if (! function_exists('getTcsRate')) {

    function getTcsRate(): array
    {
        return TcsRate::all()->pluck('full_name', 'id')->toArray();
    }
}

if (! function_exists('getExpenseCreditNoteInvoiceGstEnabled')) {

    function getExpenseCreditNoteInvoiceGstEnabled($expenseCreditNoteId): bool|int
    {
        $isGstEnabled = getCurrentCompany()->is_gst_applicable;

        $expenseCreditNoteTransaction = ExpenseCreditNoteTransaction::find($expenseCreditNoteId);

        if (! empty($expenseCreditNoteTransaction)) {
            $isGstEnabled = $expenseCreditNoteTransaction->is_gst_enabled;
        }

        return $isGstEnabled;
    }
}

if (! function_exists('getCityName')) {
    function getCityName($cityId): mixed
    {
        if ($cityId) {
            return City::whereId($cityId)->first()->name ?? '';
        }

        return null;
    }
}

if (! function_exists('getStateName')) {

    function getStateName($stateId): mixed
    {
        if ($stateId) {
            $states = Cache::get('states_array');
            if (empty($states)) {
                $states = State::toBase()->get(['id', 'name'])->pluck('name', 'id')->toArray(); // @phpstan-ignore-line
                Cache::put('states_array', $states);
            }

            if (! isset($states[$stateId])) {
                $states = State::toBase()->get(['id', 'name'])->pluck('name', 'id')->toArray(); // @phpstan-ignore-line
                Cache::put('states_array', $states);
            }

            return $states[$stateId] ?? State::whereId($stateId)->first()->name ?? '';
        }

        return null;
    }
}

if (! function_exists('getCountryName')) {

    function getCountryName($countryName): mixed
    {
        if ($countryName) {

            return Country::whereId($countryName)->first()->name;
        }

        return null;
    }
}
if (! function_exists('getStateNameWithCode')) {
    function getStateNameWithCode($stateId): ?string
    {
        $stateName = getStateName($stateId);
        if ($stateName) {
            $stateCode = getStateCode($stateId);

            return $stateCode.'-'.$stateName;
        }

        return null;
    }
}

if (! function_exists('getStateCode')) {

    function getStateCode($stateId): ?string
    {
        if ($stateId) {
            return SaleTransaction::STATE_CODE[$stateId] ?? SaleTransaction::OTHER_TERRITORY;
        }

        return SaleTransaction::OTHER_TERRITORY;
    }
}

if (! function_exists('getAmountToWord')) {
    function getAmountToWord(float $amount): string
    {
        if ($amount < 0) {
            return 'Minus '.getAmountToWord(abs($amount));
        }
        $amount = round($amount, 2);
        $amount_after_decimal = round($amount - ($num = floor($amount)), 2) * 100;
        // Check if there is any number after decimal
        $amt_hundred = null;
        $count_length = strlen($num);
        $x = 0;
        $string = [];
        $change_words = [
            0 => '',
            1 => 'One',
            2 => 'Two',
            3 => 'Three',
            4 => 'Four',
            5 => 'Five',
            6 => 'Six',
            7 => 'Seven',
            8 => 'Eight',
            9 => 'Nine',
            10 => 'Ten',
            11 => 'Eleven',
            12 => 'Twelve',
            13 => 'Thirteen',
            14 => 'Fourteen',
            15 => 'Fifteen',
            16 => 'Sixteen',
            17 => 'Seventeen',
            18 => 'Eighteen',
            19 => 'Nineteen',
            20 => 'Twenty',
            30 => 'Thirty',
            40 => 'Forty',
            50 => 'Fifty',
            60 => 'Sixty',
            70 => 'Seventy',
            80 => 'Eighty',
            90 => 'Ninety',
        ];

        if (getPrintPdfCurrencyInWords()['name'] == 'Rupees') {
            $f = new \NumberFormatter('en_IN', \NumberFormatter::SPELLOUT);
        } else {
            $f = new \NumberFormatter('en_US', \NumberFormatter::SPELLOUT);
        }
        $string[] = ucwords($f->format($num));
        $implode_to_Rupees = implode('', array_reverse($string));
        $get_paise = '';

        if ($amount_after_decimal > 0) {
            if ($amount_after_decimal < 10) {
                $paise_words = $change_words[$amount_after_decimal];
            } elseif ($amount_after_decimal < 20) {
                $paise_words = $change_words[$amount_after_decimal];
            } else {
                $paise_words = $change_words[floor($amount_after_decimal / 10) * 10].' '.$change_words[$amount_after_decimal % 10];
            }
            $get_paise = ' And '.$paise_words.' '.getPrintPdfCurrencyInWords()['paise'];
        }

        return ($implode_to_Rupees ? $implode_to_Rupees.' '.getPrintPdfCurrencyInWords()['name'] : '').$get_paise;
    }
}

if (! function_exists('getOutstandingReceivedCreditPeriodData')) {

    function getOutstandingReceivedCreditPeriodData($item): array
    {
        $creditPeriodType = ! empty($item->credit_period_type) ? $item->credit_period_type : '';
        $creditPeriod = ! empty($item->credit_period) ? $item->credit_period : '';

        if (! empty($creditPeriodType) && ! empty($creditPeriod)) {
            if ($creditPeriodType == Customer::CREDIT_PERIOD_TYPE_MONTH) {
                $data['dueDate'] = Carbon::parse($item->date)->addMonths($creditPeriod);
                $data['creditPeriodTime'] = $creditPeriod.' M';
            } elseif ($creditPeriodType == Customer::CREDIT_PERIOD_TYPE_DAY) {
                $data['dueDate'] = Carbon::parse($item->date)->adddays($creditPeriod);
                $data['creditPeriodTime'] = $creditPeriod.' D';
            }
        } else {
            $data['dueDate'] = $item->date;
            $data['creditPeriodTime'] = '0';
        }
        $data['overdueDays'] = 0;
        if (Carbon::now() > Carbon::parse($data['dueDate'])) {
            $data['overdueDays'] = $data['dueDate']->diffInDays(Carbon::now());
        }
        // $data['receivedAmount'] = $item->paid_amount ?? 0;
        $data['receivedAmount'] = $item->grand_total - $item->due_amount;
        $data['pendingAmount'] = $item->due_amount ?? (float) 0;
        $data['rateOfInterest'] = ! empty($item->customer->model->rate_of_interest) ? $item->customer->model->rate_of_interest : '0';
        $data['interestAmt'] = $data['pendingAmount'] * $data['rateOfInterest'] * $data['overdueDays'] / 365;

        return $data;
    }
}

if (! function_exists('getOutstandingPayableCreditPeriodData')) {

    function getOutstandingPayableCreditPeriodData($item, $isPurchaseTransaction = false): array
    {
        if ($isPurchaseTransaction) {
            $date = $item->date_of_invoice;    // For purchase transactions outstanding report due date is not shown properly because the original inv_date key does not exist in the purchase transaction
        } else {
            $date = $item->original_inv_date;
        }

        $creditPeriodType = ! empty($item->supplier->model->credit_period_type) ? $item->supplier->model->credit_period_type : '';
        $creditPeriod = ! empty($item->supplier->model->credit_limit_period) ? $item->supplier->model->credit_limit_period : '';
        if (! empty($creditPeriodType) && ! empty($creditPeriod)) {
            if ($creditPeriodType == Supplier::CREDIT_PERIOD_TYPE_MONTH) {
                $data['dueDate'] = Carbon::parse($date)->addMonths($creditPeriod);
                $data['creditPeriodTime'] = $creditPeriod.' M';
            } elseif ($creditPeriodType == Supplier::CREDIT_PERIOD_TYPE_DAY) {
                $data['dueDate'] = Carbon::parse($date)->addDays($creditPeriod);
                $data['creditPeriodTime'] = $creditPeriod.' D';
            }
        } else {
            $data['dueDate'] = $date;
            $data['creditPeriodTime'] = '0';
        }
        $data['receivedAmount'] = $item->grand_total - $item->due_amount ?? (float) 0;
        $data['pendingAmount'] = $item->due_amount ?? (float) 0;
        $data['overdueDays'] = 0;
        if (Carbon::now() > Carbon::parse($data['dueDate'])) {
            $data['overdueDays'] = $data['dueDate']->diffInDays(Carbon::now());
        }

        return $data;
    }
}

if (! function_exists('getBandAndCashLedgers')) {

    function getBandAndCashLedgers(): array
    {
        $bankGroups = getParentGroupsValue(Ledger::BANK);
        $cashGroups = getParentGroupsValue(Ledger::CASH);
        $groupIds = array_merge(array_keys($bankGroups), array_keys($cashGroups));

        return Ledger::whereIn('group_id', $groupIds)->orderBy('name', 'ASC')->pluck('name', 'id')->toArray();
    }
}

if (! function_exists('getIncomeAndExpenseGroup')) {

    function getIncomeAndExpenseGroup(): array
    {
        $incomeLedgerGroup = getParentGroupsValue(Ledger::INCOME);
        $expenseLedgerGroup = getParentGroupsValue(Ledger::EXPENSE);
        $groupIds = array_merge(array_keys($incomeLedgerGroup), array_keys($expenseLedgerGroup));
        $incomeExpenseGroups = CompanyGroup::whereCompanyId(getCurrentCompany()->id)->whereIn(
            'id',
            $groupIds
        )->pluck('name', 'id')->toArray();

        return $incomeExpenseGroups;
    }
}

if (! function_exists('checkItemMasterIsExistsInTransaction')) {
    function checkItemMasterIsExistsInTransaction($itemMasterId): bool
    {
        $isExists = false;

        if (SaleTransactionItem::whereItemId($itemMasterId)->exists()) {
            return true;
        }
        if (DeliveryChallanTransactionItem::whereItemId($itemMasterId)->exists()) {
            return true;
        }
        if (IncomeEstimateQuoteItemInvoice::whereItemId($itemMasterId)->exists()) {
            return true;
        }
        if (PurchaseOrderItemInvoice::whereItemId($itemMasterId)->exists()) {
            return true;
        }
        if (SaleReturnItemTransaction::whereItemId($itemMasterId)->exists()) {
            return true;
        }
        if (IncomeDebitNoteItemTransaction::whereItemId($itemMasterId)->exists()) {
            return true;
        }
        if (IncomeCreditNoteItemTransaction::whereItemId($itemMasterId)->exists()) {
            return true;
        }
        if (PurchaseItemTransaction::whereItemId($itemMasterId)->exists()) {
            return true;
        }
        if (PurchaseReturnItemTransaction::whereItemId($itemMasterId)->exists()) {
            return true;
        }
        if (ExpenseCreditNoteItemTransaction::whereItemId($itemMasterId)->exists()) {
            return true;
        }
        if (ExpenseDebitNoteItemTransaction::whereItemId($itemMasterId)->exists()) {
            return true;
        }

        return $isExists;
    }
}

if (! function_exists('checkLedgerIsExistsInTransaction')) {
    function checkLedgerIsExistsInTransaction($ledgerId): bool
    {

        $isExists = false;
        //expense_credit_note_item_transactions
        if (ExpenseCreditNoteItemTransaction::whereLedgerId($ledgerId)->exists()) {
            return true;
        }
        //expense_credit_note_ledger_transactions
        if (ExpenseCreditNoteLedgerTransaction::whereLedgerId($ledgerId)->exists()) {
            return true;
        }
        //expense_credit_note_transactions
        if (ExpenseCreditNoteTransaction::where(function ($q) use ($ledgerId) {
            $q->where('payment_type_ledger_id', $ledgerId)
                ->orWhere('ledger_of_tds', $ledgerId)
                ->orWhere('supplier_id', $ledgerId)
                ->orWhere('tcs_tax_id', $ledgerId);
        })->exists()) {
            return true;
        }
        //expense_debit_note_transaction_items
        if (ExpenseDebitNoteItemTransaction::whereLedgerId($ledgerId)->exists()) {
            return true;
        }
        //expense_debit_note_transaction_ledgers
        if (ExpenseDebitNoteLedgerTransaction::whereLedgerId($ledgerId)->exists()) {
            return true;
        }
        //expense_debit_note_transactions
        if (ExpenseDebitNoteTransaction::where(function ($q) use ($ledgerId) {
            $q->where('payment_type_ledger_id', $ledgerId)
                ->orWhere('ledger_of_tds', $ledgerId)
                ->orWhere('supplier_id', $ledgerId)
                ->orWhere('tcs_tax_id', $ledgerId);
        })->exists()) {
            return true;
        }
        //income_credit_note_item_transactions
        if (IncomeCreditNoteItemTransaction::whereLedgerId($ledgerId)->exists()) {
            return true;
        }
        //income_credit_note_ledger_transactions
        if (IncomeCreditNoteLedgerTransaction::whereLedgerId($ledgerId)->exists()) {
            return true;
        }
        //income_credit_note_transactions
        if (IncomeCreditNoteTransaction::where(function ($q) use ($ledgerId) {
            $q->where('customer_ledger_id', $ledgerId)
                ->orWhere('payment_type_ledger_id', $ledgerId)
                ->orWhere('tcs_tax_id', $ledgerId);
        })->exists()) {
            return true;
        }
        //income_debit_note_item_transactions
        if (IncomeDebitNoteItemTransaction::whereLedgerId($ledgerId)->exists()) {
            return true;
        }
        //income_debit_note_ledger_transactions
        if (IncomeDebitNoteLedgerTransaction::whereLedgerId($ledgerId)->exists()) {
            return true;
        }
        //income_debit_note_transactions
        if (IncomeDebitNoteTransaction::where(function ($q) use ($ledgerId) {
            $q->where('customer_ledger_id', $ledgerId)
                ->orWhere('payment_type_ledger_id', $ledgerId)
                ->orWhere('tcs_tax_id', $ledgerId);
        })->exists()) {
            return true;
        }
        //item_master_goods
        if (ItemMasterGoods::whereExpenseLedgerId($ledgerId)->exists()) {
            return true;
        }
        //item_master_goods
        if (ItemMasterGoods::whereIncomeLedgerId($ledgerId)->exists()) {
            return true;
        }
        //item_master_services
        if (ItemMasterService::whereExpenseLedgerId($ledgerId)->exists()) {
            return true;
        }
        //item_master_services
        if (ItemMasterService::whereIncomeLedgerId($ledgerId)->exists()) {
            return true;
        }
        //journal_debit_credit_transactions
        if (JournalDebitCreditTransaction::whereLedgerId($ledgerId)->exists()) {
            return true;
        }
        //jr_credit_customer_transactions
        if (JournalCreditCustomerTransaction::whereLedgerId($ledgerId)->exists()) {
            return true;
        }
        //jr_debit_supplier_transactions
        if (JournalDebitSupplierTransaction::whereLedgerId($ledgerId)->exists()) {
            return true;
        }
        //master_income_credit_note_transactions
        if (IncomeCreditNoteTransactionMaster::where('bank_id', $ledgerId)->exists()) {
            return true;
        }
        //master_income_debit_note_transactions
        if (IncomeDebitNoteTransactionMaster::where(function ($q) use ($ledgerId) {
            $q->where('on_pay_ledger_id', $ledgerId)
                ->orWhere('bank_id', $ledgerId);
        })->exists()) {
            return true;
        }
        //master_income_return_transactions
        if (IncomeReturnTransactionMaster::where('bank_id', $ledgerId)->exists()) {
            return true;
        }
        //master_income_transactions
        if (IncomeTransactionMaster::where(function ($q) use ($ledgerId) {
            $q->where('on_pay_ledger_id', $ledgerId)
                ->orWhere('bank_id', $ledgerId);
        })->exists()) {
            return true;
        }
        //payment_transactions
        if (PaymentTransaction::where(function ($q) use ($ledgerId) {
            $q->where('bank_cash_ledger_id', $ledgerId)
                ->orWhere('ledger_id', $ledgerId);
        })->exists()) {
            return true;
        }
        //purchase_return_item_transactions
        if (PurchaseReturnItemTransaction::whereLedgerId($ledgerId)->exists()) {
            return true;
        }
        //purchase_return_ledger_transactions
        if (PurchaseReturnLedgerTransaction::whereLedgerId($ledgerId)->exists()) {
            return true;
        }
        //purchase_return_transactions
        if (PurchaseReturnTransaction::where(function ($q) use ($ledgerId) {
            $q->where('ledger_of_tds', $ledgerId)
                ->orWhere('payment_type_ledger_id', $ledgerId)
                ->orWhere('supplier_id', $ledgerId)
                ->orWhere('tcs_tax_id', $ledgerId);
        })->exists()) {
            return true;
        }
        //purchase_transaction_items
        if (PurchaseItemTransaction::whereLedgerId($ledgerId)->exists()) {
            return true;
        }
        //purchase_transaction_ledgers
        if (PurchaseLedgerTransaction::whereLedgerId($ledgerId)->exists()) {
            return true;
        }
        //purchase_transactions
        if (PurchaseTransaction::where(function ($q) use ($ledgerId) {
            $q->where('ledger_of_tds', $ledgerId)
                ->orWhere('payment_type_ledger_id', $ledgerId)
                ->orWhere('supplier_ledger_id', $ledgerId)
                ->orWhere('tcs_tax_id', $ledgerId);
        })->exists()) {
            return true;
        }
        //receipt_transactions
        if (ReceiptTransaction::where(function ($q) use ($ledgerId) {
            $q->where('bank_cash_ledger_id', $ledgerId)
                ->orWhere('ledger_id', $ledgerId);
        })->exists()) {
            return true;
        }
        //sale_return_item_transactions
        if (SaleReturnItemTransaction::whereLedgerId($ledgerId)->exists()) {
            return true;
        }
        //sale_return_ledger_transactions
        if (SaleReturnLedgerTransaction::whereLedgerId($ledgerId)->exists()) {
            return true;
        }
        //sale_return_transactions
        if (SaleReturnTransaction::where(function ($q) use ($ledgerId) {
            $q->where('customer_ledger_id', $ledgerId)
                ->orWhere('payment_type_ledger_id', $ledgerId)
                ->orWhere('tcs_tax_id', $ledgerId);
        })->exists()) {
            return true;
        }
        //sale_transaction_items
        if (SaleTransactionItem::whereLedgerId($ledgerId)->exists()) {
            return true;
        }
        //sale_transaction_ledgers
        if (SaleTransactionLedger::whereLedgerId($ledgerId)->exists()) {
            return true;
        }
        //sales_transactions
        if (SaleTransaction::where(function ($q) use ($ledgerId) {
            $q->where('customer_ledger_id', $ledgerId)
                ->orWhere('payment_type_ledger_id', $ledgerId)
                ->orWhere('tcs_tax_id', $ledgerId);
        })->exists()) {
            return true;
        }
        //item_stock_closing_balance
        if (ItemStockClosingBalance::whereLedgerId($ledgerId)->exists()) {
            return true;
        }

        return $isExists;
    }
}

if (! function_exists('getTaxesTCSType')) {
    /**
     * @return int
     */
    function getTaxesTCSType($name)
    {
        $parts = explode(' - ', $name);
        $natureOfPayment = $parts[0];

        /** @var TcsRate $tcsRate */
        $tcsRate = TcsRate::whereRaw('lower(nature_of_payment) = ? ', [strtolower($natureOfPayment)])->first();

        return $tcsRate?->id;
    }
}

if (! function_exists('getTaxesTDSType')) {
    function getTaxesTDSType($name)
    {
        /** @var TdsRate $tdsRate */
        $tdsRate = TdsRate::where(static function ($q) use ($name) {
            $natureOfPayment = explode(' - ', $name)[0];
            $sectionName = explode(' - ', $name)[1] ?? null;
            $q->where('section_name', 'like', '%'.$sectionName.'%')->orWhere(
                'nature_of_payment',
                'like',
                '%'.$natureOfPayment.'%'
            );
        })->first();

        return $tdsRate?->id;
    }
}

if (! function_exists('getTaxesTCSTypeById')) {
    function getTaxesTCSTypeById($id)
    {
        return TcsRate::whereId($id)->first()?->full_name;
    }
}

if (! function_exists('getTaxesTDSTypeById')) {
    function getTaxesTDSTypeById($id)
    {
        return TdsRate::whereId($id)->first()?->full_name;
    }
}

if (! function_exists('getCompany')) {

    function getCompany(): array
    {
        return Company::toBase()->pluck('trade_name', 'id')->toArray();
    }
}

if (! function_exists('getAllLedgerLists')) {
    function getAllLedgerLists(): array
    {
        return Ledger::toBase()->pluck('name', 'id')->toArray();
    }
}

if (! function_exists('getGstCessRate')) {
    /**
     * @return mixed
     */
    function getGstCessRate()
    {
        return CessRate::toBase()->pluck('rate', 'id')->toArray();
    }
}

if (! function_exists('getAllParentGroupLedgerList')) {

    function getAllParentGroupLedgerList($modalName): array
    {
        if ($modalName) {
            $modalGroups = getParentGroupsValue($modalName);
            $groupIds = array_keys($modalGroups);

            return Ledger::whereIn('group_id', $groupIds)->orderBy('name', 'ASC')->pluck('name', 'id')->toArray();
        }

        return [];
    }
}

if (! function_exists('getIncomeDebitNoteTransactionData')) {
    function getIncomeDebitNoteTransactionData($input = null)
    {
        $startDate = $input['start_date'] ?? null;
        $endDate = $input['end_date'] ?? null;
        $input['party_name'] = $input['party_name'] ?? null;
        $input['invoice_no'] = $input['invoice_no'] ?? null;

        $data = [];
        $data['incomeDebitNoteTransactions'] = [];
        $data['grandTotal'] = 0;
        $data['igstTotal'] = 0;
        $data['cgstTotal'] = 0;
        $data['sgstTotal'] = 0;
        $data['roundOffTotal'] = 0;
        $data['tcsTotal'] = 0;
        $data['taxableValueTotal'] = 0;
        $data['dueAmountTotal'] = 0;

        if ($startDate == null && $endDate == null) {
            $startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
            $endDate = Carbon::now()->endOfMonth()->format('Y-m-d');
        }

        $data['durationDate'] = Carbon::parse($startDate)->format('d-M-Y').' to '.Carbon::parse($endDate)->format('d-M-Y');

        $data['incomeDebitNoteTransactions'] = IncomeDebitNoteTransaction::with(
            'customer',
            'incomeDebitNoteItems'
        )->select('income_debit_note_transactions.*')
            ->whereBetween('date', [$startDate, $endDate])
            ->where(function (Builder $q) use ($input) {
                if (isset($input['party_name']) && $input['party_name'] != null) {
                    $q->where('customer_ledger_id', $input['party_name']);
                }
                if (isset($input['invoice_no']) && $input['invoice_no'] != null) {
                    $q->where('full_invoice_number', 'like', '%'.$input['invoice_no'].'%')->financialYearDate();
                }
            })
            ->get();

        $data['grandTotal'] = $data['incomeDebitNoteTransactions']->sum('grand_total');
        $data['igstTotal'] = $data['incomeDebitNoteTransactions']->sum('igst');
        $data['cgstTotal'] = $data['incomeDebitNoteTransactions']->sum('cgst');
        $data['sgstTotal'] = $data['incomeDebitNoteTransactions']->sum('sgst');
        $data['roundOffTotal'] = $data['incomeDebitNoteTransactions']->sum('rounding_amount');
        $data['tcsTotal'] = $data['incomeDebitNoteTransactions']->sum('tcs_amount');
        $data['taxableValueTotal'] = $data['incomeDebitNoteTransactions']->sum('taxable_value');
        $data['dueAmountTotal'] = $data['incomeDebitNoteTransactions']->sum('due_amount');

        return $data;
    }
}

if (! function_exists('getPurchaseTransactionData')) {
    function getPurchaseTransactionData($input = null)
    {
        $startDate = $input['start_date'] ?? null;
        $endDate = $input['end_date'] ?? null;
        $input['party_name'] = $input['party_name'] ?? null;
        $input['voucher_no'] = $input['voucher_no'] ?? null;
        $input['payment_status'] = $input['payment_status'] ?? null;

        $data = [];
        $data['purchaseTransactions'] = [];
        $data['grandTotal'] = 0;
        $data['igstTotal'] = 0;
        $data['cgstTotal'] = 0;
        $data['sgstTotal'] = 0;
        $data['roundOffTotal'] = 0;
        $data['tcsTotal'] = 0;
        $data['tdsTotal'] = 0;
        $data['taxableValueTotal'] = 0;
        $data['dueAmountTotal'] = 0;

        if ($startDate == null && $endDate == null) {
            $startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
            $endDate = Carbon::now()->endOfMonth()->format('Y-m-d');
        }

        $data['durationDate'] = Carbon::parse($startDate)->format('d-M-Y').' to '.Carbon::parse($endDate)->format('d-M-Y');

        $data['purchaseTransactions'] = PurchaseTransaction::with(
            'supplier',
            'purchaseTransactionItems'
        )->select('purchase_transactions.*')
            ->whereBetween('voucher_date', [$startDate, $endDate])
            ->where(function (Builder $q) use ($input) {
                if (isset($input['party_name']) && $input['party_name'] != null) {
                    $q->where('supplier_ledger_id', $input['party_name']);
                }

                if (isset($input['voucher_no']) && $input['voucher_no'] != null) {
                    $q->where('voucher_number', 'like', '%'.$input['voucher_no'].'%')->financialYearDate();
                }
            })
            ->get();

        if (isset($input['payment_status']) && $input['payment_status'] != null) {
            $data['purchaseTransactions'] = $data['purchaseTransactions']->where(
                'payment_status',
                '=',
                $input['payment_status']
            );
        }

        $data['grandTotal'] = $data['purchaseTransactions']->sum('grand_total');
        $data['igstTotal'] = $data['purchaseTransactions']->sum('igst');
        $data['cgstTotal'] = $data['purchaseTransactions']->sum('cgst');
        $data['sgstTotal'] = $data['purchaseTransactions']->sum('sgst');
        $data['roundOffTotal'] = $data['purchaseTransactions']->sum('rounding_amount');
        $data['tcsTotal'] = $data['purchaseTransactions']->sum('tcs_amount');
        $data['tdsTotal'] = $data['purchaseTransactions']->sum('tds_amount');
        $data['taxableValueTotal'] = $data['purchaseTransactions']->sum('taxableValue');
        $data['dueAmountTotal'] = $data['purchaseTransactions']->sum('due_amount');

        return $data;
    }
}

if (! function_exists('getPurchaseReturnTransactionData')) {
    function getPurchaseReturnTransactionData($input = null)
    {
        $startDate = $input['start_date'] ?? null;
        $endDate = $input['end_date'] ?? null;
        $input['party_name'] = $input['party_name'] ?? null;
        $input['voucher_no'] = $input['voucher_no'] ?? null;

        $data = [];
        $data['purchaseReturnTransactions'] = [];
        $data['grandTotal'] = 0;
        $data['igstTotal'] = 0;
        $data['cgstTotal'] = 0;
        $data['sgstTotal'] = 0;
        $data['roundOffTotal'] = 0;
        $data['tcsTotal'] = 0;
        $data['tdsTotal'] = 0;
        $data['taxableValueTotal'] = 0;
        $data['dueAmountTotal'] = 0;

        if ($startDate == null && $endDate == null) {
            $startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
            $endDate = Carbon::now()->endOfMonth()->format('Y-m-d');
        }

        $data['durationDate'] = Carbon::parse($startDate)->format('d-M-Y').' to '.Carbon::parse($endDate)->format('d-M-Y');

        $data['purchaseReturnTransactions'] = PurchaseReturnTransaction::with(
            'purchaseReturnItems',
            'supplier'
        )->select('purchase_return_transactions.*')
            ->whereBetween('voucher_date', [$startDate, $endDate])
            ->where(function (Builder $q) use ($input) {
                if (isset($input['party_name']) && $input['party_name'] != null) {
                    $q->where('supplier_id', $input['party_name']);
                }

                if (isset($input['voucher_no']) && $input['voucher_no'] != null) {
                    $q->where('voucher_number', 'like', '%'.$input['voucher_no'].'%')->financialYearDate();
                }
            })
            ->get();

        $data['grandTotal'] = $data['purchaseReturnTransactions']->sum('grand_total');
        $data['igstTotal'] = $data['purchaseReturnTransactions']->sum('igst');
        $data['cgstTotal'] = $data['purchaseReturnTransactions']->sum('cgst');
        $data['sgstTotal'] = $data['purchaseReturnTransactions']->sum('sgst');
        $data['roundOffTotal'] = $data['purchaseReturnTransactions']->sum('rounding_amount');
        $data['tcsTotal'] = $data['purchaseReturnTransactions']->sum('tcs_amount');
        $data['tdsTotal'] = $data['purchaseReturnTransactions']->sum('tds_amount');
        $data['taxableValueTotal'] = $data['purchaseReturnTransactions']->sum('taxable_value');
        $data['dueAmountTotal'] = $data['purchaseReturnTransactions']->sum('due_amount');

        return $data;
    }
}

if (! function_exists('getSupplierPurchaseInvoiceNumber')) {
    function getSupplierPurchaseInvoiceNumber($supplierId): array
    {
        return PurchaseTransaction::whereSupplierLedgerId($supplierId)->pluck('sale_number', 'id')->toArray();
    }
}

if (! function_exists('getCustomerSaleInvoiceNumber')) {

    function getCustomerSaleInvoiceNumber($customerId): array
    {
        return SaleTransaction::whereCustomerLedgerId($customerId)->pluck('full_invoice_number', 'id')->toArray();
    }
}

if (! function_exists('pdfPreviewerUi')) {

    /**
     * @return HigherOrderBuilderProxyAlias|int|mixed|string
     */
    function pdfPreviewerUi($transaction = null): mixed
    {
        if (! empty($transaction)) {
            $company = Company::findOrFail($transaction->company_id);
        } else {
            $company = getCurrentCompany();
        }
        $invoiceUINo = CompanySetting::whereCompanyId($company?->id)->where('key', 'invoice_pdf_ui')->first()?->value;
        if ($invoiceUINo) {

            return $invoiceUINo;
        }

        return (string) CompanySetting::INVOICE_PDF_UI_3;
    }
}

if (! function_exists('estimatePdfPreviewerUi')) {
    function estimatePdfPreviewerUi($transaction = null): mixed
    {
        if (! empty($transaction)) {
            $company = Company::findOrFail($transaction->company_id);
        } else {
            $company = getCurrentCompany();
        }
        $invoiceUINo = CompanySetting::whereCompanyId($company?->id)->where('key', 'estimate_invoice_pdf_ui')->first()?->value;
        if ($invoiceUINo) {

            return $invoiceUINo;
        }

        return (string) CompanySetting::INVOICE_PDF_UI_3;
    }
}

if (! function_exists('deliveryChallanPdfPreviewerUi')) {
    function deliveryChallanPdfPreviewerUi($transaction = null): mixed
    {
        if (! empty($transaction)) {
            $company = Company::findOrFail($transaction->company_id);
        } else {
            $company = getCurrentCompany();
        }
        $invoiceUINo = CompanySetting::whereCompanyId($company?->id)->where('key', 'delivery_challan_invoice_pdf_ui')->first()?->value;
        if ($invoiceUINo) {

            return $invoiceUINo;
        }

        return (string) CompanySetting::INVOICE_PDF_UI_3;
    }
}

if (! function_exists('landscapePreviewerUi')) {

    /**
     * @return HigherOrderBuilderProxyAlias|int|mixed|string
     */
    function landscapePreviewerUi($transaction = null, $type = CompanySetting::TRANSACTION_PRINT): mixed
    {
        if (! empty($transaction)) {
            $company = Company::findOrFail($transaction->company_id);
        } else {
            $company = getCurrentCompany();
        }

        $keyName = 'invoice_landscape_ui';
        if ($type == CompanySetting::ESTIMATE_PRINT) {
            $keyName = 'estimate_landscape_ui';
        }
        if ($type == CompanySetting::DELIVERY_CHALLAN_PRINT) {
            $keyName = 'delivery_challan_landscape_ui';
        }

        $invoiceUINo = CompanySetting::whereCompanyId($company?->id)->where('key', $keyName)->first()?->value;
        if ($invoiceUINo) {

            return $invoiceUINo;
        }

        return (string) CompanySetting::LANDSCAPE_PDF_UI_1;
    }
}

if (! function_exists('landscapeA5PreviewerUi')) {

    /**
     * @return HigherOrderBuilderProxyAlias|int|mixed|string
     */
    function landscapeA5PreviewerUi($transaction = null, $type = CompanySetting::TRANSACTION_PRINT): mixed
    {
        if (! empty($transaction)) {
            $company = Company::findOrFail($transaction->company_id);
        } else {
            $company = getCurrentCompany();
        }

        $keyName = 'invoice_landscape_a5_ui';
        if ($type == CompanySetting::ESTIMATE_PRINT) {
            $keyName = 'estimate_landscape_a5_ui';
        }
        if ($type == CompanySetting::DELIVERY_CHALLAN_PRINT) {
            $keyName = 'delivery_challan_landscape_a5_ui';
        }

        $invoiceUINo = CompanySetting::whereCompanyId($company?->id)->where('key', $keyName)->first()?->value;
        if ($invoiceUINo) {

            return $invoiceUINo;
        }

        return (string) CompanySetting::LANDSCAPE_A5_PDF_UI_1;
    }
}

if (! function_exists('hsnSummaryOutWordData')) {
    function hsnSummaryOutWordData($input = null): array
    {
        $startDate = $input['start_date'] ?? null;
        $endDate = $input['end_date'] ?? null;
        $input['transaction_type'] = $input['transaction_type'] ?? 0;

        $currentCompanyId = getCurrentCompany()->id;

        if ($startDate == null && $endDate == null) {
            $startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
            $endDate = Carbon::now()->endOfMonth()->format('Y-m-d');
        }

        $data['durationDate'] = Carbon::parse($startDate)->format('d-M-Y').' to '.Carbon::parse($endDate)->format('d-M-Y');

        $data['saleItems'] = SaleTransactionItem::with(['saleTransaction', 'unit', 'items.model'])
            ->whereHas('saleTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)->where('is_gst_na', '=', 0)
                    ->whereBetween('date', [$startDate, $endDate]);
            })
            ->whereHas('items.model', function (Builder $query) {
                // $query->whereNotNull('hsn_sac_code');
            })
            ->get();

        $data['saleTransactions'] = SaleTransaction::with(['saleItems', 'customer'])
            ->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)->get();

        $data['saleLedgers'] = SaleTransactionLedger::with(['saleTransaction', 'ledgers.model'])
            ->whereHas('saleTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)->where('is_gst_na', '=', 0)
                    ->whereBetween('date', [$startDate, $endDate]);
            })->get();

        $data['saleTransactionAdditionalCharges'] = DB::table('additional_charges_for_sales_transactions as additional_charges')
            ->leftJoin('sales_transactions', 'additional_charges.sale_id', '=', 'sales_transactions.id')
            ->where('sales_transactions.company_id', $currentCompanyId)
            ->where('sales_transactions.is_gst_na', '=', 0)
            ->whereNull('sales_transactions.deleted_at')
            ->selectRaw('
                additional_charges.sale_id as id,
                SUM(additional_charges.value) as additional_charges_sum,
                SUM(CASE WHEN sales_transactions.cgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_cgst_sum,
                SUM(CASE WHEN sales_transactions.sgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_sgst_sum,
                SUM(CASE WHEN sales_transactions.igst != 0 THEN additional_charges.value * additional_charges.gst_percentage / 100 ELSE 0 END) as additional_charges_igst_sum
            ')

            ->whereBetween('sales_transactions.date', [$startDate, $endDate])
            ->groupBy('additional_charges.sale_id')
            ->get();

        $data['saleReturnItems'] = SaleReturnItemTransaction::with(['saleReturnTransaction', 'unit', 'items.model'])
            ->whereHas('saleReturnTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)->where('is_gst_na', '=', 0)
                    ->whereBetween('date', [$startDate, $endDate]);
            })
            ->whereHas('items.model', function (Builder $query) {
                // $query->whereNotNull('hsn_sac_code');
            })->get();

        $data['saleReturnTransactions'] = SaleReturnTransaction::with(['saleReturnItems', 'customer'])
            ->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)->get();
        $data['saleReturnLedgers'] = SaleReturnLedgerTransaction::with(['saleReturnTransaction', 'ledgers'])
            ->whereHas('saleReturnTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)->where('is_gst_na', '=', 0)
                    ->whereBetween('date', [$startDate, $endDate]);
            })->get();
        $data['saleReturnTransactionAdditionalCharges'] = DB::table('additional_charges_for_sales_return_transactions as additional_charges')
            ->leftJoin('sale_return_transactions', 'additional_charges.sale_return_id', '=', 'sale_return_transactions.id')
            ->where('sale_return_transactions.company_id', $currentCompanyId)
            ->where('sale_return_transactions.is_gst_na', '=', 0)
            ->whereNull('sale_return_transactions.deleted_at')
            ->selectRaw('
                additional_charges.sale_return_id as id,
                SUM(additional_charges.value) as additional_charges_sum,
                SUM(CASE WHEN sale_return_transactions.cgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_cgst_sum,
                SUM(CASE WHEN sale_return_transactions.sgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_sgst_sum,
                SUM(CASE WHEN sale_return_transactions.igst != 0 THEN additional_charges.value * additional_charges.gst_percentage / 100 ELSE 0 END) as additional_charges_igst_sum
            ')
            ->whereBetween('sale_return_transactions.date', [$startDate, $endDate])
            ->groupBy('additional_charges.sale_return_id')
            ->get();

        $data['incomeDebitNoteItems'] = IncomeDebitNoteItemTransaction::with(['incomeDebitNoteTransaction', 'unit', 'items.model'])
            ->whereHas('incomeDebitNoteTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)->where('is_gst_na', '=', 0)
                    ->whereBetween('date', [$startDate, $endDate]);
            })
            ->whereHas('items.model', function (Builder $query) {
                // $query->whereNotNull('hsn_sac_code');
            })->get();

        $data['incomeDebitNoteLedgers'] = IncomeDebitNoteLedgerTransaction::with(['incomeDebitNoteTransaction', 'ledgers'])
            ->whereHas('incomeDebitNoteTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)->where('is_gst_na', '=', 0)
                    ->whereBetween('date', [$startDate, $endDate]);
            })->get();

        $data['incomeDebitTransactions'] = IncomeDebitNoteTransaction::with(['incomeDebitNoteItems', 'customer'])
            ->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)->get();

        $data['incomeDrTransactionAdditionalCharges'] = DB::table('additional_charges_for_income_debit_note_transactions as additional_charges')
            ->leftJoin('income_debit_note_transactions', 'additional_charges.income_debit_note_id', '=', 'income_debit_note_transactions.id')
            ->where('income_debit_note_transactions.company_id', $currentCompanyId)
            ->where('income_debit_note_transactions.is_gst_na', '=', 0)
            ->whereNull('income_debit_note_transactions.deleted_at')
            ->selectRaw('
            additional_charges.income_debit_note_id as id,
            SUM(additional_charges.value) as additional_charges_sum,
            SUM(CASE WHEN income_debit_note_transactions.cgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_cgst_sum,
            SUM(CASE WHEN income_debit_note_transactions.sgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_sgst_sum,
            SUM(CASE WHEN income_debit_note_transactions.igst != 0 THEN additional_charges.value * additional_charges.gst_percentage / 100 ELSE 0 END) as additional_charges_igst_sum
        ')
            ->whereBetween('income_debit_note_transactions.date', [$startDate, $endDate])
            ->groupBy('additional_charges.income_debit_note_id')
            ->get();

        $data['incomeCreditNoteItems'] = IncomeCreditNoteItemTransaction::with(['incomeCreditNoteTransaction', 'unit', 'items.model'])
            ->whereHas('incomeCreditNoteTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)->where('is_gst_na', '=', 0)
                    ->whereBetween('date', [$startDate, $endDate]);
            })
            ->whereHas('items.model', function (Builder $query) {
                // $query->whereNotNull('hsn_sac_code');
            })->get();

        $data['incomeCreditTransactions'] = IncomeCreditNoteTransaction::with(['incomeCreditNoteItems', 'customer'])
            ->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)->get();

        $data['incomeCreditNoteLedgers'] = IncomeCreditNoteLedgerTransaction::with(['incomeCreditNoteTransaction', 'ledger'])
            ->whereHas('incomeCreditNoteTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)->where('is_gst_na', '=', 0)
                    ->whereBetween('date', [$startDate, $endDate]);
            })
            ->get();

        $data['incomeCrTransactionAdditionalCharges'] = DB::table('additional_charges_for_income_credit_note_transactions as additional_charges')
            ->leftJoin('income_credit_note_transactions', 'additional_charges.income_cn_id', '=', 'income_credit_note_transactions.id')
            ->where('income_credit_note_transactions.company_id', $currentCompanyId)
            ->where('income_credit_note_transactions.is_gst_na', '=', 0)
            ->whereNull('income_credit_note_transactions.deleted_at')
            ->selectRaw('
                    additional_charges.income_cn_id as id,
                    SUM(additional_charges.value) as additional_charges_sum,
                    SUM(CASE WHEN income_credit_note_transactions.cgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_cgst_sum,
                    SUM(CASE WHEN income_credit_note_transactions.sgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_sgst_sum,
                    SUM(CASE WHEN income_credit_note_transactions.igst != 0 THEN additional_charges.value * additional_charges.gst_percentage / 100 ELSE 0 END) as additional_charges_igst_sum
                ')
            ->whereBetween('income_credit_note_transactions.date', [$startDate, $endDate])
            ->groupBy('additional_charges.income_cn_id')
            ->get();

        $data['taxableAmountTotal'] = $data['saleItems']->sum('taxable_value')
            + $data['saleLedgers']->sum('taxable_value')
            - $data['saleReturnItems']->sum('taxable_value')
            - $data['saleReturnLedgers']->sum('taxable_value')
            + $data['incomeDebitNoteItems']->sum('taxable_value')
            + $data['incomeDebitNoteLedgers']->sum('taxable_value')
            - $data['incomeCreditNoteItems']->sum('taxable_value')
            - $data['incomeCreditNoteLedgers']->sum('taxable_value')
            + $data['saleTransactionAdditionalCharges']->sum('additional_charges_sum')
            - $data['saleReturnTransactionAdditionalCharges']->sum('additional_charges_sum')
            + $data['incomeDrTransactionAdditionalCharges']->sum('additional_charges_sum')
            - $data['incomeCrTransactionAdditionalCharges']->sum('additional_charges_sum');

        $data['cgstTaxTotal'] = $data['saleItems']->sum('classification_cgst_tax')
            + $data['saleLedgers']->sum('classification_cgst_tax')
            - $data['saleReturnItems']->sum('classification_cgst_tax')
            - $data['saleReturnLedgers']->sum('classification_cgst_tax')
            + $data['incomeDebitNoteItems']->sum('classification_cgst_tax')
            + $data['incomeDebitNoteLedgers']->sum('classification_cgst_tax')
            - $data['incomeCreditNoteItems']->sum('classification_cgst_tax')
            - $data['incomeCreditNoteLedgers']->sum('classification_cgst_tax')
            + $data['saleTransactionAdditionalCharges']->sum('additional_charges_cgst_sum')
            - $data['saleReturnTransactionAdditionalCharges']->sum('additional_charges_cgst_sum')
            + $data['incomeDrTransactionAdditionalCharges']->sum('additional_charges_cgst_sum')
            - $data['incomeCrTransactionAdditionalCharges']->sum('additional_charges_cgst_sum');

        $data['sgstTaxTotal'] = $data['saleItems']->sum('classification_sgst_tax')
            + $data['saleLedgers']->sum('classification_sgst_tax')
            - $data['saleReturnItems']->sum('classification_sgst_tax')
            - $data['saleReturnLedgers']->sum('classification_sgst_tax')
            + $data['incomeDebitNoteItems']->sum('classification_sgst_tax')
            + $data['incomeDebitNoteLedgers']->sum('classification_sgst_tax')
            - $data['incomeCreditNoteItems']->sum('classification_sgst_tax')
            - $data['incomeCreditNoteLedgers']->sum('classification_sgst_tax')
            + $data['saleTransactionAdditionalCharges']->sum('additional_charges_sgst_sum')
            - $data['saleReturnTransactionAdditionalCharges']->sum('additional_charges_sgst_sum')
            + $data['incomeDrTransactionAdditionalCharges']->sum('additional_charges_sgst_sum')
            - $data['incomeCrTransactionAdditionalCharges']->sum('additional_charges_sgst_sum');

        $data['igstTaxTotal'] = $data['saleItems']->sum('classification_igst_tax')
            + $data['saleLedgers']->sum('classification_igst_tax')
            - $data['saleReturnItems']->sum('classification_igst_tax')
            - $data['saleReturnLedgers']->sum('classification_igst_tax')
            + $data['incomeDebitNoteItems']->sum('classification_igst_tax')
            + $data['incomeDebitNoteLedgers']->sum('classification_igst_tax')
            - $data['incomeCreditNoteItems']->sum('classification_igst_tax')
            - $data['incomeCreditNoteLedgers']->sum('classification_igst_tax')
            + $data['saleTransactionAdditionalCharges']->sum('additional_charges_igst_sum')
            - $data['saleReturnTransactionAdditionalCharges']->sum('additional_charges_igst_sum')
            + $data['incomeDrTransactionAdditionalCharges']->sum('additional_charges_igst_sum')
            - $data['incomeCrTransactionAdditionalCharges']->sum('additional_charges_igst_sum');

        $data['cessTaxTotal'] = $data['saleItems']->sum('cess_amount')
            - $data['saleReturnItems']->sum('cess_amount')
            + $data['incomeDebitNoteItems']->sum('cess_amount')
            - $data['incomeCreditNoteItems']->sum('cess_amount');

        return $data;
    }
}

if (! function_exists('getGstr1HsnData')) {

    function getGstr1HsnData($data): array
    {
        $dataItem = [];

        $checkItemIdExist = [];
        $checkTaxExist = [];

        foreach ($data['saleItems'] as $item) {
            $hsnCode = $item->hsn_code ?? $item->items->model->hsn_sac_code ?? null;
            $uniqueKey = $hsnCode.'-'.$item->gst_tax_percentage ?? '0';
            $customUnit = UnitOfMeasurement::whereName($item->unit->for_gst_use_name)->first();
            $item->saleTransaction->load('saleItems');
            $saleTransactionItemsCount = $item->saleTransaction->saleItems->count();

            if (array_key_exists($uniqueKey, $checkItemIdExist)) {
                $checkItemIdExist[$uniqueKey] = $item->hsn_code ?? $item->items->model->hsn_sac_code;
                $checkTaxExist['quantity'][$uniqueKey] += ($item->items->item_type == ItemMaster::ITEM_MASTER_GOODS) ? $item->quantity : 0;
                $checkTaxExist['taxable'][$uniqueKey] += ($item->taxable_value ?? 0);
                $checkTaxExist['cgst'][$uniqueKey] += ($item->classification_cgst_tax ?? 0);
                $checkTaxExist['sgst'][$uniqueKey] += ($item->classification_sgst_tax ?? 0);
                $checkTaxExist['igst'][$uniqueKey] += ($item->classification_igst_tax ?? 0);
                $checkTaxExist['invoice_amount'][$uniqueKey] += ($item->taxable_value ?? 0) + ($item->classification_cgst_tax ?? 0) +
                    ($item->classification_sgst_tax ?? 0) + ($item->cess_amount ?? 0) + ($item->classification_igst_tax ?? 0) + (round($item->saleTransaction->rounding_amount / $saleTransactionItemsCount, 2) ?? 0);
                $checkTaxExist['description'][$uniqueKey] = $item->items->model->description ?? '';
                $checkTaxExist['unit_name'][$uniqueKey] = empty($customUnit) ? (! empty($item->unit->full_name) ? $item->unit->full_name : '') : $customUnit->full_name;
                $checkTaxExist['unit_code'][$uniqueKey] = ($item->items->item_type == ItemMaster::ITEM_MASTER_GOODS) ? (empty($customUnit) ? (! empty($item->unit->code) ? $item->unit->code : '') : $customUnit->code) : 'NA';
                $checkTaxExist['cess'][$uniqueKey] += $item->cess_amount ?? 0;
                $checkTaxExist['round_off'][$uniqueKey] += round($item->saleTransaction->rounding_amount / $saleTransactionItemsCount, 2) ?? 0;
                $checkTaxExist['hsn_code'][$uniqueKey] = $item->hsn_code ?? $item->items->model->hsn_sac_code ?? '';
                $checkTaxExist['gst_rate'][$uniqueKey] = $item->gst_tax_percentage ?? '';
            } else {
                $checkItemIdExist[$uniqueKey] = $item->hsn_code ?? $item->items->model->hsn_sac_code;
                $checkTaxExist['quantity'][$uniqueKey] = ($item->items->item_type == ItemMaster::ITEM_MASTER_GOODS) ? $item->quantity : 0;
                $checkTaxExist['taxable'][$uniqueKey] = ($item->taxable_value ?? 0);
                $checkTaxExist['cgst'][$uniqueKey] = ($item->classification_cgst_tax ?? 0);
                $checkTaxExist['sgst'][$uniqueKey] = ($item->classification_sgst_tax ?? 0);
                $checkTaxExist['igst'][$uniqueKey] = ($item->classification_igst_tax ?? 0);
                $checkTaxExist['invoice_amount'][$uniqueKey] = ($item->taxable_value ?? 0) + ($item->classification_cgst_tax ?? 0) +
                    ($item->classification_sgst_tax ?? 0) + ($item->cess_amount ?? 0) + ($item->classification_igst_tax ?? 0) + (round($item->saleTransaction->rounding_amount / $saleTransactionItemsCount, 2) ?? 0);
                $checkTaxExist['description'][$uniqueKey] = $item->items->model->description ?? '';
                $checkTaxExist['unit_name'][$uniqueKey] = empty($customUnit) ? (! empty($item->unit->full_name) ? $item->unit->full_name : '') : $customUnit->full_name;
                $checkTaxExist['unit_code'][$uniqueKey] = ($item->items->item_type == ItemMaster::ITEM_MASTER_GOODS) ? (empty($customUnit) ? (! empty($item->unit->code) ? $item->unit->code : '') : $customUnit->code) : 'NA';
                $checkTaxExist['cess'][$uniqueKey] = $item->cess_amount ?? 0;
                $checkTaxExist['round_off'][$uniqueKey] = round($item->saleTransaction->rounding_amount / $saleTransactionItemsCount, 2) ?? 0;
                $checkTaxExist['hsn_code'][$uniqueKey] = $item->hsn_code ?? $item->items->model->hsn_sac_code ?? '';
                $checkTaxExist['gst_rate'][$uniqueKey] = $item->gst_tax_percentage ?? '';
            }
        }

        foreach ($data['saleLedgers'] as $ledger) {

            $uniqueKey = $ledger->ledgers->model->hsn_sac_code.'-'.$ledger->gst_tax_percentage ?? '0';
            $ledger->saleTransaction->load('saleLedgers');
            $saleTransactionLedgerCount = $ledger->saleTransaction->saleLedgers->count();
            if (array_key_exists($uniqueKey, $checkItemIdExist)) {
                $checkItemIdExist[$uniqueKey] = $ledger->ledgers->model->hsn_sac_code;
                $checkTaxExist['quantity'][$uniqueKey] += 0;
                $checkTaxExist['taxable'][$uniqueKey] += ($ledger->taxable_value ?? 0);
                $checkTaxExist['cgst'][$uniqueKey] += ($ledger->classification_cgst_tax ?? 0);
                $checkTaxExist['sgst'][$uniqueKey] += ($ledger->classification_sgst_tax ?? 0);
                $checkTaxExist['igst'][$uniqueKey] += ($ledger->classification_igst_tax ?? 0);
                $checkTaxExist['invoice_amount'][$uniqueKey] += ($ledger->taxable_value ?? 0) + ($ledger->classification_cgst_tax ?? 0) +
                    ($ledger->classification_sgst_tax ?? 0) + ($ledger->cess_amount ?? 0) + ($ledger->classification_igst_tax ?? 0) + (round($ledger->saleTransaction->rounding_amount / $saleTransactionLedgerCount, 2) ?? 0);
                $checkTaxExist['description'][$uniqueKey] = $ledger->ledgers->model->description ?? '';
                $checkTaxExist['cess'][$uniqueKey] += $ledger->cess_amount ?? 0;
                $checkTaxExist['round_off'][$uniqueKey] += round($ledger->saleTransaction->rounding_amount / $saleTransactionLedgerCount, 2) ?? 0;
                $checkTaxExist['hsn_code'][$uniqueKey] = $ledger->ledgers->model->hsn_sac_code ?? '';
                $checkTaxExist['gst_rate'][$uniqueKey] = $ledger->gst_tax_percentage ?? '';
            } else {
                $checkItemIdExist[$uniqueKey] = $ledger->ledgers->model->hsn_sac_code;
                $checkTaxExist['quantity'][$uniqueKey] = 0;
                $checkTaxExist['taxable'][$uniqueKey] = ($ledger->taxable_value ?? 0);
                $checkTaxExist['cgst'][$uniqueKey] = ($ledger->classification_cgst_tax ?? 0);
                $checkTaxExist['sgst'][$uniqueKey] = ($ledger->classification_sgst_tax ?? 0);
                $checkTaxExist['igst'][$uniqueKey] = ($ledger->classification_igst_tax ?? 0);
                $checkTaxExist['invoice_amount'][$uniqueKey] = ($ledger->taxable_value ?? 0) + ($ledger->classification_cgst_tax ?? 0) +
                    ($ledger->classification_sgst_tax ?? 0) + ($ledger->cess_amount ?? 0) + ($ledger->classification_igst_tax ?? 0) + (round($ledger->saleTransaction->rounding_amount / $saleTransactionLedgerCount, 2) ?? 0);
                $checkTaxExist['description'][$uniqueKey] = $ledger->ledgers->model->description ?? '';
                $checkTaxExist['cess'][$uniqueKey] = $ledger->cess_amount ?? 0;
                $checkTaxExist['round_off'][$uniqueKey] = round($ledger->saleTransaction->rounding_amount / $saleTransactionLedgerCount, 2) ?? 0;
                $checkTaxExist['hsn_code'][$uniqueKey] = $ledger->ledgers->model->hsn_sac_code ?? '';
                $checkTaxExist['gst_rate'][$uniqueKey] = $ledger->gst_tax_percentage ?? '';
            }
        }

        foreach ($data['saleReturnItems'] as $item) {
            $hsnCode = $item->hsn_code ?? $item->items->model->hsn_sac_code ?? null;
            $uniqueKey = $hsnCode.'-'.$item->gst_tax_percentage ?? '0';
            $customUnit = UnitOfMeasurement::whereName($item->unit->for_gst_use_name)->first();

            $item->saleReturnTransaction->load('saleReturnItems');
            $saleReturnTransactionItemsCount = $item->saleReturnTransaction->saleReturnItems->count();

            if (array_key_exists($uniqueKey, $checkItemIdExist)) {
                $checkItemIdExist[$uniqueKey] = $item->hsn_code ?? $item->items->model->hsn_sac_code;
                $checkTaxExist['quantity'][$uniqueKey] -= ($item->items->item_type == ItemMaster::ITEM_MASTER_GOODS) ? $item->quantity : 0;
                $checkTaxExist['taxable'][$uniqueKey] -= ($item->taxable_value ?? 0);
                $checkTaxExist['cgst'][$uniqueKey] -= ($item->classification_cgst_tax ?? 0);
                $checkTaxExist['sgst'][$uniqueKey] -= ($item->classification_sgst_tax ?? 0);
                $checkTaxExist['igst'][$uniqueKey] -= ($item->classification_igst_tax ?? 0);
                $checkTaxExist['invoice_amount'][$uniqueKey] -= ($item->taxable_value ?? 0) + ($item->classification_cgst_tax ?? 0) +
                    ($item->classification_sgst_tax ?? 0) + ($item->cess_amount ?? 0) + ($item->classification_igst_tax ?? 0) + (round($item->saleReturnTransaction->rounding_amount / $saleReturnTransactionItemsCount, 2) ?? 0);
                $checkTaxExist['description'][$uniqueKey] = $item->items->model->description ?? '';
                $checkTaxExist['unit_name'][$uniqueKey] = empty($customUnit) ? (! empty($item->unit->full_name) ? $item->unit->full_name : '') : $customUnit->full_name;
                $checkTaxExist['unit_code'][$uniqueKey] = ($item->items->item_type == ItemMaster::ITEM_MASTER_GOODS) ? (empty($customUnit) ? (! empty($item->unit->code) ? $item->unit->code : '') : $customUnit->code) : 'NA';
                $checkTaxExist['cess'][$uniqueKey] -= $item->cess_amount ?? 0;
                $checkTaxExist['round_off'][$uniqueKey] -= round($item->saleReturnTransaction->rounding_amount / $saleReturnTransactionItemsCount, 2) ?? 0;
                $checkTaxExist['hsn_code'][$uniqueKey] = $item->hsn_code ?? $item->items->model->hsn_sac_code ?? '';
                $checkTaxExist['gst_rate'][$uniqueKey] = $item->gst_tax_percentage ?? '';
            } else {
                $checkItemIdExist[$uniqueKey] = $item->hsn_code ?? $item->items->model->hsn_sac_code;
                $checkTaxExist['quantity'][$uniqueKey] = ($item->items->item_type == ItemMaster::ITEM_MASTER_GOODS) ? (-$item->quantity) : 0;
                $checkTaxExist['taxable'][$uniqueKey] = (-$item->taxable_value ?? 0);
                $checkTaxExist['cgst'][$uniqueKey] = (-$item->classification_cgst_tax ?? 0);
                $checkTaxExist['sgst'][$uniqueKey] = (-$item->classification_sgst_tax ?? 0);
                $checkTaxExist['igst'][$uniqueKey] = (-$item->classification_igst_tax ?? 0);
                $checkTaxExist['invoice_amount'][$uniqueKey] = -(
                    ($item->taxable_value ?? 0)
                    + ($item->classification_cgst_tax ?? 0)
                    + ($item->classification_sgst_tax ?? 0)
                    + ($item->cess_amount ?? 0)
                    + ($item->classification_igst_tax ?? 0)
                    + (round($item->saleReturnTransaction->rounding_amount / $saleReturnTransactionItemsCount, 2) ?? 0)
                );
                $checkTaxExist['description'][$uniqueKey] = $item->items->model->description ?? '';
                $checkTaxExist['unit_name'][$uniqueKey] = empty($customUnit) ? (! empty($item->unit->full_name) ? $item->unit->full_name : '') : $customUnit->full_name;
                $checkTaxExist['unit_code'][$uniqueKey] = ($item->items->item_type == ItemMaster::ITEM_MASTER_GOODS) ? (empty($customUnit) ? (! empty($item->unit->code) ? $item->unit->code : '') : $customUnit->code) : 'NA';
                $checkTaxExist['cess'][$uniqueKey] = -$item->cess_amount ?? 0;
                $checkTaxExist['round_off'][$uniqueKey] = -(round($item->saleReturnTransaction->rounding_amount / $saleReturnTransactionItemsCount, 2) ?? 0);
                $checkTaxExist['hsn_code'][$uniqueKey] = $item->hsn_code ?? $item->items->model->hsn_sac_code ?? '';
                $checkTaxExist['gst_rate'][$uniqueKey] = $item->gst_tax_percentage ?? '';
            }
        }

        foreach ($data['saleReturnLedgers'] as $ledger) {
            $uniqueKey = $ledger->ledgers->model->hsn_sac_code.'-'.$ledger->gst_tax_percentage ?? '0';
            $ledger->saleReturnTransaction->load('saleReturnLedgers');
            $saleReturnTransactionLedgerCount = $ledger->saleReturnTransaction->saleReturnLedgers->count();

            if (array_key_exists($uniqueKey, $checkItemIdExist)) {
                $checkItemIdExist[$uniqueKey] = $ledger->ledgers->model->hsn_sac_code;
                $checkTaxExist['taxable'][$uniqueKey] -= ($ledger->taxable_value ?? 0);
                $checkTaxExist['quantity'][$uniqueKey] -= 0;
                $checkTaxExist['cgst'][$uniqueKey] -= ($ledger->classification_cgst_tax ?? 0);
                $checkTaxExist['sgst'][$uniqueKey] -= ($ledger->classification_sgst_tax ?? 0);
                $checkTaxExist['igst'][$uniqueKey] -= ($ledger->classification_igst_tax ?? 0);
                $checkTaxExist['invoice_amount'][$uniqueKey] -= ($ledger->taxable_value ?? 0) + ($ledger->classification_cgst_tax ?? 0) +
                    ($ledger->classification_sgst_tax ?? 0) + ($ledger->cess_amount ?? 0) + ($ledger->classification_igst_tax ?? 0) + (round($ledger->saleReturnTransaction->rounding_amount / $saleReturnTransactionLedgerCount, 2) ?? 0);
                $checkTaxExist['description'][$uniqueKey] = $ledger->ledgers->model->description ?? '';
                $checkTaxExist['cess'][$uniqueKey] -= $ledger->cess_amount ?? 0;
                $checkTaxExist['round_off'][$uniqueKey] -= round($ledger->saleReturnTransaction->rounding_amount / $saleReturnTransactionLedgerCount, 2) ?? 0;
                $checkTaxExist['hsn_code'][$uniqueKey] = $ledger->ledgers->model->hsn_sac_code ?? '';
                $checkTaxExist['gst_rate'][$uniqueKey] = $ledger->gst_tax_percentage ?? '';
            } else {
                $checkTaxExist['quantity'][$uniqueKey] = 0;
                $checkItemIdExist[$uniqueKey] = $ledger->ledgers->model->hsn_sac_code;
                $checkTaxExist['taxable'][$uniqueKey] = ($ledger->taxable_value ?? 0);
                $checkTaxExist['cgst'][$uniqueKey] = ($ledger->classification_cgst_tax ?? 0);
                $checkTaxExist['sgst'][$uniqueKey] = ($ledger->classification_sgst_tax ?? 0);
                $checkTaxExist['igst'][$uniqueKey] = ($ledger->classification_igst_tax ?? 0);
                $checkTaxExist['invoice_amount'][$uniqueKey] = ($ledger->taxable_value ?? 0) + ($ledger->classification_cgst_tax ?? 0) +
                    ($ledger->classification_sgst_tax ?? 0) + ($ledger->cess_amount ?? 0) + ($ledger->classification_igst_tax ?? 0) + (round($ledger->saleReturnTransaction->rounding_amount / $saleReturnTransactionLedgerCount, 2) ?? 0);
                $checkTaxExist['description'][$uniqueKey] = $ledger->ledgers->model->description ?? '';
                $checkTaxExist['cess'][$uniqueKey] = $ledger->cess_amount ?? 0;
                $checkTaxExist['round_off'][$uniqueKey] = round($ledger->saleReturnTransaction->rounding_amount / $saleReturnTransactionLedgerCount, 2) ?? 0;
                $checkTaxExist['hsn_code'][$uniqueKey] = $ledger->ledgers->model->hsn_sac_code ?? '';
                $checkTaxExist['gst_rate'][$uniqueKey] = $ledger->gst_tax_percentage ?? '';
            }
        }

        foreach ($data['incomeDebitNoteItems'] as $item) {
            $hsnCode = $item->hsn_code ?? $item->items->model->hsn_sac_code ?? null;
            $uniqueKey = $hsnCode.'-'.$item->gst_tax_percentage ?? '0';
            $customUnit = UnitOfMeasurement::whereName($item->unit->for_gst_use_name)->first();

            $item->incomeDebitNoteTransaction->load('incomeDebitNoteItems');
            $incomeDebitNoteTransactionItemsCount = $item->incomeDebitNoteTransaction->incomeDebitNoteItems->count();

            if (array_key_exists($uniqueKey, $checkItemIdExist)) {
                $checkItemIdExist[$uniqueKey] = $item->hsn_code ?? $item->items->model->hsn_sac_code;
                $checkTaxExist['quantity'][$uniqueKey] += ($item->items->item_type == ItemMaster::ITEM_MASTER_GOODS) ? $item->quantity : 0;
                $checkTaxExist['taxable'][$uniqueKey] += ($item->taxable_value ?? 0);
                $checkTaxExist['cgst'][$uniqueKey] += ($item->classification_cgst_tax ?? 0);
                $checkTaxExist['sgst'][$uniqueKey] += ($item->classification_sgst_tax ?? 0);
                $checkTaxExist['igst'][$uniqueKey] += ($item->classification_igst_tax ?? 0);
                $checkTaxExist['invoice_amount'][$uniqueKey] += ($item->taxable_value ?? 0) + ($item->classification_cgst_tax ?? 0) +
                    ($item->classification_sgst_tax ?? 0) + ($item->cess_amount ?? 0) + ($item->classification_igst_tax ?? 0) + (round($item->incomeDebitNoteTransaction->rounding_amount / $incomeDebitNoteTransactionItemsCount, 2));
                $checkTaxExist['description'][$uniqueKey] = $item->items->model->description ?? '';
                $checkTaxExist['unit_name'][$uniqueKey] = empty($customUnit) ? (! empty($item->unit->full_name) ? $item->unit->full_name : '') : $customUnit->full_name;
                $checkTaxExist['unit_code'][$uniqueKey] = ($item->items->item_type == ItemMaster::ITEM_MASTER_GOODS) ? (empty($customUnit) ? (! empty($item->unit->code) ? $item->unit->code : '') : $customUnit->code) : 'NA';
                $checkTaxExist['cess'][$uniqueKey] += $item->cess_amount ?? 0;
                $checkTaxExist['round_off'][$uniqueKey] += round($item->incomeDebitNoteTransaction->rounding_amount / $incomeDebitNoteTransactionItemsCount, 2) ?? 0;
                $checkTaxExist['hsn_code'][$uniqueKey] = $item->hsn_code ?? $item->items->model->hsn_sac_code ?? '';
                $checkTaxExist['gst_rate'][$uniqueKey] = $item->gst_tax_percentage ?? '';
            } else {
                $checkItemIdExist[$uniqueKey] = $item->hsn_code ?? $item->items->model->hsn_sac_code;
                $checkTaxExist['quantity'][$uniqueKey] = ($item->items->item_type == ItemMaster::ITEM_MASTER_GOODS) ? $item->quantity : 0;
                $checkTaxExist['taxable'][$uniqueKey] = ($item->taxable_value ?? 0);
                $checkTaxExist['cgst'][$uniqueKey] = ($item->classification_cgst_tax ?? 0);
                $checkTaxExist['sgst'][$uniqueKey] = ($item->classification_sgst_tax ?? 0);
                $checkTaxExist['igst'][$uniqueKey] = ($item->classification_igst_tax ?? 0);
                $checkTaxExist['invoice_amount'][$uniqueKey] = ($item->taxable_value ?? 0) + ($item->classification_cgst_tax ?? 0) +
                    ($item->classification_sgst_tax ?? 0) + ($item->cess_amount ?? 0) + ($item->classification_igst_tax ?? 0) + (round($item->incomeDebitNoteTransaction->rounding_amount / $incomeDebitNoteTransactionItemsCount, 2));
                $checkTaxExist['description'][$uniqueKey] = $item->items->model->description ?? '';
                $checkTaxExist['unit_name'][$uniqueKey] = empty($customUnit) ? (! empty($item->unit->full_name) ? $item->unit->full_name : '') : $customUnit->full_name;
                $checkTaxExist['unit_code'][$uniqueKey] = ($item->items->item_type == ItemMaster::ITEM_MASTER_GOODS) ? (empty($customUnit) ? (! empty($item->unit->code) ? $item->unit->code : '') : $customUnit->code) : 'NA';
                $checkTaxExist['cess'][$uniqueKey] = $item->cess_amount ?? 0;
                $checkTaxExist['round_off'][$uniqueKey] = round($item->incomeDebitNoteTransaction->rounding_amount / $incomeDebitNoteTransactionItemsCount, 2) ?? 0;
                $checkTaxExist['hsn_code'][$uniqueKey] = $item->hsn_code ?? $item->items->model->hsn_sac_code ?? '';
                $checkTaxExist['gst_rate'][$uniqueKey] = $item->gst_tax_percentage ?? '';
            }
        }

        foreach ($data['incomeDebitNoteLedgers'] as $ledger) {
            $uniqueKey = $ledger->ledgers->model->hsn_sac_code.'-'.$ledger->gst_tax_percentage ?? '0';
            $ledger->incomeDebitNoteTransaction->load('incomeDebitNoteLedgers');
            $incomeDebitNoteTransactionLedgerCount = $ledger->incomeDebitNoteTransaction->incomeDebitNoteLedgers->count();
            if (array_key_exists($uniqueKey, $checkItemIdExist)) {
                $checkItemIdExist[$uniqueKey] = $ledger->ledgers->model->hsn_sac_code;
                $checkTaxExist['taxable'][$uniqueKey] += ($ledger->taxable_value ?? 0);
                $checkTaxExist['quantity'][$uniqueKey] += 0;
                $checkTaxExist['cgst'][$uniqueKey] += ($ledger->classification_cgst_tax ?? 0);
                $checkTaxExist['sgst'][$uniqueKey] += ($ledger->classification_sgst_tax ?? 0);
                $checkTaxExist['igst'][$uniqueKey] += ($ledger->classification_igst_tax ?? 0);
                $checkTaxExist['invoice_amount'][$uniqueKey] += ($ledger->taxable_value ?? 0) + ($ledger->classification_cgst_tax ?? 0) +
                    ($ledger->classification_sgst_tax ?? 0) + ($ledger->cess_amount ?? 0) + ($ledger->classification_igst_tax ?? 0) + (round($ledger->incomeDebitNoteTransaction->rounding_amount / $incomeDebitNoteTransactionLedgerCount, 2) ?? 0);
                $checkTaxExist['description'][$uniqueKey] = $ledger->ledgers->model->description ?? '';
                $checkTaxExist['cess'][$uniqueKey] += $ledger->cess_amount ?? 0;
                $checkTaxExist['round_off'][$uniqueKey] += round($ledger->incomeDebitNoteTransaction->rounding_amount / $incomeDebitNoteTransactionLedgerCount, 2) ?? 0;
                $checkTaxExist['hsn_code'][$uniqueKey] = $ledger->ledgers->model->hsn_sac_code ?? '';
                $checkTaxExist['gst_rate'][$uniqueKey] = $ledger->gst_tax_percentage ?? '';
            } else {
                $checkTaxExist['quantity'][$uniqueKey] = 0;
                $checkItemIdExist[$uniqueKey] = $ledger->ledgers->model->hsn_sac_code;
                $checkTaxExist['taxable'][$uniqueKey] = ($ledger->taxable_value ?? 0);
                $checkTaxExist['cgst'][$uniqueKey] = ($ledger->classification_cgst_tax ?? 0);
                $checkTaxExist['sgst'][$uniqueKey] = ($ledger->classification_sgst_tax ?? 0);
                $checkTaxExist['igst'][$uniqueKey] = ($ledger->classification_igst_tax ?? 0);
                $checkTaxExist['invoice_amount'][$uniqueKey] = ($ledger->taxable_value ?? 0) + ($ledger->classification_cgst_tax ?? 0) +
                    ($ledger->classification_sgst_tax ?? 0) + ($ledger->cess_amount ?? 0) + ($ledger->classification_igst_tax ?? 0) + (round($ledger->incomeDebitNoteTransaction->rounding_amount / $incomeDebitNoteTransactionLedgerCount, 2) ?? 0);
                $checkTaxExist['description'][$uniqueKey] = $ledger->ledgers->model->description ?? '';
                $checkTaxExist['cess'][$uniqueKey] = $ledger->cess_amount ?? 0;
                $checkTaxExist['round_off'][$uniqueKey] = round($ledger->incomeDebitNoteTransaction->rounding_amount / $incomeDebitNoteTransactionLedgerCount, 2) ?? 0;
                $checkTaxExist['hsn_code'][$uniqueKey] = $ledger->ledgers->model->hsn_sac_code ?? '';
                $checkTaxExist['gst_rate'][$uniqueKey] = $ledger->gst_tax_percentage ?? '';
            }
        }

        foreach ($data['incomeCreditNoteItems'] as $item) {
            $hsnCode = $item->hsn_code ?? $item->items->model->hsn_sac_code ?? null;
            $uniqueKey = $hsnCode.'-'.$item->gst_tax_percentage ?? '0';
            $customUnit = UnitOfMeasurement::whereName($item->unit->for_gst_use_name)->first();

            $item->incomeCreditNoteTransaction->load('incomeCreditNoteItems');
            $incomeCreditNoteTransactionItemsCount = $item->incomeCreditNoteTransaction->incomeCreditNoteItems->count();

            if (array_key_exists($uniqueKey, $checkItemIdExist)) {
                $checkItemIdExist[$uniqueKey] = $item->hsn_code ?? $item->items->model->hsn_sac_code;
                $checkTaxExist['quantity'][$uniqueKey] = ($item->items->item_type == ItemMaster::ITEM_MASTER_GOODS) ? round($checkTaxExist['quantity'][$uniqueKey] - $item->quantity, 2) : 0;
                $checkTaxExist['taxable'][$uniqueKey] = round($checkTaxExist['taxable'][$uniqueKey] - ($item->taxable_value ?? 0), 2);
                $checkTaxExist['cgst'][$uniqueKey] = round($checkTaxExist['cgst'][$uniqueKey] - ($item->classification_cgst_tax ?? 0), 2);
                $checkTaxExist['sgst'][$uniqueKey] = round($checkTaxExist['sgst'][$uniqueKey] - ($item->classification_sgst_tax ?? 0), 2);
                $checkTaxExist['igst'][$uniqueKey] = round($checkTaxExist['igst'][$uniqueKey] - ($item->classification_igst_tax ?? 0), 2);
                $checkTaxExist['invoice_amount'][$uniqueKey] = round($checkTaxExist['invoice_amount'][$uniqueKey] - (($item->taxable_value ?? 0) + ($item->classification_cgst_tax ?? 0) +
                 ($item->classification_sgst_tax ?? 0) + ($item->cess_amount ?? 0) + ($item->classification_igst_tax ?? 0) + (round($item->incomeCreditNoteTransaction->rounding_amount / $incomeCreditNoteTransactionItemsCount, 2))), 2);
                $checkTaxExist['description'][$uniqueKey] = $item->items->model->description ?? '';
                $checkTaxExist['unit_name'][$uniqueKey] = empty($customUnit) ? (! empty($item->unit->full_name) ? $item->unit->full_name : '') : $customUnit->full_name;
                $checkTaxExist['unit_code'][$uniqueKey] = ($item->items->item_type == ItemMaster::ITEM_MASTER_GOODS) ? (empty($customUnit) ? (! empty($item->unit->code) ? $item->unit->code : '') : $customUnit->code) : 'NA';
                $checkTaxExist['cess'][$uniqueKey] = round($checkTaxExist['cess'][$uniqueKey] - ($item->cess_amount ?? 0), 2);
                $checkTaxExist['round_off'][$uniqueKey] = round($checkTaxExist['round_off'][$uniqueKey] - round($item->incomeCreditNoteTransaction->rounding_amount / $incomeCreditNoteTransactionItemsCount, 2), 2) ?? 0;
                $checkTaxExist['hsn_code'][$uniqueKey] = $item->hsn_code ?? $item->items->model->hsn_sac_code ?? '';
                $checkTaxExist['gst_rate'][$uniqueKey] = $item->gst_tax_percentage ?? '';
            } else {
                $checkItemIdExist[$uniqueKey] = $item->hsn_code ?? $item->items->model->hsn_sac_code;
                $checkTaxExist['quantity'][$uniqueKey] = ($item->items->item_type == ItemMaster::ITEM_MASTER_GOODS) ? (-$item->quantity) : 0;
                $checkTaxExist['taxable'][$uniqueKey] = (-$item->taxable_value ?? 0);
                $checkTaxExist['sgst'][$uniqueKey] = (-$item->classification_sgst_tax ?? 0);
                $checkTaxExist['cgst'][$uniqueKey] = (-$item->classification_cgst_tax ?? 0);
                $checkTaxExist['igst'][$uniqueKey] = (-$item->classification_igst_tax ?? 0);
                $checkTaxExist['invoice_amount'][$uniqueKey] = -(
                    ($item->taxable_value ?? 0)
                    + ($item->classification_cgst_tax ?? 0)
                    + ($item->classification_sgst_tax ?? 0)
                    + ($item->cess_amount ?? 0)
                    + ($item->classification_igst_tax ?? 0)
                    + (round($item->incomeCreditNoteTransaction->rounding_amount / $incomeCreditNoteTransactionItemsCount, 2))
                );
                $checkTaxExist['description'][$uniqueKey] = $item->items->model->description ?? '';
                $checkTaxExist['unit_name'][$uniqueKey] = empty($customUnit) ? (! empty($item->unit->full_name) ? $item->unit->full_name : '') : $customUnit->for_gst_use_name;
                $checkTaxExist['unit_code'][$uniqueKey] = ($item->items->item_type == ItemMaster::ITEM_MASTER_GOODS) ? (empty($customUnit) ? (! empty($item->unit->code) ? $item->unit->code : '') : $customUnit->code) : 'NA';
                $checkTaxExist['cess'][$uniqueKey] = -$item->cess_amount ?? 0;
                $checkTaxExist['round_off'][$uniqueKey] = -(round($item->incomeCreditNoteTransaction->rounding_amount / $incomeCreditNoteTransactionItemsCount, 2)) ?? 0;
                $checkTaxExist['hsn_code'][$uniqueKey] = $item->hsn_code ?? $item->items->model->hsn_sac_code ?? '';
                $checkTaxExist['gst_rate'][$uniqueKey] = $item->gst_tax_percentage ?? '';
            }
        }

        foreach ($data['incomeCreditNoteLedgers'] as $ledger) {
            $uniqueKey = $ledger->ledgers->model->hsn_sac_code.'-'.$ledger->gst_tax_percentage ?? '0';
            $ledger->incomeCreditNoteTransaction->load('incomeCreditNoteLedgers');
            $incomeCreditNoteTransactionLedgerCount = $ledger->incomeCreditNoteTransaction->incomeCreditNoteLedgers->count();
            if (array_key_exists($uniqueKey, $checkItemIdExist)) {
                $checkTaxExist['quantity'][$uniqueKey] -= 0;
                $checkItemIdExist[$uniqueKey] = $ledger->ledgers->model->hsn_sac_code;
                $checkTaxExist['taxable'][$uniqueKey] = round($checkTaxExist['taxable'][$uniqueKey] - ($ledger->taxable_value ?? 0), 2);
                $checkTaxExist['cgst'][$uniqueKey] = round($checkTaxExist['cgst'][$uniqueKey] - ($ledger->classification_cgst_tax ?? 0), 2);
                $checkTaxExist['sgst'][$uniqueKey] = round($checkTaxExist['sgst'][$uniqueKey] - ($ledger->classification_sgst_tax ?? 0), 2);
                $checkTaxExist['igst'][$uniqueKey] = round($checkTaxExist['igst'][$uniqueKey] - ($ledger->classification_igst_tax ?? 0), 2);
                $checkTaxExist['invoice_amount'][$uniqueKey] = round($checkTaxExist['invoice_amount'][$uniqueKey] - (($ledger->taxable_value ?? 0) + ($ledger->classification_cgst_tax ?? 0) +
                    ($ledger->classification_sgst_tax ?? 0) + ($ledger->cess_amount ?? 0) + ($ledger->classification_igst_tax ?? 0) + (round($ledger->incomeCreditNoteTransaction->rounding_amount / $incomeCreditNoteTransactionLedgerCount, 2) ?? 0)), 2);
                $checkTaxExist['description'][$uniqueKey] = $ledger->ledgers->model->description ?? '';
                $checkTaxExist['cess'][$uniqueKey] = round($checkTaxExist['cess'][$uniqueKey] - ($ledger->cess_amount ?? 0), 2);
                $checkTaxExist['round_off'][$uniqueKey] = round($checkTaxExist['round_off'][$uniqueKey] - (round($ledger->incomeCreditNoteTransaction->rounding_amount / $incomeCreditNoteTransactionLedgerCount, 2) ?? 0), 2);
                $checkTaxExist['hsn_code'][$uniqueKey] = $ledger->ledgers->model->hsn_sac_code ?? '';
                $checkTaxExist['gst_rate'][$uniqueKey] = $ledger->gst_tax_percentage ?? '';
            } else {
                $checkTaxExist['quantity'][$uniqueKey] = 0;
                $checkItemIdExist[$uniqueKey] = $ledger->ledgers->model->hsn_sac_code;
                $checkTaxExist['taxable'][$uniqueKey] = (-$ledger->taxable_value ?? 0);
                $checkTaxExist['cgst'][$uniqueKey] = (-$ledger->classification_cgst_tax ?? 0);
                $checkTaxExist['sgst'][$uniqueKey] = (-$ledger->classification_sgst_tax ?? 0);
                $checkTaxExist['igst'][$uniqueKey] = (-$ledger->classification_igst_tax ?? 0);
                $checkTaxExist['invoice_amount'][$uniqueKey] = -($ledger->taxable_value ?? 0) + ($ledger->classification_cgst_tax ?? 0) +
                    ($ledger->classification_sgst_tax ?? 0) + ($ledger->cess_amount ?? 0) + ($ledger->classification_igst_tax ?? 0) + (round($ledger->incomeCreditNoteTransaction->rounding_amount / $incomeCreditNoteTransactionLedgerCount, 2) ?? 0);
                $checkTaxExist['description'][$uniqueKey] = $ledger->ledgers->model->description ?? '';
                $checkTaxExist['cess'][$uniqueKey] = -($ledger->cess_amount ?? 0);
                $checkTaxExist['round_off'][$uniqueKey] = -(round($ledger->incomeCreditNoteTransaction->rounding_amount / $incomeCreditNoteTransactionLedgerCount, 2) ?? 0);
                $checkTaxExist['hsn_code'][$uniqueKey] = $ledger->ledgers->model->hsn_sac_code ?? '';
                $checkTaxExist['gst_rate'][$uniqueKey] = $ledger->gst_tax_percentage ?? '';
            }
        }

        foreach ($data['saleTransactions'] as $item) {
            $shippingCharges = $item->prepareAdditionalCharges(true);

            foreach ($shippingCharges as $shippingCharge) {
                $uniqueKey = $shippingCharge['hsn_code'].'-'.$shippingCharge['rate_of_gst'] ?? '0';
                if (array_key_exists($uniqueKey, $checkItemIdExist)) {
                    $checkItemIdExist[$uniqueKey] = $shippingCharge['hsn_code'];
                    $checkTaxExist['quantity'][$uniqueKey] += 0;
                    $checkTaxExist['taxable'][$uniqueKey] += $shippingCharge['taxable_value'];
                    $checkTaxExist['cgst'][$uniqueKey] += $shippingCharge['cgst'] ?? 0;
                    $checkTaxExist['sgst'][$uniqueKey] += $shippingCharge['sgst'] ?? 0;
                    $checkTaxExist['igst'][$uniqueKey] += $shippingCharge['igst'] ?? 0;
                    $checkTaxExist['cess'][$uniqueKey] += $shippingCharge['cess_amount'] ?? 0;
                    $checkTaxExist['invoice_amount'][$uniqueKey] += $shippingCharge['cgst'] + $shippingCharge['sgst']
                        + $shippingCharge['igst'] + $shippingCharge['cess_amount'] + $shippingCharge['taxable_value'];
                    $checkTaxExist['description'][$uniqueKey] = '';
                    $checkTaxExist['unit_name'][$uniqueKey] = 'NA';
                    $checkTaxExist['unit_code'][$uniqueKey] = 'NA';
                    $checkTaxExist['hsn_code'][$uniqueKey] = $shippingCharge['hsn_code'];
                    $checkTaxExist['gst_rate'][$uniqueKey] = $shippingCharge['rate_of_gst'];
                } else {
                    $checkItemIdExist[$uniqueKey] = $shippingCharge['hsn_code'];
                    $checkTaxExist['quantity'][$uniqueKey] = 0;
                    $checkTaxExist['taxable'][$uniqueKey] = $shippingCharge['taxable_value'];
                    $checkTaxExist['sgst'][$uniqueKey] = $shippingCharge['cgst'] ?? 0;
                    $checkTaxExist['cgst'][$uniqueKey] = $shippingCharge['sgst'] ?? 0;
                    $checkTaxExist['igst'][$uniqueKey] = $shippingCharge['igst'] ?? 0;
                    $checkTaxExist['cess'][$uniqueKey] = $shippingCharge['cess_amount'] ?? 0;
                    $checkTaxExist['invoice_amount'][$uniqueKey] = $shippingCharge['cgst'] + $shippingCharge['sgst']
                        + $shippingCharge['igst'] + $shippingCharge['cess_amount'] + $shippingCharge['taxable_value'];
                    $checkTaxExist['description'][$uniqueKey] = '';
                    $checkTaxExist['unit_name'][$uniqueKey] = 'NA';
                    $checkTaxExist['unit_code'][$uniqueKey] = 'NA';
                    $checkTaxExist['hsn_code'][$uniqueKey] = $shippingCharge['hsn_code'];
                    $checkTaxExist['gst_rate'][$uniqueKey] = $shippingCharge['rate_of_gst'];
                }
            }
        }

        foreach ($data['saleReturnTransactions'] as $item) {
            $shippingCharges = $item->prepareAdditionalCharges(true);
            foreach ($shippingCharges as $shippingCharge) {
                $uniqueKey = $shippingCharge['hsn_code'].'-'.$shippingCharge['rate_of_gst'] ?? '0';
                if (array_key_exists($uniqueKey, $checkItemIdExist)) {
                    $checkItemIdExist[$uniqueKey] = $shippingCharge['hsn_code'];
                    $checkTaxExist['quantity'][$uniqueKey] -= 0;
                    $checkTaxExist['taxable'][$uniqueKey] -= $shippingCharge['taxable_value'];
                    $checkTaxExist['cgst'][$uniqueKey] -= $shippingCharge['cgst'] ?? 0;
                    $checkTaxExist['sgst'][$uniqueKey] -= $shippingCharge['sgst'] ?? 0;
                    $checkTaxExist['igst'][$uniqueKey] -= $shippingCharge['igst'] ?? 0;
                    $checkTaxExist['cess'][$uniqueKey] -= $shippingCharge['cess_amount'] ?? 0;
                    $checkTaxExist['invoice_amount'][$uniqueKey] -= $shippingCharge['cgst'] + $shippingCharge['sgst']
                        + $shippingCharge['igst'] + $shippingCharge['cess_amount'] + $shippingCharge['taxable_value'];
                    $checkTaxExist['description'][$uniqueKey] = '';
                    $checkTaxExist['unit_name'][$uniqueKey] = 'NA';
                    $checkTaxExist['unit_code'][$uniqueKey] = 'NA';
                    $checkTaxExist['hsn_code'][$uniqueKey] = $shippingCharge['hsn_code'];
                    $checkTaxExist['gst_rate'][$uniqueKey] = $shippingCharge['rate_of_gst'];
                } else {
                    $checkItemIdExist[$uniqueKey] = $shippingCharge['hsn_code'];
                    $checkTaxExist['quantity'][$uniqueKey] = 0;
                    $checkTaxExist['taxable'][$uniqueKey] = $shippingCharge['taxable_value'];
                    $checkTaxExist['sgst'][$uniqueKey] = $shippingCharge['cgst'] ?? 0;
                    $checkTaxExist['cgst'][$uniqueKey] = $shippingCharge['sgst'] ?? 0;
                    $checkTaxExist['igst'][$uniqueKey] = $shippingCharge['igst'] ?? 0;
                    $checkTaxExist['cess'][$uniqueKey] = $shippingCharge['cess_amount'] ?? 0;
                    $checkTaxExist['invoice_amount'][$uniqueKey] = $shippingCharge['cgst'] + $shippingCharge['sgst']
                        + $shippingCharge['igst'] + $shippingCharge['cess_amount'] + $shippingCharge['taxable_value'];
                    $checkTaxExist['description'][$uniqueKey] = '';
                    $checkTaxExist['unit_name'][$uniqueKey] = 'NA';
                    $checkTaxExist['unit_code'][$uniqueKey] = 'NA';
                    $checkTaxExist['hsn_code'][$uniqueKey] = $shippingCharge['hsn_code'];
                    $checkTaxExist['gst_rate'][$uniqueKey] = $shippingCharge['rate_of_gst'];
                }
            }
        }

        foreach ($data['incomeDebitTransactions'] as $item) {
            $shippingCharges = $item->prepareAdditionalCharges(true);

            foreach ($shippingCharges as $shippingCharge) {
                $uniqueKey = $shippingCharge['hsn_code'].'-'.$shippingCharge['rate_of_gst'] ?? '0';
                if (array_key_exists($uniqueKey, $checkItemIdExist)) {
                    $checkItemIdExist[$uniqueKey] = $shippingCharge['hsn_code'];
                    $checkTaxExist['quantity'][$uniqueKey] += 0;
                    $checkTaxExist['taxable'][$uniqueKey] += $shippingCharge['taxable_value'];
                    $checkTaxExist['cgst'][$uniqueKey] += $shippingCharge['cgst'] ?? 0;
                    $checkTaxExist['sgst'][$uniqueKey] += $shippingCharge['sgst'] ?? 0;
                    $checkTaxExist['igst'][$uniqueKey] += $shippingCharge['igst'] ?? 0;
                    $checkTaxExist['cess'][$uniqueKey] += $shippingCharge['cess_amount'] ?? 0;
                    $checkTaxExist['invoice_amount'][$uniqueKey] += $shippingCharge['cgst'] + $shippingCharge['sgst']
                        + $shippingCharge['igst'] + $shippingCharge['cess_amount'] + $shippingCharge['taxable_value'];
                    $checkTaxExist['description'][$uniqueKey] = '';
                    $checkTaxExist['unit_name'][$uniqueKey] = 'NA';
                    $checkTaxExist['unit_code'][$uniqueKey] = 'NA';
                    $checkTaxExist['hsn_code'][$uniqueKey] = $shippingCharge['hsn_code'];
                    $checkTaxExist['gst_rate'][$uniqueKey] = $shippingCharge['rate_of_gst'];
                } else {
                    $checkItemIdExist[$uniqueKey] = $shippingCharge['hsn_code'];
                    $checkTaxExist['quantity'][$uniqueKey] = 0;
                    $checkTaxExist['taxable'][$uniqueKey] = $shippingCharge['taxable_value'];
                    $checkTaxExist['sgst'][$uniqueKey] = $shippingCharge['cgst'] ?? 0;
                    $checkTaxExist['cgst'][$uniqueKey] = $shippingCharge['sgst'] ?? 0;
                    $checkTaxExist['igst'][$uniqueKey] = $shippingCharge['igst'] ?? 0;
                    $checkTaxExist['cess'][$uniqueKey] = $shippingCharge['cess_amount'] ?? 0;
                    $checkTaxExist['invoice_amount'][$uniqueKey] = $shippingCharge['cgst'] + $shippingCharge['sgst']
                        + $shippingCharge['igst'] + $shippingCharge['cess_amount'] + $shippingCharge['taxable_value'];
                    $checkTaxExist['description'][$uniqueKey] = '';
                    $checkTaxExist['unit_name'][$uniqueKey] = 'NA';
                    $checkTaxExist['unit_code'][$uniqueKey] = 'NA';
                    $checkTaxExist['hsn_code'][$uniqueKey] = $shippingCharge['hsn_code'];
                    $checkTaxExist['gst_rate'][$uniqueKey] = $shippingCharge['rate_of_gst'];
                }
            }
        }

        foreach ($data['incomeCreditTransactions'] as $item) {
            $shippingCharges = $item->prepareAdditionalCharges(true);

            foreach ($shippingCharges as $shippingCharge) {
                $uniqueKey = $shippingCharge['hsn_code'].'-'.$shippingCharge['rate_of_gst'] ?? '0';
                if (array_key_exists($uniqueKey, $checkItemIdExist)) {
                    $checkItemIdExist[$uniqueKey] = $shippingCharge['hsn_code'];
                    $checkTaxExist['quantity'][$uniqueKey] -= 0;
                    $checkTaxExist['taxable'][$uniqueKey] -= $shippingCharge['taxable_value'];
                    $checkTaxExist['cgst'][$uniqueKey] -= $shippingCharge['cgst'] ?? 0;
                    $checkTaxExist['sgst'][$uniqueKey] -= $shippingCharge['sgst'] ?? 0;
                    $checkTaxExist['igst'][$uniqueKey] -= $shippingCharge['igst'] ?? 0;
                    $checkTaxExist['cess'][$uniqueKey] -= $shippingCharge['cess_amount'] ?? 0;
                    $checkTaxExist['invoice_amount'][$uniqueKey] -= $shippingCharge['cgst'] + $shippingCharge['sgst']
                        + $shippingCharge['igst'] + $shippingCharge['cess_amount'] + $shippingCharge['taxable_value'];
                    $checkTaxExist['description'][$uniqueKey] = '';
                    $checkTaxExist['unit_name'][$uniqueKey] = 'NA';
                    $checkTaxExist['unit_code'][$uniqueKey] = 'NA';
                    $checkTaxExist['hsn_code'][$uniqueKey] = $shippingCharge['hsn_code'];
                    $checkTaxExist['gst_rate'][$uniqueKey] = $shippingCharge['rate_of_gst'];
                } else {
                    $checkItemIdExist[$uniqueKey] = $shippingCharge['hsn_code'];
                    $checkTaxExist['quantity'][$uniqueKey] = 0;
                    $checkTaxExist['taxable'][$uniqueKey] = $shippingCharge['taxable_value'];
                    $checkTaxExist['sgst'][$uniqueKey] = $shippingCharge['cgst'] ?? 0;
                    $checkTaxExist['cgst'][$uniqueKey] = $shippingCharge['sgst'] ?? 0;
                    $checkTaxExist['igst'][$uniqueKey] = $shippingCharge['igst'] ?? 0;
                    $checkTaxExist['cess'][$uniqueKey] = $shippingCharge['cess_amount'] ?? 0;
                    $checkTaxExist['invoice_amount'][$uniqueKey] = $shippingCharge['cgst'] + $shippingCharge['sgst']
                        + $shippingCharge['igst'] + $shippingCharge['cess_amount'] + $shippingCharge['taxable_value'];
                    $checkTaxExist['description'][$uniqueKey] = '';
                    $checkTaxExist['unit_name'][$uniqueKey] = 'NA';
                    $checkTaxExist['unit_code'][$uniqueKey] = 'NA';
                    $checkTaxExist['hsn_code'][$uniqueKey] = $shippingCharge['hsn_code'];
                    $checkTaxExist['gst_rate'][$uniqueKey] = $shippingCharge['rate_of_gst'];
                }
            }
        }

        foreach ($checkItemIdExist as $key => $item) {
            $dataItem[] = [
                'hsn_code' => $checkTaxExist['hsn_code'][$key],
                'quantity' => $checkTaxExist['quantity'][$key] ?? 0,
                'taxable_value' => $checkTaxExist['taxable'][$key],
                'cgst' => $checkTaxExist['cgst'][$key],
                'sgst' => $checkTaxExist['sgst'][$key],
                'igst' => $checkTaxExist['igst'][$key],
                'cess_amount' => $checkTaxExist['cess'][$key],
                'invoice_amount' => $checkTaxExist['invoice_amount'][$key],
                'description' => $checkTaxExist['description'][$key],
                'unit_name' => $checkTaxExist['unit_name'][$key] ?? 'NA',
                'unit_code' => $checkTaxExist['unit_code'][$key] ?? 'NA',
                'cess' => $checkTaxExist['cess'][$key],
                'round_off' => $checkTaxExist['round_off'][$key] ?? 0,
                'gst_rate' => $checkTaxExist['gst_rate'][$key],
            ];
        }

        usort($dataItem, function ($a, $b) {
            return strtotime($a['hsn_code']) - strtotime($b['hsn_code']);
        });

        return $dataItem;
    }
}

if (! function_exists('extractedMethod')) {

    function extractedMethod($ledger, mixed $creditAmount, mixed $debitAmount): array
    {
        $amount = $ledger['opening_balance'];
        $type = $ledger['opening_balance_type'];

        if ($debitAmount < 0) {
            $finalAmount = $creditAmount + $debitAmount;
        } else {
            $finalAmount = $creditAmount - $debitAmount;
        }

        if ($finalAmount < 0) {
            $finalType = Ledger::DR;
        } else {
            $finalType = Ledger::CR;
        }
        $finalAmount = abs($finalAmount);

        if ($type == $finalType) {
            $amount += $finalAmount;
        } else {
            if ($amount < $finalAmount) {
                if ($type == Ledger::DR) {
                    $type = Ledger::CR;
                } else {
                    $type = Ledger::DR;
                }
            }
            $amount -= $finalAmount;
        }

        return ['balance' => abs(round($amount, getCompanyFixedDigitNumber())), 'balanceType' => $type];
    }
}

if (! function_exists('calculationTransactionExpense')) {
    function calculationTransactionExpense($transaction)
    {
        return (float) $transaction->sum('shipping_freight_with_gst') +
            (float) $transaction->sum('packing_charge_with_gst') +
            (float) $transaction->sum('rounding_amount') +
            (float) $transaction->sum('cgst') +
            (float) $transaction->sum('sgst') +
            (float) $transaction->sum('igst') +
            (float) $transaction->sum('tcs_amount');
    }
}

if (! function_exists('getChargesGstAmount')) {
    function getChargesGstAmount($withGstAmount, $withoutGstAMount)
    {
        $chargeTaxAmount = 0;
        if ($withGstAmount != 0) {
            $chargeTaxAmount = $withGstAmount - $withoutGstAMount;
        }

        return $chargeTaxAmount;
    }
}

if (! function_exists('getTcsRateSection')) {

    function getTcsRateSection(): array
    {
        return TcsRate::all()->pluck('section_name', 'id')->toArray();
    }
}

if (! function_exists('getTdsRateSection')) {

    function getTdsRateSection(): array
    {
        return TdsRate::all()->pluck('section_name', 'id')->toArray();
    }
}

if (! function_exists('getDeducteeCode')) {

    function getDeducteeCode($panNo): string
    {
        $deducteeCode = '0';
        $panFourCharacters = substr($panNo, 3, 1);
        if ($panFourCharacters == 'C') {
            $deducteeCode = '01';
        } else {
            $deducteeCode = '02';
        }

        return $deducteeCode;
    }
}

if (! function_exists('getFranchiseId')) {
    function getFranchiseId()
    {
        if (! empty(getCurrentFranchise())) {
            $franchiseId = getCurrentFranchise()->id;
        } else {
            $teamManagement = TeamManagement::whereUserId(getLoginUser()->id)->first();
            if ($teamManagement == null) {
                $franchiseSession = session('current_franchise');
                if (! empty($franchiseSession)) {
                    $franchiseId = $franchiseSession->id;
                } else {
                    $userId = session('impersonated_by');
                    $user = User::where('id', $userId)->with('franchise')->first();
                    if ($user->franchise == null) {
                        $teamMgtId = TeamManagement::whereUserId($userId)->first();
                        $franchiseId = $teamMgtId->franchise_id;
                    } else {
                        $franchiseId = $user->franchise->id;
                    }
                }
            } else {
                $franchiseId = $teamManagement->franchise_id;
            }
        }

        return $franchiseId;
    }
}

if (! function_exists('enableDeletedScope')) {
    function enableDeletedScope(): bool
    {
        $companyScope = new CompanyScope();
        $companyScope->setDeletedScope(true);

        return true;
    }
}

if (! function_exists('disableDeletedScope')) {
    function disableDeletedScope(): bool
    {
        $companyScope = new CompanyScope();
        $companyScope->setDeletedScope(false);

        return true;
    }
}

if (! function_exists('disableCompanyScope')) {
    function disableCompanyScope(): bool
    {
        $companyScope = new CompanyScope();
        $companyScope->setApply(false);

        return true;
    }
}

if (! function_exists('enableCompanyScope')) {
    function enableCompanyScope(): bool
    {
        $companyScope = new CompanyScope();
        $companyScope->setApply(true);

        return true;
    }
}

if (! function_exists('disableRecurringScope')) {
    function disableRecurringScope(): bool
    {
        $recurringScope = new RecurringScope();
        $recurringScope->setApply(false);

        return true;
    }
}

if (! function_exists('enableRecurringScope')) {
    function enableRecurringScope(): bool
    {
        $recurringScope = new RecurringScope();
        $recurringScope->setApply(true);

        return true;
    }
}

if (! function_exists('isCompanyScopeEnabled')) {

    function isCompanyScopeEnabled()
    {
        return session()->get('companyScopeEnabled');
    }
}

if (! function_exists('checkIncomeTransactionType')) {
    function checkIncomeTransactionType($collection)
    {
        if (get_class($collection) == SaleTransaction::class) {
            return SaleTransaction::TRANSACTION_TYPE;
        }
        if (get_class($collection) == SaleReturnTransaction::class) {
            return SaleReturnTransaction::TRANSACTION_TYPE;
        }
        if (get_class($collection) == IncomeCreditNoteTransaction::class) {
            return IncomeCreditNoteTransaction::TRANSACTION_TYPE;
        }
        if (get_class($collection) == IncomeDebitNoteTransaction::class) {
            return IncomeDebitNoteTransaction::TRANSACTION_TYPE;
        }
    }
}

if (! function_exists('checkExpenseTransactionType')) {
    function checkExpenseTransactionType($collection)
    {
        if (get_class($collection) == PurchaseTransaction::class) {
            return PurchaseTransaction::TRANSACTION_TYPE;
        }
        if (get_class($collection) == PurchaseReturnTransaction::class) {
            return PurchaseReturnTransaction::TRANSACTION_TYPE;
        }
        if (get_class($collection) == ExpenseCreditNoteTransaction::class) {
            return ExpenseCreditNoteTransaction::TRANSACTION_TYPE;
        }
        if (get_class($collection) == ExpenseDebitNoteTransaction::class) {
            return ExpenseDebitNoteTransaction::TRANSACTION_TYPE;
        }
    }
}

if (! function_exists('checkGstr3bRecordIsAvailable')) {
    function checkGstr3bRecordIsAvailable($data)
    {
        $isRecordAvailable = false;
        if (
            count($data['a41']['purchaseTranItems']) || count($data['a41']['purchaseReTranItems'])
            || count($data['a41']['drNoteIncItems']) || count($data['a41']['crNoteIncItems']) ||
            count($data['a42']['purchaseTranItems']) || count($data['a42']['purchaseReTranItems'])
            || count($data['a42']['drNoteIncItems']) || count($data['a42']['crNoteIncItems']) ||
            count($data['d31']['saleTranItems']) || count($data['d31']['saleReTranItems'])
            || count($data['d31']['drNoteIncItems']) || count($data['d31']['crNoteIncItems']) ||
            count($data['a45']['purchaseTranItems']) || count($data['a45']['purchaseReTranItems'])
            || count($data['a45']['drNoteIncItems']) || count($data['a45']['crNoteIncItems'])
        ) {
            $isRecordAvailable = true;
        }

        return $isRecordAvailable;
    }
}

if (! function_exists('getCompanyDashboardButtonArr')) {
    function getCompanyDashboardButtonArr()
    {
        $buttonArr = [];
        $buttonIdArr = CompanyDashboardButton::whereCompanyId(getCurrentCompany()->id)
            ->pluck('button_id')->toArray();

        foreach (CompanyDashboardButton::REPORT_ARR as $key => $value) {
            if (! in_array($key, $buttonIdArr)) {
                $buttonArr[$key] = $value;
            }
        }

        return $buttonArr;
    }
}

if (! function_exists('getTotalAmount')) {
    function getTotalAmount($group, &$amount = 0): float|int
    {
        $amount += array_sum(array_column($group['ledger'], 'amount'));
        if (isset($group['recursive_children']) && ! empty($group['recursive_children'])) {
            foreach ($group['recursive_children'] as $childGroup) {
                getTotalAmount($childGroup, $amount);
            }
        }

        return $amount;
    }
}

if (! function_exists('getTotalAmountOfAllTransactions')) {

    function getTotalAmountOfAllTransactions($groups, &$amount = 0)
    {
        foreach ($groups as $group) {
            $amount += getTotalAmount($group);
        }

        return $amount;
    }
}

if (! function_exists('getPaymentTotalAmount')) {

    function getPaymentTotalAmount($group, &$amount = 0)
    {
        $amount += array_sum(array_column($group['ledger'], 'amount'));
        if (isset($group['recursive_children']) && ! empty($group['recursive_children'])) {
            foreach ($group['recursive_children'] as $childGroup) {
                getPaymentTotalAmount($childGroup, $amount);
            }
        }

        return $amount;
    }
}

if (! function_exists('getPaymentTotalAmountOfAllTransactions')) {

    function getPaymentTotalAmountOfAllTransactions($groups, &$amount = 0)
    {
        foreach ($groups as $group) {
            $amount += getPaymentTotalAmount($group);
        }

        return $amount;
    }
}

if (! function_exists('getFinancialYearStartDate')) {
    function getFinancialYearStartDate(): string
    {
        $month = \Illuminate\Support\Carbon::now()->format('m');
        if ($month > 3) {
            $year = date('Y');
        } else {
            $year = date('Y', strtotime('-1 year'));
        }

        return Carbon::createFromDate($year, 4, 01)->format('Y-m-d');
    }
}

if (! function_exists('getFinancialYearsLists')) {
    function getFinancialYearsLists(): array
    {

        $company = getCurrentCompany();

        $month = Carbon::now()->format('m');

        if ($month > 3) {
            $endYear = date('Y', strtotime('+1 year'));
            $startYear = date('Y');
        } else {
            $endYear = date('Y');
            $startYear = date('Y', strtotime('-1 year'));
        }

        $bookStartDate = $company->companyTax->book_start_date ?? $startYear.'-04-01';
        $bookStartYear = getFinancialYearStart($bookStartDate);

        $financialYears = [];
        $loopCount = $endYear - $bookStartYear ?: 1;
        for ($i = 1; $i <= $loopCount; $i++) {
            $financialYears[($startYear - $i + 1).' - '.($endYear - $i + 1)] = ($startYear - $i + 1).' - '.($endYear - $i + 1);
        }

        return $financialYears;
    }
}

if (! function_exists('getFinancialYearStart')) {
    /**
     * get start year from date
     *
     * @param  string  $date
     * @return string
     */
    function getFinancialYearStart($date)
    {
        $inputDate = new DateTime($date);
        $year = $inputDate->format('Y');
        $startYear = ($inputDate->format('n') >= 4) ? $year : ($year - 1);

        return $startYear;
    }
}

if (! function_exists('getFinancialYearEndDate')) {
    function getFinancialYearEndDate(): string
    {
        $month = \Illuminate\Support\Carbon::now()->format('m');

        if ($month > 3) {
            $year = date('Y', strtotime('+1 year'));
        } else {
            $year = date('Y');
        }

        return Carbon::createFromDate($year, 3, 31)->format('Y-m-d');
    }
}

if (! function_exists('getBankCashGroups')) {
    function getBankCashGroups($type = null)
    {
        //do not use array_merge here, cause it resets numeric indexes instead just use the "+" sign, to merge the array
        if ($type == 'bank') {
            $groupList = getParentGroupsValue(Ledger::BANK);
        } else {
            $groupList = getParentGroupsValue(Ledger::CASH) + getParentGroupsValue(Ledger::BANK);
        }

        return $groupList;
    }
}

if (! function_exists('getCompanyDashboardButton')) {
    function getCompanyDashboardButton()
    {
        static $companyDashboardButton;
        if (empty($companyDashboardButton)) {
            $companyDashboardButton = CompanyDashboardButton::whereCompanyId(getCurrentCompany()->id)
                ->whereNotIn('button_id', [
                    CompanyDashboardButton::TREND_ANALYSIS,
                    CompanyDashboardButton::GSTR_2A,
                    CompanyDashboardButton::GSTR_2B,
                    CompanyDashboardButton::GSTR_3B_VS_1,
                    CompanyDashboardButton::GSTR_9,
                    CompanyDashboardButton::GSTR_9C,
                ])
                ->take(5)->get();
        }

        return $companyDashboardButton;
    }
}

if (! function_exists('getTcsRoundingMethod')) {
    function getTcsRoundingMethod($tcsTaxId)
    {
        if ($tcsTaxId) {
            $taxDetails = Ledger::whereId($tcsTaxId)->with('model')->first();

            return $taxDetails->model->rounding_method ?? Ledger::NO_ROUNDING;
        }

        return Ledger::NO_ROUNDING;
    }
}

if (! function_exists('getTdsRoundingMethod')) {
    function getTdsRoundingMethod($tdsTaxId)
    {
        if ($tdsTaxId) {
            $taxDetails = Ledger::whereId($tdsTaxId)->with('model')->first();

            return $taxDetails->model->rounding_method ?? Ledger::NO_ROUNDING;
        }

        return Ledger::NO_ROUNDING;
    }
}

if (! function_exists('sumOfB2csExcelSheet')) {

    function sumOfB2csExcelSheet($transactionData)
    {
        $sum = [];
        $sum['invoiceAmount'] = 0;
        $sum['invoiceTaxableAmount'] = 0;
        $sum['cess'] = 0;
        $sum['sgst'] = 0;
        $sum['cgst'] = 0;
        $sum['igst'] = 0;
        $sum['totalTaxAmount'] = 0;
        $sum['totalRecord'] = count($transactionData);
        foreach ($transactionData as $data) {
            $sum['invoiceAmount'] += $data['invoice_amount'];
            $sum['invoiceTaxableAmount'] += $data['taxable_value'];
            $sum['cess'] += $data['cess'];
            $sum['sgst'] += $data['sgst'] ?? 0;
            $sum['cgst'] += $data['cgst'] ?? 0;
            $sum['igst'] += $data['igst'] ?? 0;
        }

        $sum['totalTaxAmount'] = $sum['sgst'] + $sum['cgst'] + $sum['igst'];

        return $sum;
    }
}

if (! function_exists('sumOfGstr1ExcelSheet')) {

    function sumOfGstr1ExcelSheet($transactionData)
    {
        $sum = [];
        $sum['invoiceAmount'] = 0;
        $sum['invoiceTaxableAmount'] = 0;
        $sum['cess'] = 0;
        $sum['sgst'] = 0;
        $sum['cgst'] = 0;
        $sum['igst'] = 0;
        $sum['totalTaxAmount'] = 0;
        $sum['totalRecord'] = 0;
        $chekUniqueKey = [];
        foreach ($transactionData as $data) {
            if (! in_array($data['uniqueKey'], $chekUniqueKey)) {
                $chekUniqueKey[] = $data['uniqueKey'];
                $sum['cess'] += $data['cess'];
                $sum['sgst'] += $data['sgst'] ?? 0;
                $sum['cgst'] += $data['cgst'] ?? 0;
                $sum['igst'] += $data['igst'] ?? 0;
                $sum['invoiceAmount'] += $data['invoice_amount'];
                $sum['totalRecord']++;
                if (! empty($data['tr_id'])) {
                    $sale[] = $data['tr_id'];
                }
            }
            if (! empty($data['tr_type'])) {
                $sum[$data['tr_type']] = ! empty($sale) ? implode(',', $sale) : '';
            }
            $sum['invoiceTaxableAmount'] += $data['taxable_value'];
        }

        $sum['totalTaxAmount'] = $sum['sgst'] + $sum['cgst'] + $sum['igst'];

        return $sum;
    }
}

if (! function_exists('getReceiptPdfData')) {

    function getReceiptPdfData($receiptId, $companyId = null)
    {
        $data = [];
        if (! empty($companyId)) {
            $data['currentCompany'] = Company::with('billingAddress', 'companyTax', 'user')->whereId($companyId)->first();
        } else {
            $data['currentCompany'] = Company::with('billingAddress', 'companyTax', 'user')->whereId(getCurrentCompany()->id)->first();
        }
        if (! empty($companyId)) {
            $data['company'] = Company::whereId($companyId)
                ->with(['user.media', 'media', 'companyTax.entityType', 'companyBank', 'addresses'])
                ->first();
        } else {
            $data['company'] = Company::whereId($data['currentCompany']->id)
                ->with(['user.media', 'media', 'companyTax.entityType', 'companyBank', 'addresses'])
                ->first();
        }
        $data['companyBillingAddress'] = $data['company']->addresses
            ->where('address_type', Company::BILLING_ADDRESS)->first();
        $data['companySettings'] = CompanySetting::all()->pluck('value', 'key')->toArray();
        $data['receiptTransactions'] = ReceiptTransaction::whereId($receiptId)->with('bankCashLedger', 'ledgers', 'paymentMode')
            ->first();
        $ledgerId = $data['receiptTransactions']->ledger_id;
        $data['taxInvoice'] = ReceiptTransaction::RECEIPT_TRANSACTION;
        $data['invoiceName'] = ReceiptTransaction::RECEIPT_TRANSACTION;
        if (! empty($companyId)) {
            $data['ledger'] = Ledger::whereId($ledgerId)->whereCompanyId($companyId)
                ->with(['group', 'model'])
                ->first();
        } else {
            $data['ledger'] = Ledger::whereId($ledgerId)->whereCompanyId($data['currentCompany']->id)
                ->with(['group', 'model'])
                ->first();
        }
        $data['receiptTransactionsItems'] = ReceiptTransactionItem::whereRcTransactionId($receiptId)->with([
            'saleBill',
            'incomeDebitBill',
            'paymentTransactionBill',
            'purchaseReturnBill',
            'expenseDebitBill',
        ])
            ->get();

        return $data;
    }
}

if (! function_exists('getPaymentPdfData')) {

    function getPaymentPdfData($paymentId, $companyId = null)
    {
        $data = [];
        $data['currentCompany'] = getCurrentCompany();
        if (! empty($companyId)) {
            $data['company'] = Company::whereId($companyId)
                ->with(['user.media', 'media', 'companyTax.entityType', 'companyBank', 'addresses'])
                ->first();
            $data['currentCompany'] = $data['company'];
        } else {
            $data['company'] = Company::whereId($data['currentCompany']->id)
                ->with(['user.media', 'media', 'companyTax.entityType', 'companyBank', 'addresses'])
                ->first();
        }
        $data['companyBillingAddress'] = $data['company']->addresses
            ->where('address_type', Company::BILLING_ADDRESS)->first();
        $data['companySettings'] = CompanySetting::all()->pluck('value', 'key')->toArray();
        $data['paymentTransactions'] = PaymentTransaction::whereId($paymentId)->with('bankCashLedger', 'ledgers', 'paymentMode')
            ->first();
        $ledgerId = $data['paymentTransactions']->ledger_id;
        $data['taxInvoice'] = PaymentTransaction::PAYMENT_TRANSACTION;
        $data['invoiceName'] = PaymentTransaction::PAYMENT_TRANSACTION;
        if (! empty($companyId)) {
            $data['ledger'] = Ledger::whereId($ledgerId)->whereCompanyId($companyId)
                ->with(['group', 'model'])
                ->first();
        } else {
            $data['ledger'] = Ledger::whereId($ledgerId)->whereCompanyId($data['currentCompany']->id)
                ->with(['group', 'model'])
                ->first();
        }
        $data['paymentTransactionsItems'] = PaymentTransactionItem::wherePcTransactionId($paymentId)->with([
            'purchaseBill',
            'saleReturnBill',
            'expenseCreditBill',
            'incomeCreditBill',
            'receiptTransactionBill',
        ])
            ->get();

        return $data;
    }
}

if (! function_exists('getJournalPdfData')) {

    function getJournalPdfData($journalId)
    {
        $data = [];
        $data['company'] = Company::whereId(getCurrentCompany()->id)
            ->with(['user.media', 'media', 'companyTax.entityType', 'companyBank', 'addresses'])
            ->first();
        $data['companySettings'] = CompanySetting::all()->pluck('value', 'key')->toArray();
        $data['companyBillingAddress'] = $data['company']->addresses
            ->where('address_type', Company::BILLING_ADDRESS)->first();
        $data['journalTransaction'] = JournalTransaction::whereId($journalId)->with('journalDebitCredits.ledger')
            ->first();

        return $data;
    }
}

if (! function_exists('sumOfGstr1hsnExcelSheet')) {
    function sumOfGstr1hsnExcelSheet($transactionData): array
    {
        $sum = [];
        $sum['invoiceAmount'] = 0;
        $sum['invoiceTaxableAmount'] = 0;
        $sum['cess'] = 0;
        $sum['cgst'] = 0;
        $sum['sgst'] = 0;
        $sum['igst'] = 0;
        $sum['round_off'] = 0;

        foreach ($transactionData as $data) {
            $sum['invoiceAmount'] += $data['invoice_amount'];
            $sum['invoiceTaxableAmount'] += $data['taxable_value'];
            $sum['cess'] += $data['cess'];
            $sum['cgst'] += $data['cgst'];
            $sum['sgst'] += $data['sgst'];
            $sum['igst'] += $data['igst'];
            $sum['round_off'] += $data['round_off'];
        }
        $sum['invoiceAmount'] = $sum['cgst'] + $sum['igst'] + $sum['sgst'] + $sum['invoiceTaxableAmount'] + $sum['cess'] + $sum['round_off'];
        $sum['totalTaxAmount'] = $sum['cgst'] + $sum['igst'] + $sum['sgst'];

        return $sum;
    }
}

if (! function_exists('gstr1ExemptReportSum')) {
    function gstr1ExemptReportSum($transaction): array
    {
        $data = [];
        $data['columnB3B4'] = $transaction['interStateNilRatedB_3'] + $transaction['intraStateNilRatedB_4'];
        $data['columnC3C4'] = $transaction['interStateExemptedC_3'] + $transaction['intraStateExemptedC_4'];
        $data['columnD1D2D3D4'] = $transaction['noneGstInterStateB2BD_1'] + $transaction['noneGstIntraStateB2BD_2']
            + $transaction['noneGstInterStateB2CD_3'] + $transaction['noneGstIntraStateB2CD_4'];

        return $data;
    }
}

if (! function_exists('getConsolidatingItemsQty')) {

    function getConsolidatingItemsQty($data)
    {
        $qtyItem = [];
        $totalQtyArr = 0;
        $qtyTotal = explode(',', $data->consolidating_items_to_invoice);
        foreach ($qtyTotal as $arr) {
            $qtyItem[] = getCurrencyFormat($arr);
        }
        $totalQtyArr = implode(',', $qtyItem);

        return array_sum($qtyItem);
    }
}

if (! function_exists('getDebitCreditAmountForTrialBalance')) {

    function getDebitCreditAmountForTrialBalance($group, $field, &$amount = 0)
    {
        if (! empty($group['recursive_children'])) {
            foreach ($group['recursive_children'] as $childGroup) {
                getDebitCreditAmountForTrialBalance($childGroup, $field, $amount);
            }
        }

        $amount += array_sum(array_column($group['ledger'], $field));

        return $amount;
    }
}

if (! function_exists('getOpeningBalanceForTrialBalance')) {
    function getOpeningBalanceForTrialBalance($group, &$data = ['balance' => 0, 'type' => Ledger::DR], $firstTime = true)
    {
        if ($group['name'] == Ledger::STOCK_IN_HAND) {
            $data['balance'] = $group['opening_bal'];
            $data['type'] = $group['opening_bal_type'];

            return $data;
        }

        foreach ($group['ledger'] as $ledger) {
            if ($firstTime) {
                $data['balance'] += $ledger['opening_bal'];
                $data['type'] = $ledger['opening_bal_type'];
                $firstTime = false;
            } else {
                if ($data['type'] == Ledger::CR && $ledger['opening_bal_type'] == Ledger::CR) {
                    $data['balance'] += $ledger['opening_bal'];
                } elseif ($data['type'] == Ledger::CR && $ledger['opening_bal_type'] == Ledger::DR) {
                    if ($data['balance'] <= $ledger['opening_bal']) {
                        $data['type'] = Ledger::DR;
                    }
                    if ($data['balance'] == 0) {
                        $data['balance'] += $ledger['opening_bal'];
                    } else {
                        $data['balance'] -= $ledger['opening_bal'];
                    }
                } elseif ($data['type'] == Ledger::DR && $ledger['opening_bal_type'] == Ledger::DR) {
                    $data['balance'] += $ledger['opening_bal'];
                } else {
                    if ($data['balance'] <= $ledger['opening_bal']) {
                        $data['type'] = Ledger::CR;
                    }
                    if ($data['balance'] == 0) {
                        $data['balance'] += $ledger['opening_bal'];
                    } else {
                        $data['balance'] -= $ledger['opening_bal'];
                    }
                }
            }
            $data['balance'] = abs($data['balance']);
        }

        if (! empty($group['recursive_children'])) {
            foreach ($group['recursive_children'] as $childGroup) {
                getOpeningBalanceForTrialBalance($childGroup, $data, $firstTime);
            }
        }

        return $data;
    }
}

if (! function_exists('getClosingBalanceForTrialBalance')) {
    function getClosingBalanceForTrialBalance($group, &$data = ['balance' => 0, 'type' => Ledger::DR], $firstTime = true)
    {
        if ($group['name'] == Ledger::STOCK_IN_HAND) {
            $data['balance'] = $group['closing_bal'];
            $data['type'] = $group['closing_bal_type'];

            return $data;
        }

        foreach ($group['ledger'] as $ledger) {
            if ($firstTime) {
                $data['balance'] = $ledger['closing_bal'] ?? 0;
                $data['type'] = $ledger['closing_bal_type'] ?? Ledger::DR;
                $firstTime = false;
            } else {
                if ($data['type'] == Ledger::CR && $ledger['closing_bal_type'] == Ledger::CR) {
                    $data['balance'] += $ledger['closing_bal'];
                } elseif ($data['type'] == Ledger::CR && $ledger['closing_bal_type'] == Ledger::DR) {
                    if ($data['balance'] <= $ledger['closing_bal']) {
                        $data['type'] = Ledger::DR;
                    }
                    if ($data['balance'] == 0) {
                        $data['balance'] += $ledger['closing_bal'];
                    } else {
                        $data['balance'] -= $ledger['closing_bal'];
                    }
                } elseif ($data['type'] == Ledger::DR && $ledger['closing_bal_type'] == Ledger::DR) {
                    $data['balance'] += $ledger['closing_bal'];
                } else {
                    if ($data['balance'] <= $ledger['closing_bal']) {
                        $data['type'] = Ledger::CR;
                    }
                    if ($data['balance'] == 0) {
                        $data['balance'] += $ledger['closing_bal'];
                    } else {
                        $data['balance'] -= $ledger['closing_bal'];
                    }
                }
            }

            $data['balance'] = abs($data['balance']);
        }

        if (! empty($group['recursive_children'])) {
            foreach ($group['recursive_children'] as $childGroup) {
                getClosingBalanceForTrialBalance($childGroup, $data, false);
            }
        }

        return $data;
    }
}

if (! function_exists('getDebitCreditForTrialBalance')) {
    function getDebitCreditForTrialBalance($group, &$data = ['credit' => 0, 'debit' => 0])
    {
        if (! empty($group['recursive_children'])) {
            foreach ($group['recursive_children'] as $childGroup) {
                getDebitCreditForTrialBalance($childGroup, $data);
            }
        }
        foreach ($group['ledger'] as $ledger) {
            $data['debit'] += $ledger['debit_amount'];
            $data['credit'] += $ledger['credit_amount'];
        }

        return $data;
    }
}

if (! function_exists('getGrandTotalOpeningBalanceForTrialBalance')) {
    function getGrandTotalOpeningBalanceForTrialBalance($groups): array
    {
        $balance = 0;
        $type = Ledger::DR;
        $firstTime = true;
        foreach ($groups as $group) {
            $data = getOpeningBalanceForTrialBalance($group);
            if ($firstTime) {
                $balance = $data['balance'];
                $type = $data['type'];
                $firstTime = false;
            } else {
                if ($type == Ledger::CR && $data['type'] == Ledger::CR) {
                    $balance += $data['balance'];
                } elseif ($type == Ledger::CR && $data['type'] == Ledger::DR) {
                    if ($balance <= $data['balance']) {
                        $type = Ledger::DR;
                    }
                    if ($balance == 0) {
                        $balance += $data['balance'];
                    } else {
                        $balance -= $data['balance'];
                    }
                } elseif ($type == Ledger::DR && $data['type'] == Ledger::DR) {
                    $balance += $data['balance'];
                } else {
                    if ($balance <= $data['balance']) {
                        $type = Ledger::CR;
                    }
                    if ($balance == 0) {
                        $balance += $data['balance'];
                    } else {
                        $balance -= $data['balance'];
                    }
                }
            }
            $balance = abs($balance);
        }

        return ['balance' => abs($balance), 'type' => $type];
    }
}

if (! function_exists('getGrandTotalClosingBalanceForTrialBalance')) {
    function getGrandTotalClosingBalanceForTrialBalance($groups): array
    {
        $balance = 0;
        $type = Ledger::DR;
        $firstTime = true;
        foreach ($groups as $group) {
            $data = getClosingBalanceForTrialBalance($group);
            if ($firstTime) {
                $balance = $data['balance'];
                $type = $data['type'];
                $firstTime = false;
            } else {
                if ($type == Ledger::CR && $data['type'] == Ledger::CR) {
                    $balance += $data['balance'];
                } elseif ($type == Ledger::CR && $data['type'] == Ledger::DR) {
                    if ($balance <= $data['balance']) {
                        $type = Ledger::DR;
                    }
                    if ($balance == 0) {
                        $balance += $data['balance'];
                    } else {
                        $balance -= $data['balance'];
                    }
                } elseif ($type == Ledger::DR && $data['type'] == Ledger::DR) {
                    $balance += $data['balance'];
                } else {
                    if ($balance <= $data['balance']) {
                        $type = Ledger::CR;
                    }
                    if ($balance == 0) {
                        $balance += $data['balance'];
                    } else {
                        $balance -= $data['balance'];
                    }
                }
            }
            $balance = abs($balance);
        }

        return ['balance' => abs($balance), 'type' => $type];
    }
}

if (! function_exists('getGrandTotalDebitCreditForTrialBalance')) {
    function getGrandTotalDebitCreditForTrialBalance($groups): array
    {
        $credit = 0;
        $debit = 0;
        foreach ($groups as $group) {
            $data = getDebitCreditForTrialBalance($group);
            $credit += $data['credit'];
            $debit += $data['debit'];
        }

        return ['credit' => $credit, 'debit' => $debit];
    }
}

if (! function_exists('getDebitCreditClosingBalanceTrialBalance')) {
    function getDebitCreditClosingBalanceTrialBalance($groups, &$data = ['debit' => 0, 'credit' => 0]): array
    {
        foreach ($groups as $group) {
            if (! empty($group['recursive_children'])) {
                getDebitCreditClosingBalanceTrialBalance($group['recursive_children'], $data);
            }

            foreach ($group['ledger'] as $ledger) {
                if ($ledger['closing_bal_type'] == Ledger::CR) {
                    $data['credit'] += $ledger['closing_bal'];
                } else {
                    $data['debit'] += $ledger['closing_bal'];
                }
            }
            /* Comment for Stock In Hand Closing Balance Not calculated in Trial Balance Report */
            // if ($group['name'] == Ledger::STOCK_IN_HAND) {
            //     if ($group['closing_bal_type'] == Ledger::CR) {
            //         $data['credit'] += $group['closing_bal'];
            //     } else {
            //         $data['debit'] += $group['closing_bal'];
            //     }
            // }
        }

        return $data;
    }
}

if (! function_exists('getValueOfIsCgstSgstIgstCalculated')) {
    /**
     * @return int
     */
    function getValueOfIsCgstSgstIgstCalculated($classificationType, $isRcmApplicable)
    {
        $isCgstSgstIgstCalculated = false;
        if (in_array($classificationType, intraStateArray())) {
            $isCgstSgstIgstCalculated = $isRcmApplicable != 1;
        }
        if (in_array($classificationType, interStateArray())) {
            $isCgstSgstIgstCalculated = $isRcmApplicable != 1;
        }
        if (in_array($classificationType, exportArray()) || in_array($classificationType, sezArray())) {
            $isCgstSgstIgstCalculated = true;
        }
        if ($classificationType == 'Deemed Export - Intrastate') {
            $isCgstSgstIgstCalculated = true;
        }
        if ($classificationType == 'Deemed Export - Interstate') {
            $isCgstSgstIgstCalculated = true;
        }

        if (in_array($classificationType, purchaseIntraStateTaxArray())) {
            $isCgstSgstIgstCalculated = $isRcmApplicable != 1;
        }
        if (in_array($classificationType, purchaseInterstateTaxArray())) {
            $isCgstSgstIgstCalculated = $isRcmApplicable != 1;
        }

        return $isCgstSgstIgstCalculated ? 1 : 0;
    }
}

if (! function_exists('intraStateArray')) {
    function intraStateArray(): array
    {
        return [
            'Sale Intra State Taxable',
            'Sale Intra State Exempt',
            'Sale Intra State Nilrated',
            'Intrastate Sales Taxable',
            'Deemed Export - Intrastate',
        ];
    }
}

if (! function_exists('interStateArray')) {
    function interStateArray(): array
    {
        return [
            'Sale Inter State Taxable',
            'Sale Inter State Exempt',
            'Sale Inter State Nilrated',
            'Interstate Sales Taxable',
        ];
    }
}

if (! function_exists('exportArray')) {
    function exportArray(): array
    {
        return [
            'Export Taxable',
            'Export Exempt',
            'Export Nilrated',
            'Export LUT/Bond',
            'Export Sales Taxable',
        ];
    }
}

if (! function_exists('sezArray')) {
    function sezArray(): array
    {
        return [
            'Sales to SEZ - Taxable',
            'Sales to SEZ - Exempt',
            'Sales to SEZ - Nilrated',
            'Sales to SEZ - LUT/Bond',
            'Sales to SEZ Taxable',
        ];
    }
}

if (! function_exists('purchaseIntraStateTaxArray')) {
    function purchaseIntraStateTaxArray(): array
    {
        return [
            'Intrastate Purchase Taxable',
            'Intrastate Purchase URD Taxable',
        ];
    }
}

if (! function_exists('purchaseInterstateTaxArray')) {
    function purchaseInterstateTaxArray(): array
    {
        return [
            'Interstate Purchase Taxable',
            'Interstate Purchase URD Taxable',
            'Purchase - Import of goods',
            'Purchase - Import of Service',
        ];
    }
}

if (! function_exists('getCompanyFixedDigitNumber')) {

    function getCompanyFixedDigitNumber()
    {
        static $digitsNumberMethod;

        if (empty($digitsNumberMethod)) {
            $company = getCurrentCompany();

            if (! $company) {
                return CompanySetting::FIXED_DEFAULT_METHOD;
            }

            $digitsNumberMethod = getCompanySettings()['fixed_digits'] ?? CompanySetting::FIXED_DEFAULT_METHOD;
        }

        return $digitsNumberMethod;
    }
}

if (! function_exists('getFixDigitFrontValue')) {

    function getFixDigitFrontValue()
    {
        $digitsNumber = getCompanyFixedDigitNumber();
        if ($digitsNumber == 2) {
            return 0.01;
        }
        if ($digitsNumber == 3) {
            return 0.001;
        }
        if ($digitsNumber == 4) {
            return 0.0001;
        }

        return 0.01;
    }
}

if (! function_exists('sumOfOutputTaxRegisteSummeryTotalAmount')) {

    function sumOfOutputTaxRegisteSummeryTotalAmount($summeryData)
    {
        $data['invoiceAmountTotal'] = 0;
        $data['taxableAmountTotal'] = 0;
        $data['cgstTaxTotal'] = 0;
        $data['cessTaxTotal'] = 0;
        $data['sgstTaxTotal'] = 0;
        $data['igstTaxTotal'] = 0;

        foreach ($summeryData as $item) {
            $data['taxableAmountTotal'] += $item['taxable_value'];
            $data['cgstTaxTotal'] += $item['cgst'];
            $data['sgstTaxTotal'] += $item['sgst'];
            $data['igstTaxTotal'] += $item['igst'];
            $data['cessTaxTotal'] += $item['cess_amount'];
            $data['invoiceAmountTotal'] += $item['invoice_amount'];
        }

        return $data;
    }
}

if (! function_exists('generateCacheKey')) {
    /**
      @param $name
      @return string
     */
    function generateCacheKey($name): string
    {

        $company = getCurrentCompany();

        return $company->id.'__'.$name;
    }
}

/*Searching Function START*/
if (! function_exists('match_string')) {
    /**
     * @return false|int
     */
    function match_string($search, $subject)
    {
        $search = str_replace('/', '\\/', $search);

        try {
            return preg_match("/$search/i", (string) $subject);
        } catch (Exception $e) {
            return false;
        }
    }
}

if (! function_exists('like_search_r')) {

    function like_search_r($array, $key, $value, array &$results = [])
    {
        if (! is_array($array)) {
            return;
        }

        $key = (string) $key;
        $value = (string) $value;

        foreach ($array as $arrayKey => $arrayValue) {
            if (match_string($key, $arrayKey) && match_string($value, $arrayValue)) {
                // add array if we have a match
                $results[] = $array;
            }

            if (is_array($arrayValue)) {
                // only do recursion on arrays
                like_search_r($arrayValue, $key, $value, $results);
            }
        }
    }
}

if (! function_exists('like_search')) {

    function like_search(&$array, $key, $value)
    {
        if (! is_array($array)) {
            return;
        }

        $key = (string) $key;
        $value = (string) $value;

        foreach ($array as $arrayKey => $arrayValue) {
            foreach ($arrayValue as $k => $v) {
                if ($key == $k && ! match_string($value, $v)) {
                    unset($array[$arrayKey]);
                }
            }
        }
    }
}

if (! function_exists('custom_search')) {

    function custom_search(&$array, $key, $value)
    {
        if (! is_array($array)) {
            return [];
        }

        $array = collect($array)->filter(function ($item) use ($key, $value) {
            return isset($item[$key]) && (
                stripos($item[$key], $value) !== false || $item[$key] == $value
            );
        })->values()->toArray();
    }
}

if (! function_exists('gte_search')) {

    function gte_search(&$array, $key, $value)
    {
        if (! is_array($array)) {
            return;
        }

        $array = collect($array)->where($key, '>=', $value)->toArray();
    }
}

if (! function_exists('gt_search')) {

    function gt_search(&$array, $key, $value)
    {
        if (! is_array($array)) {
            return;
        }

        $array = collect($array)->where($key, '>', $value)->toArray();
    }
}

if (! function_exists('lte_search')) {

    function lte_search(&$array, $key, $value)
    {
        if (! is_array($array)) {
            return;
        }

        $array = collect($array)->where($key, '<=', $value)->toArray();
    }
}

if (! function_exists('lt_search')) {

    function lt_search(&$array, $key, $value)
    {
        if (! is_array($array)) {
            return;
        }

        $array = collect($array)->where($key, '<', $value)->toArray();
    }
}

if (! function_exists('equal_search')) {
    function equal_search(&$array, $key, $value)
    {
        if (! is_array($array)) {
            return;
        }

        $array = collect($array)->where($key, '=', $value)->toArray();
    }
}

if (! function_exists('search')) {
    function search(&$array, $key, $value)
    {
        if (! is_array($array)) {
            return;
        }

        $key = (string) $key;
        $value = (string) $value;

        foreach ($array as $arrayKey => $arrayValue) {
            foreach ($arrayValue as $k => $v) {
                if ($key == $k && $value != $v) {
                    unset($array[$arrayKey]);
                }
            }
        }
    }
}
/*Searching Function END*/

if (! function_exists('getCurrentFranchiseCompany')) {

    function getCurrentFranchiseCompany()
    {
        return Company::where('franchise_id', getFranchiseId())->pluck('trade_name', 'id')->toArray();
    }
}

if (! function_exists('getTimeDifferentSchedule')) {

    function getTimeDifferentSchedule($from, $to)
    {
        $startTime = new Carbon($from);
        $endTime = new Carbon($to);
        $diffHour = $startTime->diffInHours($endTime);
        //        $diffMin = $endTime->diffInMinutes($startTime);
        $startTimeFormat = Carbon::parse($from)->format('H:i');
        $endTimeFormat = Carbon::parse($to)->format('H:i');
        $startExplode = explode(':', $startTimeFormat);
        $endExplode = explode(':', $endTimeFormat);
        if ($diffHour < 10) {
            $diffHour = '0'.$diffHour;
        }
        $diffMin = $endExplode[1] - $startExplode[1];
        $diffMin = abs($diffMin);
        if ($diffMin < 10) {
            $diffMin = '0'.$diffMin;
        }

        return $diffHour.':'.$diffMin;
    }
}

if (! function_exists('checkCompanyAddressIsEmpty')) {
    function checkCompanyAddressIsEmpty($address): bool
    {

        return is_null($address) || is_null($address->address_1 ?? '') ||
            is_null($address->address_2 ?? '') || is_null($address->country_id ?? '') || is_null($address->state_id ?? '') ||
            is_null($address->city_id ?? '') || is_null($address->pin_code ?? '');
    }
}

if (! function_exists('getGstEnabledFields')) {
    function getGstEnabledFields(): array
    {
        return [
            'cgst',
            'sgst',
            'gstin',
            'igst',
            'cess',
            'selling_price_with_gst',
            'shipping_gstin',
            'gst_applicable',
            'gst_cess_rate',
            'gst_rate',
            'hsn_code',
            'purchase_price_with_gst',
            'rcm_applicable',
            'eway_details',
            'einvoice_details',
        ];
    }
}

if (! function_exists('getSadminSettingValue')) {

    function getSadminSettingValue($key)
    {
        return SadminSetting::toBase()->where('key', $key)->value('value');
    }
}

if (! function_exists('getSadminSettings')) {

    function getSadminSettings(): array
    {
        return SadminSetting::all()->pluck('value', 'key')->toArray();
    }
}

if (! function_exists('getSadminMailSetting')) {

    function getSadminMailSetting(): array
    {
        $settings = getSadminSettings();

        if (! count($settings)) {
            return [];
        }

        return [
            'driver' => $settings['mail_protocol'] ?? null,
            'host' => $settings['mail_host'] ?? null,
            'port' => $settings['mail_port'] ?? null,
            'username' => $settings['mail_username'] ?? null,
            'password' => $settings['mail_password'] ?? null,
            'encryption' => $settings['mail_encryption'] ?? null,
            'from' => [
                'address' => $settings['mail_from_address'] ?? null,
                'name' => $settings['mail_from_name'] ?? null,
            ],
        ];
    }
}

if (! function_exists('getAddress')) {
    function getAddress($row, $addressType): string
    {
        /** @var Address $address */
        $address = $row->addresses->where('address_type', $addressType)->first();
        $addressData = '';
        if (! empty($address)) {
            $addressData = $address->address_1.', '.$address->address_2.', '.($address->city->name ?? '').', '.
                ($address->state->name ?? '').', '.($address->country->name ?? '').', '.$address->pin_code;
        }

        return $addressData;
    }
}

if (! function_exists('sendInvoiceToWhatsapp')) {
    /**
     * @return string
     */
    function sendInvoiceToWhatsapp($data, $viewPDFInvoiceRoute, $input)
    {
        $companyDetail = getCurrentCompany();
        $customerDetail = $data->customer;
        $dueDate = Carbon::parse($data->date)->format('d-m-Y');

        if (! empty($data->credit_period)) {
            if ($data->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_MONTH) {
                $dueDate = Carbon::parse($data->date)->addMonths($data->credit_period)->format('d-m-Y');
            } elseif ($data->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_DAY) {
                $dueDate = Carbon::parse($data->date)->addDays($data->credit_period)->format('d-m-Y');
            }
        } elseif (! empty($customerDetail->model->credit_limit_period)) {
            if ($customerDetail->model->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_MONTH) {
                $dueDate = Carbon::parse($data->date)->addMonths($customerDetail->model->credit_limit_period)->format('d-m-Y');
            } elseif ($customerDetail->model->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_DAY) {
                $dueDate = Carbon::parse($data->date)->addDays($customerDetail->model->credit_limit_period)->format('d-m-Y');
            }
        }

        $whatsappMessage = 'Hello *'.urlencode($customerDetail->name).'*, Thank you for doing business with *'.urlencode($companyDetail->trade_name).'*%0a%0aPlease find details of the transaction done with us%0a%0aInvoice number: '.urlencode($data->full_invoice_number).'%0aDate: '.Carbon::parse($data->date)->format('d-m-Y').'%0aInvoice Value: '.getCurrencySymbol().getCurrencyFormat($data->grand_total).'%0aDue Date of Payment: '.$dueDate.'%0a%0aYou can view the invoice PDF here:- '.$viewPDFInvoiceRoute;
        $customerPhoneNumber = $input['phone'] ?? $customerDetail->model->phone_1 ?? $customerDetail->model->phone_2;

        return 'https://api.whatsapp.com/send?phone=+91'.$customerPhoneNumber.'&text='.$whatsappMessage.'';
    }
}

if (! function_exists('sendInvoiceToWhatsappForEstimate')) {
    /**
     * @return string
     */
    function sendInvoiceToWhatsappForEstimate($data, $viewPDFInvoiceRoute, $input)
    {
        $companyDetail = getCurrentCompany();
        $partyDetail = $data->party;
        $dueDate = Carbon::parse($data->date)->format('d-m-Y');

        if (! empty($data->credit_period)) {
            if ($data->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_MONTH) {
                $dueDate = Carbon::parse($data->date)->addMonths($data->credit_period)->format('d-m-Y');
            } elseif ($data->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_DAY) {
                $dueDate = Carbon::parse($data->date)->addDays($data->credit_period)->format('d-m-Y');
            }
        } elseif (! empty($partyDetail->model->credit_limit_period)) {
            if ($partyDetail->model->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_MONTH) {
                $dueDate = Carbon::parse($data->date)->addMonths($partyDetail->model->credit_limit_period)->format('d-m-Y');
            } elseif ($partyDetail->model->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_DAY) {
                $dueDate = Carbon::parse($data->date)->addDays($partyDetail->model->credit_limit_period)->format('d-m-Y');
            }
        }

        $whatsappMessage = 'Hello *'.urlencode($partyDetail->name).'*, Thank you for considering us for your requirement. *'.urlencode($companyDetail->trade_name).'*%0a%0aPlease find details of the transaction done with us%0a%0aInvoice number: '.urlencode($data->document_number).'%0aDate: '.Carbon::parse($data->document_date)->format('d-m-Y').'%0aInvoice Value: '.getCurrencySymbol().getCurrencyFormat($data->grand_total).'%0aDue Date of Payment: '.$dueDate.'%0a%0aYou can view the invoice PDF here:- '.$viewPDFInvoiceRoute;
        $customerPhoneNumber = $input['phone'] ?? $partyDetail->model->phone_1 ?? $partyDetail->model->phone_2;

        return 'https://api.whatsapp.com/send?phone=+91'.$customerPhoneNumber.'&text='.$whatsappMessage.'';
    }
}

if (! function_exists('sendReceiptToWhatsapp')) {
    function sendReceiptToWhatsapp($data, $viewPDFInvoiceRoute, $input)
    {
        $companyDetail = getCurrentCompany();
        $ledgerDetail = $data->ledgers;
        $customerDetail = $data->ledgers->model;

        $whatsappMessage = 'Hello *'.urlencode($ledgerDetail->name).'*, %0a%0aWe would like to extend our sincere thanks for your payment to *'.urlencode($companyDetail->trade_name).'*. Your business is greatly appreciated. %0a%0aHere the receipt for your recent transaction:%0a%0aReceipt Number: '.urlencode($data->receipt_number).'%0aTransaction Date: '.Carbon::parse($data->date)->format('d-m-Y').'%0aTransaction Amount: '.getCurrencySymbol().getCurrencyFormat($data->total_received_amount).'%0a%0aYou can view and download the receipt in PDF format by clicking on the following link:- '.$viewPDFInvoiceRoute;
        $customerPhoneNumber = $input['phone'] ?? $customerDetail->phone_1 ?? $customerDetail->phone_2;

        return 'https://api.whatsapp.com/send?phone=+91'.$customerPhoneNumber.'&text='.$whatsappMessage.'';
    }
}

if (! function_exists('sendPaymentToWhatsapp')) {
    function sendPaymentToWhatsapp($data, $viewPDFInvoiceRoute, $input)
    {
        $ledgerDetail = $data->ledgers;
        $customerDetail = $data->ledgers->model;

        $whatsappMessage = 'Hello *'.urlencode($ledgerDetail->name).'*, %0a%0aWe We have made payment to you.%0a%0aYour business is greatly appreciated. %0a%0aHere the payment advice of our recent transaction:%0a%0aPayment Voucher Number: '.urlencode($data->payment_voucher_number).'%0aTransaction Date: '.Carbon::parse($data->date)->format('d-m-Y').'%0aTransaction Amount: '.getCurrencySymbol().getCurrencyFormat($data->total_paid_amount).'%0a%0aYou can view and download the receipt in PDF format by clicking on the following link:- '.$viewPDFInvoiceRoute;
        $customerPhoneNumber = $input['phone'] ?? $customerDetail->phone_1 ?? $customerDetail->phone_2;

        return 'https://api.whatsapp.com/send?phone=+91'.$customerPhoneNumber.'&text='.$whatsappMessage.'';
    }
}

if (! function_exists('sendCustomerSummaryReportToWhatsapp')) {
    function sendCustomerSummaryReportToWhatsapp($data, $viewPDFInvoiceRoute, $input)
    {
        $companyDetail = getCurrentCompany();
        $ledgerDetail = $data;
        $customerDetail = $ledgerDetail->model;

        $whatsappMessage = 'Hello *'.urlencode($ledgerDetail->name).'*, %0a%0aThank you for doing business with *'.urlencode($companyDetail->trade_name).'*.%0a%0aPlease find your attached Ledger:- '.$viewPDFInvoiceRoute;
        $customerPhoneNumber = $input['phone'] ?? $customerDetail->phone_1 ?? $customerDetail->phone_2;

        return 'https://api.whatsapp.com/send?phone=+91'.$customerPhoneNumber.'&text='.$whatsappMessage.'';
    }
}

if (! function_exists('sendSupplierSummaryReportToWhatsapp')) {
    function sendSupplierSummaryReportToWhatsapp($data, $viewPDFInvoiceRoute, $input)
    {
        $companyDetail = getCurrentCompany();
        $ledgerDetail = $data;
        $supplierDetail = $ledgerDetail->model;

        $whatsappMessage = 'Hello *'.urlencode($ledgerDetail->name).'*, %0a%0aThank you for doing business with *'.urlencode($companyDetail->trade_name).'*.%0a%0aPlease find your attached Ledger:- '.$viewPDFInvoiceRoute;
        $supplierPhoneNumber = $input['phone'] ?? $supplierDetail->phone_1 ?? $supplierDetail->phone_2;

        return 'https://api.whatsapp.com/send?phone=+91'.$supplierPhoneNumber.'&text='.$whatsappMessage.'';
    }
}

if (! function_exists('getRazorpayCredentials')) {

    /**
     * @return CompanyRazorpayCredential|Builder|Model|object|null
     */
    function getRazorpayCredentials($companyId)
    {
        return CompanyRazorpayCredential::whereCompanyId($companyId)->first();
    }
}

if (! function_exists('setCompanyInSession')) {
    function setCompanyInSession($company): void
    {
        Session::put('company', $company);
    }
}

if (! function_exists('forgetCompanySession')) {
    function forgetCompanySession(): void
    {
        if (Session::exists('company')) {
            Session::forget('company');
        }
    }
}

if (! function_exists('getCompanyFilters')) {
    /**
     * @return HigherOrderBuilderProxyAlias|string|User
     */
    function getCompanyFilters($companyId = null)
    {
        /** @var CompanyFilter $companyFilter */
        static $companyFilter;

        $currentCompanyId = getCurrentCompany()?->id;
        if ($companyFilter === null && ($companyId || $currentCompanyId)) {
            $companyId = $companyId ?? $currentCompanyId;
            $companyFilter = CompanyFilter::whereCompanyId($companyId)->first()->filter_meta ?? '';
        }

        return $companyFilter;
    }
}

if (! function_exists('getCompanyFiltersString')) {
    /**
     * @return string
     */
    function getCompanyFiltersString()
    {
        $companyFilterData = getCompanyFilters() ?? '';
        if ($companyFilterData != '') {
            return json_encode($companyFilterData);
        }

        return $companyFilterData;
    }
}

if (! function_exists('getInvoiceSeriesCountArr')) {

    function getInvoiceSeriesCountArr($invoiceNoArray)
    {
        $invoiceNumberArr = [];
        foreach ($invoiceNoArray as $key => $value) {
            $prefix = preg_replace('/[0-9]+$/', '', $value);
            $number = preg_replace('/[^0-9]+/', '', $value);

            if (! array_key_exists($prefix, $invoiceNumberArr)) {
                $invoiceNumberArr[$prefix] = [];
            }

            $invoiceNumberArr[$prefix][] = [
                'original' => $value,
                'number' => (int) $number,
            ];
        }

        foreach ($invoiceNumberArr as $prefix => $invoices) {
            usort($invoices, function ($a, $b) {
                return $a['number'] - $b['number'];
            });

            $invoiceNumberArr[$prefix] = array_map(function ($invoice) {
                return $invoice['original'];
            }, $invoices);
        }

        return $invoiceNumberArr;
    }
}

if (! function_exists('getMissingInvoiceSeries')) {
    function getMissingInvoiceSeries($data)
    {
        $numbers = [];
        foreach ($data as $invoice) {
            preg_match('/\d+/', $invoice, $matches);
            if (! empty($matches)) {
                $numbers[] = (int) $matches[0];
            }
        }

        if (empty($numbers)) {
            return 0;
        }

        $minInvoice = min($numbers);
        $maxInvoice = max($numbers);

        $presence = array_fill($minInvoice, $maxInvoice - $minInvoice + 1, false);

        foreach ($numbers as $number) {
            $presence[$number] = true;
        }

        $missingNumbers = [];
        for ($i = $minInvoice; $i <= $maxInvoice; $i++) {
            if (! $presence[$i]) {
                $missingNumbers[] = $i;
            }
        }

        return count($missingNumbers);
    }
}

if (! function_exists('genNextInvNo')) {
    function genNextInvNo($paymentVoucherNumber)
    {

        preg_match_all('/\d+/', $paymentVoucherNumber, $matches, PREG_OFFSET_CAPTURE);
        if (count($matches[0])) {
            $match = $matches[0][count($matches[0]) - 1];
            $currentNumberLen = strlen($match[0]);
            $currentNumber = intval($match[0]);
            $nextNumber = $currentNumber + 1;
            $offset = $match[1];

            return substr($paymentVoucherNumber, 0, $offset).strval($nextNumber).substr($paymentVoucherNumber, $offset + $currentNumberLen);
        }
    }
}

if (! function_exists('getInvoiceType')) {

    function getInvoiceType($classificationNatureType): string
    {
        if (in_array($classificationNatureType, [
            'Intrastate Sales Taxable',
            'Intrastate Sales Exempt',
            'Intrastate Sales Nil Rated',
            'Interstate Sales Taxable',
            'Interstate Sales Exempt',
            'Interstate Sales Nil Rated',
        ])) {
            return 'Regular B2B';
        }

        if (in_array($classificationNatureType, [
            'Sales to SEZ Taxable',
            'Sales to SEZ Exempt',
            'Sales to SEZ Nil Rated',
            'Sales to SEZ under LUT/Bond',
        ])) {
            return 'SEZ supplies without payment';
        }

        if (in_array($classificationNatureType, ['Sales to SEZ Taxable'])) {
            return 'SEZ supplies with payment';
        }

        if (in_array($classificationNatureType, ['Deemed Export - Intrastate', 'Deemed Export - Interstate'])) {
            return 'Deemed Exp';
        }

        return '';
    }
}

if (! function_exists('getExportType')) {

    function getExportType($classificationNatureType): string
    {
        if (in_array($classificationNatureType, ['Export Sales Taxable'])) {
            return 'WPAY';
        }

        return 'WOPAY';
    }
}

if (! function_exists('getCompanyProfileDetails')) {

    /**
     * @return mixed
     */
    function getCompanyProfileDetails($attribute)
    {
        return Company::whereId(getCurrentCompany()->id)->value($attribute);
    }
}

if (! function_exists('checkGroupIsExists')) {

    /**
     * @return mixed
     */
    function checkGroupIsExists($groupId)
    {
        $recordExists = false;
        // company_group

        if (CompanyGroup::whereParentId($groupId)->exists()) {
            return true;
        }
        // item_masters
        if (ItemMaster::whereGroupId($groupId)->exists()) {
            return true;
        }
        // ledgers
        if (Ledger::where(function ($q) use ($groupId) {
            $q->where('group_id', $groupId)
                ->orWhere('parent_id', $groupId);
        })->exists()) {
            return true;
        }
        // Recurring Invoice
        if (RecurringInvoice::with('partyDetails')->whereHas('partyDetails', function ($q) use ($groupId) {
            $q->where('model_id', $groupId)
                ->where('model_type', CompanyGroup::class);
        })->exists()) {
            return true;
        }

        return $recordExists;
    }
}
if (! function_exists('checkBrokerIsExists')) {

    /**
     * @return mixed
     */
    function checkBrokerIsExists($brokerId)
    {
        $recordExists = false;

        // expense_credit_note_transaction
        if (ExpenseCreditNoteTransaction::whereBrokerId($brokerId)->exists()) {
            return true;
        }

        // expense_debit_note_transactions
        if (ExpenseDebitNoteTransaction::whereBrokerId($brokerId)->exists()) {
            return true;
        }

        // income_credit_note_transactions
        if (IncomeCreditNoteTransaction::whereBrokerId($brokerId)->exists()) {
            return true;
        }

        // income_debit_note_transactions
        if (IncomeDebitNoteTransaction::whereBrokerId($brokerId)->exists()) {
            return true;
        }

        // master_customers
        if (Customer::whereBrokerMaster($brokerId)->exists()) {
            return true;
        }

        // master_suppliers
        if (Supplier::whereBrokerMaster($brokerId)->exists()) {
            return true;
        }

        // purchase_return_transactions
        if (PurchaseReturnTransaction::whereBrokerId($brokerId)->exists()) {
            return true;
        }

        // purchase_transactions
        if (PurchaseTransaction::whereBrokerId($brokerId)->exists()) {
            return true;
        }

        // purchase_order_transactions
        if (PurchaseOrderTransaction::whereBrokerId($brokerId)->exists()) {
            return true;
        }

        // sales_transactions
        if (SaleTransaction::whereBrokerId($brokerId)->exists()) {
            return true;
        }

        // recurring_invoice
        if (RecurringInvoice::whereBrokerId($brokerId)->exists()) {
            return true;
        }

        // sale_return_transactions
        if (SaleReturnTransaction::whereBrokerId($brokerId)->exists()) {
            return true;
        }

        // estimate_quote_transactions
        if (IncomeEstimateQuoteTransaction::whereBrokerId($brokerId)->exists()) {
            return true;
        }

        // Delivery Challan Transaction
        if (DeliveryChallanTransaction::whereBrokerId($brokerId)->exists()) {
            return true;
        }

        if (VastraDeliveryChallan::where('broker_id', $brokerId)->exists()) {
            return true;
        }

        return $recordExists;
    }
}

if (! function_exists('getDefaultDate')) {
    /**
     * @return mixed
     */
    function getDefaultDate()
    {
        $getCurrentFinancialYearDetails = getCurrentFinancialYearDetails();
        $endDate = $getCurrentFinancialYearDetails['yearEndDate'];

        return ! Carbon::now()->gt($endDate) ? Carbon::now() : Carbon::parse($endDate);
    }
}

if (! function_exists('getCurrentFinancialStartDate')) {
    /**
     * @return string
     */
    function getCurrentFinancialStartDate()
    {
        $getCurrentFinancialYearDetails = getCurrentFinancialYearDetails();
        $startYear = $getCurrentFinancialYearDetails['startYear'];

        return $startYear.'-04-01';
    }
}
if (! function_exists('getCurrentFinancialYearDetails')) {

    /**
     * @return mixed
     */
    function getCurrentFinancialYearDetails($companyId = null)
    {
        static $getCurrentFinancialYearData;

        if (empty($getCurrentFinancialYearData)) {
            $companyFilter = getCompanyFilters($companyId);
            $financialYear = $companyFilter['current_financial_year'] ??
                Carbon::parse(getFinancialYearStartDate())->format('Y').' - '.Carbon::parse(getFinancialYearEndDate())->format('Y');
            $startYear = explode(' - ', $financialYear)[0];
            $endYear = explode(' - ', $financialYear)[1];

            $getCurrentFinancialYearData = [
                'currentFinancialYear' => $financialYear,
                'startYear' => $startYear,
                'endYear' => $endYear,
                'yearStartDate' => $startYear.'-04-01',
                'yearEndDate' => $endYear.'-03-31',
            ];
        }

        return $getCurrentFinancialYearData;
    }
}
if (! function_exists('checkTransportIsExists')) {

    /**
     * @return mixed
     */
    function checkTransportIsExists($transportId)
    {
        $recordExists = false;
        // expense_credit_note_transactions
        if (ExpenseCreditNoteTransaction::whereTransportId($transportId)->exists()) {
            return true;
        }

        //expense_credit_note_transactions
        if (ExpenseDebitNoteTransaction::whereTransportId($transportId)->exists()) {
            return true;
        }

        // income_credit_note_transactions
        if (IncomeCreditNoteTransaction::whereTransportId($transportId)->exists()) {
            return true;
        }

        // income_debit_note_transactions
        if (IncomeDebitNoteTransaction::whereTransportId($transportId)->exists()) {
            return true;
        }

        // purchase_return_transactions
        if (PurchaseReturnTransaction::whereTransportId($transportId)->exists()) {
            return true;
        }

        // purchase_transactions
        if (PurchaseTransaction::whereTransportId($transportId)->exists()) {
            return true;
        }

        // sales_transactions
        if (SaleTransaction::whereTransportId($transportId)->exists()) {
            return true;
        }

        // recurring_invoice
        if (RecurringInvoice::whereTransportId($transportId)->exists()) {
            return true;
        }

        // sale_return_transactions
        if (SaleReturnTransaction::whereTransportId($transportId)->exists()) {
            return true;
        }

        // purchase_order_transactions
        if (PurchaseOrderTransaction::whereTransportId($transportId)->exists()) {
            return true;
        }

        // estimate_quote_transactions
        if (IncomeEstimateQuoteTransaction::whereTransportId($transportId)->exists()) {
            return true;
        }

        // Delivery Challan Transaction
        if (DeliveryChallanTransaction::whereTransportId($transportId)->exists()) {
            return true;
        }

        // master_customers
        if (Customer::whereTransporterId($transportId)->exists()) {
            return true;
        }

        // master_suppliers
        if (Supplier::whereTransporterId($transportId)->exists()) {
            return true;
        }

        return $recordExists;
    }
}

if (! function_exists('getPageSize')) {

    /**
     * @return mixed
     */
    function getPageSize($request)
    {
        return $request->input('page.size', 10);
    }
}
if (! function_exists('parseExcelDateForImportTransaction')) {
    function parseExcelDateForImportTransaction($date)
    {
        $parsedDate = ! empty($date) ? getDateFromExcelDate($date, 'd-m-Y') : null;
        if ($parsedDate === false) {
            $parsedDate = $date;
        }

        return $parsedDate;
    }
}
if (! function_exists('getDateFromExcelDate')) {
    function getDateFromExcelDate($date, $format = 'Y-m-d')
    {
        try {
            if (is_int($date)) {
                return Date::excelToDateTimeObject($date)->format($format);
            }
            $pattern = '/^(0[1-9]|[1-2][0-9]|3[0-1])-(0[1-9]|1[0-2])-\d{4}$/';

            if (preg_match($pattern, $date)) {
                return Carbon::parse($date)->format($format);
            }

            return false;
        } catch (Exception $e) {
            Log::error($e);
        }

        return false;
    }
}

if (! function_exists('convertAssociativeArrayToKeyValueArray')) {
    /**
     * @param  array  $data
     * @param  string  $keyName
     * @param  string  $valueName
     * @return array
     */
    function convertAssociativeArrayToKeyValueArray($data, $keyName = 'id', $valueName = 'value')
    {
        $convertedData = [];
        foreach ($data as $key => $value) {
            $convertedData[] = [
                $keyName => $key,
                $valueName => $value,
            ];
        }

        return $convertedData;
    }
}

if (! function_exists('checkMissingKeys')) {
    function checkMissingKeys($fields, $collection)
    {
        $errors = '';
        $missingKeys = array_diff($fields, $collection->first()->keys()->toArray());
        if (count($missingKeys) > 0) {
            $key = implode(',', $missingKeys);
            $errors = $key.' column does not exist in the excel file';
        }

        return $errors;
    }
}

if (! function_exists('removeExcelFileExtraFields')) {
    function removeExcelFileExtraFields($data)
    {
        foreach ($data as $key => $val) {
            if (is_numeric($key) && $val === null) {
                unset($data[$key]);
            }
        }

        return $data;
    }
}

if (! function_exists('getAPIPdfFilePath')) {
    function getAPIPdfFilePath()
    {
        $disk = config('app.media_disc');
        $path = 'mobile-assets/'.Carbon::now()->format('d-m-Y').'/pdf/'.getCurrentCompany()->id.'/'.Carbon::now()->format('d-m-Y H:i:s').'/';
        if (! file_exists(Storage::disk($disk)->exists($path))) {
            Storage::disk($disk)->makeDirectory($path);
        }

        return $path;
    }
}
if (! function_exists('getAPIExcelFilePath')) {

    function getAPIExcelFilePath()
    {
        $disk = config('app.media_disc');
        $path = 'mobile-assets/'.Carbon::now()->format('d-m-Y').'/excel/'.getCurrentCompany()->id.'/'.Carbon::now()->format('d-m-Y H:i:s').'/';
        if (! file_exists(Storage::disk($disk)->exists($path))) {
            Storage::disk($disk)->makeDirectory($path);
        }

        return $path;
    }
}

if (! function_exists('activeSubscription')) {
    function activeSubscription()
    {
        static $activeSubscription;

        if (getLoginUser()->hasRole([Role::CONSULTANT, Role::CONSULTANT_USER])) {
            return true;
        }

        if (isset($activeSubscription)) {
            return $activeSubscription;
        }

        if (loggedInViaFranchise()) {
            $activeSubscription = true;

            return true;
        }

        $account = getAccount();

        if (! $account) {
            $activeSubscription = false;

            return false;
        }

        if (! empty(isFranchiseCompany())) {
            $activeSubscription = true;

            return true;
        }

        $activeSubscription = Subscription::whereAccountId($account->id)->whereStatus(1)->first();
        if ($activeSubscription && $activeSubscription->end_date > Carbon::now()) {
            $planData = json_decode($activeSubscription->meta);
            if (isset($planData->is_for_mobile) && $planData->is_for_mobile) {
                $activeSubscription = true;

                return true;
            }
            $activeSubscription = true;

            return true;
        }

        if (! empty($account->trial_ends_at) && $account->trial_ends_at > Carbon::now()) {
            $activeSubscription = true;

            return true;
        }

        $activeSubscription = false;

        return false;
    }
}

if (! function_exists('isSubscriptionExists')) {
    function isSubscriptionExists()
    {
        $account = getAccount();

        if (! $account) {
            return false;
        }

        $subscription = Subscription::whereAccountId($account->id)->first();

        if ($subscription) {
            return true;
        }

        return false;
    }
}

if (! function_exists('loggedInViaFranchise')) {
    function loggedInViaFranchise()
    {
        $franchiseUser = Session::get('current_ho_admin');

        if ($franchiseUser && $franchiseUser->hasRole([
            Role::HO_ADMIN,
            Role::FRANCHISE_ADMIN,
            Role::FRANCHISE_SENIOR_MANAGER,
            Role::FRANCHISE_JUNIOR_ACCOUNT_EXECUTIVE,
            Role::FRANCHISE_SENIOR_ACCOUNT_EXECUTIVE,
            Role::FRANCHISE_MANAGER,
            Role::FRANCHISE_OFFICE_EXECUTIVE,
            Role::FRANCHISE_SALES_PERSON,
        ])) {
            return true;
        }

        return false;
    }
}

if (! function_exists('isFranchiseCompany')) {
    function isFranchiseCompany()
    {
        $account = getAccount();
        if (empty($account)) {
            return null;
        }

        static $franchiesID;

        if (! isset($franchiesID)) {
            $franchiesID = Company::whereAccountId($account->id)->value('franchise_id');

            if ($franchiesID === null) {
                $franchiesID = '';
            }
        }

        return ! empty($franchiesID) ? $franchiesID : null;
    }
}

if (! function_exists('onTrialPeriod')) {
    function onTrialPeriod($dayRemaining = null)
    {
        $account = getAccount();

        if (! $account || empty($account->trial_ends_at) || ! empty(isFranchiseCompany())) {
            return false;
        }

        $trialEndsAt = Carbon::parse($account->trial_ends_at);

        if ($dayRemaining) {
            if ($trialEndsAt->diffInDays(Carbon::now()) <= $dayRemaining) {
                return true;
            } else {
                return false;
            }
        }

        if ($trialEndsAt > Carbon::now()) {
            return true;
        }

        return false;
    }
}

if (! function_exists('subscriptionRemainingDays')) {
    function subscriptionRemainingDays($dayRemaining)
    {
        $account = getAccount();

        if (! empty(isFranchiseCompany())) {
            return false;
        }

        $subscription = Subscription::whereAccountId($account->id)->whereStatus(1)->first();
        if ($subscription) {
            $endsAt = Carbon::parse($subscription->end_date);

            if (getDayDiff($endsAt) <= $dayRemaining) {
                return true;
            }
        }

        return false;
    }
}

if (! function_exists('isOnlyForMobilePlan')) {
    function isOnlyForMobilePlan()
    {
        $account = getAccount();

        if (! $account || ! empty(isFranchiseCompany())) {
            return false;
        }

        static $accountSubscription;
        if (empty($accountSubscription)) {
            $accountSubscription = Subscription::whereAccountId($account->id)->whereStatus(Subscription::ACTIVE)->first();
        }

        if (empty($accountSubscription)) {
            return false;
        }

        $meta = json_decode($accountSubscription->meta);

        if (isset($meta->is_for_mobile) && $meta->is_for_mobile) {
            return true;
        }

        return false;
    }
}

/* if (!function_exists('isOnGracePeriod')) {
    function isOnGracePeriod()
    {
        $account = getAccount();

        if (!$account) {
            return false;
        }

        $activeSubscription = Subscription::whereAccountId($account->id)->whereStatus(1)->first();
        $graceDays = getSadminSettings()['grace_period'];

        if ($activeSubscription && Carbon::parse($activeSubscription->end_date)->addDays($graceDays)->isFuture()) {
            return true;
        }

        return false;
    }
} */

/* if (!function_exists('remainingGraceDays')) {
    function remainingGraceDays()
    {
        $account = getAccount();

        if (!$account) {
            return false;
        }

        $activeSubscription = Subscription::whereAccountId($account->id)->whereStatus(1)->first();
        $graceDays = getSadminSettings()['grace_period'];

        if ($activeSubscription && Carbon::parse($activeSubscription->end_date)->addDays($graceDays)->isPast()) {
            return 0;
        }

        return Carbon::parse($activeSubscription->end_date)->addDays($graceDays)->diffInDays(Carbon::now());
    }
} */

if (! function_exists('remainingSubscriptionDays')) {
    function remainingSubscriptionDays()
    {
        $account = getAccount();

        if (! $account || ! empty(isFranchiseCompany())) {
            return false;
        }

        $activeSubscription = Subscription::whereAccountId($account->id)->whereStatus(1)->first();
        if ($activeSubscription && $activeSubscription->end_date < Carbon::now()) {
            return 0;
        }

        return getDayDiff($activeSubscription->end_date);
    }
}
if (! function_exists('getCustomInvoiceSetting')) {
    function getCustomInvoiceSetting($key)
    {

        $customSetting = CompanySetting::whereCompanyId(getCurrentCompany()->id)->where('key', $key)->first();

        if ($key == 'qr_code') {

            return isset($customSetting) ? $customSetting->value : false;
        }

        return isset($customSetting) ? $customSetting->value : true;
    }
}

if (! function_exists('getPlanCalculation')) {
    function getPlanCalculation($planID, $extraUsers = null, $extraCompanies = null)
    {
        $account = getAccount();
        $data = [];
        $plan = Plan::whereId($planID)->first();

        $billingDate = Subscription::where('account_id', $account->id)
            ->orderBy('start_date', 'desc')
            ->first();

        $billingDate = $billingDate ? $billingDate->start_date : null;
        if (empty($billingDate)) {
            $billingDate = Carbon::now()->toDateTimeString();
        }

        $data['new_users'] = User::where('account_id', $account->id)
            ->active()
            ->whereDate('created_at', '>', $billingDate)
            ->whereHas('roles', function ($query) {
                $query->where('name', Role::CLIENT_USER);
            })->count();

        $data['users'] = User::where('account_id', $account->id)
            ->active()
            ->whereDate('created_at', '<=', $billingDate)
            ->whereHas('roles', function ($query) {
                $query->where('name', Role::CLIENT_USER);
            })->count();

        $data['new_companies'] = Company::active()->where('account_id', $account->id)
            ->whereDate('created_at', '>', $billingDate)
            ->count();

        $data['companies'] = Company::active()->where('account_id', $account->id)->count();

        $data['extraUsers'] = (1 + $data['users']) - $plan->default_user;
        $data['extraUsers'] = $data['extraUsers'] > 0 ? $data['extraUsers'] : 0;

        if (! empty($extraUsers)) {
            $data['extraUsers'] = $extraUsers;
            $data['extraUsers'] = $data['extraUsers'] > 0 ? $data['extraUsers'] : $data['users'] - $plan->default_user;
        }

        $data['extraCompanies'] = $data['companies'] - $plan->default_company;
        $data['extraCompanies'] = $data['extraCompanies'] > 0 ? $data['extraCompanies'] : 0;

        if (! empty($extraCompanies)) {
            $data['extraCompanies'] = $extraCompanies;
            $data['extraCompanies'] = $data['extraCompanies'] > 0 ? $data['extraCompanies'] : $data['companies'] - $plan->default_company;
        }

        if ($plan->default_user == -1) {
            $data['extraUsers'] = 0;
        }
        if ($plan->default_company == -1) {
            $data['extraCompanies'] = 0;
        }

        $data['extraUsersTotal'] = $data['extraUsers'] * $plan->new_user_price;
        $data['extraCompaniesTotal'] = $data['extraCompanies'] * $plan->new_company_price;
        $data['total'] = $plan->price + $data['extraUsersTotal'] + $data['extraCompaniesTotal'];
        $data['gstTax'] = $data['total'] * $plan->gst_percentage / 100;
        $data['grandTotal'] = $data['total'] + $data['gstTax'];

        return $data;
    }
}

if (! function_exists('getDayDiff')) {
    function getDayDiff($startDate, $endDate = null)
    {
        if (empty($endDate)) {
            $endDate = Carbon::now();
        }
        $startDate = Carbon::parse($startDate)->format('y-m-d');
        $endDate = Carbon::parse($endDate)->format('y-m-d');

        return Carbon::parse($startDate)->diffInDays($endDate);
    }
}

if (! function_exists('getOrderID')) {
    function getOrderID()
    {
        $orderId = time().mt_rand();
        while (true) {
            $isExist = Transaction::whereOrderId($orderId)->exists();
            if ($isExist) {
                getOrderID();
            }
            break;
        }

        return $orderId;
    }
}

if (! function_exists('getWpOrderID')) {
    function getWpOrderID()
    {
        $orderId = 'wp_'.time().mt_rand();
        while (true) {
            $isExist = WhatsappTransaction::whereOrderId($orderId)->exists();
            if ($isExist) {
                return getWpOrderID();
            }
            break;
        }

        return $orderId;
    }
}

if (! function_exists('getSessionCompany')) {
    function getSessionCompany()
    {
        $data = [];
        $data['user'] = getLoginUser();
        $data['account'] = getAccount();

        $sessionCompany = session('current_company');
        if ($sessionCompany) {
            $data['user'] = $sessionCompany->user;
            $data['account'] = $sessionCompany->user->account;
        }

        return $data;
    }
}

if (! function_exists('generateUPIQRCode')) {
    function generateUPIQRCode($upiID, $dueAmount)
    {
        $companySetting = getCompanySettings();
        if (isset($companySetting['qr_code_with_amount']) && $companySetting['qr_code_with_amount'] == CompanySetting::WITHOUT_AMOUNT) {
            $upiID = 'upi://pay?pa='.$upiID.'&pn='.getCurrentCompany()->trade_name;
        } else {
            $upiID = 'upi://pay?pa='.$upiID.'&pn='.getCurrentCompany()->trade_name.'&am='.$dueAmount.'';
        }

        return (string) QrCode::format('svg')
            ->margin(0)
            ->size(200)
            ->generate($upiID);
    }
}

if (! function_exists('getDateCompanyFilter')) {
    function getDateCompanyFilter($key)
    {

        if (isset(getCompanyFilters()[$key])) {
            $date = explode(' - ', getCompanyFilters()[$key]);

            return $date;
        }

        $date = [
            getCurrentFinancialYearDetails()['yearStartDate'],
            getCurrentFinancialYearDetails()['yearEndDate'],
        ];

        return $date;
    }
}

if (! function_exists('getEInvoiceData')) {
    function getEInvoiceData($transactionId, $transactionType)
    {
        return EInvoice::where('transaction_id', $transactionId)
            ->where('transaction_type', $transactionType)
            ->first();
    }
}

if (! function_exists('getEInvoiceDataForPdf')) {
    function getEInvoiceDataForPdf($transactionId, $transactionType)
    {
        return EInvoice::where('company_id', getCurrentCompany()->id)
            ->where('transaction_id', $transactionId)
            ->where('transaction_type', $transactionType)
            // ->where('is_canceled', false)
            ->first();
    }
}

if (! function_exists('getEInvAPICredentials')) {
    function getEInvAPICredentials()
    {
        return EInvAPICredentials::first();
    }
}

if (! function_exists('getEWayBillAPICredentials')) {
    function getEWayBillAPICredentials()
    {
        return EwayBillAPICredentials::first();
    }
}

if (! function_exists('getIncrementKey')) {
    function getIncrementKey($value = 0)
    {
        static $increment = 1;
        $incrementArr = [];

        if (! isset($incrementArr[$value])) {
            $incrementArr[$value] = $increment++;
        }

        return $incrementArr[$value];
    }
}

if (! function_exists('calculateSubGroupTotal')) {
    function calculateSubGroupTotal(&$incomeData)
    {
        $totalDirectIncome = 0;

        foreach ($incomeData as $key => $income) {
            if ($key == 'recursiveChildren') {
                foreach ($income as &$incomeData) {
                    $totalDirectIncome += $incomeData['groupTotal'] ?? 0;
                }
            }
            $totalDirectIncome += $income['amount'] ?? 0;
        }
        $groupTotal = $totalDirectIncome;

        return $groupTotal;
    }
}

if (! function_exists('webIntroduction')) {
    function webIntroduction()
    {

        /** @var User $user */
        $user = getLoginUser();

        if (getLoginUser()->hasRole(Role::CLIENT_ADMIN) || getLoginUser()->hasRole(Role::CLIENT_USER)) {
            return ! $user->web_introduction;
        }

        return false;
    }
}

if (! function_exists('getCreditPeriod')) {
    function getCreditPeriod($creditPeriod, $type)
    {
        $data['creditPeriod'] = null;
        if ($type == SaleTransaction::CREDIT_PERIOD_TYPE_DAY) {
            if ($creditPeriod > 1) {
                $data['creditPeriod'] = $creditPeriod.' Days';
            } else {
                $data['creditPeriod'] = $creditPeriod.' Day';
            }
        }
        if ($type == SaleTransaction::CREDIT_PERIOD_TYPE_MONTH) {
            if ($creditPeriod > 1) {
                $data['creditPeriod'] = $creditPeriod.' Months ';
            } else {
                $data['creditPeriod'] = $creditPeriod.' Month';
            }
        }

        return $data['creditPeriod'];
    }
}

if (! function_exists('countArrays')) {
    function countArrays($array, $count = 0)
    {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $count = countArrays($value, $count);
            }
        }

        return $count + 1;
    }
}
/* For Use Trading P&L Report PDF */
if (! function_exists('countCharacters')) {
    function countCharacters(&$array1, &$array2)
    {
        foreach ($array1 as $key => &$element) {
            $array1Strlen = strlen($element[0]);
            $array2Strlen = strlen($array2[$key][0]);
            $max = max($array1Strlen, $array2Strlen);
            if ($array1Strlen == $max) {
                if ($max > 35) {
                    $array2[$key]['pb'] = 'padding-bottom: 24px;';
                }
            } else {
                if ($max > 35) {
                    $element['pb'] = 'padding-bottom: 24px;';
                }
            }
        }
    }
}

if (! function_exists('changeStrToCamelCase')) {
    function changeStrToCamelCase($str)
    {
        $words = preg_split('/[\s_]+/', $str);

        $camelCaseStr = '';
        foreach ($words as $word) {
            $camelCaseStr .= ucfirst(strtolower($word)).' ';
        }

        return rtrim($camelCaseStr);
    }
}

if (! function_exists('getCompanyItemGroups')) {
    function getCompanyItemGroups()
    {
        $currentCompany = getCurrentCompany();
        $defaultGroups = CompanyGroup::with(['childGroups'])
            ->when(! empty($currentCompany), function ($query) use ($currentCompany) {
                $query->whereCompanyId($currentCompany->id);
            })
            ->whereIn('name', [Ledger::ITEM_DEFAULT_GROUP])
            ->get();

        return processGroups($defaultGroups);
    }
}

if (! function_exists('processGroups')) {
    function processGroups($groups)
    {
        $result = [];

        foreach ($groups as $group) {
            $result[$group->id] = $group->name;
            if (! empty($group->childGroups)) {
                $result += processGroups($group->childGroups);
            }
        }

        return $result;
    }
}
/*
*
*/
if (! function_exists('getLoginUserCompanyDate')) {
    function getLoginUserCompanyDate()
    {
        return CompanyTeamManagement::whereUserId(getLoginUser()->id)->count();
    }
}

if (! function_exists('getDailyReportsPDFPath')) {
    function getDailyReportsPDFPath($companyID)
    {
        $disk = config('app.media_disc');
        $path = 'daily-reports/'.Carbon::now()->format('d-m-Y').'/pdf/'.$companyID.'/';
        if (! file_exists(Storage::disk($disk)->exists($path))) {
            Storage::disk($disk)->makeDirectory($path);
        }

        return $path;
    }
}

if (! function_exists('getDailyOutstandingReportsPDFPath')) {
    function getDailyOutstandingReportsPDFPath($companyID)
    {
        $disk = config('app.media_disc');
        $path = 'daily-outstanding-reports/'.Carbon::now()->format('d-m-Y').'/pdf/'.$companyID.'/';
        if (! file_exists(Storage::disk($disk)->exists($path))) {
            Storage::disk($disk)->makeDirectory($path);
        }

        return $path;
    }
}

if (! function_exists('getClosingBalanceAndType')) {
    function getClosingBalanceAndType($ledgerId)
    {
        $ledger = Ledger::whereId($ledgerId)->first();
        if (empty($ledger)) {
            return [
                'closing_bal' => 0,
                'closing_bal_type' => null,
            ];
        }
        $getCurrentFinancialYearDetails = getCurrentFinancialYearDetails();
        $startDate = $getCurrentFinancialYearDetails['yearStartDate'];
        $endDate = $getCurrentFinancialYearDetails['yearEndDate'];
        $startYear = $getCurrentFinancialYearDetails['startYear'];
        $endYear = $getCurrentFinancialYearDetails['endYear'];

        /** @var TrialBalanceRepository $trialBalanceRepo */
        $trialBalanceRepo = App::make(TrialBalanceRepository::class);
        /** @var TrialBalanceController $trialBalanceController */
        $trialBalanceController = App::make(TrialBalanceController::class);
        $company = getCurrentCompany();

        $transactions = $trialBalanceRepo->getTransactionDataForBalance($company, $startDate, $endDate);

        $trialBalanceController->transactions = $transactions;

        $financialYearOpeningBalances = FinancialYearOpeningBalance::whereCompanyId($company->id)
            ->whereStartYear($startYear)->whereEndYear($endYear)->get()->keyBy('ledger_id');

        $trialBalanceRepo->financialYearOpeningBalance = $financialYearOpeningBalances;
        $trialBalanceController->financialYearOpeningBalance = $financialYearOpeningBalances;

        $data = $trialBalanceController->getTrialBalanceDataForLedger($ledger, $startDate, $endDate);

        return $data;
    }
}

if (! function_exists('getCompanyProfileEmptyDetails')) {
    function getCompanyProfileEmptyDetails($company)
    {
        if (empty($company->trade_name)) {
            $emptyDetails[] = 'Business name';
        }
        if (empty($company->billingAddress)) {
            $emptyDetails[] = 'Billing address';
        }
        if (empty($company->dispatchAddress)) {
            $emptyDetails[] = 'Dispatch address';
        }

        if (! empty($emptyDetails)) {
            return implode(', ', $emptyDetails);
        }

        return false;
    }
}

if (! function_exists('getCompanyList')) {
    function getCompanyList()
    {
        $user = getLoginUser();
        $getCurrentCompany = getCurrentCompany();
        $companyIds = CompanyTeamManagement::whereUserId(Auth::id())->pluck('company_id')->toArray();
        $companyList = [];
        if ($user->hasRole([Role::CLIENT_ADMIN, Role::CLIENT_USER])) {
            $companyList = Company::where('status', Company::ACTIVE)
                ->whereIn('id', $companyIds)
                ->get()->mapWithKeys(function ($company) {
                    $tradeName = $company->trade_name;

                    return [$company->id => $tradeName];
                })->toArray();
        } else {
            $companyList[$getCurrentCompany->id] = $getCurrentCompany->trade_name;
        }

        return $companyList;
    }
}

if (! function_exists('getCompanyListForConsultant')) {
    function getCompanyListForConsultant()
    {
        $user = getLoginUser();
        if (! $user->hasRole(Role::CONSULTANT_USER)) {
            $companyIds = CompanyTeamManagement::whereUserId($user->id)->pluck('company_id')->toArray();
        } else {
            $companyIds = ConsultantTeamManagement::whereUserId($user->id)->pluck('company_id')->toArray();
        }

        $companyList = [];
        if ($user->hasRole([Role::CONSULTANT_USER])) {
            $companyList = Company::where('status', Company::ACTIVE)
                ->whereIn('id', $companyIds)
                ->get()->mapWithKeys(function ($company) {
                    $tradeName = $company->trade_name;

                    return [$company->id => $tradeName];
                })->toArray();
        } else {
            foreach ($companyIds as $companyId) {
                $company = Company::whereId($companyId)->first();
                if (! empty($company)) {
                    $companyList[$companyId] = $company->trade_name;
                }
            }
        }

        return $companyList;
    }
}

if (! function_exists('getCurrentQuarter')) {
    function getCurrentQuarter()
    {
        $currentDate = Carbon::now();
        $startOfQ1 = Carbon::now()->month(1)->startOfQuarter();
        $endOfQ1 = Carbon::now()->month(1)->endOfQuarter();

        $startOfQ2 = Carbon::now()->month(4)->startOfQuarter();
        $endOfQ2 = Carbon::now()->month(4)->endOfQuarter();

        $startOfQ3 = Carbon::now()->month(7)->startOfQuarter();
        $endOfQ3 = Carbon::now()->subYear()->month(7)->endOfQuarter();

        $startOfQ4 = Carbon::now()->month(10)->startOfQuarter();
        $endOfQ4 = Carbon::now()->month(10)->endOfQuarter();

        $quarterlyStartDate = null;
        $quarterlyEndDate = null;
        if ($currentDate->between($startOfQ1, $endOfQ1)) {
            $quarterlyStartDate = $startOfQ1;
            $quarterlyEndDate = $endOfQ1;
        }
        if ($currentDate->between($startOfQ2, $endOfQ2)) {
            $quarterlyStartDate = $startOfQ2;
            $quarterlyEndDate = $endOfQ2;
        }
        if ($currentDate->between($startOfQ3, $endOfQ3)) {
            $quarterlyStartDate = $startOfQ3;
            $quarterlyEndDate = $endOfQ3;
        }
        if ($currentDate->between($startOfQ4, $endOfQ4)) {
            $quarterlyStartDate = $startOfQ4;
            $quarterlyEndDate = $endOfQ4;
        }

        return [$quarterlyStartDate, $quarterlyEndDate];
    }
}

if (! function_exists('getLedgerGroupsWishlist')) {
    function getLedgerGroupsWishlist($groupName)
    {
        /** @var CompanyGroup $companyGroup */
        $companyGroup = CompanyGroup::whereCompanyId(getCurrentCompany()->id)->whereIn('name', $groupName)
            ->pluck('id')->toArray();

        $companyGroupsParentGroup = CompanyGroup::whereIn('parent_id', $companyGroup)->pluck('id')->toArray();

        $companyGroup = array_merge($companyGroup, $companyGroupsParentGroup);

        return Ledger::whereIn('group_id', $companyGroup)->orderBy('name', 'ASC')->pluck('name', 'id');
    }
}

if (! function_exists('getVideo')) {
    function getVideo()
    {
        $videoIds = [
            'q4RxdnVkKWs',
            'nHF8k4X86KA',
            '7B654Jk-8Us',
            'iQe-hJ3sbuY',
        ];

        $client = new \GuzzleHttp\Client();
        $videoData = [];

        foreach ($videoIds as $videoId) {
            $url = 'https://www.youtube.com/watch?v='.$videoId;
            $oembedUrl = 'https://www.youtube.com/oembed?url='.$url.'&format=json';

            $response = $client->get($oembedUrl);
            $result = json_decode($response->getBody(), true);

            $videoData[] = [
                'title' => $result['title'],
                'videoUrl' => $videoId,
                'redirectVideoUrl' => $url,
            ];
        }

        return $videoData;
    }
}

if (! function_exists('getCurrentCompanyIsoCode')) {
    function getCurrentCompanyIsoCode($user = null)
    {
        $currentCompanyId = session('current_company')?->id;

        if (empty($currentCompanyId) && (! empty(getLoginUser() || ! empty($user)))) {
            $userId = getLoginUser()->id ?? $user->id;
            $currentCompanyId = CompanyTeamManagement::whereUserId($userId)->value('company_id');
        }

        $code = CompanySetting::whereCompanyId($currentCompanyId)->where('key', 'company_region_iso')->value('value');
        $code = empty($code) ? 'in' : $code;

        return $code;
    }
}
if (! function_exists('checkCustomerAndSupplerTcsTaxTypeValue')) {
    function checkCustomerAndSupplerTcsTaxTypeValue($ledgerId)
    {

        $ledger = Ledger::whereId($ledgerId)->with('model')->first();

        $tcsTaxType = 2;
        if (
            $ledger->model->pan_card_number == null ||
            $ledger->model->pan_card_number == ''
        ) {
            $tcsTaxType = 0;
        } elseif ($ledger->model->entity_type == 2) {
            $tcsTaxType = 1;
        } else {
            $tcsTaxType = 2;
        }

        return $tcsTaxType;
    }
}

if (! function_exists('getAnalyticData')) {
    function getAnalyticData($userId)
    {
        $analyticData = Analytic::whereUserId($userId)->first();

        return ! empty($analyticData) ? $analyticData : '';
    }
}

/*
* Check if import transaction on a given date falls within the current financial year,
*/
if (! function_exists('checkDateIsCurrentFinancialYear')) {
    function checkDateIsCurrentFinancialYear($date)
    {
        static $financialYearDetails;

        if (empty($financialYearDetails)) {
            $financialYearDetails = getCurrentFinancialYearDetails();
        }
        $currentFinancialYearStartDate = $financialYearDetails['yearStartDate'];
        $currentFinancialYearEndDate = $financialYearDetails['yearEndDate'];
        $dateToCheck = Carbon::parse($date);

        return ! $dateToCheck->between($currentFinancialYearStartDate, $currentFinancialYearEndDate);
    }
}

if (! function_exists('updateFieldsValue')) {
    function updateFieldsValue($row, $isId = false, $model = null)
    {
        $oldRow = $row;
        try {
            DB::beginTransaction();

            if (isset($isId) && $isId && ! empty($model)) {
                $row = $model::whereId($row)->first();
            }

            if (! empty($row)) {
                // $taxableValue = $row->get_taxable_value;
                $paymentStatus = $row->get_payment_status;
                $dueAmount = $row->get_due_amount;

                // $row->taxable_value = $taxableValue;
                $row->payment_status = $paymentStatus;
                $row->due_amount = $dueAmount;
                $row->save();
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            if (! empty($row)) {
                Log::error('Error on update for id '.$row->id.' in '.get_class($row));
            } else {
                Log::error('Error on update :- '.$oldRow);
            }
            Log::error($e->getMessage());
            throw $e;
        }
    }
}

if (! function_exists('updateOpeningBalanceFieldsValue')) {
    function updateOpeningBalanceFieldsValue($row, $isId = false, $model = null)
    {
        $oldRow = $row;
        try {
            DB::beginTransaction();

            if (isset($isId) && $isId && ! empty($model)) {
                $row = $model::whereId($row)->first();
            }

            if (! empty($row)) {
                $dueAmount = $row->get_due_amount;

                $row->total_paid_amount = $dueAmount;
                $row->save();
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            if (! empty($row)) {
                Log::error('Error on update for id '.$row->id.' in '.get_class($row));
            } else {
                Log::error('Error on update :- '.$oldRow);
            }
            Log::error($e->getMessage());
            throw $e;
        }
    }
}

if (! function_exists('updateStatusValueOfVastraDeliveryChallan')) {
    function updateStatusValueOfVastraDeliveryChallan($row, $isId = false, $model = null)
    {
        try {
            DB::beginTransaction();

            if (isset($isId) && $isId && ! empty($model)) {
                $row = $model::whereId($row)->first();
            }

            if (! empty($row)) {
                $status = $row->get_status;

                $row->status = $status;
                $row->save();
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage());
            throw $e;
        }
    }
}

if (! function_exists('updateEstimateQuoteStatus')) {
    function updateEstimateQuoteStatus($estimateQuoteId)
    {
        $row = IncomeEstimateQuoteTransaction::whereId($estimateQuoteId)->first();
        if (! empty($row)) {
            $status = $row->get_status;
            $row->status = $status;
            $row->save();
        }
    }
}

if (! function_exists('updateDeliveryChallanStatus')) {
    function updateDeliveryChallanStatus($deliveryChallanId)
    {
        $row = DeliveryChallanTransaction::whereId($deliveryChallanId)->first();
        if (! empty($row)) {
            $status = $row->get_status;
            $row->status = $status;
            $row->save();
        }
    }
}

if (! function_exists('updatePurchaseOrderStatus')) {
    function updatePurchaseOrderStatus($estimateQuoteId)
    {
        $row = PurchaseOrderTransaction::whereId($estimateQuoteId)->first();
        if (! empty($row)) {
            $status = $row->get_status;
            $row->status = $status;
            $row->save();
        }
    }
}

if (! function_exists('updateMultiplePurchaseOrderStatus')) {
    function updateMultiplePurchaseOrderStatus($purchaseOrderId, $purchaseId)
    {
        $purchaseOrderIds = explode(',', $purchaseOrderId);
        $purchaseOrderTransactions = PurchaseOrderTransaction::with(['transactionItems', 'transactionLedgers'])
            ->whereIn('id', $purchaseOrderIds)
            ->get();
        foreach ($purchaseOrderTransactions as $purchaseOrderTransaction) {
            $status = null;
            if ($purchaseOrderTransaction->order_type == PurchaseOrderTransaction::ACCOUNTING_INVOICE) {
                $billedAmount = 0;
                $totalAmount = 0;
                foreach ($purchaseOrderTransaction->transactionLedgers as $ledger) {
                    $purchaseLedger = PurchaseLedgerTransaction::whereHas('purchaseTransaction', function ($query) {
                        $query->whereNull('deleted_at');
                    })->where('purchase_transaction_id', $purchaseId)
                        ->where('ledger_id', $ledger->ledger_id)
                        ->first();

                    if (! empty($purchaseLedger) && isset($purchaseLedger->total) && is_numeric($purchaseLedger->total)) {
                        $billedAmount += intval($purchaseLedger->total);
                    }

                    if (isset($ledger->total) && is_numeric($ledger->total)) {
                        $totalAmount += intval($ledger->total);
                    }
                }
                $pendingAmount = ($totalAmount - $billedAmount);
                $pendingAmount = $pendingAmount > 0 ? $pendingAmount : 0;

                if ($billedAmount == 0) {
                    $status = PurchaseOrderTransaction::OPEN;
                } elseif ($pendingAmount != 0) {
                    $status = PurchaseOrderTransaction::PARTIALLY_INVOICED;
                } else {
                    $status = PurchaseOrderTransaction::FULLY_INVOICED;
                }
            } else {
                $billedQuantity = 0;
                $totalQuantity = 0;
                foreach ($purchaseOrderTransaction->transactionItems as $item) {
                    $purchaseOrderId = $item->transactions_id;
                    $purchaseItem = PurchaseItemTransaction::whereHas('purchaseTransaction', function ($query) use ($purchaseOrderId) {
                        $query->whereNull('deleted_at')->whereRaw('FIND_IN_SET(?, purchase_order_no)', [$purchaseOrderId]);
                    })
                        // ->where('purchase_transaction_id', $purchaseId)
                        ->where('item_id', $item->item_id)
                        ->sum('quantity');

                    if (! empty($purchaseItem) && is_numeric($purchaseItem)) {
                        $billedQuantity += intval($purchaseItem);
                    }
                    if (isset($item->quantity) && is_numeric($item->quantity)) {
                        $totalQuantity += intval($item->quantity);
                    }

                    $pendingQuantity = ($totalQuantity - $billedQuantity);
                    $pendingQuantity = $pendingQuantity > 0 ? $pendingQuantity : 0;

                    if ($billedQuantity == 0) {
                        $status = PurchaseOrderTransaction::OPEN;
                    } elseif ($pendingQuantity != 0) {
                        $status = PurchaseOrderTransaction::PARTIALLY_INVOICED;
                    } else {
                        $status = PurchaseOrderTransaction::FULLY_INVOICED;
                    }
                }
            }

            if (! empty($purchaseOrderTransaction->valid_for)) {
                if ($purchaseOrderTransaction->valid_for_type == PurchaseOrderTransaction::CREDIT_PERIOD_TYPE_DAY) {
                    $validTillTime = Carbon::parse($purchaseOrderTransaction->order_date)->addDays($purchaseOrderTransaction->valid_for)->endOfDay();
                } else {
                    $validTillTime = Carbon::parse($purchaseOrderTransaction->order_date)->addMonths($purchaseOrderTransaction->valid_for)->endOfDay();
                }
                if ($validTillTime < Carbon::now()) {
                    $status = PurchaseOrderTransaction::EXPIRED;
                }
            }

            PurchaseOrderTransaction::where('id', $purchaseOrderTransaction->id)->update(['status' => $status]);
        }
    }
}

if (! function_exists('updateMultipleEstimateQuoteStatus')) {
    function updateMultipleEstimateQuoteStatus($estimateQuoteId, $saleTransactionId)
    {
        $estimateQuoteIds = explode(',', $estimateQuoteId);
        $estimateTransactions = IncomeEstimateQuoteTransaction::with(['transactionItems', 'transactionLedgers'])
            ->whereIn('id', $estimateQuoteIds)
            ->get();

        foreach ($estimateTransactions as $estimateTransaction) {
            if ($estimateTransaction->invoice_type == IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE) {
                $billedAmount = 0;
                $totalAmount = 0;
                foreach ($estimateTransaction->transactionLedgers as $ledger) {
                    $saleLedger = SaleTransactionLedger::whereHas('saleTransaction', function ($query) {
                        $query->whereNull('deleted_at');
                    })->where('sale_transactions_id', $saleTransactionId)
                        ->where('ledger_id', $ledger->ledger_id)
                        ->first();

                    if (! empty($saleLedger) && isset($saleLedger->total) && is_numeric($saleLedger->total)) {
                        $billedAmount += $saleLedger->total;
                    }

                    if (isset($ledger->total) && is_numeric($ledger->total)) {
                        $totalAmount += $ledger->total;
                    }
                }

                $pendingAmount = ($totalAmount - $billedAmount);
                $pendingAmount = $pendingAmount > 0 ? $pendingAmount : 0;

                if ($billedAmount == 0) {
                    $status = IncomeEstimateQuoteTransaction::OPEN;
                } elseif ($pendingAmount != 0) {
                    $status = IncomeEstimateQuoteTransaction::PARTIALLY_INVOICED;
                } else {
                    $status = IncomeEstimateQuoteTransaction::FULLY_INVOICED;
                }
            } else {
                $billedQuantity = 0;
                $totalQuantity = 0;
                foreach ($estimateTransaction->transactionItems as $item) {
                    $estimateQuoteId = $item->transactions_id;
                    $saleItem = SaleTransactionItem::whereHas('saleTransaction', function ($query) use ($estimateQuoteId) {
                        $query->whereNull('deleted_at')->whereRaw('FIND_IN_SET(?, estimate_quote_no)', [$estimateQuoteId]);
                    })
                        // ->where('sale_transactions_id', $saleTransactionId)
                        ->where('item_id', $item->item_id)
                        ->sum('quantity');

                    if (! empty($saleItem) && is_numeric($saleItem)) {
                        $billedQuantity += intval($saleItem);
                    }

                    if (isset($item->quantity) && is_numeric($item->quantity)) {
                        $totalQuantity += intval($item->quantity);
                    }
                }

                $pendingQuantity = ($totalQuantity - $billedQuantity);
                $pendingQuantity = $pendingQuantity > 0 ? $pendingQuantity : 0;

                if ($billedQuantity == 0) {
                    $status = IncomeEstimateQuoteTransaction::OPEN;
                } elseif ($pendingQuantity != 0) {
                    $status = IncomeEstimateQuoteTransaction::PARTIALLY_INVOICED;
                } else {
                    $status = IncomeEstimateQuoteTransaction::FULLY_INVOICED;
                }
            }

            IncomeEstimateQuoteTransaction::where('id', $estimateTransaction->id)->update(['status' => $status]);
        }
    }
}

if (! function_exists('updateMultipleDeliveryChallanStatus')) {
    function updateMultipleDeliveryChallanStatus($deliveryChallanId, $saleTransactionId)
    {
        $deliveryChallanIds = explode(',', $deliveryChallanId);
        $deliveryChallanTransactions = DeliveryChallanTransaction::with('transactionItems')
            ->whereIn('id', $deliveryChallanIds)
            ->get();

        foreach ($deliveryChallanTransactions as $deliveryChallanTransaction) {
            $billedQuantity = 0;
            $totalQuantity = 0;
            foreach ($deliveryChallanTransaction->transactionItems as $item) {
                $deliveryChallanId = $item->transaction_id;
                $saleItem = SaleTransactionItem::whereHas('saleTransaction', function ($query) use ($deliveryChallanId) {
                    $query->whereNull('deleted_at')->whereRaw('FIND_IN_SET(?, delivery_challan_no)', [$deliveryChallanId]);
                })
                    // ->where('sale_transactions_id', $saleTransactionId)
                    ->where('item_id', $item->item_id)
                    ->sum('quantity');

                if (! empty($saleItem) && is_numeric($saleItem)) {
                    $billedQuantity += intval($saleItem);
                }

                if (isset($item->quantity) && is_numeric($item->quantity)) {
                    $totalQuantity += intval($item->quantity);
                }
            }

            $pendingQuantity = ($totalQuantity - $billedQuantity);
            $pendingQuantity = $pendingQuantity > 0 ? $pendingQuantity : 0;

            if ($billedQuantity == 0) {
                $status = DeliveryChallanTransaction::OPEN;
            } elseif ($pendingQuantity != 0) {
                $status = DeliveryChallanTransaction::PARTIALLY_INVOICED;
            } else {
                $status = DeliveryChallanTransaction::FULLY_INVOICED;
            }

            DeliveryChallanTransaction::where('id', $deliveryChallanTransaction->id)->update(['status' => $status]);
        }
    }
}

if (! function_exists('validDate')) {

    function validDate($date)
    {
        return ! preg_match('/[^0-9\-\/]/', $date);
    }
}

if (! function_exists('getAccountSetting')) {
    function getAccountSetting($key)
    {
        $accountSetting = AccountSetting::where('key', $key)->where('account_id', getAccount()->id)->first();

        return ! empty($accountSetting) ? $accountSetting->value : '';
    }
}

if (! function_exists('getSpecificGroupList')) {

    function getSpecificGroupList(array $groups)
    {
        $currentCompanyId = getCurrentCompany()->id;

        $finalGroups = [];

        /** @var CompanyGroup $mainParentCompany */
        $mainParentCompany = CompanyGroup::whereIn('name', $groups)->whereCompanyId($currentCompanyId)
            ->with('childGroups')->get();

        foreach ($mainParentCompany as $parentCompany) {
            $finalGroups[$parentCompany->id] = $parentCompany->name;
            foreach ($parentCompany->childGroups as $childGroup) {
                $finalGroups[$childGroup->id] = $childGroup->name;
                if ($childGroup->childGroups) {
                    getChildGroupsValue($childGroup->id, $finalGroups);
                }
            }
        }

        return $finalGroups;
    }
}

if (! function_exists('getRepoTileUrl')) {
    function getOriginalInvoiceDetails($model)
    {
        if (! empty($model->original_inv_no)) {
            return PurchaseTransaction::where('id', $model->original_inv_no)->first();
        }

        return null;
    }
}

if (! function_exists('getRepoTileUrl')) {
    function getRepoTileUrl($menu)
    {
        $companyFilters = getCompanyFilters();
        if ($menu == CompanyTile::TRIAL_BALANCE) {
            return route('company.trial-balance-report');
        }

        if ($menu == CompanyTile::BALANCE_SHEET) {
            $dates = isset($companyFilters['balance_sheet_date']) ? $companyFilters['balance_sheet_date'] : [];
            $date = ! empty($dates)
                ? \Carbon\Carbon::parse($dates)->format('Y-m-d')
                : \Carbon\Carbon::now()
                    ->startOfMonth()
                    ->format('Y-m-d');

            return route('company.balance-sheet-report', [
                'date' => $date,
                'details' => 0,
                'hideZeroBalance' => 0,
            ]);
        }

        if ($menu == CompanyTile::TRADING_PROFIT_LOSS_ACCOUNT) {
            $dates = isset($companyFilters['trading_profit_loss_report_date']) ? explode(' - ', $companyFilters['trading_profit_loss_report_date']) : [];
            $sDate = ! empty($dates) ? $dates[0] : \Carbon\Carbon::now()->startOfMonth()->format('Y-m-d');
            $eDate = ! empty($dates) ? $dates[1] : \Carbon\Carbon::now()->endOfMonth()->format('Y-m-d');

            return route('company.trading-profit-loss', [
                'start_date' => $sDate,
                'end_date' => $eDate,
                'stock_method' => '',
                'details' => false,
                'hideZeroBalance' => false,
            ]);
        }

        if ($menu == CompanyTile::CASH_FLOW) {
            return route('company.cash-flow-report');
        }

        if ($menu == CompanyTile::E_WAY_BILL) {
            return route('company.e-way-bill-table-index');
        }

        if ($menu == CompanyTile::E_INVOICE) {
            return route('company.e-invoice-table-index');
        }

        $dates = isset($companyFilters['gstr_1_report_date']) ? explode(' - ', $companyFilters['gstr_1_report_date']) : [];
        $sDate = ! empty($dates) ? $dates[0] : \Carbon\Carbon::now()->startOfMonth()->format('Y-m-d');
        $eDate = ! empty($dates) ? $dates[1] : \Carbon\Carbon::now()->endOfMonth()->format('Y-m-d');

        if ($menu == CompanyTile::GSTR_1) {
            return route('company.reports.gstr-1', ['start_date' => $sDate, 'end_date' => $eDate, 'filter_type' => 3]);
        }

        if ($menu == CompanyTile::GST_DASHBOARD_REPORT) {
            return route('company.gst-dashboard');
        }

        $dates = isset($companyFilters['gstr_3b_report_date']) ? explode(' - ', $companyFilters['gstr_3b_report_date']) : [];
        $sDate = ! empty($dates) ? $dates[0] : \Carbon\Carbon::now()->startOfMonth()->format('Y-m-d');
        $eDate = ! empty($dates) ? $dates[1] : \Carbon\Carbon::now()->endOfMonth()->format('Y-m-d');

        if ($menu == CompanyTile::GSTR_3B_SUMMARY) {
            return route('company.reports.gstr-3b-summary', ['start_date' => $sDate, 'end_date' => $eDate]);
        }

        if ($menu == CompanyTile::INPUT_TAX_REPORT) {
            return route('company.input-tax-register-report');
        }

        if ($menu == CompanyTile::OUTPUT_TAX_REPORT) {
            return route('company.output-tax-register-report');
        }

        if ($menu == CompanyTile::HSN_SUMMARY_OUTWARD) {
            return route('company.hsn-summary-outward-report');
        }

        if ($menu == CompanyTile::HSN_SUMMARY_INWARD) {
            return route('company.hsn-summary-inward-report');
        }

        if ($menu == CompanyTile::BILL_WISE_PROFIT_REPORT) {
            return route('company.bill-wise-profit-report');
        }

        if ($menu == CompanyTile::ITEM_WISE_PROFIT_REPORT) {
            return route('company.item-wise-profit-report');
        }

        if ($menu == CompanyTile::CUSTOMER_SUMMARY_REPORT) {
            return route('company.customer-summary-report');
        }
        if ($menu == CompanyTile::PARTY_WISE_SALE_REPORT) {
            return route('company.sale-report', ['type' => 'party-wise']);
        }
        if ($menu == CompanyTile::INVOICE_WISE_SALE_REPORT) {
            return route('company.sale-report', ['type' => 'invoice-wise']);
        }
        if ($menu == CompanyTile::PARTY_WISE_PURCHASE_REPORT) {
            return route('company.purchase-report', ['type' => 'party-wise']);
        }
        if ($menu == CompanyTile::INVOICE_WISE_PURCHASE_REPORT) {
            return route('company.purchase-report', ['type' => 'invoice-wise']);
        }
        if ($menu == CompanyTile::RECEIVABLE_AGEING_REPORT) {
            return route('company.ageing-report', ['type' => 'receivable']);
        }
        if ($menu == CompanyTile::PAYABLE_AGEING_REPORT) {
            return route('company.ageing-report', ['type' => 'payable']);
        }
        if ($menu == CompanyTile::RECEIVABLE_OUTSTANDING_REPORT) {
            return route('company.outstanding-report', ['type' => 'receivable']);
        }
        if ($menu == CompanyTile::PAYABLE_OUTSTANDING_REPORT) {
            return route('company.outstanding-report', ['type' => 'payable']);
        }
        if ($menu == CompanyTile::STOCK_SUMMARY_REPORT) {
            return route('company.report-stock');
        }
        if ($menu == CompanyTile::LEDGER) {
            return route('company.ledger-report');
        }
        if ($menu == CompanyTile::DAY_BOOK) {
            return route('company.day-book-report');
        }
        if ($menu == CompanyTile::CASH_BANK) {
            return route('company.cash-bank-report');
        }
        if ($menu == CompanyTile::BROKER) {
            return route('company.broker-report');
        }
        if ($menu == CompanyTile::PARTY_WISE_SALES_PURCHASE_REPORT) {
            return route('company.party-wise-sales-purchase-report');
        }
        if ($menu == CompanyTile::SUPPLIER_SUMMARY_REPORT) {
            return route('company.supplier-summary-report');
        }
        if ($menu == CompanyTile::CUSTOMER_MASTER_REPORT) {
            return route('company.customer-master-report');
        }
        if ($menu == CompanyTile::SUPPLIER_MASTER_REPORT) {
            return route('company.supplier-master-report');
        }
        if ($menu == CompanyTile::LOW_STOCK_REPORT) {
            return route('company.low-stock-report');
        }
    }
}

if (! function_exists('getReportShotCut')) {
    function getReportShotCut($menu)
    {

        if ($menu == CompanyTile::LEDGER) {
            return 'Shortcut Key : Shift + 1';
        }
        if ($menu == CompanyTile::STOCK_SUMMARY_REPORT) {
            return 'Shortcut Key : Shift + 6';
        }
        if ($menu == CompanyTile::TRIAL_BALANCE) {
            return 'Shortcut Key : Shift + 4';
        }
        if ($menu == CompanyTile::TRADING_PROFIT_LOSS_ACCOUNT) {
            return 'Shortcut Key : Shift + 2';
        }
        if ($menu == CompanyTile::BALANCE_SHEET) {
            return 'Shortcut Key : Shift + 3';
        }
        if ($menu == CompanyTile::CASH_FLOW) {
            return 'Shortcut Key : Shift + 5';
        }
        if ($menu == CompanyTile::GSTR_1) {
            return 'Shortcut Key : Shift + 8';
        }
        if ($menu == CompanyTile::GSTR_3B_SUMMARY) {
            return 'Shortcut Key : Shift + 7';
        }
        if ($menu == CompanyTile::TCS_LIABILITY_STATEMENT) {
            return 'Shortcut Key : Shift + 9';
        }
    }
}

if (! function_exists('getNotInArrayGroupList')) {
    function getNotInArrayGroupList(array $groups)
    {
        $currentCompanyId = getCurrentCompany()->id;

        $finalGroups = [];

        $itemGroups = getCompanyItemGroups();
        $itemGroups['profit_and_loss'] = Ledger::PROFIT_AND_LOSS;
        $groups = array_merge($itemGroups, $groups);

        /** @var CompanyGroup $mainParentCompany */
        $mainParentCompany = CompanyGroup::with('childGroups')->where('company_id', $currentCompanyId)
            ->whereNotIn('name', $groups)
            ->get();

        foreach ($mainParentCompany as $parentCompany) {
            $finalGroups[$parentCompany->id] = $parentCompany->name;
            foreach ($parentCompany->childGroups as $childGroup) {
                $finalGroups[$childGroup->id] = $childGroup->name;
                if ($childGroup->childGroups) {
                    getChildGroupsValue($childGroup->id, $finalGroups);
                }
            }
        }

        return $finalGroups;
    }
}

if (! function_exists('getAccountInvoicesLedgerGroups')) {

    function getAccountInvoicesLedgerGroups()
    {
        return getSpecificGroupList([Ledger::INCOME, Ledger::EXPENSE, Ledger::FIXED_ASSET, Ledger::TAXES_GST, Ledger::TAXES_TCS, Ledger::TAXES_TDS]);
    }
}

if (! function_exists('getCustomerSupplierLedgerGroups')) {

    function getCustomerSupplierLedgerGroups()
    {
        return getSpecificGroupList([Ledger::CUSTOMER, Ledger::SUPPLIER]);
    }
}

if (! function_exists('getIncomeExpenseLedgerGroups')) {

    function getIncomeExpenseLedgerGroups()
    {
        return getSpecificGroupList([Ledger::INCOME, Ledger::EXPENSE, Ledger::FIXED_ASSET]);
    }
}

if (! function_exists('remove_html_tags')) {

    function remove_html_tags($text)
    {
        $text = str_replace('<br>', "\n\n", $text);
        $clean_text = preg_replace('/<[^>]*>/', '', $text);

        return $clean_text;
    }
}

if (! function_exists('explodeDateForSearching')) {
    function explodeDateForSearching($date)
    {
        if (strstr($date, '-')) {
            return explode('-', $date);
        } elseif (strstr($date, '/')) {
            return explode('/', $date);
        } else {
            return [$date];
        }
    }
}
if (! function_exists('getRecordsFromSpecificModel')) {

    function getRecordsFromSpecificModel($model, $id)
    {
        return $model::whereId($id)->get();
    }
}

if (! function_exists('getPaymentReceiptModeList')) {
    function getPaymentReceiptModeList()
    {
        return PaymentMode::whereIn('use_for', [PaymentMode::RECEIPT, PaymentMode::BOTH])->orderBy('id', 'ASC')->pluck('name', 'id')->toArray();
    }
}

if (! function_exists('getPaymentModeForPayment')) {
    function getPaymentModeForPayment()
    {
        return PaymentMode::whereIn('use_for', [PaymentMode::PAYMENT, PaymentMode::BOTH])->orderBy('id', 'ASC')->pluck('name', 'id')->toArray();
    }
}

if (! function_exists('getEstimateQuoteTitles')) {
    function getEstimateQuoteTitles()
    {
        return EstimateQuoteTitle::whereCompanyId(getCurrentCompany()->id)->pluck('name', 'id')->toArray();
    }
}

if (! function_exists('getPurchaseOrderTitles')) {
    function getPurchaseOrderTitles()
    {
        return PurchaseOrderTitle::whereCompanyId(getCurrentCompany()->id)->pluck('name', 'id')->toArray();
    }
}

if (! function_exists('getTemporaryPDFFilePath')) {
    function getTemporaryPDFFilePath()
    {
        $disk = config('app.media_disc');
        $path = 'temp-pdf-uploads/'.Carbon::now()->format('d-m-Y').'/pdf/'.getCurrentCompany()->id.'/'.Carbon::now()->format('d-m-Y H:i:s').'/';
        if (! file_exists(Storage::disk($disk)->exists($path))) {
            Storage::disk($disk)->makeDirectory($path);
        }

        return $path;
    }
}

if (! function_exists('getTemporaryExcelFilePath')) {
    function getTemporaryExcelFilePath()
    {
        $disk = config('app.media_disc');
        $path = 'temp-pdf-uploads/'.Carbon::now()->format('d-m-Y').'/excel/'.getCurrentCompany()->id.'/'.Carbon::now()->format('d-m-Y H:i:s').'/';
        if (! file_exists(Storage::disk($disk)->exists($path))) {
            Storage::disk($disk)->makeDirectory($path);
        }

        return $path;
    }
}

if (! function_exists('getBulkInvoicesFilePath')) {

    function getBulkInvoicesFilePath()
    {
        $disk = config('app.media_disc');
        $path = 'bulk-export/'.Carbon::now()->format('d-m-Y').'/'.getCurrentCompany()->id.'/'.Carbon::now()->format('d-m-Y H:i:s').'/';
        if (! file_exists(Storage::disk($disk)->exists($path))) {
            Storage::disk($disk)->makeDirectory($path);
        }

        return $path;
    }
}

if (! function_exists('useHumanTime')) {
    function useHumanTime($createdAt)
    {
        return Carbon::parse($createdAt)->diffForHumans();
    }
}

if (! function_exists('getQuantityCountForEstimate')) {
    function getQuantityCountForEstimate($data, $type = 'estimate')
    {
        $count = 0;
        if ($type == 'sale') {
            foreach ($data as $transaction) {
                foreach ($transaction->saleItems as $item) {
                    if (isset($item->quantity) && is_numeric($item->quantity)) {
                        // $count += intval($item->quantity);
                        $count += $item->quantity;
                    }
                }
            }
        } elseif ($type == 'delivery_challan') {
            foreach ($data as $item) {
                if (isset($item->quantity) && is_numeric($item->quantity)) {
                    // $count += intval($item->quantity);
                    $count += $item->quantity;
                }
            }
        } else {
            foreach ($data as $item) {
                if (isset($item->quantity) && is_numeric($item->quantity)) {
                    // $count += intval($item->quantity);
                    $count += $item->quantity;
                }
            }
        }

        return $count;
    }
}

if (! function_exists('getQuantityCountForPurchaseOrder')) {
    function getQuantityCountForPurchaseOrder($data, $type)
    {
        if ($type == 'purchase') {
            $count = 0;
            foreach ($data as $transaction) {
                if ($transaction->deleted_at == null) {
                    if (isset($transaction->purchaseTransactionItems)) {
                        foreach ($transaction->purchaseTransactionItems as $item) {
                            if (isset($item->quantity) && is_numeric($item->quantity)) {
                                // $count += intval($item->quantity);
                                $count += $item->quantity;
                            }
                        }
                    }
                }
            }

            return $count;
        } else {
            $count = 0;
            foreach ($data as $item) {
                if (isset($item->quantity) && is_numeric($item->quantity)) {
                    // $count += intval($item->quantity);
                    $count += $item->quantity;
                }
            }

            return $count;
        }

        return null;
    }
}

if (! function_exists('getQuantityCountForPurchaseOrder')) {
    function getQuantityCountForPurchaseOrder($data)
    {
        $count = 0;
        foreach ($data as $transaction) {
            foreach ($transaction->saleItems as $item) {
                if (isset($item->quantity) && is_numeric($item->quantity)) {
                    // $count += intval($item->quantity);
                    $count += $item->quantity;
                }
            }
        }

        return $count;
    }
}

if (! function_exists('getAccountingInvoiceTotalCountForEstimate')) {
    function getAccountingInvoiceTotalCountForEstimate($data, $type = 'estimate')
    {
        $count = 0;
        if ($type == 'sale') {
            foreach ($data as $transaction) {
                foreach ($transaction->saleLedgers as $ledger) {
                    if (isset($ledger->total) && is_numeric($ledger->total)) {
                        $count += intval($ledger->total);
                    }
                }
            }
        } else {
            foreach ($data as $ledger) {
                if (isset($ledger->total) && is_numeric($ledger->total)) {
                    $count += intval($ledger->total);
                }
            }
        }

        return $count;
    }
}

if (! function_exists('getAccountingInvoiceTotalCountForPurchaseOrder')) {
    function getAccountingInvoiceTotalCountForPurchaseOrder($data, $type)
    {
        if ($type == 'purchase') {
            $count = 0;
            foreach ($data as $transaction) {
                if ($transaction->deleted_at == null) {
                    foreach ($transaction->purchaseTransactionLedger as $ledger) {
                        if (isset($ledger->total) && is_numeric($ledger->total)) {
                            $count += intval($ledger->total);
                        }
                    }
                }
            }

            return $count;
        } else {
            $count = 0;
            foreach ($data as $ledger) {
                if (isset($ledger->total) && is_numeric($ledger->total)) {
                    $count += intval($ledger->total);
                }
            }

            return $count;
        }

        return null;
    }
}

if (! function_exists('getAccountingInvoiceTotalCountForPurchaseOrder')) {
    function getAccountingInvoiceTotalCountForPurchaseOrder($data, $type = 'purchase-order')
    {
        $count = 0;

        foreach ($data as $ledger) {
            if (isset($ledger->total) && is_numeric($ledger->total)) {
                $count += intval($ledger->total);
            }
        }

        return $count;
    }
}

if (! function_exists('getEstimateQuoteDocumentNumbers')) {
    function getEstimateQuoteDocumentNumbers($partyId, $selectedIds = [])
    {
        return IncomeEstimateQuoteTransaction::wherePartyLedgerId($partyId)
            ->where(function ($query) use ($selectedIds) {
                $query->whereNotIn('status', [IncomeEstimateQuoteTransaction::FULLY_INVOICED, IncomeEstimateQuoteTransaction::EXPIRED])
                    ->orWhereIn('id', $selectedIds);
            })
            ->pluck('document_number', 'id')
            ->toArray();
    }
}

if (! function_exists('getDeliveryChallanDocumentNumbers')) {
    function getDeliveryChallanDocumentNumbers($partyId, $dcSelectedIds = [])
    {
        return DeliveryChallanTransaction::wherePartyLedgerId($partyId)
            ->where(function ($q) use ($dcSelectedIds) {
                $q->where('status', '!=', DeliveryChallanTransaction::FULLY_INVOICED)
                    ->orWhereIn('id', $dcSelectedIds);
            })
            ->pluck('challan_number', 'id')
            ->toArray();
    }
}

if (! function_exists('getPurchaseOrderNumbers')) {
    function getPurchaseOrderNumbers($partyId, $selectedIds = [])
    {
        return PurchaseOrderTransaction::wherePartyLedgerId($partyId)
            ->where(function ($q) use ($selectedIds) {
                $q->where('status', '!=', PurchaseOrderTransaction::FULLY_INVOICED)
                    ->orWhereIn('id', $selectedIds);
            })
            ->pluck('full_order_number', 'id')->toArray();
    }
}

if (! function_exists('countOfUnreadNotifications')) {
    function countOfUnreadNotifications()
    {
        return Notification::whereUserId(getLoginUser()->id)->whereNull('read_at')->count();
    }
}

if (! function_exists('returnFileThumbnailForNotification')) {
    function returnFileThumbnailForNotification($fileName): string
    {
        $extension = pathinfo($fileName, PATHINFO_EXTENSION);

        return match ($extension) {
            'pdf' => asset('assets/images/pdf.png'),
            'xlsx' => asset('assets/images/xlse.png'),
        };
    }
}

if (! function_exists('disableReturnFileThumbnailForNotification')) {
    function disableReturnFileThumbnailForNotification($fileName): string
    {
        $extension = pathinfo($fileName, PATHINFO_EXTENSION);

        return match ($extension) {
            'pdf' => asset('assets/images/disable-pdf.png'),
            'xlsx' => asset('assets/images/disable-xlse.png'),
        };
    }
}

if (! function_exists('consolidatingItemsToInvoice')) {
    function consolidatingItemsToInvoice($consolidatingItems)
    {
        $qtyTotal = explode(',', $consolidatingItems);

        // Remove zero values
        $total = array_filter($qtyTotal, function ($value) {
            return $value != 0;
        });

        return implode(',', $total);
    }
}

if (! function_exists('getCompanyPdfFormat')) {
    function getCompanyPdfFormat($isFormatNumber = false, $replaceThermalFormat = true)
    {
        $companySetting = CompanySetting::where('key', 'pdf_format')->first();

        if ($isFormatNumber && ! empty($companySetting)) {
            return $companySetting->value;
        }
        if ($isFormatNumber && empty($companySetting)) {
            return (string) CompanySetting::A4;
        }

        if ((! empty($companySetting) && $companySetting->value == CompanySetting::THERMAL_PRINT) && $replaceThermalFormat) {
            return CompanySetting::PDF_FORMAT[CompanySetting::A4];
        }

        return ! empty($companySetting) ? CompanySetting::PDF_FORMAT[$companySetting->value] : (string) CompanySetting::PDF_FORMAT[CompanySetting::A4];
    }
}

if (! function_exists('getCompanyExpensePDFFormat')) {
    function getCompanyExpensePDFFormat($isFormatNumber = false)
    {
        $companySetting = CompanySetting::where('key', 'expense_pdf_format')->first();

        if ($isFormatNumber && ! empty($companySetting)) {
            return $companySetting->value;
        }
        if ($isFormatNumber && empty($companySetting)) {
            return CompanySetting::A4;
        }

        return ! empty($companySetting) ? CompanySetting::PDF_FORMAT[$companySetting->value] : CompanySetting::PDF_FORMAT[CompanySetting::A4];
    }
}

if (! function_exists('getCompanyEstimatePDFFormat')) {
    function getCompanyEstimatePDFFormat($isFormatNumber = false)
    {
        $companySetting = CompanySetting::where('key', 'estimate_pdf_format')->first();

        if ($isFormatNumber && ! empty($companySetting)) {
            return $companySetting->value;
        }
        if ($isFormatNumber && empty($companySetting)) {
            return CompanySetting::A4;
        }

        return ! empty($companySetting) ? CompanySetting::PDF_FORMAT[$companySetting->value] : CompanySetting::PDF_FORMAT[CompanySetting::A4];
    }
}

if (! function_exists('getCompanyDeliveryChallanPDFFormat')) {
    function getCompanyDeliveryChallanPDFFormat($isFormatNumber = false)
    {
        $companySetting = CompanySetting::where('key', 'delivery_challan_pdf_format')->first();

        if ($isFormatNumber && ! empty($companySetting)) {
            return $companySetting->value;
        }
        if ($isFormatNumber && empty($companySetting)) {
            return CompanySetting::A4;
        }

        return ! empty($companySetting) ? CompanySetting::PDF_FORMAT[$companySetting->value] : CompanySetting::PDF_FORMAT[CompanySetting::A4];
    }
}

if (! function_exists('getItemMastersUnitsList')) {
    function getItemMastersUnitsList($itemMasterID)
    {
        $item = ItemMaster::with('model')
            ->whereId($itemMasterID)
            ->first();

        $unitOFArray = UnitOfMeasurement::whereIn('id', [$item->model->unit_of_measurement, $item->model->secondary_unit_of_measurement])->pluck('name', 'id')->toArray();

        return $unitOFArray;
    }
}

if (! function_exists('getAllItemMasters')) {
    function getAllItemMasters()
    {
        return ItemMaster::with('model', 'group')->get();
    }
}

if (! function_exists('printCustomPDFLabelsForSale')) {
    function printCustomPDFLabelsForSale()
    {
        return InvoicesLabel::where('transaction_type', 1)->where('is_custom_label', 1)->pluck('label_value', 'label_name')->toArray();
    }
}

if (! function_exists('printCustomPDFLabelsForExpense')) {
    function printCustomPDFLabelsForExpense()
    {
        return InvoicesLabel::where('transaction_type', 4)->where('is_custom_label', 1)->pluck('label_value', 'label_name')->toArray();
    }
}

if (! function_exists('printCustomPDFLabelsForEstimate')) {
    function printCustomPDFLabelsForEstimate()
    {
        return InvoicesLabel::where('transaction_type', 2)->where('is_custom_label', 1)->pluck('label_value', 'label_name')->toArray();
    }
}

if (! function_exists('printCustomPDFLabelsForDeliveryChallan')) {
    function printCustomPDFLabelsForDeliveryChallan()
    {
        return InvoicesLabel::where('transaction_type', CompanySetting::DELIVERY_CHALLAN_PRINT)->where('is_custom_label', 1)->pluck('label_value', 'label_name')->toArray();
    }
}

if (! function_exists('isPositive')) {
    function isPositive($number)
    {
        if ($number < 0) {
            return false;
        }

        return true;
    }
}

if (! function_exists('dateFormateChangeForAPILedgerReport')) {
    function dateFormateChangeForAPILedgerReport($date)
    {
        return Carbon::createFromFormat('d-m-y', $date)->format('d-m-Y');
    }
}

if (! function_exists('getEmailIcon')) {

    function getEmailIcon(): string
    {
        return asset('assets/images/email.png');
    }
}

if (! function_exists('calculateCreditPeriodDate')) {
    function calculateCreditPeriodDate($creditPeriod, $creditPeriodType, $invoiceDate)
    {
        if (! empty($creditPeriod)) {
            if ($creditPeriodType == Customer::CREDIT_PERIOD_TYPE_MONTH) {
                $data['dueDate'] = Carbon::parse($invoiceDate)->addMonths($creditPeriod);
            } elseif ($creditPeriodType == Customer::CREDIT_PERIOD_TYPE_DAY) {
                $data['dueDate'] = Carbon::parse($invoiceDate)->adddays($creditPeriod);
            }
        }

        return $data['dueDate'] ?? null;
    }
}

if (! function_exists('getSalePdfName')) {
    function getSalePdfName($transaction): string
    {
        return 'Sale_'.$transaction->full_invoice_number.'_'.$transaction->customer->name;
    }
}

if (! function_exists('shippingIntraStateArray')) {
    function shippingIntraStateArray()
    {
        return [
            // old types
            'Sale Intra State Taxable',
            'Sale Intra State Exempt',
            'Sale Intra State Nilrated',
            // new types
            'Intrastate Sales Taxable',
            'Intrastate Sales Exempt',
            'Intrastate Sales Nil Rated',
            'Deemed Export - Intrastate',
            //purchase tax classification
            'Intrastate Purchase Taxable',
            'Intrastate Purchase Exempt',
            'Intrastate Purchase Nil Rated',
            'Intrastate Purchase URD Taxable',
            'Intrastate Purchase URD Exempt',
            'Intrastate Purchase URD Nil Rated',
        ];
    }
}

if (! function_exists('shippingInterStateArray')) {
    function shippingInterStateArray()
    {
        return [
            // old types
            'Sale Inter State Taxable',
            'Sale Inter State Exempt',
            'Sale Inter State Nilrated',
            // new types
            'Interstate Sales Taxable',
            'Interstate Sales Exempt',
            'Interstate Sales Nil Rated',
            'Export Sales Taxable',
            'Export Sales Exempt',
            'Export Sales Nil Rated',
            'Export Sales under LUT/Bond',
            'Sales to SEZ Taxable',
            'Sales to SEZ Exempt',
            'Sales to SEZ Nil Rated',
            'Sales to SEZ under LUT/Bond',
            'Deemed Export - Interstate',
            //purchase taxes
            'Interstate Purchase Taxable',
            'Interstate Purchase Exempt',
            'Interstate Purchase Nil Rated',
            'Interstate Purchase URD Taxable',
            'Interstate Purchase URD Exempt',
            'Interstate Purchase URD Nil Rated',
            'Purchase - Import of goods',
            'Purchase - Import of Service',
        ];
    }
}

if (! function_exists('getDiscountSymbol')) {
    function getDiscountSymbol(): array
    {
        return [
            '1' => getCurrencySymbol() ?: '₹',
            '2' => '%',
        ];
    }
}

if (! function_exists('getPrintPdfCurrencySymbol')) {
    function getPrintPdfCurrencySymbol($companyID = null)
    {
        if (! empty($companyID)) {
            $settings = DB::table('company_settings')
                ->where('company_id', $companyID)
                ->whereIn('key', ['default_currency', 'currency_symbol'])
                ->get();

            // Convert settings to associative array
            foreach ($settings as $setting) {
                $companySettings[$setting->key] = $setting->value;
            }
        } else {
            $currentCompany = getCurrentCompany();
            if ($currentCompany) {
                $settings = CompanySetting::where('company_id', $currentCompany->id)
                    ->whereIn('key', ['default_currency', 'currency_symbol'])
                    ->pluck('value', 'key');

                $companySettings = $settings->toArray();
            }
        }

        $currencyOptions = getCurrencyOptions();

        if (! isset($companySettings['currency_symbol']) || ! $companySettings['currency_symbol']) {
            return '';
        }

        return getCurrencyIcons()[$companySettings['default_currency'] ?? $currencyOptions['in']] ?? $currencyOptions['in'];
    }
}

if (! function_exists('getPrintPdfCurrencyInWords')) {
    function getPrintPdfCurrencyInWords()
    {
        $currentCompany = getCurrentCompany();
        if ($currentCompany) {
            $settings = CompanySetting::where('company_id', $currentCompany->id)
                ->whereIn('key', ['default_currency', 'currency_symbol'])
                ->pluck('value', 'key');

            $companySettings = $settings->toArray();
        }
        $currencyOptions = getCurrencyInWords();

        return getCurrencyInWords()[$companySettings['default_currency'] ?? 'in'] ?? $currencyOptions['in'];
    }
}

if (! function_exists('getSortingValues')) {
    function getSortingValues($sorting)
    {
        $values = [];
        $defaultSortingParts = explode('|', $sorting);

        foreach ($defaultSortingParts as $part) {
            $sortingParts = explode(',', $part, 2); // Allow optional direction
            $column = trim($sortingParts[0]);
            $direction = isset($sortingParts[1]) ? trim($sortingParts[1]) : 'desc';

            $values[$column] = $direction;
        }

        return $values;
    }
}

if (! function_exists('getBusinessCategories')) {
    function getBusinessCategories()
    {
        return BusinessCategory::orderBy('category_name', 'ASC')->pluck('category_name', 'id');
    }
}

if (! function_exists('checkThisDeleted')) {
    function checkThisDeleted($data)
    {
        $currentURL = $data['url'];

        return last(explode('/', $currentURL));
    }
}

if (! function_exists('replaceSpecialCharacters')) {
    function replaceSpecialCharacters($fileName)
    {
        $specialCharacters = ['/', '\\', ':', '*', '?', '"', '<', '>', '|', "'", '~', '`', '!', '@', '#', '$', '%', '^', '&', '(', ')', '[', ']', '{', '}', '+', '=', ',', '.', ';'];
        $fileName = str_replace($specialCharacters, '_', $fileName);

        return $fileName;
    }
}

if (! function_exists('getUnitOfMeasurement')) {
    function getUnitOfMeasurement()
    {
        static $unitName;
        if (empty($unitName)) {
            $unitName = UnitOfMeasurement::get()->pluck('full_name', 'id')->toArray();
        }

        return $unitName;
    }
}
if (! function_exists('getGstTaxRate')) {
    function getGstTaxRate()
    {
        static $gstTaxeRate;
        if (empty($gstTaxeRate)) {
            $gstTaxeRate = GstTax::get()->pluck('tax_rate', 'id')->toArray();
        }

        return $gstTaxeRate;
    }
}

if (! function_exists('getTransactionsLockDate')) {
    function getTransactionsLockDate()
    {
        $lockTransaction = LockTransaction::first();

        $lockDates = [
            LockTransaction::INCOME => null,
            LockTransaction::EXPENSE => null,
            LockTransaction::RECEIPT => null,
            LockTransaction::PAYMENT => null,
            LockTransaction::JOURNAL => null,
        ];
        if ($lockTransaction) {
            if ($lockTransaction->all_transactions) {
                $lockDates = array_fill_keys(array_keys($lockDates), $lockTransaction->all_transactions_unlock_date);
            } else {
                $lockDates[LockTransaction::INCOME] = $lockTransaction->income ? $lockTransaction->income_unlock_date : null;
                $lockDates[LockTransaction::EXPENSE] = $lockTransaction->expense ? $lockTransaction->expense_unlock_date : null;
                $lockDates[LockTransaction::RECEIPT] = $lockTransaction->receipt ? $lockTransaction->receipt_unlock_date : null;
                $lockDates[LockTransaction::PAYMENT] = $lockTransaction->payment ? $lockTransaction->payment_unlock_date : null;
                $lockDates[LockTransaction::JOURNAL] = $lockTransaction->journal ? $lockTransaction->journal_unlock_date : null;
            }
        }

        return $lockDates;
    }
}

if (! function_exists('dockerEnabled')) {
    function dockerEnabled(): bool
    {
        return config('services.docker.pdf_download');
    }
}

if (! function_exists('getDecimalPlacesForRate')) {
    function getDecimalPlacesForRate($itemId)
    {
        $item = ItemMaster::with('model')->find($itemId);

        return GetStepForDecimalPlaceAction::run($item?->model?->decimal_places_for_rate);
    }
}

if (! function_exists('disableAuditingLedgerAndMaster')) {
    function disableAuditingLedgerAndMaster()
    {
        Ledger::disableAuditing();
        ExpenseCreditNoteTransactionMaster::disableAuditing();
        ExpenseDebitNoteTransactionMaster::disableAuditing();
        ExpenseReturnTransactionMaster::disableAuditing();
        ExpenseTransactionMaster::disableAuditing();
        IncomeCreditNoteTransactionMaster::disableAuditing();
        IncomeDebitNoteTransactionMaster::disableAuditing();
        IncomeEstimateQuoteTransactionMaster::disableAuditing();
        IncomeReturnTransactionMaster::disableAuditing();
        IncomeTransactionMaster::disableAuditing();
        JournalTransactionMaster::disableAuditing();
        PaymentTransaction::disableAuditing();
        PurchaseOrderTransactionMaster::disableAuditing();
        ReceiptTransaction::disableAuditing();
        CompanyGroup::disableAuditing();
        DeliveryChallanTransactionMaster::disableAuditing();
    }
}

if (! function_exists('enableAuditingLedgerAndMaster')) {
    function enableAuditingLedgerAndMaster()
    {
        Ledger::enableAuditing();
        ExpenseCreditNoteTransactionMaster::enableAuditing();
        ExpenseDebitNoteTransactionMaster::enableAuditing();
        ExpenseReturnTransactionMaster::enableAuditing();
        ExpenseTransactionMaster::enableAuditing();
        IncomeCreditNoteTransactionMaster::enableAuditing();
        IncomeDebitNoteTransactionMaster::enableAuditing();
        IncomeEstimateQuoteTransactionMaster::enableAuditing();
        IncomeReturnTransactionMaster::enableAuditing();
        IncomeTransactionMaster::enableAuditing();
        JournalTransactionMaster::enableAuditing();
        PaymentTransaction::enableAuditing();
        PurchaseOrderTransactionMaster::enableAuditing();
        ReceiptTransaction::enableAuditing();
        CompanyGroup::enableAuditing();
        DeliveryChallanTransactionMaster::enableAuditing();
    }
}

if (! function_exists('isLockTransaction')) {
    function isLockTransaction($type, $transactionDate)
    {
        $lockDate = getTransactionsLockDate()[$type] ?? null;
        $formats = ['d-m-Y', 'd-m-y', 'Y-m-d', 'y-m-d'];

        if (! $transactionDate instanceof \Carbon\Carbon) {
            foreach ($formats as $format) {
                try {
                    $transactionDate = Carbon::createFromFormat($format, $transactionDate)->format('Y-m-d');
                    break;
                } catch (\Exception $e) {
                    continue;
                }
            }
        }
        $isLocked = ! empty($lockDate) && Carbon::parse($lockDate)->greaterThanOrEqualTo($transactionDate);

        return $isLocked;
    }
}

if (! function_exists('isLedgerUsedInAdditionCharges')) {
    function isLedgerUsedInAdditionCharges($ledgerId)
    {
        $additionalChargeModels = [
            AdditionalChargesForSalesTransaction::class,
            AdditionalChargesForSalesReturnTransaction::class,
            AdditionalChargesForIncomeDebitNoteTransaction::class,
            AdditionalChargesForIncomeCreditNoteTransaction::class,
            AdditionalChargesForIncomeEstimateQuoteTransaction::class,
            AdditionalChargesForPurchaseOrderTransaction::class,
            AdditionalChargesForPurchaseTransaction::class,
            AdditionalChargesForPurchaseReturnTransaction::class,
            AdditionalChargesForExpenseDebitNoteTransaction::class,
            AdditionalChargesForExpenseCreditNoteTransaction::class,
        ];

        foreach ($additionalChargeModels as $model) {
            if ($model::where('ledger_id', $ledgerId)->exists()) {
                return true;
            }
        }

        return false;
    }
}

if (! function_exists('prepareAddLess')) {
    function prepareAddLess($addLessItems)
    {
        return $addLessItems
            ->map(function ($item) {
                $ledgerName = $item->ledger->name;

                if ($item->type == 2) {
                    $ledgerName .= ' ('.$item->value.'%)';
                }

                return [
                    'ledger_name' => $ledgerName,
                    'amount' => $item->total,
                    'is_show_in_print' => $item->is_show_in_print,
                ];
            })
            ->toArray();
    }
}

if (! function_exists('prepareAdditionalCharges')) {
    function prepareAdditionalCharges($additionalCharges)
    {
        return $additionalCharges->map(function ($item) {
            $ledgerName = $item->ledger->name;
            if ($item->charge_type == 2) {
                $ledgerName .= ' ('.$item->value.'%)';
            }

            return [
                'ledger_name' => $ledgerName,
                'amount' => $item->total_without_tax,
            ];
        })->toArray();
    }
}

if (! function_exists('getCurrentPlanData')) {
    function getCurrentPlanData()
    {
        $data = [];
        $account = getAccount();

        if (! $account || ! empty(isFranchiseCompany())) {
            return $data;
        }

        if (! activeSubscription()) {
            return $data;
        }

        $subscription = Subscription::whereAccountId($account->id)
            ->where('status', Subscription::ACTIVE)
            ->latest()
            ->first();

        if (! $subscription) {
            return $data;
        }

        $meta = json_decode($subscription->meta, true) ?? [];
        $plan = $subscription->plan;
        $billingDate = $subscription->start_date;

        $companies = Company::where('account_id', $account->id);
        $totalCompaniesCount = $companies->count();
        $newCompaniesCount = $companies->whereDate('created_at', '>', $billingDate)->count();
        $extraCompanies = max(0, $totalCompaniesCount - ($meta['plan_companies'] + $meta['extra_companies']));

        $users = User::where('account_id', $account->id)
            ->whereHas('roles', function ($query) {
                $query->where('name', Role::CLIENT_USER);
            });
        $totalUserCount = $users->count();
        $newUsersCount = $users->whereDate('created_at', '>', $billingDate)->count();

        $extraUsers = max(0, $totalUserCount - ($meta['plan_users'] + $meta['extra_users']));

        if ($meta['plan_companies'] == -1) {
            $totalCompaniesCount = -1;
        }

        if ($meta['plan_users'] == -1) {
            $totalUserCount = -1;
        }

        $data = [
            'plan_id' => $plan->id ?? null,
            'plan_name' => $meta['name'] ?? null,
            'subscription_id' => $subscription->id,
            'plan_start_at' => $subscription->start_date,
            'plan_end_at' => $subscription->end_date,
            'plan_amount' => $meta['plan_price'] ?? 0,
            'plan_frequency' => $meta['plan_frequency'] ?? null,
            'active_users' => $subscription->active_users ?? 0,
            'plan_limit_companies' => $subscription->active_companies ?? 0,
            'new_companies' => $newCompaniesCount,
            'plan_limit_users' => $subscription->active_users ?? 0,
            'new_users' => $newUsersCount,
            'meta' => $meta,
            'plan_users' => $meta['plan_users'] ?? 0,
            'total_users' => $totalUserCount,
            'user_limit' => $meta['plan_users'] + $meta['extra_users'],
            'extra_users' => $extraUsers,
            'plan_companies' => $meta['plan_companies'] ?? 0,
            'total_companies' => $totalCompaniesCount,
            'company_limit' => $meta['plan_companies'] + $meta['extra_companies'],
            'extra_companies' => $extraCompanies,
        ];

        return $data;
    }
}
if (! function_exists('dockerTime')) {
    function dockerTime()
    {
        return config('services.docker.time');
    }
}

if (! function_exists('companyDispatchAddress')) {
    function companyDispatchAddress()
    {
        static $dispatchAddress;

        if (! isset($dispatchAddress)) {
            $dispatchAddress = getCurrentCompany()->dispatchAddress()->first();

            if (empty($dispatchAddress)) {
                $dispatchAddress = '';
            }
        }

        return (! empty($dispatchAddress)) ? $dispatchAddress : null;
    }
}

if (! function_exists('isOldDevice')) {
    function isOldDevice($deviceID)
    {
        return strlen($deviceID) == 12 ? false : true;
    }
}

if (! function_exists('getGstReturnPeriodMonths')) {
    function getGstReturnPeriodMonths($startDate)
    {
        $start = Carbon::parse($startDate);
        $end = Carbon::now()->endOfMonth();

        $months = [];
        while ($start->lte($end)) {
            $months[] = $start->format('mY');
            $start->addMonth();
        }

        return $months;
    }
}

if (! function_exists('getGstDashboardLogin')) {
    function getGstDashboardLogin($field)
    {
        $gstrLogin = GstrLogin::whereCompanyId(getCurrentCompany()->id)->latest()->first();

        if (! empty($gstrLogin)) {
            $data = $gstrLogin->toArray();

            return $data[$field];
        }

        return null;
    }
}
if (! function_exists('checkFinancialYearForGST')) {
    function checkFinancialYearForGST()
    {
        $data = GstDashboardData::with(['saleGstR1Data'])->orderBy(DB::raw("STR_TO_DATE(CONCAT('01', month), '%d%m%Y')"))->first();
        $month = Carbon::createFromFormat('mY', $data->month ?? Carbon::now()->format('mY'))->format('m');

        if ($month > 3) {
            $endYear = date('Y', strtotime('+1 year'));
            $startYear = date('Y');
        } else {
            $endYear = date('Y');
            $startYear = date('Y', strtotime('-1 year'));
        }

        $bookStartDate = Carbon::createFromFormat('mY', $data->month ?? Carbon::now()->format('mY'))->format('Y-m-d');
        $bookStartYear = getFinancialYearStart($bookStartDate);

        $financialYears = [];
        $loopCount = $endYear - $bookStartYear ?: 1;
        for ($i = 1; $i <= $loopCount; $i++) {
            $financialYears[($startYear - $i + 1).' - '.($endYear - $i + 1)] = ($startYear - $i + 1).' - '.($endYear - $i + 1);
        }

        return $financialYears;
    }
}

if (! function_exists('getLastStatusUpdateGstDate')) {
    function getLastStatusUpdateGstDate($field)
    {
        $gstData = GstDashboardData::whereCompanyId(getCurrentCompany()->id)->latest()->first();

        if (! empty($gstData)) {
            return $gstData->$field;
        }

        return null;

    }
}

if (! function_exists('gstFillingStatus')) {
    function gstFillingStatus($field)
    {
        $gstData = GstFiling::whereCompanyId(getCurrentCompany()->id)->latest()->first();

        if (! empty($gstData)) {
            return $gstData->$field;
        }

        return Carbon::now()->format('mY');
    }
}
