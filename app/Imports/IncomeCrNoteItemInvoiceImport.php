<?php

namespace App\Imports;

use App\Actions\Imports\CalculationForIncomeItemInvoiceAction;
use App\Actions\Income\CheckItemUintOfMeasurement;
use App\Actions\Validation\CheckValidationsAction;
use App\Models\GstTax;
use App\Models\IncomeCreditNoteTransaction;
use App\Models\Ledger;
use App\Models\LockTransaction;
use App\Models\Master\Bank;
use App\Models\Master\Broker;
use App\Models\Master\Cash;
use App\Models\Master\Customer;
use App\Models\Master\Expense;
use App\Models\Master\FixedAsset;
use App\Models\Master\Income;
use App\Models\Master\IncomeCreditNoteTransactionMaster;
use App\Models\Master\ItemMaster;
use App\Models\Master\Supplier;
use App\Models\Master\TaxesTcs;
use App\Models\Master\TaxesTds;
use App\Models\Master\TdsReceivable;
use App\Models\Master\Transport;
use App\Models\PaymentMode;
use App\Models\SaleTransaction;
use App\Models\UnitOfMeasurement;
use App\Repositories\v1\IncomeCNTransactionRepository;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

/**
 * Class IncomeCrNoteItemInvoiceImport
 */
class IncomeCrNoteItemInvoiceImport implements SkipsEmptyRows, ToCollection, WithHeadingRow, WithMultipleSheets
{
    use Importable;

    public $importErrors = [];

    public $company;

    public $fixDigit;

    public $roundMethod;

    public $totalInvoice;

    public $notImportedInvoice;

    public function sheets(): array
    {
        return [
            0 => $this,
        ];
    }

    public function collection(Collection $collection)
    {
        if ($collection->isEmpty()) {
            $this->importErrors['empty_excel'] = "The excel sheet doesn't have any transactions.";
        } else {
            $transactions = [];
            $isCompanyGstApplicable = isCompanyGstApplicable();
            $this->company = getCurrentCompany();
            $companyId = $this->company->id;
            $this->fixDigit = getCompanyFixedDigitNumber();
            $creditNoteTransactionTransactionMaster = IncomeCreditNoteTransactionMaster::first();
            $this->roundMethod = $creditNoteTransactionTransactionMaster->round_off_method ?? Ledger::NORMAL_ROUNDING;

            if ($this->company->is_gst_applicable == 1) {
                $transactions = $this->dataArray($collection, $transactions);
            } else {
                $transactions = $this->dataArrayForNonGstApplicable($collection, $transactions);
            }

            $currentTime = Carbon::now();
            $iteration = 0;
            foreach ($transactions as $creditNoteNo => $transaction) {
                // this is for next invoice number issue fix
                $transaction['created_at'] = $currentTime->copy()->addSeconds($iteration);
                $transaction['updated_at'] = $currentTime->copy()->addSeconds($iteration);
                $iteration++;

                enableDeletedScope();
                $creditNoteTransaction = IncomeCreditNoteTransaction::whereFullInvoiceNumber($creditNoteNo)->financialYearDate()->first();
                if (! empty($creditNoteTransaction)) {
                    $this->importErrors[$creditNoteNo][] = "This credit note no {$creditNoteNo} already exists";

                    continue;
                }
                disableDeletedScope();

                $isTransactionCreate = true;

                if (checkDateIsCurrentFinancialYear($transaction['date'])) {
                    $this->importErrors[$creditNoteNo][] = 'This transaction credit note date does not have a current financial year date.';
                    $isTransactionCreate = false;
                }
                if (empty($transaction['main_classification_nature_type']) && $this->company->is_gst_applicable == 1) {
                    $this->importErrors[$creditNoteNo][] = 'Classification nature type does not exist in the excel file.';
                    $isTransactionCreate = false;
                }
                if (empty($transaction['date'])) {
                    $this->importErrors[$creditNoteNo][] = 'Credit note date does not exist in the excel file.';
                    $isTransactionCreate = false;
                }
                if ($transaction['date'] == false) {
                    $this->importErrors[$creditNoteNo][] = 'Credit note date formate is invalid, please enter a dd-mm-yyyy format.';
                    $isTransactionCreate = false;
                }
                if (! empty($transaction['date']) && $transaction['date'] !== false && ! checkDateIsCurrentFinancialYear($transaction['date'])) {
                    $transactionDate = Carbon::parse($transaction['date'])->format('Y-m-d');
                    $isLocked = isLockTransaction(LockTransaction::INCOME, $transactionDate) ?? false;
                    if ($isLocked) {
                        $lockDate = Carbon::parse(getTransactionsLockDate()[LockTransaction::INCOME])->format('d-m-Y');
                        $this->importErrors[$creditNoteNo][] = 'This transaction date is locked please add date after '.$lockDate;
                        $isTransactionCreate = false;
                    }
                }
                if (! empty($transaction['party_phone_number'])) {
                    if (empty($transaction['region_iso']) || empty($transaction['region_code'])) {
                        $this->importErrors[$creditNoteNo][] = 'Region ISO code or region code does not exist in the Excel file.';
                        $isTransactionCreate = false;
                    }

                    // Validate phone number length if the region is India
                    if (! empty($transaction['region_iso']) && strtoupper($transaction['region_iso']) === 'IN') {
                        if (! preg_match('/^\d{10}$/', $transaction['party_phone_number'])) {
                            $this->importErrors[$creditNoteNo][] = 'Phone number must be exactly 10 digits for India.';
                            $isTransactionCreate = false;
                        }
                    }
                }
                if ($transaction['original_inv_date'] !== null && $transaction['original_inv_date'] == false) {
                    $this->importErrors[$creditNoteNo][] = 'Original invoice date formate is invalid, please enter a dd-mm-yyyy format.';
                    $isTransactionCreate = false;
                }
                if ($transaction['transport_details']['transporter_document_date'] !== null && $transaction['transport_details']['transporter_document_date'] === false) {
                    $this->importErrors[$creditNoteNo][] = 'Document date format is invalid. Please enter a dd-mm-yyyy format.';
                    $isTransactionCreate = false;
                }
                if ($transaction['other_details']['po_date'] !== null && $transaction['other_details']['po_date'] === false) {
                    $this->importErrors[$creditNoteNo][] = 'PO date formate is invalid, please enter a dd-mm-yyyy format.';
                    $isTransactionCreate = false;
                }

                $customer = Ledger::whereRaw('lower(name) = ? ', [strtolower(trim($transaction['customer_name']))])
                    ->whereIn('model_type', [Customer::class, Supplier::class])->first();
                if (empty($customer)) {
                    $this->importErrors[$creditNoteNo][] = "This customer {$transaction['customer_name']} does not exist in the system.";
                    $isTransactionCreate = false;
                } else {
                    $transaction['customer_ledger_id'] = $customer->id;
                }

                $saleInvoiceNumber = null;
                if (! empty($transaction['original_inv_no'])) {
                    $saleInvoiceNumber = SaleTransaction::whereCustomerLedgerId($customer->id)
                        ->whereFullInvoiceNumber($transaction['original_inv_no'])
                        ->first();
                }
                $transaction['original_inv_no'] = ! empty($saleInvoiceNumber) ? $saleInvoiceNumber->id : null;

                if (! empty($transaction['gstin']) && strlen($transaction['gstin']) !== 15) {
                    $this->importErrors[$creditNoteNo][] = "GST number {$transaction['gstin']} is invalid. Please enter a valid GST number.";
                    $isTransactionCreate = false;
                }

                // $transaction['dispatch_address'] = [
                //     'dispatch_address_1' => ! empty($transaction['dispatch_address_1']) ? $transaction['dispatch_address_1'] : $this->company->billingAddress->address_1,
                //     'dispatch_address_2' => ! empty($transaction['dispatch_address_2']) ? $transaction['dispatch_address_2'] : $this->company->billingAddress->address_2,
                //     'dispatch_address_city' => ! empty($transaction['dispatch_address_city']) ? $transaction['dispatch_address_city'] : $this->company->billingAddress->city_id,
                //     'dispatch_address_state' => ! empty($transaction['dispatch_address_state']) ? $transaction['dispatch_address_state'] : $this->company->billingAddress->state_id,
                //     'dispatch_address_country' => ! empty($transaction['dispatch_address_country']) ? $transaction['dispatch_address_country'] : $this->company->billingAddress->country_id,
                //     'dispatch_address_pincode' => ! empty($transaction['dispatch_address_pincode']) ? $transaction['dispatch_address_pincode'] : $this->company->billingAddress->pincode,
                // ];

                $billingCountry = $transaction['billing_address']['country'];
                $transaction['billing_address']['country_id'] = null;
                if (! empty($billingCountry)) {
                    $transaction['billing_address']['country_id'] = getCountryId($billingCountry);
                    if (empty($transaction['billing_address']['country_id'])) {
                        $this->importErrors[$creditNoteNo][] = "This country {$billingCountry} does not exist in the system.";
                        $isTransactionCreate = false;
                    }
                } elseif (empty($billingCountry) && $this->company->is_gst_applicable) {
                    $this->importErrors[$creditNoteNo][] = "This country {$billingCountry} does not exist in the excel sheet.";
                    $isTransactionCreate = false;
                }

                $billingState = $transaction['billing_address']['state'];
                $transaction['billing_address']['state_id'] = null;
                if (! empty($billingState)) {
                    $transaction['billing_address']['state_id'] = getStateId($billingState, $transaction['billing_address']['country_id']);
                    if (empty($transaction['billing_address']['state_id'])) {
                        $this->importErrors[$creditNoteNo][] = "This state {$billingState} does not exist in the system.";
                        $isTransactionCreate = false;
                    }
                } elseif (empty($billingState) && $this->company->is_gst_applicable) {
                    $this->importErrors[$creditNoteNo][] = "This state {$billingState} does not exist in the excel sheet.";
                    $isTransactionCreate = false;
                }

                $billingCity = $transaction['billing_address']['city'];
                $transaction['billing_address']['city_id'] = null;
                if (! empty($billingCity)) {
                    $transaction['billing_address']['city_id'] = getCityId($billingCity, $transaction['billing_address']['state_id']) ?? null;
                }

                $shippingCountry = $transaction['shipping_address']['country'];
                if (! empty($shippingCountry)) {
                    $transaction['shipping_address']['country_id'] = getCountryId($shippingCountry) ?? null;
                }

                $shippingState = $transaction['shipping_address']['state'];
                if (! empty($shippingState)) {
                    $transaction['shipping_address']['state_id'] = getStateId($shippingState, $transaction['shipping_address']['country_id']) ?? null;
                }

                $shippingCity = $transaction['shipping_address']['city'];
                if (! empty($shippingCity)) {
                    $transaction['shipping_address']['city_id'] = getCityId($shippingCity, $transaction['shipping_address']['state_id']) ?? null;
                }

                $brokerName = $transaction['broker_details']['broker_name'];
                if (! empty($brokerName)) {
                    $broker = Broker::whereRaw('lower(broker_name) = ? ', [strtolower($brokerName)])->first();
                    if (empty($broker)) {
                        $this->importErrors[$creditNoteNo][] = "This broker {$brokerName} does not exist in the system.";
                        $isTransactionCreate = false;
                    } else {
                        $transaction['broker_details']['broker_id'] = $broker->id;
                    }
                }

                $creditPeriod = $transaction['other_details']['credit_period'] ?? null;
                if (! empty($creditPeriod) && ! is_numeric($creditPeriod)) {
                    $this->importErrors[$creditNoteNo][] = 'Credit period should be a number of days or months.';
                    $isTransactionCreate = false;
                }

                $creditPeriodType = $transaction['other_details']['credit_period_type'] ?? null;
                if (! empty($creditPeriodType)) {
                    if (! in_array(strtolower($creditPeriodType), ['day', 'month'])) {
                        $this->importErrors[$creditNoteNo][] = 'Credit period type should be days or months.';
                        $isTransactionCreate = false;
                    } else {
                        $transaction['other_details']['credit_period_type'] = $creditPeriodType == 'Day' ? Customer::CREDIT_PERIOD_TYPE_DAY : Customer::CREDIT_PERIOD_TYPE_MONTH;
                    }
                }

                $transportName = $transaction['transport_details']['transport_name'];
                if (! empty($transportName)) {
                    $transport = Transport::whereRaw('lower(transporter_name) = ? ', [strtolower($transportName)])->first();
                    if (empty($transport)) {
                        $this->importErrors[$creditNoteNo][] = "This transporter {$transportName} does not exist in the system.";
                        $isTransactionCreate = false;
                    } else {
                        $transaction['transport_details']['transport_id'] = $transport->id;
                    }
                }

                $tcsLedgerName = $transaction['tcs_details']['tcs_ledger'];
                if (! empty($tcsLedgerName)) {
                    $tcsLedger = Ledger::whereRaw('lower(name) = ? ', [strtolower(trim($tcsLedgerName))])->whereModelType(TaxesTcs::class)->first();
                    if (empty($tcsLedger)) {
                        $this->importErrors[$creditNoteNo][] = "This tcs ledger {$tcsLedgerName} does not exist in the system.";
                        $isTransactionCreate = false;
                    } else {
                        $transaction['tcs_details']['tcs_tax_id'] = $tcsLedger->id;
                    }
                }

                $tdsLedgerName = $transaction['tds_details']['tds_ledger'];
                if (! empty($tdsLedgerName)) {
                    $tdsLedger = Ledger::whereRaw('lower(name) = ? ', [strtolower(trim($tdsLedgerName))])->whereIn('model_type', [TaxesTds::class, TdsReceivable::class])->first();
                    if (empty($tdsLedger)) {
                        $this->importErrors[$creditNoteNo][] = "This tds ledger {$tdsLedgerName} does not exist in the system.";
                        $isTransactionCreate = false;
                    } else {
                        $transaction['tds_details']['tds_tax_id'] = $tdsLedger->id;
                    }
                }

                if (! empty($transaction['narration']) && mb_strlen($transaction['narration']) > 250) {
                    $this->importErrors[$creditNoteNo][] = 'Narration should be less than or equal to 250 characters.';
                    $isTransactionCreate = false;
                }

                if (! empty($transaction['term_and_condition']) && mb_strlen($transaction['term_and_condition']) > 2000) {
                    $this->importErrors[$creditNoteNo][] = 'Term & Condition should be less than or equal to 2000 characters.';
                    $isTransactionCreate = false;
                }

                foreach ($transaction['items'] as $key => $item) {
                    $itemDetails = ItemMaster::with('model')->whereRaw('lower(item_name) = ? ', [strtolower($item['item_name'])])->first();
                    if (empty($itemDetails)) {
                        $this->importErrors[$creditNoteNo][] = "This item {$item['item_name']} does not exist in the system.";
                        $isTransactionCreate = false;
                    } else {
                        if ($itemDetails->status == false) {
                            $this->importErrors[$creditNoteNo][] = "This item {$item['item_name']} is not active in the system.";
                            $isTransactionCreate = false;
                        }
                        $transaction['items'][$key]['item_id'] = $itemDetails->id;
                    }

                    if (empty($item['ledger_name'])) {
                        $this->importErrors[$creditNoteNo][] = "This item's ledger does not exist in the excel sheet.";
                        $isTransactionCreate = false;
                    } else {
                        $ledgerDetails = Ledger::whereRaw('lower(name) = ? ', [strtolower(trim($item['ledger_name']))])
                            ->whereIn('model_type', [Income::class, Expense::class, FixedAsset::class])->first();
                        if (empty($ledgerDetails)) {
                            $this->importErrors[$creditNoteNo][] = "This item's ledger {$item['ledger_name']} does not exist in the system.";
                            $isTransactionCreate = false;
                        } else {
                            $transaction['items'][$key]['ledger_id'] = $ledgerDetails->id;
                        }
                    }

                    $unitCode = explode('-', $item['unit'])[0] ?? $item['unit'];
                    $unitOfMeasurement = UnitOfMeasurement::whereRaw('lower(code) = ? ', [strtolower($unitCode)])->where(function ($query) use ($companyId) {
                        $query->whereNull('company_id')
                            ->orWhere('company_id', $companyId);
                    })->first();
                    if (empty($unitOfMeasurement)) {
                        $this->importErrors[$creditNoteNo][] = "This item's unit {$item['unit']} does not exist in the system.";
                        $isTransactionCreate = false;
                    } elseif (! empty($itemDetails)) {
                        $checkUnitOfMeasurement = '';
                        $checkUnitOfMeasurement = CheckItemUintOfMeasurement::run($unitOfMeasurement, $itemDetails);
                        if (! $checkUnitOfMeasurement) {
                            $this->importErrors[$creditNoteNo][] = "This item's unit {$item['unit']} does not match with primary or secondary unit in the system.";
                            $isTransactionCreate = false;
                        }
                        $transaction['items'][$key]['unit_id'] = $unitOfMeasurement->id;
                    } else {
                        $transaction['items'][$key]['unit_id'] = $unitOfMeasurement->id;
                    }

                    if (! empty($item['additional_description']) && mb_strlen($item['additional_description']) > 1000) {
                        $this->importErrors[$creditNoteNo][] = "Additional description for item {$item['item_name']} should be less than or equal to 1000 characters.";
                        $isTransactionCreate = false;
                    }

                    if (empty($item['rpu']) || $item['rpu'] <= 0) {
                        $this->importErrors[$creditNoteNo][] = "This item's amount {$item['rpu']} is not valid. It should be greater than 0.";
                        $isTransactionCreate = false;
                    } else {
                        $transaction['items'][$key]['rpu'] = $item['rpu'];
                    }

                    $decimalPlaces = $itemDetails->model->decimal_places_for_rate ?? 2;
                    $decimalPlacesForQuantity = $itemDetails->model->decimal_places ?? 2;
                    $rpu = (string) $item['rpu'];
                    $quantity = (string) $item['quantity'];

                    if (strpos($rpu, '.') !== false) {
                        $actualDecimals = strlen(explode('.', $rpu)[1] ?? '');
                        if ($actualDecimals > $decimalPlaces) {
                            $this->importErrors[$creditNoteNo][] = "Rate per unit should have a maximum of {$decimalPlaces} decimal places.";
                            $isTransactionCreate = false;
                        }
                    }

                    if (strpos($quantity, '.') !== false) {
                        $actualDecimals = strlen(explode('.', $quantity)[1] ?? '');
                        if ($actualDecimals > $decimalPlacesForQuantity) {
                            $this->importErrors[$creditNoteNo][] = "Quantity should have a maximum of {$decimalPlacesForQuantity} decimal places.";
                            $isTransactionCreate = false;
                        }
                    }

                    if (empty($item['quantity']) || $item['quantity'] <= 0) {
                        $this->importErrors[$creditNoteNo][] = "This item's quantity {$item['quantity']} is not valid. It should be greater than 0.";
                        $isTransactionCreate = false;
                    } else {
                        $quantity = 0;
                        $quantity = isset(explode('.', $item['quantity'])[1]) ? explode('.', $item['quantity'])[1] : 0;
                        if (! empty($quantity) && ! empty($itemDetails) && strlen($quantity) > $itemDetails->model->decimal_places) {
                            $this->importErrors[$creditNoteNo][] = "This item's quantity {$item['quantity']} is not valid. It should be less or equal to {$itemDetails->model->decimal_places} decimal places.";
                            $isTransactionCreate = false;
                        } else {
                            $transaction['items'][$key]['quantity'] = $item['quantity'];
                        }
                    }

                    if ($transaction['is_gst_enabled']) {
                        if (empty($item['gst']) && $item['gst'] != '0') {
                            $this->importErrors[$creditNoteNo][] = "This item's gst does not exist in the excel sheet.";
                            $isTransactionCreate = false;
                        } else {
                            $gst = null;
                            if (! in_array(strtolower((string) $item['gst']), ['na', 'n/a', '0', 'exempt'])) {
                                $gst = GstTax::where('tax_rate', 'LIKE', $item['gst'].'%')->first();
                            } else {
                                $gst = GstTax::whereRaw('lower(name) = ? ', [strtolower(trim($item['gst']))])->first();
                            }

                            if (empty($gst)) {
                                $this->importErrors[$creditNoteNo][] = "This gst {$item['gst']} does not exist in the system.";
                                $isTransactionCreate = false;
                            } else {
                                $transaction['items'][$key]['gst_id'] = $gst->id;
                            }
                        }
                    }
                }

                if (! empty($transaction['additional_charges']) && is_array($transaction['additional_charges'])) {
                    foreach ($transaction['additional_charges'] as $key => $charge) {
                        $ledgerDetails = Ledger::whereRaw('lower(name) = ?', [strtolower(trim($charge['ledger']))])
                            ->whereIn('model_type', [Income::class, Expense::class, FixedAsset::class])
                            ->first();

                        if (empty($ledgerDetails)) {
                            $this->importErrors[$creditNoteNo][] = "This additional charge's ledger {$charge['ledger']} does not exist in the system.";
                            $isTransactionCreate = false;
                        } else {
                            $transaction['additional_charges'][$key]['ac_ledger_id'] = $ledgerDetails->id;
                        }

                        if (! empty($charge['gst'])) {
                            $gstValue = strtolower(trim($charge['gst']));

                            if ($gstValue !== 'na' && $gstValue !== 'n/a') {
                                $gst = GstTax::where('tax_rate', 'LIKE', $gstValue.'%')->first();
                            } else {
                                $gst = GstTax::whereRaw('lower(name) = ?', [$gstValue])->first();
                            }

                            if (empty($gst)) {
                                $this->importErrors[$creditNoteNo][] = "This GST rate {$charge['gst']} does not exist in the system.";
                                $isTransactionCreate = false;
                            } else {
                                $transaction['additional_charges'][$key]['ac_gst_rate_id'] = $gst->id;
                            }
                        }
                    }
                }

                if (! empty($transaction['add_less']) && is_array($transaction['add_less'])) {
                    foreach ($transaction['add_less'] as $key => $addLess) {
                        $ledgerName = strtolower(trim($addLess['ledger']));
                        $ledgerDetails = Ledger::whereRaw('lower(name) = ?', [$ledgerName])->first();

                        if (empty($ledgerDetails)) {
                            $this->importErrors[$creditNoteNo][] = "This add less's ledger {$addLess['ledger']} does not exist in the system.";
                            $isTransactionCreate = false;
                        } else {
                            $transaction['add_less'][$key]['al_ledger_id'] = $ledgerDetails->id;
                        }
                    }
                }

                if (! empty($transaction['payment_details']) && is_array($transaction['payment_details'])) {
                    foreach ($transaction['payment_details'] as $key => $payment) {
                        if (! empty($payment['ledger'])) {
                            $ledgerDetails = Ledger::whereRaw('lower(name) = ? ', [strtolower(trim($payment['ledger']))])
                                ->whereIn('model_type', [Bank::class, Cash::class])->first();

                            if (empty($ledgerDetails)) {
                                $this->importErrors[$creditNoteNo][] = "This payment detail's ledger {$payment['ledger']} does not exist in the system.";
                                $isTransactionCreate = false;
                            } else {
                                $transaction['payment_details'][$key]['pd_ledger_id'] = $ledgerDetails->id;
                            }
                        } else {
                            $transaction['payment_details'][$key]['pd_ledger_id'] = null;
                        }

                        if (! empty($payment['mode'])) {
                            $paymentMode = PaymentMode::whereIn('use_for', [PaymentMode::RECEIPT, PaymentMode::BOTH])->where('name', 'LIKE', $payment['mode'].'%')->first();

                            if (empty($paymentMode)) {
                                $this->importErrors[$creditNoteNo][] = "This payment detail's mode {$payment['mode']} does not exist in the system.";
                                $isTransactionCreate = false;
                            } else {
                                $transaction['payment_details'][$key]['pd_mode'] = $paymentMode->id;
                            }
                        } else {
                            $transaction['payment_details'][$key]['pd_mode'] = null;
                        }
                    }
                }

                $isPaymentDetailsNull = true;
                foreach ($transaction['payment_details'] as $payment) {
                    if (! is_null($payment['ledger']) || ! is_null($payment['pd_date']) || $payment['pd_amount'] != 0) {
                        $isPaymentDetailsNull = false;
                        break;
                    }
                }
                if ($isPaymentDetailsNull) {
                    $transaction['payment_details'] = [];
                }

                if (! empty($transaction['payment_details'])) {
                    $totalPayment = collect($transaction['payment_details'] ?? [])->sum(fn ($pd) => $pd['pd_amount'] ?? 0);
                    $grandTotal = $transaction['grand_total'] ?? 0;

                    if (! empty($transaction['tds_details']) && is_array($transaction['tds_details'])) {
                        $totalTdsAmount = collect($transaction['tds_details'])->sum(fn ($tds) => $tds['tds_amount'] ?? 0);
                        $grandTotal -= $totalTdsAmount;
                    }

                    if (isset($transaction['grand_total']) && is_numeric($grandTotal)) {
                        if ($totalPayment > $transaction['grand_total']) {
                            $this->importErrors[$creditNoteNo][] = 'Payment amount cannot be greater than grand total.';
                            $isTransactionCreate = false;
                        }
                    } else {
                        $this->importErrors[$creditNoteNo][] = 'Grand total is missing or invalid.';
                        $isTransactionCreate = false;
                    }
                }

                if ($isTransactionCreate) {

                    $input = CheckValidationsAction::run($transaction, IncomeCreditNoteTransaction::class);

                    $input['is_import'] = true;
                    $input['company'] = $this->company;

                    /** @var IncomeCNTransactionRepository $incomeCNTransactionRepository */
                    $incomeCNTransactionRepository = App::make(IncomeCNTransactionRepository::class);
                    IncomeCreditNoteTransaction::withoutTimestamps(function () use ($incomeCNTransactionRepository, $input) {
                        $incomeCNTransactionRepository->store($input);
                    });
                }
            }
        }

        if (! isset($this->importErrors['missing_voucher_number']) && ! isset($this->importErrors['wrong_excel']) && ! isset($this->importErrors['empty_excel']) && ! isset($this->importErrors['field_missing']) && ! isset($this->importErrors['unsupported_formula'])) {
            $data = $collection->whereIn('credit_note_number', array_keys($this->importErrors));
            foreach ($data as $key => $d) {
                $data[$key] = collect($d)->merge(['error' => implode(', ', array_unique($this->importErrors[$d['credit_note_number']]))]);
                $data[$key]['credit_note_date'] = parseExcelDateForImportTransaction($data[$key]['credit_note_date']);
                $data[$key]['original_invoice_date'] = parseExcelDateForImportTransaction($data[$key]['original_invoice_date']);
                $data[$key]['document_date'] = parseExcelDateForImportTransaction($data[$key]['document_date']);
                $data[$key]['po_date'] = parseExcelDateForImportTransaction($data[$key]['po_date']);
            }
            $this->totalInvoice = empty($transactions) ? 0 : count($transactions);
            $this->notImportedInvoice = count($this->importErrors);
            $this->importErrors = $data;
        }

        return $this->importErrors;
    }

    public function dataArray(Collection $collection, array $transactions): array
    {
        $fieldMissing = checkMissingKeys($this->gstFields(), $collection);
        if (! empty($fieldMissing)) {
            $this->importErrors['field_missing'] = $fieldMissing;
        } else {
            foreach ($collection as $row) {
                removeExcelFileExtraFields($row);
                if (! $row->has('item_name')) {
                    $this->importErrors['wrong_excel'] = 'You are importing wrong excel file.';
                    break;
                }
                foreach ($row as $cellKey => $cellValue) {
                    if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                        $this->importErrors['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey.'.';
                        break 2;
                    }
                }
                if (! empty($row['credit_note_number'])) {
                    if (\array_key_exists($row['credit_note_number'], $transactions)) {
                        $transactions[$row['credit_note_number']]['items'][] = $this->getItemFields($row);
                    } else {
                        $transactions[$row['credit_note_number']] = $this->getTransactionFields($row);
                    }
                } else {
                    $this->importErrors['missing_voucher_number'] = 'Credit note number does not exist in excel sheet.';
                }
            }
        }

        $result = CalculationForIncomeItemInvoiceAction::run($transactions, $this->importErrors, $this->fixDigit, $this->roundMethod);
        $transactions = $result['transactions'];
        $this->importErrors = $result['importErrors'];

        return $transactions;
    }

    public function dataArrayForNonGstApplicable(Collection $collection, array $transactions): array
    {
        $fieldMissing = checkMissingKeys($this->nonGstFields(), $collection);
        if (! empty($fieldMissing)) {
            $this->importErrors['field_missing'] = $fieldMissing;
        } else {
            foreach ($collection as $row) {
                removeExcelFileExtraFields($row);
                if (! $row->has('item_name')) {
                    $this->importErrors['wrong_excel'] = 'You are importing wrong excel file.';
                    break;
                }
                foreach ($row as $cellKey => $cellValue) {
                    if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                        $this->importErrors['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey.'.';
                        break 2;
                    }
                }

                if (! empty($row['credit_note_number'])) {
                    if (\array_key_exists($row['credit_note_number'], $transactions)) {
                        $transactions[$row['credit_note_number']]['items'][] = $this->getNonGstItemFields($row);
                    } else {
                        $transactions[$row['credit_note_number']] = $this->getNonGstTransactionFields($row);
                    }
                } else {
                    $this->importErrors['missing_voucher_number'] = 'Credit note number does not exist in excel sheet.';
                }
            }
        }

        $result = CalculationForIncomeItemInvoiceAction::run($transactions, $this->importErrors, $this->fixDigit, $this->roundMethod);
        $transactions = $result['transactions'];
        $this->importErrors = $result['importErrors'];

        return $transactions;
    }

    private function getTransactionFields($row)
    {
        $regionIso = isset($row['country_code_for_mobile_number']) ? explode('-', $row['country_code_for_mobile_number'])[0] : null;
        $regionCode = isset($row['country_code_for_mobile_number']) ? explode('-', $row['country_code_for_mobile_number'])[1] : null;

        return [
            'invoice_number' => $row['credit_note_number'] ?? null,
            'full_invoice_number' => $row['credit_note_number'] ?? null,
            'date' => ! empty($row['credit_note_date']) ? getDateFromExcelDate($row['credit_note_date']) : null,
            'original_inv_date' => ! empty($row['original_invoice_date']) ? getDateFromExcelDate($row['original_invoice_date']) : null,
            'original_inv_no' => $row['original_invoice_number'] ?? null,
            'party_phone_number' => $row['mobile_number'] ?? null,
            'region_iso' => $regionIso ? trim($regionIso) : null,
            'region_code' => $regionCode ? trim($regionCode) : null,
            // 'dispatch_address' => [
            //     'address_1' => $row['dispatch_address_1'] ?? null,
            //     'address_2' => $row['dispatch_address_2'] ?? null,
            //     'city' => $row['dispatch_address_city'] ?? null,
            //     'state' => $row['dispatch_address_state'] ?? null,
            //     'country' => $row['dispatch_address_country'] ?? null,
            //     'pin_code' => $row['dispatch_address_pincode'] ?? null,
            // ],
            'customer_name' => $row['customer_name'] ?? '',
            'gstin' => $row['gstin'] ?? null,
            'billing_address' => [
                'address_1' => $row['billing_address_line_1'] ?? null,
                'address_2' => $row['billing_address_line_2'] ?? null,
                'city' => $row['billing_address_city'] ?? null,
                'state' => $row['billing_address_state'] ?? null,
                'country' => $row['billing_address_country'] ?? null,
                'pin_code' => $row['billing_address_pincode'] ?? null,
            ],
            'shipping_address' => [
                'shipping_name' => $row['shipping_name'] ?? null,
                'shipping_gstin' => $row['shipping_gstin'] ?? null,
                'address_1' => $row['shipping_address_line_1'] ?? null,
                'address_2' => $row['shipping_address_line_2'] ?? null,
                'city' => $row['shipping_address_city'] ?? null,
                'state' => $row['shipping_address_state'] ?? null,
                'country' => $row['shipping_address_country'] ?? null,
                'pin_code' => $row['shipping_address_pincode'] ?? null,
            ],
            'broker_details' => [
                'broker_name' => $row['broker_name'],
                'brokerage_for_sale' => $row['brokerage'],
                'brokerage_on_value_type' => ! empty($row['brokerage_on_value']) ? (strtolower($row['brokerage_on_value']) == 'invoice value' ? Broker::INVOICE_VALUE : Broker::TAXABLE_VALUE) : null,
            ],
            'transport_details' => [
                'transport_name' => $row['transport_name'],
                'transporter_document_number' => $row['document_number'],
                'transporter_document_date' => ! empty($row['document_date']) ? getDateFromExcelDate($row['document_date']) : null,
                'transporter_vehicle_number' => $row['vehicle_number'],
            ],
            'other_details' => [
                'po_no' => $row['po_no'],
                'po_date' => ! empty($row['po_date']) ? getDateFromExcelDate($row['po_date']) : null,
                'credit_period' => $row['credit_period'],
                'credit_period_type' => $row['credit_period_type'],
            ],
            'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
            'items' => [$this->getItemFields($row)],
            'main_classification_nature_type' => $row['classification_nature_type'],
            'is_rcm_applicable' => strtolower($row['rcm_applicable']) == 'yes',
            'narration' => $row['note'] ?? '',
            'term_and_condition' => $row['terms_conditions'] ?? '',
            'additional_charges' => $this->getAdditionalCharges($row),
            'cess' => isset($row['cess']) && is_numeric($row['cess']) ? $row['cess'] : 0,
            'tcs_details' => [
                'tcs_ledger' => $row['tcs_ledger'],
                'tcs_rate' => isset($row['tcs_rate']) && is_numeric($row['tcs_rate']) ? $row['tcs_rate'] : 0,
            ],
            'add_less' => $this->getAddLess($row),
            'rounding_amount' => isset($row['round_off_amount']) && is_numeric($row['round_off_amount']) ? $row['round_off_amount'] : null,
            'tds_details' => [
                'tds_ledger' => $row['tds_ledger'],
                'tds_rate' => isset($row['tds_rate']) && is_numeric($row['tds_rate']) ? $row['tds_rate'] : 0,
            ],
            'payment_details' => $this->getPaymentDetails($row),
            'is_gst_enabled' => true,
            'is_round_off_not_changed' => false,
        ];
    }

    private function getNonGstTransactionFields($row)
    {
        $regionIso = isset($row['country_code_for_mobile_number']) ? explode('-', $row['country_code_for_mobile_number'])[0] : null;
        $regionCode = isset($row['country_code_for_mobile_number']) ? explode('-', $row['country_code_for_mobile_number'])[1] : null;

        return [
            'invoice_number' => $row['credit_note_number'] ?? null,
            'full_invoice_number' => $row['credit_note_number'] ?? null,
            'date' => ! empty($row['credit_note_date']) ? getDateFromExcelDate($row['credit_note_date']) : null,
            'original_inv_date' => ! empty($row['original_invoice_date']) ? getDateFromExcelDate($row['original_invoice_date']) : null,
            'original_inv_no' => $row['original_invoice_number'] ?? null,
            'party_phone_number' => $row['mobile_number'] ?? null,
            'region_iso' => $regionIso ? trim($regionIso) : null,
            'region_code' => $regionCode ? trim($regionCode) : null,
            // 'dispatch_address' => [
            //     'address_1' => $row['dispatch_address_1'] ?? null,
            //     'address_2' => $row['dispatch_address_2'] ?? null,
            //     'city' => $row['dispatch_address_city'] ?? null,
            //     'state' => $row['dispatch_address_state'] ?? null,
            //     'country' => $row['dispatch_address_country'] ?? null,
            //     'pin_code' => $row['dispatch_address_pincode'] ?? null,
            // ],
            'customer_name' => $row['customer_name'] ?? '',
            'billing_address' => [
                'address_1' => $row['billing_address_line_1'] ?? null,
                'address_2' => $row['billing_address_line_2'] ?? null,
                'city' => $row['billing_address_city'] ?? null,
                'state' => $row['billing_address_state'] ?? null,
                'country' => $row['billing_address_country'] ?? null,
                'pin_code' => $row['billing_address_pincode'] ?? null,
            ],
            'shipping_address' => [
                'shipping_name' => $row['shipping_name'] ?? null,
                'shipping_gstin' => $row['shipping_gstin'] ?? null,
                'address_1' => $row['shipping_address_line_1'] ?? null,
                'address_2' => $row['shipping_address_line_2'] ?? null,
                'city' => $row['shipping_address_city'] ?? null,
                'state' => $row['shipping_address_state'] ?? null,
                'country' => $row['shipping_address_country'] ?? null,
                'pin_code' => $row['shipping_address_pincode'] ?? null,
            ],
            'broker_details' => [
                'broker_name' => $row['broker_name'],
                'brokerage_for_sale' => $row['brokerage'],
                'brokerage_on_value_type' => ! empty($row['brokerage_on_value']) ? (strtolower($row['brokerage_on_value']) == 'invoice value' ? Broker::INVOICE_VALUE : Broker::TAXABLE_VALUE) : null,
            ],
            'transport_details' => [
                'transport_name' => $row['transport_name'],
                'transporter_document_number' => $row['document_number'],
                'transporter_document_date' => ! empty($row['document_date']) ? getDateFromExcelDate($row['document_date']) : null,
                'transporter_vehicle_number' => $row['vehicle_number'],
            ],
            'other_details' => [
                'po_no' => $row['po_no'],
                'po_date' => ! empty($row['po_date']) ? getDateFromExcelDate($row['po_date']) : null,
                'credit_period' => $row['credit_period'],
                'credit_period_type' => $row['credit_period_type'],
            ],
            'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
            'items' => [$this->getNonGstItemFields($row)],
            'main_classification_nature_type' => null,
            'is_rcm_applicable' => null,
            'narration' => $row['note'] ?? '',
            'term_and_condition' => $row['terms_conditions'] ?? '',
            'additional_charges' => $this->getAdditionalChargesForNonGst($row),
            'cess' => isset($row['cess']) && is_numeric($row['cess']) ? $row['cess'] : 0,
            'tcs_details' => [
                'tcs_ledger' => $row['tcs_ledger'],
                'tcs_rate' => isset($row['tcs_rate']) && is_numeric($row['tcs_rate']) ? $row['tcs_rate'] : 0,
            ],
            'add_less' => $this->getAddLess($row),
            'rounding_amount' => isset($row['round_off_amount']) && is_numeric($row['round_off_amount']) ? $row['round_off_amount'] : null,
            'tds_details' => [
                'tds_ledger' => $row['tds_ledger'],
                'tds_rate' => isset($row['tds_rate']) && is_numeric($row['tds_rate']) ? $row['tds_rate'] : 0,
            ],
            'payment_details' => $this->getPaymentDetails($row),
            'is_gst_enabled' => false,
            'is_round_off_not_changed' => false,
        ];
    }

    private function getItemFields($row)
    {
        return [
            'item_name' => $row['item_name'] ?? '',
            'additional_description' => $row['additional_description_for_item'] ?? '',
            'ledger_name' => $row['ledger_name'] ?? '',
            'unit' => $row['unit'],
            'quantity' => isset($row['quantity']) && is_numeric($row['quantity']) ? $row['quantity'] : 0,
            'mrp' => isset($row['mrp']) && is_numeric($row['mrp']) ? $row['mrp'] : null,
            'rpu' => isset($row['rate_per_unit_without_gst']) && is_numeric($row['rate_per_unit_without_gst']) ? $row['rate_per_unit_without_gst'] : 0,
            'with_tax' => 0, //false
            'discount_type' => $row['discount_1_type'] == '%' ? 2 : 1,
            'discount_value' => isset($row['discount_1']) && is_numeric($row['discount_1']) ? ($row['discount_1_type'] == '%' ? round($row['discount_1'], 3) : round($row['discount_1'], $this->fixDigit)) : 0,
            'discount_type_2' => $row['discount_2_type'] == '%' ? 2 : 1,
            'discount_value_2' => isset($row['discount_2']) && is_numeric($row['discount_2']) ? ($row['discount_2_type'] == '%' ? round($row['discount_2'], 3) : round($row['discount_2'], $this->fixDigit)) : 0,
            'gst' => isset($row['gst']) ? $row['gst'] : null,
        ];
    }

    private function getNonGstItemFields($row)
    {
        return [
            'item_name' => $row['item_name'] ?? '',
            'additional_description' => $row['additional_description_for_item'] ?? '',
            'ledger_name' => $row['ledger_name'] ?? '',
            'unit' => $row['unit'],
            'quantity' => isset($row['quantity']) && is_numeric($row['quantity']) ? $row['quantity'] : 0,
            'mrp' => isset($row['mrp']) && is_numeric($row['mrp']) ? $row['mrp'] : null,
            'rpu' => isset($row['rate_per_unit']) && is_numeric($row['rate_per_unit']) ? $row['rate_per_unit'] : 0,
            'with_tax' => 0, //false
            'discount_type' => $row['discount_1_type'] == '%' ? 2 : 1,
            'discount_value' => isset($row['discount_1']) && is_numeric($row['discount_1']) ? (($row['discount_1_type'] ?? '') == '%' ? round($row['discount_1'], 3) : round($row['discount_1'], $this->fixDigit)) : 0,
            'discount_type_2' => $row['discount_2_type'] == '%' ? 2 : 1,
            'discount_value_2' => isset($row['discount_2']) && is_numeric($row['discount_2']) ? (($row['discount_2_type'] ?? '') == '%' ? round($row['discount_2'], 3) : round($row['discount_2'], $this->fixDigit)) : 0,
        ];
    }

    private function getAdditionalCharges($row)
    {
        $data = [];

        if (! empty($row['additional_charges_1_ledger'])) {
            $data['additional_charges_1'] = [
                'ledger' => $row['additional_charges_1_ledger'],
                'ac_type' => ($row['additional_charges_1_type'] ?? '') == '%' ? 2 : 1,
                'ac_value' => isset($row['additional_charges_1_amount_without_gst']) && is_numeric($row['additional_charges_1_amount_without_gst']) ? (($row['additional_charges_1_type'] ?? '') == '%' ? round($row['additional_charges_1_amount_without_gst'], 3) : round($row['additional_charges_1_amount_without_gst'], $this->fixDigit)) : 0,
                'ac_gst' => isset($row['additional_charges_1_gst']) ? $row['additional_charges_1_gst'] : null,
            ];
        }

        if (! empty($row['additional_charges_2_ledger'])) {
            $data['additional_charges_2'] = [
                'ledger' => $row['additional_charges_2_ledger'],
                'ac_type' => ($row['additional_charges_2_type'] ?? '') == '%' ? 2 : 1,
                'ac_value' => isset($row['additional_charges_2_amount_without_gst']) && is_numeric($row['additional_charges_2_amount_without_gst']) ? (($row['additional_charges_2_type'] ?? '') == '%' ? round($row['additional_charges_2_amount_without_gst'], 3) : round($row['additional_charges_2_amount_without_gst'], $this->fixDigit)) : 0,
                'ac_gst' => isset($row['additional_charges_2_gst']) ? $row['additional_charges_2_gst'] : null,
            ];
        }

        return $data;
    }

    private function getAdditionalChargesForNonGst($row)
    {
        $data = [];

        if (! empty($row['additional_charges_1_ledger'])) {
            $data['additional_charges_1'] = [
                'ledger' => $row['additional_charges_1_ledger'],
                'ac_type' => ($row['additional_charges_1_type'] ?? '') == '%' ? 2 : 1,
                'ac_value' => isset($row['additional_charges_1_amount']) && is_numeric($row['additional_charges_1_amount']) ? (($row['additional_charges_1_type'] ?? '') == '%' ? round($row['additional_charges_1_amount'], 3) : round($row['additional_charges_1_amount'], $this->fixDigit)) : 0,
            ];
        }

        if (! empty($row['additional_charges_2_ledger'])) {
            $data['additional_charges_2'] = [
                'ledger' => $row['additional_charges_2_ledger'],
                'ac_type' => ($row['additional_charges_2_type'] ?? '') == '%' ? 2 : 1,
                'ac_value' => isset($row['additional_charges_2_amount']) && is_numeric($row['additional_charges_2_amount']) ? (($row['additional_charges_2_type'] ?? '') == '%' ? round($row['additional_charges_2_amount'], 3) : round($row['additional_charges_2_amount'], $this->fixDigit)) : 0,
            ];
        }

        return $data;
    }

    private function getAddLess($row)
    {
        $data = [];

        if (! empty($row['add_less_1_ledger'])) {
            $data['add_less_1'] = [
                'ledger' => $row['add_less_1_ledger'],
                'al_ledger_id' => null,
                'al_type' => $row['add_less_1_type'] == '%' ? 2 : 1,
                'al_value' => isset($row['add_less_1_amount']) && is_numeric($row['add_less_1_amount']) ? ($row['add_less_1_type'] == '%' ? round($row['add_less_1_amount'], 3) : round($row['add_less_1_amount'], $this->fixDigit)) : 0,
                'al_is_show_in_print' => true,
            ];
        }

        if (! empty($row['add_less_2_ledger'])) {
            $data['add_less_2'] = [
                'ledger' => $row['add_less_2_ledger'],
                'al_ledger_id' => null,
                'al_type' => $row['add_less_2_type'] == '%' ? 2 : 1,
                'al_value' => isset($row['add_less_2_amount']) && is_numeric($row['add_less_2_amount']) ? ($row['add_less_2_type'] == '%' ? round($row['add_less_2_amount'], 3) : round($row['add_less_2_amount'], $this->fixDigit)) : 0,
                'al_is_show_in_print' => true,
            ];
        }

        return $data;
    }

    private function getPaymentDetails($row)
    {
        $data = [];

        if (! empty($row['payment_1_ledger'])) {
            $data['payment_1'] = [
                'ledger' => $row['payment_1_ledger'],
                'pd_date' => ! empty($row['payment_1_date']) ? getDateFromExcelDate($row['payment_1_date']) : null,
                'pd_amount' => isset($row['payment_1_amount']) && is_numeric($row['payment_1_amount']) ? $row['payment_1_amount'] : 0,
                'mode' => $row['payment_1_mode'] ?? null,
                'pd_reference_number' => $row['payment_1_reference_number'] ?? null,
            ];
        }

        if (! empty($row['payment_2_ledger'])) {
            $data['payment_2'] = [
                'ledger' => $row['payment_2_ledger'],
                'pd_date' => ! empty($row['payment_2_date']) ? getDateFromExcelDate($row['payment_2_date']) : null,
                'pd_amount' => isset($row['payment_2_amount']) && is_numeric($row['payment_2_amount']) ? $row['payment_2_amount'] : 0,
                'mode' => $row['payment_2_mode'] ?? null,
                'pd_reference_number' => $row['payment_2_reference_number'] ?? null,
            ];
        }

        return $data;
    }

    public function gstFields()
    {
        return [
            'credit_note_number',
            'credit_note_date',
            'customer_name',
            'original_invoice_number',
            'original_invoice_date',
            'gstin',
            'shipping_gstin',
            'shipping_name',
            'billing_address_line_1',
            'billing_address_line_2',
            'billing_address_country',
            'billing_address_state',
            'billing_address_city',
            'billing_address_pincode',
            'shipping_address_line_1',
            'shipping_address_line_2',
            'shipping_address_country',
            'shipping_address_state',
            'shipping_address_city',
            'shipping_address_pincode',
            'broker_name',
            'brokerage',
            'brokerage_on_value',
            'credit_period',
            'credit_period_type',
            'transport_name',
            'document_number',
            'vehicle_number',
            'document_date',
            'po_no',
            'po_date',
            'item_name',
            'additional_description_for_item',
            'ledger_name',
            'unit',
            'quantity',
            'mrp',
            'rate_per_unit_without_gst',
            'gst',
            'discount_1_type',
            'discount_1',
            'discount_2_type',
            'discount_2',
            'classification_nature_type',
            'rcm_applicable',
            'additional_charges_1_ledger',
            'additional_charges_1_type',
            'additional_charges_1_amount_without_gst',
            'additional_charges_1_gst',
            'additional_charges_2_ledger',
            'additional_charges_2_type',
            'additional_charges_2_amount_without_gst',
            'additional_charges_2_gst',
            'cess',
            'tcs_ledger',
            'tcs_rate',
            'add_less_1_ledger',
            'add_less_1_type',
            'add_less_1_amount',
            'add_less_2_ledger',
            'add_less_2_type',
            'add_less_2_amount',
            'round_off_amount',
            'note',
            'terms_conditions',
            'payment_1_ledger',
            'payment_1_date',
            'payment_1_amount',
            'payment_1_mode',
            'payment_1_reference_number',
            'payment_2_ledger',
            'payment_2_date',
            'payment_2_amount',
            'payment_2_mode',
            'payment_2_reference_number',
        ];
    }

    public function nonGstFields()
    {
        return [
            'credit_note_number',
            'credit_note_date',
            'customer_name',
            'shipping_name',
            'original_invoice_number',
            'original_invoice_date',
            'billing_address_line_1',
            'billing_address_line_2',
            'billing_address_country',
            'billing_address_state',
            'billing_address_city',
            'billing_address_pincode',
            'shipping_address_line_1',
            'shipping_address_line_2',
            'shipping_address_country',
            'shipping_address_state',
            'shipping_address_city',
            'shipping_address_pincode',
            'broker_name',
            'brokerage',
            'brokerage_on_value',
            'credit_period',
            'credit_period_type',
            'transport_name',
            'document_number',
            'vehicle_number',
            'document_date',
            'po_no',
            'po_date',
            'item_name',
            'additional_description_for_item',
            'ledger_name',
            'unit',
            'quantity',
            'mrp',
            'rate_per_unit',
            'discount_1_type',
            'discount_1',
            'discount_2_type',
            'discount_2',
            'additional_charges_1_ledger',
            'additional_charges_1_type',
            'additional_charges_1_amount',
            'additional_charges_2_ledger',
            'additional_charges_2_type',
            'additional_charges_2_amount',
            'tcs_ledger',
            'tcs_rate',
            'add_less_1_ledger',
            'add_less_1_type',
            'add_less_1_amount',
            'add_less_2_ledger',
            'add_less_2_type',
            'add_less_2_amount',
            'round_off_amount',
            'note',
            'terms_conditions',
            'payment_1_ledger',
            'payment_1_date',
            'payment_1_amount',
            'payment_1_mode',
            'payment_1_reference_number',
            'payment_2_ledger',
            'payment_2_date',
            'payment_2_amount',
            'payment_2_mode',
            'payment_2_reference_number',
        ];
    }
}
