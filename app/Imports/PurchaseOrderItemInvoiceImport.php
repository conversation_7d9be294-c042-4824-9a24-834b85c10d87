<?php

namespace App\Imports;

use App\Actions\Imports\CalculationForExpenseItemInvoiceAction;
use App\Actions\Income\CheckItemUintOfMeasurement;
use App\Actions\Validation\CheckValidationsAction;
use App\Models\GstTax;
use App\Models\Ledger;
use App\Models\LockTransaction;
use App\Models\Master\Broker;
use App\Models\Master\Customer;
use App\Models\Master\Expense;
use App\Models\Master\FixedAsset;
use App\Models\Master\Income;
use App\Models\Master\ItemMaster;
use App\Models\Master\PurchaseOrderTransactionMaster;
use App\Models\Master\Supplier;
use App\Models\Master\Transport;
use App\Models\PurchaseOrderTitle;
use App\Models\PurchaseOrderTransaction;
use App\Models\UnitOfMeasurement;
use App\Repositories\v1\PurchaseOrderTransactionRepository;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class PurchaseOrderItemInvoiceImport implements SkipsEmptyRows, ToCollection, WithHeadingRow, WithMultipleSheets
{
    use Importable;

    public $importErrors = [];

    public $company;

    public $fixDigit;

    public $roundMethod;

    public $totalInvoice;

    public $notImportedInvoice;

    public function sheets(): array
    {
        return [
            0 => $this,
        ];
    }

    public function collection(Collection $collection)
    {
        if ($collection->isEmpty()) {
            $this->importErrors['empty_excel'] = "The excel sheet doesn't have any transactions.";
        } else {
            $transactions = [];
            $this->company = getCurrentCompany();
            $this->fixDigit = getCompanyFixedDigitNumber();
            $purchaseOrderTransactionMaster = PurchaseOrderTransactionMaster::first();
            $this->roundMethod = $purchaseOrderTransactionMaster->round_off_method ?? Ledger::NORMAL_ROUNDING;

            if ($this->company->is_gst_applicable == 1) {
                $transactions = $this->dataArray($collection, $transactions);
            } else {
                $transactions = $this->dataArrayForNonGstApplicable($collection, $transactions);
            }

            $currentTime = Carbon::now();
            $iteration = 0;
            foreach ($transactions as $voucherNo => $transaction) {
                // this is for next invoice number issue fix
                $transaction['created_at'] = $currentTime->copy()->addSeconds($iteration);
                $transaction['updated_at'] = $currentTime->copy()->addSeconds($iteration);
                $iteration++;

                $purchaseOrderTransaction = PurchaseOrderTransaction::where('full_order_number', $voucherNo)->financialYearDate()->first();
                if (! empty($purchaseOrderTransaction)) {
                    $this->importErrors[$voucherNo][] = "This Order no {$voucherNo} already exists.";

                    continue;
                }

                $isTransactionCreate = true;

                if (! empty($transaction['order_date']) && $transaction['order_date'] !== false && ! checkDateIsCurrentFinancialYear($transaction['order_date'])) {
                    $transactionDate = Carbon::parse($transaction['order_date'])->format('Y-m-d');
                    $isLocked = isLockTransaction(LockTransaction::EXPENSE, $transactionDate) ?? false;
                    if ($isLocked) {
                        $lockDate = Carbon::parse(getTransactionsLockDate()[LockTransaction::EXPENSE])->format('d-m-Y');
                        $this->importErrors[$voucherNo][] = 'This transaction date is locked please add date after '.$lockDate;
                        $isTransactionCreate = false;
                    }
                }

                if (empty($transaction['order_date'])) {
                    $this->importErrors[$voucherNo][] = 'Voucher Date does not exist in the excel file.';
                    $isTransactionCreate = false;
                }
                if ($transaction['order_date'] == false) {
                    $this->importErrors[$voucherNo][] = 'Date formate is invalid, please enter a dd-mm-yyyy format.';
                    $isTransactionCreate = false;
                }
                if (checkDateIsCurrentFinancialYear($transaction['order_date'])) {
                    $this->importErrors[$voucherNo][] = 'This transaction voucher date does not have a current financial year date.';
                    $isTransactionCreate = false;
                }
                if ($transaction['transport_details']['transporter_document_date'] !== null && $transaction['transport_details']['transporter_document_date'] === false) {
                    $this->importErrors[$voucherNo][] = 'Document date format is invalid. Please enter a dd-mm-yyyy format.';
                    $isTransactionCreate = false;
                }

                // title check
                $purchase_title = PurchaseOrderTitle::whereRaw('lower(name) = ? ', [strtolower(trim($transaction['purchase_title']))])->first();
                if (empty($purchase_title)) {
                    $this->importErrors[$voucherNo][] = "This title {$transaction['purchase_title']} does not exist in the system.";
                    $isTransactionCreate = false;
                } else {
                    $transaction['title'] = $purchase_title->id;
                }

                $supplier = Ledger::whereRaw('lower(name) = ? ', [strtolower(trim($transaction['supplier_name']))])
                    ->whereIn('model_type', [Customer::class, Supplier::class])->first();
                if (empty($supplier)) {
                    $this->importErrors[$voucherNo][] = "This supplier {$transaction['supplier_name']} does not exist in the system.";
                    $isTransactionCreate = false;
                } else {
                    $transaction['party_ledger_id'] = $supplier->id;
                }

                if (! empty($transaction['gstin']) && strlen($transaction['gstin']) !== 15) {
                    $this->importErrors[$voucherNo][] = "GST number {$transaction['gstin']} is invalid. Please enter a valid GST number.";
                    $isTransactionCreate = false;
                }

                if (empty($transaction['main_classification_nature_type']) && $this->company->is_gst_applicable == 1) {
                    $this->importErrors[$voucherNo][] = 'Classification nature type does not exist in the excel file.';
                    $isTransactionCreate = false;
                }

                $validFor = $transaction['valid_for'] ?? null;
                if (! empty($validFor) && ! is_numeric($validFor)) {
                    $this->importErrors[$voucherNo][] = 'Estimate quote valid for should be a number of days or months.';
                    $isTransactionCreate = false;
                }

                $validForType = $transaction['valid_for_type'] ?? null;
                if (! empty($validForType)) {
                    if (! in_array(strtolower($validForType), ['day', 'month'])) {
                        $this->importErrors[$voucherNo][] = 'Estimate quote valid for type should be days or months.';
                        $isTransactionCreate = false;
                    } else {
                        $transaction['valid_for_type'] = $validForType == 'Day' ? PurchaseOrderTransaction::VALID_FOR_TYPE_DAY : PurchaseOrderTransaction::VALID_FOR_TYPE_MONTH;
                    }
                }

                // Billing Country
                $billingCountry = $transaction['billing_address']['country'];
                $transaction['billing_address']['country_id'] = null;
                if (! empty($billingCountry)) {
                    $transaction['billing_address']['country_id'] = getCountryId($billingCountry);
                    if (empty($transaction['billing_address']['country_id'])) {
                        $this->importErrors[$voucherNo][] = "This country {$billingCountry} does not exist in the system.";
                        $isTransactionCreate = false;
                    }
                } elseif (empty($billingCountry) && $this->company->is_gst_applicable) {
                    $this->importErrors[$voucherNo][] = "This country {$billingCountry} does not exist in the excel sheet.";
                    $isTransactionCreate = false;
                }

                // Billing State
                $billingState = $transaction['billing_address']['state'];
                $transaction['billing_address']['state_id'] = null;
                if (! empty($billingState)) {
                    $transaction['billing_address']['state_id'] = getStateId($billingState, $transaction['billing_address']['country_id']);
                    if (empty($transaction['billing_address']['state_id'])) {
                        $this->importErrors[$voucherNo][] = "This state {$billingState} does not exist in the system.";
                        $isTransactionCreate = false;
                    }
                } elseif (empty($billingState) && $this->company->is_gst_applicable) {
                    $this->importErrors[$voucherNo][] = "This state {$billingState} does not exist in the excel sheet.";
                    $isTransactionCreate = false;
                }

                // Billing City
                $billingCity = $transaction['billing_address']['city'];
                $transaction['billing_address']['city_id'] = null;
                if (! empty($billingCity)) {
                    $transaction['billing_address']['city_id'] = getCityId($billingCity, $transaction['billing_address']['state_id']) ?? null;
                }

                $shippingCountry = $transaction['shipping_address']['country'];
                if (! empty($shippingCountry)) {
                    $transaction['shipping_address']['country_id'] = getCountryId($shippingCountry) ?? null;
                }

                $shippingState = $transaction['shipping_address']['state'];
                if (! empty($shippingState)) {
                    $transaction['shipping_address']['state_id'] = getStateId($shippingState, $transaction['shipping_address']['country_id']) ?? null;
                }

                $shippingCity = $transaction['shipping_address']['city'];
                if (! empty($shippingCity)) {
                    $transaction['shipping_address']['city_id'] = getCityId($shippingCity, $transaction['shipping_address']['state_id']) ?? null;
                }

                $brokerName = $transaction['broker_details']['broker_name'];
                if (! empty($brokerName)) {
                    $broker = Broker::whereRaw('lower(broker_name) = ? ', [strtolower($brokerName)])->first();
                    if (empty($broker)) {
                        $this->importErrors[$voucherNo][] = "This broker {$brokerName} does not exist in the system.";
                        $isTransactionCreate = false;
                    } else {
                        $transaction['broker_details']['broker_id'] = $broker->id;
                    }
                }

                $transportName = $transaction['transport_details']['transport_name'];
                if (! empty($transportName)) {
                    $transport = Transport::whereRaw('lower(transporter_name) = ? ', [strtolower($transportName)])->first();
                    if (empty($transport)) {
                        $this->importErrors[$voucherNo][] = "This transporter {$transportName} does not exist in the system.";
                        $isTransactionCreate = false;
                    } else {
                        $transaction['transport_details']['transport_id'] = $transport->id;
                    }
                }

                if (! empty($transaction['narration']) && mb_strlen($transaction['narration']) > 250) {
                    $this->importErrors[$voucherNo][] = 'Narration should be less than or equal to 250 characters.';
                    $isTransactionCreate = false;
                }

                foreach ($transaction['items'] as $key => $item) {
                    $itemDetails = ItemMaster::with('model')->whereRaw('lower(item_name) = ? ', [strtolower($item['item_name'])])->first();
                    if (empty($itemDetails)) {
                        $this->importErrors[$voucherNo][] = "This item {$item['item_name']} does not exist in the system.";
                        $isTransactionCreate = false;
                    } else {
                        if ($itemDetails->status == false) {
                            $this->importErrors[$voucherNo][] = "This item {$item['item_name']} is not active in the system.";
                            $isTransactionCreate = false;
                        }
                        $transaction['items'][$key]['item_id'] = $itemDetails->id;
                    }

                    if (! empty($item['additional_description']) && mb_strlen($item['additional_description']) > 1000) {
                        $this->importErrors[$voucherNo][] = "Additional description for Item {$item['ledger_name']} should be less than or equal to 1000 characters.";
                        $isTransactionCreate = false;
                    }

                    if (empty($item['rpu']) || $item['rpu'] <= 0) {
                        $this->importErrors[$voucherNo][] = "This item's amount {$item['rpu']} is not valid. It should be greater than 0.";
                        $isTransactionCreate = false;
                    } else {
                        $transaction['items'][$key]['rpu'] = $item['rpu'];
                    }

                    $decimalPlaces = $itemDetails->model->decimal_places_for_rate ?? 2;
                    $decimalPlacesForQuantity = $itemDetails->model->decimal_places ?? 2;
                    $rpu = (string) $item['rpu'];
                    $quantity = (string) $item['quantity'];

                    if (strpos($rpu, '.') !== false) {
                        $actualDecimals = strlen(explode('.', $rpu)[1] ?? '');
                        if ($actualDecimals > $decimalPlaces) {
                            $this->importErrors[$voucherNo][] = "Rate per unit should have a maximum of {$decimalPlaces} decimal places.";
                            $isTransactionCreate = false;
                        }
                    }

                    if (strpos($quantity, '.') !== false) {
                        $actualDecimals = strlen(explode('.', $quantity)[1] ?? '');
                        if ($actualDecimals > $decimalPlacesForQuantity) {
                            $this->importErrors[$voucherNo][] = "Quantity should have a maximum of {$decimalPlacesForQuantity} decimal places.";
                            $isTransactionCreate = false;
                        }
                    }

                    $ledgerDetails = Ledger::whereRaw('lower(name) = ? ', [strtolower(trim($item['ledger_name']))])
                        ->whereIn('model_type', [Income::class, Expense::class, FixedAsset::class])->first();
                    if (empty($ledgerDetails)) {
                        $this->importErrors[$voucherNo][] = "This item's ledger {$item['ledger_name']} does not exist in the system.";
                        $isTransactionCreate = false;
                    } else {
                        $transaction['items'][$key]['ledger_id'] = $ledgerDetails->id;
                    }

                    $unitCode = explode('-', $item['unit'])[0] ?? $item['unit'];
                    $unitOfMeasurement = UnitOfMeasurement::whereRaw('lower(code) = ? ', [strtolower($unitCode)])->first();
                    if (empty($unitOfMeasurement)) {
                        $this->importErrors[$voucherNo][] = "This item's unit {$item['unit']} does not exist in the system.";
                        $isTransactionCreate = false;
                    } elseif (! empty($itemDetails)) {
                        $checkUnitOfMeasurement = '';
                        $checkUnitOfMeasurement = CheckItemUintOfMeasurement::run($unitOfMeasurement, $itemDetails);
                        if (! $checkUnitOfMeasurement) {
                            $this->importErrors[$voucherNo][] = "This item's unit {$item['unit']} does not match with primary or secondary unit in the system.";
                            $isTransactionCreate = false;
                        }
                        $transaction['items'][$key]['unit_id'] = $unitOfMeasurement->id;
                    } else {
                        $transaction['items'][$key]['unit_id'] = $unitOfMeasurement->id;
                    }

                    if (empty($item['quantity']) || $item['quantity'] <= 0) {
                        $this->importErrors[$voucherNo][] = "This item's quantity {$item['quantity']} is not valid. It should be greater than 0.";
                        $isTransactionCreate = false;
                    } else {
                        $quantity = 0;
                        $quantity = isset(explode('.', $item['quantity'])[1]) ? explode('.', $item['quantity'])[1] : 0;
                        if (! empty($quantity) && ! empty($itemDetails) && strlen($quantity) > $itemDetails->model->decimal_places) {
                            $this->importErrors[$voucherNo][] = "This item's quantity {$item['quantity']} is not valid. It should be less or equal to {$itemDetails->model->decimal_places} decimal places.";
                            $isTransactionCreate = false;
                        } else {
                            $transaction['items'][$key]['quantity'] = $item['quantity'];
                        }
                    }

                    if ($transaction['is_gst_enabled']) {
                        if (empty($item['gst']) && $item['gst'] != '0') {
                            $this->importErrors[$voucherNo][] = "This item's gst does not exist in the excel sheet.";
                            $isTransactionCreate = false;
                        } else {
                            $gst = null;
                            if (! in_array(strtolower((string) $item['gst']), ['na', 'n/a', '0', 'exempt'])) {
                                $gst = GstTax::where('tax_rate', 'LIKE', $item['gst'].'%')->first();
                            } else {
                                $gst = GstTax::whereRaw('lower(name) = ? ', [strtolower(trim($item['gst']))])->first();
                            }

                            if (empty($gst)) {
                                $this->importErrors[$voucherNo][] = "This gst {$item['gst']} does not exist in the system.";
                                $isTransactionCreate = false;
                            } else {
                                $transaction['items'][$key]['gst_id'] = $gst->id;
                            }
                        }
                    }
                }

                if (! empty($transaction['additional_charges']) && is_array($transaction['additional_charges'])) {
                    foreach ($transaction['additional_charges'] as $key => $charge) {
                        $ledgerDetails = Ledger::whereRaw('lower(name) = ?', [strtolower(trim($charge['ledger']))])
                            ->whereIn('model_type', [Income::class, Expense::class, FixedAsset::class])
                            ->first();

                        if (empty($ledgerDetails)) {
                            $this->importErrors[$voucherNo][] = "This additional charge's ledger {$charge['ledger']} does not exist in the system.";
                            $isTransactionCreate = false;
                        } else {
                            $transaction['additional_charges'][$key]['ac_ledger_id'] = $ledgerDetails->id;
                        }

                        if (! empty($charge['gst'])) {
                            $gstValue = strtolower(trim($charge['gst']));

                            if ($gstValue !== 'na' && $gstValue !== 'n/a') {
                                $gst = GstTax::where('tax_rate', 'LIKE', $gstValue.'%')->first();
                            } else {
                                $gst = GstTax::whereRaw('lower(name) = ?', [$gstValue])->first();
                            }

                            if (empty($gst)) {
                                $this->importErrors[$voucherNo][] = "This GST rate {$charge['gst']} does not exist in the system.";
                                $isTransactionCreate = false;
                            } else {
                                $transaction['additional_charges'][$key]['ac_gst_rate_id'] = $gst->id;
                            }
                        }
                    }
                }

                if (! empty($transaction['add_less']) && is_array($transaction['add_less'])) {
                    foreach ($transaction['add_less'] as $key => $addLess) {
                        $ledgerName = strtolower(trim($addLess['ledger']));
                        $ledgerDetails = Ledger::whereRaw('lower(name) = ?', [$ledgerName])->first();

                        if (empty($ledgerDetails)) {
                            $this->importErrors[$voucherNo][] = "This add less's ledger {$addLess['ledger']} does not exist in the system.";
                            $isTransactionCreate = false;
                        } else {
                            $transaction['add_less'][$key]['al_ledger_id'] = $ledgerDetails->id;
                        }
                    }
                }

                if ($isTransactionCreate) {
                    $input = CheckValidationsAction::run($transaction, PurchaseOrderTransaction::class);
                    $input['is_import'] = true;
                    $input['company'] = $this->company;

                    /** @var PurchaseOrderTransactionRepository $purchaseOrderTransactionRepository */
                    $purchaseOrderTransactionRepository = App::make(PurchaseOrderTransactionRepository::class);
                    PurchaseOrderTransaction::withoutTimestamps(function () use ($purchaseOrderTransactionRepository, $input) {
                        $purchaseOrderTransactionRepository->store($input);
                    });
                }
            }
        }

        if (! isset($this->importErrors['missing_purchase_order_number']) && ! isset($this->importErrors['wrong_excel']) && ! isset($this->importErrors['empty_excel']) && ! isset($this->importErrors['field_missing']) && ! isset($this->importErrors['unsupported_formula'])) {
            $data = $collection->whereIn('purchase_order_number', array_keys($this->importErrors));
            foreach ($data as $key => $d) {
                $data[$key] = collect($d)->merge(['error' => implode(', ', array_unique($this->importErrors[$d['purchase_order_number']]))]);
                $data[$key]['purchase_order_date'] = parseExcelDateForImportTransaction($data[$key]['purchase_order_date']);
                $data[$key]['document_date'] = parseExcelDateForImportTransaction($data[$key]['document_date']);
            }
            $this->totalInvoice = empty($transactions) ? 0 : count($transactions);
            $this->notImportedInvoice = count($this->importErrors);
            $this->importErrors = $data;
        }

        return $this->importErrors;
    }

    public function dataArray(Collection $collection, array $transactions)
    {
        $fieldMissing = checkMissingKeys($this->gstFields(), $collection);
        if (! empty($fieldMissing)) {
            $this->importErrors['field_missing'] = $fieldMissing;
        } else {
            foreach ($collection as $row) {
                removeExcelFileExtraFields($row);
                if (! $row->has('item_name')) {
                    $this->importErrors['wrong_excel'] = 'You are importing wrong excel file.';
                    break;
                }
                foreach ($row as $cellKey => $cellValue) {
                    if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                        $this->importErrors['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey.'.';
                        break 2;
                    }
                }
                if (! empty($row['purchase_order_number'])) {
                    if (array_key_exists($row['purchase_order_number'], $transactions)) {
                        $transactions[$row['purchase_order_number']]['items'][] = $this->getItemFields($row);
                    } else {
                        $transactions[$row['purchase_order_number']] = $this->getTransactionFields($row);
                    }
                } else {
                    $this->importErrors['missing_purchase_order_number'] = 'Purchase order number does not exist in excel sheet.';
                }
            }
        }

        $result = CalculationForExpenseItemInvoiceAction::run($transactions, $this->importErrors, $this->fixDigit, $this->roundMethod);
        $transactions = $result['transactions'];
        $this->importErrors = $result['importErrors'];

        return $transactions;
    }

    public function dataArrayForNonGstApplicable(Collection $collection, array $transactions): array
    {
        $fieldMissing = checkMissingKeys($this->nonGstFields(), $collection);
        if (! empty($fieldMissing)) {
            $this->importErrors['field_missing'] = $fieldMissing;
        } else {
            foreach ($collection as $row) {
                removeExcelFileExtraFields($row);
                if (! $row->has('item_name')) {
                    $this->importErrors['wrong_excel'] = 'You are importing wrong excel file.';
                    break;
                }
                foreach ($row as $cellKey => $cellValue) {
                    if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                        $this->importErrors['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey.'.';
                        break 2;
                    }
                }
                if (! empty($row['purchase_order_number'])) {
                    if (array_key_exists($row['purchase_order_number'], $transactions)) {
                        $transactions[$row['purchase_order_number']]['items'][] = $this->getNonGstItemFields($row);
                    } else {
                        $transactions[$row['purchase_order_number']] = $this->getNonGstTransactionFields($row);
                    }
                } else {
                    $this->importErrors['missing_purchase_order_number'] = 'Purchase order number does not exist in excel sheet.';
                }
            }
        }

        $result = CalculationForExpenseItemInvoiceAction::run($transactions, $this->importErrors, $this->fixDigit, $this->roundMethod);
        $transactions = $result['transactions'];
        $this->importErrors = $result['importErrors'];

        return $transactions;
    }

    private function getItemFields($row)
    {
        return [
            'item_name' => $row['item_name'] ?? '',
            'additional_description' => $row['additional_description_for_item'] ?? '',
            'ledger_name' => $row['ledger_name'] ?? '',
            'unit' => $row['unit'],
            'quantity' => isset($row['quantity']) && is_numeric($row['quantity']) ? $row['quantity'] : 0,
            'mrp' => isset($row['mrp']) && is_numeric($row['mrp']) ? $row['mrp'] : null,
            'rpu' => isset($row['rate_per_unit_without_gst']) && is_numeric($row['rate_per_unit_without_gst']) ? $row['rate_per_unit_without_gst'] : 0,
            'with_tax' => 0, //false
            'discount_type' => $row['discount_1_type'] == '%' ? 2 : 1,
            'discount_value' => isset($row['discount_1']) && is_numeric($row['discount_1']) ? (($row['discount_1_type'] ?? '') == '%' ? round($row['discount_1'], 3) : round($row['discount_1'], $this->fixDigit)) : 0,
            'discount_type_2' => $row['discount_2_type'] == '%' ? 2 : 1,
            'discount_value_2' => isset($row['discount_2']) && is_numeric($row['discount_2']) ? (($row['discount_2_type'] ?? '') == '%' ? round($row['discount_2'], 3) : round($row['discount_2'], $this->fixDigit)) : 0,
            'gst' => is_numeric($row['gst']) ? $row['gst'] : null,
        ];
    }

    private function getNonGstItemFields($row)
    {
        return [
            'item_name' => $row['item_name'] ?? '',
            'additional_description' => $row['additional_description_for_item'] ?? '',
            'ledger_name' => $row['ledger_name'] ?? '',
            'unit' => $row['unit'],
            'quantity' => isset($row['quantity']) && is_numeric($row['quantity']) ? $row['quantity'] : 0,
            'mrp' => isset($row['mrp']) && is_numeric($row['mrp']) ? $row['mrp'] : null,
            'rpu' => isset($row['rate_per_unit']) && is_numeric($row['rate_per_unit']) ? $row['rate_per_unit'] : 0,
            'with_tax' => 0, //false
            'discount_type' => $row['discount_1_type'] == '%' ? 2 : 1,
            'discount_value' => isset($row['discount_1']) && is_numeric($row['discount_1']) ? (($row['discount_1_type'] ?? '') == '%' ? round($row['discount_1'], 3) : round($row['discount_1'], $this->fixDigit)) : 0,
            'discount_type_2' => $row['discount_2_type'] == '%' ? 2 : 1,
            'discount_value_2' => isset($row['discount_2']) && is_numeric($row['discount_2']) ? (($row['discount_2_type'] ?? '') == '%' ? round($row['discount_2'], 3) : round($row['discount_2'], $this->fixDigit)) : 0,
        ];
    }

    private function getTransactionFields($row)
    {
        return [
            'purchase_title' => $row['purchase_title'] ?? null,
            'order_number' => $row['purchase_order_number'],
            'order_date' => ! empty($row['purchase_order_date']) ? getDateFromExcelDate($row['purchase_order_date']) : null,
            'full_order_number' => $row['purchase_order_number'],
            'supplier_name' => $row['supplier_name'] ?? '',
            'valid_for_type' => $row['valid_for_type'],
            'valid_for' => $row['valid_for'] ?? null,
            'gstin' => $row['gstin'] ?? null,
            'billing_address' => [
                'address_1' => $row['billing_address_line_1'] ?? null,
                'address_2' => $row['billing_address_line_2'] ?? null,
                'city' => $row['billing_address_city'] ?? null,
                'state' => $row['billing_address_state'] ?? null,
                'country' => $row['billing_address_country'] ?? null,
                'pin_code' => $row['billing_address_pincode'] ?? null,
            ],
            'shipping_address' => [
                'shipping_name' => $row['shipping_name'] ?? null,
                'shipping_gstin' => $row['shipping_gstin'] ?? null,
                'address_1' => $row['shipping_address_line_1'] ?? null,
                'address_2' => $row['shipping_address_line_2'] ?? null,
                'city' => $row['shipping_address_city'] ?? null,
                'state' => $row['shipping_address_state'] ?? null,
                'country' => $row['shipping_address_country'] ?? null,
                'pin_code' => $row['shipping_address_pincode'] ?? null,
            ],
            'broker_details' => [
                'broker_name' => $row['broker_name'],
                'brokerage_for_sale' => $row['brokerage'],
                'brokerage_on_value_type' => ! empty($row['brokerage_on_value']) ? (strtolower($row['brokerage_on_value']) == 'invoice value' ? Broker::INVOICE_VALUE : Broker::TAXABLE_VALUE) : null,
            ],
            'transport_details' => [
                'transport_name' => $row['transport_name'],
                'transporter_document_number' => $row['document_number'],
                'transporter_document_date' => ! empty($row['document_date']) ? getDateFromExcelDate($row['document_date']) : null,
                'transporter_vehicle_number' => $row['vehicle_number'],
            ],
            'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
            'items' => [$this->getItemFields($row)],
            'main_classification_nature_type' => $row['classification_nature_type'],
            'is_rcm_applicable' => strtolower($row['rcm_applicable']) == 'yes',
            'narration' => $row['note'] ?? '',
            'additional_charges' => $this->getAdditionalCharges($row),
            'cess' => isset($row['cess']) && is_numeric($row['cess']) ? $row['cess'] : 0,
            'add_less' => $this->getAddLess($row),
            'rounding_amount' => isset($row['round_off_amount']) && is_numeric($row['round_off_amount']) ? $row['round_off_amount'] : null,
            'is_gst_enabled' => true,
            'is_round_off_not_changed' => true,
        ];
    }

    private function getNonGstTransactionFields($row)
    {
        return [
            'purchase_title' => $row['purchase_title'] ?? null,
            'order_number' => $row['purchase_order_number'],
            'order_date' => ! empty($row['purchase_order_date']) ? getDateFromExcelDate($row['purchase_order_date']) : null,
            'full_order_number' => $row['purchase_order_number'],
            'supplier_name' => $row['supplier_name'] ?? '',
            'valid_for_type' => $row['valid_for_type'],
            'valid_for' => $row['valid_for'] ?? null,
            'billing_address' => [
                'address_1' => $row['billing_address_line_1'] ?? null,
                'address_2' => $row['billing_address_line_2'] ?? null,
                'city' => $row['billing_address_city'] ?? null,
                'state' => $row['billing_address_state'] ?? null,
                'country' => $row['billing_address_country'] ?? null,
                'pin_code' => $row['billing_address_pincode'] ?? null,
            ],
            'shipping_address' => [
                'shipping_name' => $row['shipping_name'] ?? null,
                'shipping_gstin' => $row['shipping_gstin'] ?? null,
                'address_1' => $row['shipping_address_line_1'] ?? null,
                'address_2' => $row['shipping_address_line_2'] ?? null,
                'city' => $row['shipping_address_city'] ?? null,
                'state' => $row['shipping_address_state'] ?? null,
                'country' => $row['shipping_address_country'] ?? null,
                'pin_code' => $row['shipping_address_pincode'] ?? null,
            ],
            'broker_details' => [
                'broker_name' => $row['broker_name'],
                'brokerage_for_sale' => $row['brokerage'],
                'brokerage_on_value_type' => ! empty($row['brokerage_on_value']) ? (strtolower($row['brokerage_on_value']) == 'invoice value' ? Broker::INVOICE_VALUE : Broker::TAXABLE_VALUE) : null,
            ],
            'transport_details' => [
                'transport_name' => $row['transport_name'],
                'transporter_document_number' => $row['document_number'],
                'transporter_document_date' => ! empty($row['document_date']) ? getDateFromExcelDate($row['document_date']) : null,
                'transporter_vehicle_number' => $row['vehicle_number'],
            ],
            'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
            'items' => [$this->getNonGstItemFields($row)],
            'main_classification_nature_type' => null,
            'is_rcm_applicable' => null,
            'narration' => $row['note'] ?? '',
            'additional_charges' => $this->getAdditionalChargesForNonGst($row),
            'add_less' => $this->getAddLess($row),
            'rounding_amount' => isset($row['round_off_amount']) && is_numeric($row['round_off_amount']) ? $row['round_off_amount'] : null,
            'is_gst_enabled' => false,
            'is_round_off_not_changed' => true,
        ];
    }

    private function getAdditionalCharges($row)
    {
        $data = [];

        if (! empty($row['additional_charges_1_ledger'])) {
            $data['additional_charges_1'] = [
                'ledger' => $row['additional_charges_1_ledger'],
                'ac_type' => ($row['additional_charges_1_type'] ?? '') == '%' ? 2 : 1,
                'ac_value' => isset($row['additional_charges_1_amount_without_gst']) && is_numeric($row['additional_charges_1_amount_without_gst']) ? (($row['additional_charges_1_type'] ?? '') == '%' ? round($row['additional_charges_1_amount_without_gst'], 3) : round($row['additional_charges_1_amount_without_gst'], $this->fixDigit)) : 0,
                'ac_gst' => $row['additional_charges_1_gst'] ?? null,
            ];
        }

        if (! empty($row['additional_charges_2_ledger'])) {
            $data['additional_charges_2'] = [
                'ledger' => $row['additional_charges_2_ledger'],
                'ac_type' => ($row['additional_charges_2_type'] ?? '') == '%' ? 2 : 1,
                'ac_value' => isset($row['additional_charges_2_amount_without_gst']) && is_numeric($row['additional_charges_2_amount_without_gst']) ? (($row['additional_charges_2_type'] ?? '') == '%' ? round($row['additional_charges_2_amount_without_gst'], 3) : round($row['additional_charges_2_amount_without_gst'], $this->fixDigit)) : 0,
                'ac_gst' => $row['additional_charges_2_gst'] ?? null,
            ];
        }

        return $data;
    }

    private function getAdditionalChargesForNonGst($row)
    {
        $data = [];

        if (! empty($row['additional_charges_1_ledger'])) {
            $data['additional_charges_1'] = [
                'ledger' => $row['additional_charges_1_ledger'],
                'ac_type' => ($row['additional_charges_1_type'] ?? '') == '%' ? 2 : 1,
                'ac_value' => isset($row['additional_charges_1_amount']) && is_numeric($row['additional_charges_1_amount']) ? (($row['additional_charges_1_type'] ?? '') == '%' ? round($row['additional_charges_1_amount'], 3) : round($row['additional_charges_1_amount'], $this->fixDigit)) : 0,
            ];
        }

        if (! empty($row['additional_charges_2_ledger'])) {
            $data['additional_charges_2'] = [
                'ledger' => $row['additional_charges_2_ledger'],
                'ac_type' => ($row['additional_charges_2_type'] ?? '') == '%' ? 2 : 1,
                'ac_value' => isset($row['additional_charges_2_amount']) && is_numeric($row['additional_charges_2_amount']) ? (($row['additional_charges_2_type'] ?? '') == '%' ? round($row['additional_charges_2_amount'], 3) : round($row['additional_charges_2_amount'], $this->fixDigit)) : 0,
            ];
        }

        return $data;
    }

    private function getAddLess($row)
    {
        $data = [];

        if (! empty($row['add_less_1_ledger'])) {
            $data['add_less_1'] = [
                'ledger' => $row['add_less_1_ledger'],
                'al_ledger_id' => null,
                'al_type' => $row['add_less_1_type'] == '%' ? 2 : 1,
                'al_value' => isset($row['add_less_1_amount']) && is_numeric($row['add_less_1_amount']) ? (($row['add_less_1_type'] ?? '') == '%' ? round($row['add_less_1_amount'], 3) : round($row['add_less_1_amount'], $this->fixDigit)) : 0,
                'al_is_show_in_print' => true,
            ];
        }

        if (! empty($row['add_less_2_ledger'])) {
            $data['add_less_2'] = [
                'ledger' => $row['add_less_2_ledger'],
                'al_ledger_id' => null,
                'al_type' => $row['add_less_2_type'] == '%' ? 2 : 1,
                'al_value' => isset($row['add_less_2_amount']) && is_numeric($row['add_less_2_amount']) ? (($row['add_less_2_type'] ?? '') == '%' ? round($row['add_less_2_amount'], 3) : round($row['add_less_2_amount'], $this->fixDigit)) : 0,
                'al_is_show_in_print' => true,
            ];
        }

        return $data;
    }

    public function gstFields()
    {
        return [
            'purchase_order_number',
            'purchase_order_date',
            'purchase_title',
            'supplier_name',
            'gstin',
            'valid_for',
            'valid_for_type',
            'billing_address_line_1',
            'billing_address_line_2',
            'billing_address_country',
            'billing_address_state',
            'billing_address_city',
            'billing_address_pincode',
            'shipping_name',
            'shipping_gstin',
            'shipping_address_line_1',
            'shipping_address_line_2',
            'shipping_address_country',
            'shipping_address_state',
            'shipping_address_city',
            'shipping_address_pincode',
            'broker_name',
            'brokerage',
            'brokerage_on_value',
            'transport_name',
            'document_number',
            'vehicle_number',
            'document_date',
            'item_name',
            'additional_description_for_item',
            'ledger_name',
            'unit',
            'quantity',
            'mrp',
            'rate_per_unit_without_gst',
            'gst',
            'discount_1_type',
            'discount_1',
            'discount_2_type',
            'discount_2',
            'classification_nature_type',
            'rcm_applicable',
            'itc_applicable',
            'additional_charges_1_ledger',
            'additional_charges_1_type',
            'additional_charges_1_amount_without_gst',
            'additional_charges_1_gst',
            'additional_charges_2_ledger',
            'additional_charges_2_type',
            'additional_charges_2_amount_without_gst',
            'additional_charges_2_gst',
            'cess',
            'add_less_1_ledger',
            'add_less_1_type',
            'add_less_1_amount',
            'add_less_2_ledger',
            'add_less_2_type',
            'add_less_2_amount',
            'round_off_amount',
            'note',
        ];
    }

    public function nonGstFields()
    {
        return [
            'purchase_order_number',
            'purchase_order_date',
            'purchase_title',
            'supplier_name',
            'valid_for',
            'valid_for_type',
            'billing_address_line_1',
            'billing_address_line_2',
            'billing_address_country',
            'billing_address_state',
            'billing_address_city',
            'billing_address_pincode',
            'shipping_name',
            'shipping_address_line_1',
            'shipping_address_line_2',
            'shipping_address_country',
            'shipping_address_state',
            'shipping_address_city',
            'shipping_address_pincode',
            'broker_name',
            'brokerage',
            'brokerage_on_value',
            'transport_name',
            'document_number',
            'vehicle_number',
            'document_date',
            'item_name',
            'additional_description_for_item',
            'ledger_name',
            'unit',
            'quantity',
            'mrp',
            'rate_per_unit',
            'discount_1_type',
            'discount_1',
            'discount_2_type',
            'discount_2',
            'additional_charges_1_ledger',
            'additional_charges_1_type',
            'additional_charges_1_amount',
            'additional_charges_2_ledger',
            'additional_charges_2_type',
            'additional_charges_2_amount',
            'add_less_1_ledger',
            'add_less_1_type',
            'add_less_1_amount',
            'add_less_2_ledger',
            'add_less_2_type',
            'add_less_2_amount',
            'round_off_amount',
            'note',
        ];
    }
}
