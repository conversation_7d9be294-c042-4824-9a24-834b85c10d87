<?php

namespace App\Imports;

use App\Actions\Income\CheckItemUintOfMeasurement;
use App\Models\DeliveryChallanTransaction;
use App\Models\Ledger;
use App\Models\LockTransaction;
use App\Models\Master\Broker;
use App\Models\Master\Customer;
use App\Models\Master\ItemMaster;
use App\Models\Master\Supplier;
use App\Models\Master\Transport;
use App\Models\SaleTransaction;
use App\Models\UnitOfMeasurement;
use App\Repositories\v1\DeliveryChallanTransactionRepository;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class DeliveryChallanInvoiceImport implements SkipsEmptyRows, ToCollection, WithHeadingRow, WithMultipleSheets
{
    use Importable;

    public $importErrors = [];

    public $company;

    public $fixDigit;

    public $roundMethod;

    public $totalInvoice;

    public $notImportedInvoice;

    public function sheets(): array
    {
        return [
            0 => $this,
        ];
    }

    public function collection(Collection $collection)
    {
        if ($collection->isEmpty()) {
            $this->importErrors['empty_excel'] = "The excel sheet doesn't have any transactions.";
        } else {
            $transactions = [];
            $this->company = getCurrentCompany();
            $this->fixDigit = getCompanyFixedDigitNumber();

            $transactions = $this->dataArray($collection, $transactions);

            $currentTime = Carbon::now();
            $iteration = 0;
            foreach ($transactions as $invoiceNo => $transaction) {
                // this is for next invoice number issue fix
                $transaction['created_at'] = $currentTime->copy()->addSeconds($iteration);
                $transaction['updated_at'] = $currentTime->copy()->addSeconds($iteration);
                $iteration++;

                $deliveryChallanExist = DeliveryChallanTransaction::whereChallanNumber($invoiceNo)
                    ->financialYearDate()
                    ->whereCompanyId($this->company->id)
                    ->exists();

                if ($deliveryChallanExist) {
                    $this->importErrors[$invoiceNo][] = "This invoice no {$invoiceNo} already exists.";

                    continue;
                }

                $isTransactionCreate = true;

                if (empty($transaction['challan_date'])) {
                    $this->importErrors[$invoiceNo][] = 'Challan date does not exist in the excel file.';
                    $isTransactionCreate = false;
                }
                if ($transaction['challan_date'] == false) {
                    $this->importErrors[$invoiceNo][] = 'Challan date formate is invalid, please enter a dd-mm-yyyy format.';
                    $isTransactionCreate = false;
                }
                if (checkDateIsCurrentFinancialYear($transaction['challan_date'])) {
                    $this->importErrors[$invoiceNo][] = 'This transaction challan date does not have a current financial year date.';
                    $isTransactionCreate = false;
                }
                if (! empty($transaction['challan_date']) && $transaction['challan_date'] !== false && ! checkDateIsCurrentFinancialYear($transaction['challan_date'])) {
                    $transactionDate = Carbon::parse($transaction['challan_date'])->format('Y-m-d');
                    $isLocked = isLockTransaction(LockTransaction::INCOME, $transactionDate) ?? false;
                    if ($isLocked) {
                        $lockDate = Carbon::parse(getTransactionsLockDate()[LockTransaction::INCOME])->format('d-m-Y');
                        $this->importErrors[$invoiceNo][] = 'This transaction challan date is locked please add date after '.$lockDate;
                        $isTransactionCreate = false;
                    }
                }
                if (! empty($transaction['party_phone_number'])) {
                    if (empty($transaction['region_iso']) || empty($transaction['region_code'])) {
                        $this->importErrors[$invoiceNo][] = 'Region ISO code or region code does not exist in the Excel file.';
                        $isTransactionCreate = false;
                    }

                    // Validate phone number length if the region is India
                    if (! empty($transaction['region_iso']) && strtoupper($transaction['region_iso']) === 'IN') {
                        if (! preg_match('/^\d{10}$/', $transaction['party_phone_number'])) {
                            $this->importErrors[$invoiceNo][] = 'Phone number must be exactly 10 digits for India.';
                            $isTransactionCreate = false;
                        }
                    }
                }
                if ($transaction['invoice_date'] !== null && $transaction['invoice_date'] === false) {
                    $this->importErrors[$invoiceNo][] = 'Invoice date format is invalid. Please enter a dd-mm-yyyy format.';
                    $isTransactionCreate = false;
                }
                if ($transaction['transport_details']['transporter_document_date'] !== null && $transaction['transport_details']['transporter_document_date'] === false) {
                    $this->importErrors[$invoiceNo][] = 'Document date format is invalid. Please enter a dd-mm-yyyy format.';
                    $isTransactionCreate = false;
                }
                if ($transaction['other_details']['po_date'] !== null && $transaction['other_details']['po_date'] === false) {
                    $this->importErrors[$invoiceNo][] = 'PO date formate is invalid, please enter a dd-mm-yyyy format.';
                    $isTransactionCreate = false;
                }

                $customer = Ledger::whereRaw('lower(name) = ? ', [strtolower(trim($transaction['customer_name']))])->whereIn('model_type', [Customer::class, Supplier::class])->first();
                if (empty($customer)) {
                    $this->importErrors[$invoiceNo][] = "This customer {$transaction['customer_name']} does not exist in the system.";
                    $isTransactionCreate = false;
                } else {
                    $transaction['party_ledger_id'] = $customer->id;
                }

                $invoiceNumber = null;
                if (! empty($transaction['invoice_number'])) {
                    $invoiceNumber = SaleTransaction::whereCustomerLedgerId($customer->id)
                        ->whereFullInvoiceNumber($transaction['invoice_number'])
                        ->first();
                }
                $transaction['invoice_number'] = ! empty($invoiceNumber) ? $invoiceNumber->id : null;

                // $transaction['dispatch_address'] = [
                //     'dispatch_address_1' => ! empty($transaction['dispatch_address_1']) ? $transaction['dispatch_address_1'] : $this->company->billingAddress->address_1,
                //     'dispatch_address_2' => ! empty($transaction['dispatch_address_2']) ? $transaction['dispatch_address_2'] : $this->company->billingAddress->address_2,
                //     'dispatch_address_city' => ! empty($transaction['dispatch_address_city']) ? $transaction['dispatch_address_city'] : $this->company->billingAddress->city_id,
                //     'dispatch_address_state' => ! empty($transaction['dispatch_address_state']) ? $transaction['dispatch_address_state'] : $this->company->billingAddress->state_id,
                //     'dispatch_address_country' => ! empty($transaction['dispatch_address_country']) ? $transaction['dispatch_address_country'] : $this->company->billingAddress->country_id,
                //     'dispatch_address_pincode' => ! empty($transaction['dispatch_address_pincode']) ? $transaction['dispatch_address_pincode'] : $this->company->billingAddress->pincode,
                // ];

                $billingCountry = $transaction['billing_address']['country'];
                $transaction['billing_address']['country_id'] = null;
                if (! empty($billingCountry)) {
                    $transaction['billing_address']['country_id'] = getCountryId($billingCountry);
                    if (empty($transaction['billing_address']['country_id'])) {
                        $this->importErrors[$invoiceNo][] = "This country {$billingCountry} does not exist in the system.";
                        $isTransactionCreate = false;
                    }
                } elseif (empty($billingCountry) && $this->company->is_gst_applicable) {
                    $this->importErrors[$invoiceNo][] = "This country {$billingCountry} does not exist in the excel sheet.";
                    $isTransactionCreate = false;
                }

                $billingState = $transaction['billing_address']['state'];
                $transaction['billing_address']['state_id'] = null;
                if (! empty($billingState)) {
                    $transaction['billing_address']['state_id'] = getStateId($billingState, $transaction['billing_address']['country_id']);
                    if (empty($transaction['billing_address']['state_id'])) {
                        $this->importErrors[$invoiceNo][] = "This state {$billingState} does not exist in the system.";
                        $isTransactionCreate = false;
                    }
                } elseif (empty($billingState) && $this->company->is_gst_applicable) {
                    $this->importErrors[$invoiceNo][] = "This state {$billingState} does not exist in the excel sheet.";
                    $isTransactionCreate = false;
                }

                $billingCity = $transaction['billing_address']['city'];
                $transaction['billing_address']['city_id'] = null;
                if (! empty($billingCity)) {
                    $transaction['billing_address']['city_id'] = getCityId($billingCity, $transaction['billing_address']['state_id']) ?? null;
                }

                $shippingCountry = $transaction['shipping_address']['country'];
                if (! empty($shippingCountry)) {
                    $transaction['shipping_address']['country_id'] = getCountryId($shippingCountry) ?? null;
                }

                $shippingState = $transaction['shipping_address']['state'];
                if (! empty($shippingState)) {
                    $transaction['shipping_address']['state_id'] = getStateId($shippingState, $transaction['shipping_address']['country_id']) ?? null;
                }

                $shippingCity = $transaction['shipping_address']['city'];
                if (! empty($shippingCity)) {
                    $transaction['shipping_address']['city_id'] = getCityId($shippingCity, $transaction['shipping_address']['state_id']) ?? null;
                }

                $brokerName = $transaction['broker_details']['broker_name'];
                if (! empty($brokerName)) {
                    $broker = Broker::whereRaw('lower(broker_name) = ? ', [strtolower($brokerName)])->first();
                    if (empty($broker)) {
                        $this->importErrors[$invoiceNo][] = "This broker {$brokerName} does not exist in the system.";
                        $isTransactionCreate = false;
                    } else {
                        $transaction['broker_details']['broker_id'] = $broker->id;
                    }
                }

                $transportName = $transaction['transport_details']['transport_name'];
                if (! empty($transportName)) {
                    $transport = Transport::whereRaw('lower(transporter_name) = ? ', [strtolower($transportName)])->first();
                    if (empty($transport)) {
                        $this->importErrors[$invoiceNo][] = "This transporter {$transportName} does not exist in the system.";
                        $isTransactionCreate = false;
                    } else {
                        $transaction['transport_details']['transport_id'] = $transport->id;
                    }
                }

                if (! empty($transaction['narration']) && mb_strlen($transaction['narration']) > 250) {
                    $this->importErrors[$invoiceNo][] = 'Narration should be less than or equal to 250 characters.';
                    $isTransactionCreate = false;
                }

                if (! empty($transaction['term_and_condition']) && mb_strlen($transaction['term_and_condition']) > 2000) {
                    $this->importErrors[$invoiceNo][] = 'Term & Condition should be less than or equal to 2000 characters.';
                    $isTransactionCreate = false;
                }

                foreach ($transaction['items'] as $key => $item) {
                    $itemDetails = ItemMaster::with('model')->whereRaw('lower(item_name) = ? ', [strtolower($item['item_name'])])->first();
                    if (empty($itemDetails)) {
                        $this->importErrors[$invoiceNo][] = "This item {$item['item_name']} does not exist in the system.";
                        $isTransactionCreate = false;
                    } else {
                        if ($itemDetails->status == false) {
                            $this->importErrors[$invoiceNo][] = "This item {$item['item_name']} is not active in the system.";
                            $isTransactionCreate = false;
                        }
                        $transaction['items'][$key]['item_id'] = $itemDetails->id;
                    }

                    $decimalPlacesForQuantity = $itemDetails->model->decimal_places ?? 2;
                    $quantity = (string) $item['quantity'];

                    if (strpos($quantity, '.') !== false) {
                        $actualDecimals = strlen(explode('.', $quantity)[1] ?? '');
                        if ($actualDecimals > $decimalPlacesForQuantity) {
                            $this->importErrors[$invoiceNo][] = "Quantity should have a maximum of {$decimalPlacesForQuantity} decimal places.";
                            $isTransactionCreate = false;
                        }
                    }

                    if (! empty($item['additional_description']) && mb_strlen($item['additional_description']) > 1000) {
                        $this->importErrors[$invoiceNo][] = "Additional description for Item {$item['ledger_name']} should be less than or equal to 1000 characters.";
                        $isTransactionCreate = false;
                    }

                    $unitCode = explode('-', $item['unit'])[0] ?? $item['unit'];
                    $unitOfMeasurement = UnitOfMeasurement::whereRaw('lower(code) = ? ', [strtolower($unitCode)])->first();
                    if (empty($unitOfMeasurement)) {
                        $this->importErrors[$invoiceNo][] = "This item's unit {$item['unit']} does not exist in the system.";
                        $isTransactionCreate = false;
                    } elseif (! empty($itemDetails)) {
                        $checkUnitOfMeasurement = '';
                        $checkUnitOfMeasurement = CheckItemUintOfMeasurement::run($unitOfMeasurement, $itemDetails);
                        if (! $checkUnitOfMeasurement) {
                            $this->importErrors[$invoiceNo][] = "This item's unit {$item['unit']} does not match with primary or secondary unit in the system.";
                            $isTransactionCreate = false;
                        }
                        $transaction['items'][$key]['unit_id'] = $unitOfMeasurement->id;
                    } else {
                        $transaction['items'][$key]['unit_id'] = $unitOfMeasurement->id;
                    }

                    if (empty($item['quantity']) || $item['quantity'] <= 0) {
                        $this->importErrors[$invoiceNo][] = "This item's quantity {$item['quantity']} is not valid. It should be greater than 0.";
                        $isTransactionCreate = false;
                    } else {
                        $quantity = 0;
                        $quantity = isset(explode('.', $item['quantity'])[1]) ? explode('.', $item['quantity'])[1] : 0;
                        if (! empty($quantity) && ! empty($itemDetails) && strlen($quantity) > $itemDetails->model->decimal_places) {
                            $this->importErrors[$invoiceNo][] = "This item's quantity {$item['quantity']} is not valid. It should be less or equal to {$itemDetails->model->decimal_places} decimal places.";
                            $isTransactionCreate = false;
                        } else {
                            $transaction['items'][$key]['quantity'] = $item['quantity'];
                        }
                    }
                }

                if ($isTransactionCreate) {
                    $input = $transaction;
                    $input['is_import'] = true;
                    $input['company'] = $this->company;
                    /** @var DeliveryChallanTransactionRepository $deliveryChallanTransactionRepository */
                    $deliveryChallanTransactionRepository = App::make(DeliveryChallanTransactionRepository::class);
                    DeliveryChallanTransaction::withoutTimestamps(function () use ($deliveryChallanTransactionRepository, $input) {
                        $deliveryChallanTransactionRepository->store($input);
                    });
                }
            }
        }

        if (! isset($this->importErrors['missing_voucher_number']) && ! isset($this->importErrors['wrong_excel']) && ! isset($this->importErrors['empty_excel']) && ! isset($this->importErrors['field_missing']) && ! isset($this->importErrors['unsupported_formula'])) {
            $data = $collection->whereIn('challan_number', array_keys($this->importErrors));
            foreach ($data as $key => $d) {
                $data[$key] = collect($d)->merge(['error' => implode(', ', array_unique($this->importErrors[$d['challan_number']]))]);
                $data[$key]['challan_date'] = parseExcelDateForImportTransaction($data[$key]['challan_date']);
                $data[$key]['invoice_date'] = parseExcelDateForImportTransaction($data[$key]['invoice_date']);
                $data[$key]['document_date'] = parseExcelDateForImportTransaction($data[$key]['document_date']);
                $data[$key]['po_date'] = parseExcelDateForImportTransaction($data[$key]['po_date']);
            }
            $this->totalInvoice = empty($transactions) ? 0 : count($transactions);
            $this->notImportedInvoice = count($this->importErrors);
            $this->importErrors = $data;
        }

        return $this->importErrors;
    }

    public function dataArray(Collection $collection, array $transactions)
    {
        $fieldMissing = checkMissingKeys($this->gstFields(), $collection);
        if (! empty($fieldMissing)) {
            $this->importErrors['field_missing'] = $fieldMissing;
        } else {
            foreach ($collection as $row) {
                removeExcelFileExtraFields($row);
                if (! $row->has('item_name')) {
                    $this->importErrors['wrong_excel'] = 'You are importing wrong excel file.';
                    break;
                }
                foreach ($row as $cellKey => $cellValue) {
                    if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                        $this->importErrors['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey.'.';
                        break 2;
                    }
                }
                if (! empty($row['challan_number'])) {
                    if (array_key_exists($row['challan_number'], $transactions)) {
                        $transactions[$row['challan_number']]['items'][] = $this->getItemFields($row);
                    } else {
                        $transactions[$row['challan_number']] = $this->getTransactionFields($row);
                    }
                } else {
                    $this->importErrors['missing_voucher_number'] = 'Delivery challan number does not exist in the excel sheet.';
                }
            }
        }

        return $transactions;
    }

    private function getItemFields($row)
    {
        return [
            'item_name' => $row['item_name'] ?? '',
            'additional_description' => $row['additional_description_for_item'] ?? '',
            'unit' => $row['unit'],
            'quantity' => isset($row['quantity']) && is_numeric($row['quantity']) ? $row['quantity'] : 0,
        ];
    }

    private function getTransactionFields($row)
    {
        $regionIso = isset($row['country_code_for_mobile_number']) ? explode('-', $row['country_code_for_mobile_number'])[0] : null;
        $regionCode = isset($row['country_code_for_mobile_number']) ? explode('-', $row['country_code_for_mobile_number'])[1] : null;

        return [
            'challan_number' => $row['challan_number'] ?? null,
            'number_for_challan' => $row['challan_number'] ?? null,
            'challan_date' => ! empty($row['challan_date']) ? getDateFromExcelDate($row['challan_date']) : null,
            'region_iso' => $regionIso ? trim($regionIso) : null,
            'region_code' => $regionCode ? trim($regionCode) : null,
            'party_phone_number' => $row['mobile_number'] ?? null,
            // 'dispatch_address' => [
            //     'address_1' => $row['dispatch_address_1'] ?? null,
            //     'address_2' => $row['dispatch_address_2'] ?? null,
            //     'city' => $row['dispatch_address_city'] ?? null,
            //     'state' => $row['dispatch_address_state'] ?? null,
            //     'country' => $row['dispatch_address_country'] ?? null,
            //     'pin_code' => $row['dispatch_address_pincode'] ?? null,
            // ],
            'customer_name' => $row['customer_name'] ?? '',
            'gstin' => $row['gstin'] ?? null,
            'invoice_number' => $row['invoice_number'] ?? null,
            'invoice_date' => ! empty($row['invoice_date']) ? getDateFromExcelDate($row['invoice_date']) : null,
            'billing_address' => [
                'address_1' => $row['billing_address_line_1'] ?? null,
                'address_2' => $row['billing_address_line_2'] ?? null,
                'city' => $row['billing_address_city'] ?? null,
                'state' => $row['billing_address_state'] ?? null,
                'country' => $row['billing_address_country'] ?? null,
                'pin_code' => $row['billing_address_pincode'] ?? null,
            ],
            'shipping_address' => [
                'shipping_name' => $row['shipping_name'] ?? null,
                'shipping_gstin' => $row['shipping_gstin'] ?? null,
                'address_1' => $row['shipping_address_line_1'] ?? null,
                'address_2' => $row['shipping_address_line_2'] ?? null,
                'city' => $row['shipping_address_city'] ?? null,
                'state' => $row['shipping_address_state'] ?? null,
                'country' => $row['shipping_address_country'] ?? null,
                'pin_code' => $row['shipping_address_pincode'] ?? null,
            ],
            'broker_details' => [
                'broker_name' => $row['broker_name'],
                'brokerage_for_sale' => $row['brokerage'],
                'brokerage_on_value_type' => ! empty($row['brokerage_on_value']) ? (strtolower($row['brokerage_on_value']) == 'invoice value' ? Broker::INVOICE_VALUE : Broker::TAXABLE_VALUE) : null,
            ],
            'transport_details' => [
                'transport_name' => $row['transport_name'],
                'transporter_document_number' => $row['document_number'],
                'transporter_document_date' => ! empty($row['document_date']) ? getDateFromExcelDate($row['document_date']) : null,
                'transporter_vehicle_number' => $row['vehicle_number'],
            ],
            'other_details' => [
                'po_no' => $row['po_no'],
                'po_date' => ! empty($row['po_date']) ? getDateFromExcelDate($row['po_date']) : null,
            ],
            'items' => [$this->getItemFields($row)],
            'narration' => $row['note'] ?? '',
            'term_and_condition' => $row['terms_conditions'] ?? '',
        ];
    }

    public function gstFields()
    {
        $data = [
            'challan_number',
            'challan_date',
            'customer_name',
            'country_code_for_mobile_number',
            'mobile_number',
            'invoice_number',
            'invoice_date',
            'billing_address_line_1',
            'billing_address_line_2',
            'billing_address_country',
            'billing_address_state',
            'billing_address_city',
            'billing_address_pincode',
            'shipping_name',
            'shipping_gstin',
            'shipping_address_line_1',
            'shipping_address_line_2',
            'shipping_address_country',
            'shipping_address_state',
            'shipping_address_city',
            'shipping_address_pincode',
            'broker_name',
            'brokerage',
            'brokerage_on_value',
            'transport_name',
            'document_number',
            'document_date',
            'vehicle_number',
            'po_no',
            'po_date',
            'item_name',
            'additional_description_for_item',
            'unit',
            'quantity',
            'note',
            'terms_conditions',
        ];

        if ($this->company->is_gst_applicable == 1) {
            $data[] = 'gstin';
        }

        return $data;
    }
}
