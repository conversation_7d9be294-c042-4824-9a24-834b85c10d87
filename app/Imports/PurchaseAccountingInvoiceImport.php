<?php

namespace App\Imports;

use App\Actions\Imports\CalculationForExpenseAccountingInvoiceAction;
use App\Actions\Validation\CheckValidationsAction;
use App\Models\GstTax;
use App\Models\Ledger;
use App\Models\LockTransaction;
use App\Models\Master\Bank;
use App\Models\Master\Broker;
use App\Models\Master\Cash;
use App\Models\Master\Customer;
use App\Models\Master\Expense;
use App\Models\Master\ExpenseTransactionMaster;
use App\Models\Master\FixedAsset;
use App\Models\Master\Income;
use App\Models\Master\Supplier;
use App\Models\Master\TaxesTcs;
use App\Models\Master\TaxesTds;
use App\Models\Master\TdsReceivable;
use App\Models\Master\Transport;
use App\Models\PaymentMode;
use App\Models\PurchaseTransaction;
use App\Repositories\v1\PurchaseTransactionRepository;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

/**
 * Class PurchaseAccountingInvoiceImport
 */
class PurchaseAccountingInvoiceImport implements SkipsEmptyRows, ToCollection, WithHeadingRow, WithMultipleSheets
{
    use Importable;

    public $importErrors = [];

    public $company;

    public $fixDigit;

    public $roundMethod;

    public $totalInvoice;

    public $notImportedInvoice;

    public function sheets(): array
    {
        return [
            0 => $this,
        ];
    }

    public function collection(Collection $collection)
    {
        if ($collection->isEmpty()) {
            $this->importErrors['empty_excel'] = "The excel sheet doesn't have any transactions.";
        } else {
            $isCompanyGstApplicable = isCompanyGstApplicable();
            $transactions = [];
            $this->company = getCurrentCompany();
            $this->fixDigit = getCompanyFixedDigitNumber();
            $expenseTransactionMaster = ExpenseTransactionMaster::first();
            $this->roundMethod = $expenseTransactionMaster->round_off_method ?? Ledger::NORMAL_ROUNDING;

            if ($this->company->is_gst_applicable == 1) {
                $transactions = $this->dataArray($collection, $transactions);
            } else {
                $transactions = $this->dataArrayForNonGstApplicable($collection, $transactions);
            }

            $currentTime = Carbon::now();
            $iteration = 0;
            foreach ($transactions as $voucherNo => $transaction) {
                // this is for next invoice number issue fix
                $transaction['created_at'] = $currentTime->copy()->addSeconds($iteration);
                $transaction['updated_at'] = $currentTime->copy()->addSeconds($iteration);
                $iteration++;

                enableDeletedScope();
                $purchaseTransaction = PurchaseTransaction::whereVoucherNumber($voucherNo)->financialYearDate()->first();
                if (! empty($purchaseTransaction)) {
                    $this->importErrors[$voucherNo][] = "This voucher no {$voucherNo} already exists.";

                    continue;
                }
                disableDeletedScope();

                $isTransactionCreate = true;

                if (! empty($transaction['voucher_date']) && $transaction['voucher_date'] !== false && ! checkDateIsCurrentFinancialYear($transaction['voucher_date'])) {
                    $transactionDate = Carbon::parse($transaction['voucher_date'])->format('Y-m-d');
                    $isLocked = isLockTransaction(LockTransaction::EXPENSE, $transactionDate) ?? false;
                    if ($isLocked) {
                        $lockDate = Carbon::parse(getTransactionsLockDate()[LockTransaction::EXPENSE])->format('d-m-Y');
                        $this->importErrors[$voucherNo][] = 'This transaction date is locked please add date after '.$lockDate;
                        $isTransactionCreate = false;
                    }
                }

                if (empty($transaction['voucher_date'])) {
                    $this->importErrors[$voucherNo][] = 'Voucher Date does not exist in the excel file.';
                    $isTransactionCreate = false;
                }
                if ($transaction['voucher_date'] == false) {
                    $this->importErrors[$voucherNo][] = 'Date formate is invalid, please enter a dd-mm-yyyy format.';
                    $isTransactionCreate = false;
                }
                if (checkDateIsCurrentFinancialYear($transaction['voucher_date'])) {
                    $this->importErrors[$voucherNo][] = 'This transaction voucher date does not have a current financial year date.';
                    $isTransactionCreate = false;
                }
                if (empty($transaction['main_classification_nature_type']) && $this->company->is_gst_applicable == 1) {
                    $this->importErrors[$voucherNo][] = 'Classification nature type does not exist in the excel file.';
                    $isTransactionCreate = false;
                }
                if (empty($transaction['sale_number'])) {
                    $this->importErrors[$voucherNo][] = 'Invoice number does not exist in the excel file.';
                    $isTransactionCreate = false;
                }
                if (empty($transaction['date_of_invoice'])) {
                    $this->importErrors[$voucherNo][] = 'Invoice date does not exist in the excel file.';
                    $isTransactionCreate = false;
                }
                if ($transaction['date_of_invoice'] == false) {
                    $this->importErrors[$voucherNo][] = 'Invoice date formate is invalid, please enter a dd-mm-yyyy format.';
                    $isTransactionCreate = false;
                }
                if (checkDateIsCurrentFinancialYear($transaction['date_of_invoice'])) {
                    $this->importErrors[$voucherNo][] = 'This transaction Invoice date does not have a current financial year date.';
                    $isTransactionCreate = false;
                }
                if ($transaction['transport_details']['transporter_document_date'] !== null && $transaction['transport_details']['transporter_document_date'] === false) {
                    $this->importErrors[$voucherNo][] = 'Document date format is invalid. Please enter a dd-mm-yyyy format.';
                    $isTransactionCreate = false;
                }

                $supplier = Ledger::whereRaw('lower(name) = ? ', [strtolower(trim($transaction['supplier_name']))])
                    ->whereIn('model_type', [Customer::class, Supplier::class])->first();
                if (empty($supplier)) {
                    $this->importErrors[$voucherNo][] = "This supplier {$transaction['supplier_name']} does not exist in the system.";
                    $isTransactionCreate = false;
                } else {
                    $transaction['supplier_ledger_id'] = $supplier->id;
                }

                if (! empty($transaction['gstin']) && strlen($transaction['gstin']) !== 15) {
                    $this->importErrors[$voucherNo][] = "GST number {$transaction['gstin']} is invalid. Please enter a valid GST number.";
                    $isTransactionCreate = false;
                }

                // Billing Country
                $billingCountry = $transaction['billing_address']['country'];
                $transaction['billing_address']['country_id'] = null;
                if (! empty($billingCountry)) {
                    $transaction['billing_address']['country_id'] = getCountryId($billingCountry);
                    if (empty($transaction['billing_address']['country_id'])) {
                        $this->importErrors[$voucherNo][] = "This country {$billingCountry} does not exist in the system.";
                        $isTransactionCreate = false;
                    }
                } elseif (empty($billingCountry) && $this->company->is_gst_applicable) {
                    $this->importErrors[$voucherNo][] = "This country {$billingCountry} does not exist in the excel sheet.";
                    $isTransactionCreate = false;
                }

                // Billing State
                $billingState = $transaction['billing_address']['state'];
                $transaction['billing_address']['state_id'] = null;
                if (! empty($billingState)) {
                    $transaction['billing_address']['state_id'] = getStateId($billingState, $transaction['billing_address']['country_id']);
                    if (empty($transaction['billing_address']['state_id'])) {
                        $this->importErrors[$voucherNo][] = "This state {$billingState} does not exist in the system.";
                        $isTransactionCreate = false;
                    }
                } elseif (empty($billingState) && $this->company->is_gst_applicable) {
                    $this->importErrors[$voucherNo][] = "This state {$billingState} does not exist in the excel sheet.";
                    $isTransactionCreate = false;
                }

                // Billing City
                $billingCity = $transaction['billing_address']['city'];
                $transaction['billing_address']['city_id'] = null;
                if (! empty($billingCity)) {
                    $transaction['billing_address']['city_id'] = getCityId($billingCity, $transaction['billing_address']['state_id']) ?? null;
                }

                $shippingCountry = $transaction['shipping_address']['country'];
                if (! empty($shippingCountry)) {
                    $transaction['shipping_address']['country_id'] = getCountryId($shippingCountry) ?? null;
                }

                $shippingState = $transaction['shipping_address']['state'];
                if (! empty($shippingState)) {
                    $transaction['shipping_address']['state_id'] = getStateId($shippingState, $transaction['shipping_address']['country_id']) ?? null;
                }

                $shippingCity = $transaction['shipping_address']['city'];
                if (! empty($shippingCity)) {
                    $transaction['shipping_address']['city_id'] = getCityId($shippingCity, $transaction['shipping_address']['state_id']) ?? null;
                }

                $brokerName = $transaction['broker_details']['broker_name'];
                if (! empty($brokerName)) {
                    $broker = Broker::whereRaw('lower(broker_name) = ? ', [strtolower($brokerName)])->first();
                    if (empty($broker)) {
                        $this->importErrors[$voucherNo][] = "This broker {$brokerName} does not exist in the system.";
                        $isTransactionCreate = false;
                    } else {
                        $transaction['broker_details']['broker_id'] = $broker->id;
                    }
                }

                $transportName = $transaction['transport_details']['transport_name'];
                if (! empty($transportName)) {
                    $transport = Transport::whereRaw('lower(transporter_name) = ? ', [strtolower($transportName)])->first();
                    if (empty($transport)) {
                        $this->importErrors[$voucherNo][] = "This transporter {$transportName} does not exist in the system.";
                        $isTransactionCreate = false;
                    } else {
                        $transaction['transport_details']['transport_id'] = $transport->id;
                    }
                }

                $tcsLedgerName = $transaction['tcs_details']['tcs_ledger'];
                if (! empty($tcsLedgerName)) {
                    $tcsLedger = Ledger::whereRaw('lower(name) = ? ', [strtolower(trim($tcsLedgerName))])->whereModelType(TaxesTcs::class)->first();
                    if (empty($tcsLedger)) {
                        $this->importErrors[$voucherNo][] = "This tcs ledger {$tcsLedgerName} does not exist in the system.";
                        $isTransactionCreate = false;
                    } else {
                        $transaction['tcs_details']['tcs_tax_id'] = $tcsLedger->id;
                    }
                }

                $tdsLedgerName = $transaction['tds_details']['tds_ledger'];
                if (! empty($tdsLedgerName)) {
                    $tdsLedger = Ledger::whereRaw('lower(name) = ? ', [strtolower(trim($tdsLedgerName))])->whereIn('model_type', [TaxesTds::class, TdsReceivable::class])->first();
                    if (empty($tdsLedger)) {
                        $this->importErrors[$voucherNo][] = "This tds ledger {$tdsLedgerName} does not exist in the system.";
                        $isTransactionCreate = false;
                    } else {
                        $transaction['tds_details']['tds_tax_id'] = $tdsLedger->id;
                    }
                }

                if (! empty($transaction['narration']) && mb_strlen($transaction['narration']) > 250) {
                    $this->importErrors[$voucherNo][] = 'Narration should be less than or equal to 250 characters.';
                    $isTransactionCreate = false;
                }

                foreach ($transaction['ledgers'] as $key => $ledger) {
                    $ledgerDetails = Ledger::whereRaw('lower(name) = ? ', [strtolower(trim($ledger['ledger_name']))])
                        ->whereIn('model_type', [Income::class, Expense::class, FixedAsset::class])->first();
                    if (empty($ledgerDetails)) {
                        $this->importErrors[$voucherNo][] = "This ledger {$ledger['ledger_name']} does not exist in the system.";
                        $isTransactionCreate = false;
                    } else {
                        $transaction['ledgers'][$key]['ledger_id'] = $ledgerDetails->id;
                    }

                    if (! empty($ledger['additional_description']) && mb_strlen($ledger['additional_description']) > 1000) {
                        $this->importErrors[$voucherNo][] = "Additional description for ledger {$ledger['ledger_name']} should be less than or equal to 1000 characters.";
                        $isTransactionCreate = false;
                    }

                    if (empty($ledger['rpu']) || $ledger['rpu'] <= 0) {
                        $this->importErrors[$voucherNo][] = "This ledger's amount {$ledger['rpu']} is not valid. It should be greater than 0.";
                        $isTransactionCreate = false;
                    } else {
                        $transaction['ledgers'][$key]['rpu'] = $ledger['rpu'];
                    }

                    if ($transaction['is_gst_enabled']) {
                        if (empty($ledger['gst']) && $ledger['gst'] != '0') {
                            $this->importErrors[$voucherNo][] = "This ledger's gst does not exist in the excel sheet.";
                            $isTransactionCreate = false;
                        } else {
                            $gst = null;
                            if (! in_array(strtolower((string) $ledger['gst']), ['na', 'n/a', '0', 'exempt'])) {
                                $gst = GstTax::where('tax_rate', 'LIKE', $ledger['gst'].'%')->first();
                            } else {
                                $gst = GstTax::whereRaw('lower(name) = ? ', [strtolower(trim($ledger['gst']))])->first();
                            }

                            if (empty($gst)) {
                                $this->importErrors[$voucherNo][] = "This gst {$ledger['gst']} does not exist in the system.";
                                $isTransactionCreate = false;
                            } else {
                                $transaction['ledgers'][$key]['gst_id'] = $gst->id;
                            }
                        }
                    }
                }

                if (! empty($transaction['additional_charges']) && is_array($transaction['additional_charges'])) {
                    foreach ($transaction['additional_charges'] as $key => $charge) {
                        $ledgerDetails = Ledger::whereRaw('lower(name) = ?', [strtolower(trim($charge['ledger']))])
                            ->whereIn('model_type', [Income::class, Expense::class, FixedAsset::class])
                            ->first();

                        if (empty($ledgerDetails)) {
                            $this->importErrors[$voucherNo][] = "This additional charge's ledger {$charge['ledger']} does not exist in the system.";
                            $isTransactionCreate = false;
                        } else {
                            $transaction['additional_charges'][$key]['ac_ledger_id'] = $ledgerDetails->id;
                        }

                        if (! empty($charge['gst'])) {
                            $gstValue = strtolower(trim($charge['gst']));

                            if ($gstValue !== 'na' && $gstValue !== 'n/a') {
                                $gst = GstTax::where('tax_rate', 'LIKE', $gstValue.'%')->first();
                            } else {
                                $gst = GstTax::whereRaw('lower(name) = ?', [$gstValue])->first();
                            }

                            if (empty($gst)) {
                                $this->importErrors[$voucherNo][] = "This GST rate {$charge['gst']} does not exist in the system.";
                                $isTransactionCreate = false;
                            } else {
                                $transaction['additional_charges'][$key]['ac_gst_rate_id'] = $gst->id;
                            }
                        }
                    }
                }

                if (! empty($transaction['add_less']) && is_array($transaction['add_less'])) {
                    foreach ($transaction['add_less'] as $key => $addLess) {
                        $ledgerName = strtolower(trim($addLess['ledger']));
                        $ledgerDetails = Ledger::whereRaw('lower(name) = ?', [$ledgerName])->first();

                        if (empty($ledgerDetails)) {
                            $this->importErrors[$voucherNo][] = "This add less's ledger {$addLess['ledger']} does not exist in the system.";
                            $isTransactionCreate = false;
                        } else {
                            $transaction['add_less'][$key]['al_ledger_id'] = $ledgerDetails->id;
                        }
                    }
                }

                foreach ($transaction['payment_details'] as $key => $payment) {
                    if (! empty($payment['ledger'])) {
                        $ledgerDetails = Ledger::whereRaw('lower(name) = ? ', [strtolower(trim($payment['ledger']))])
                            ->whereIn('model_type', [Bank::class, Cash::class])->first();

                        if (empty($ledgerDetails)) {
                            $this->importErrors[$voucherNo][] = "This payment detail's ledger {$payment['ledger']} does not exist in the system.";
                            $isTransactionCreate = false;
                        } else {
                            $transaction['payment_details'][$key]['pd_ledger_id'] = $ledgerDetails->id;
                        }
                    } else {
                        $transaction['payment_details'][$key]['pd_ledger_id'] = null;
                    }

                    if (! empty($payment['mode'])) {
                        $paymentMode = PaymentMode::whereIn('use_for', [PaymentMode::PAYMENT, PaymentMode::BOTH])->where('name', 'LIKE', $payment['mode'].'%')->first();

                        if (empty($paymentMode)) {
                            $this->importErrors[$voucherNo][] = "This payment detail's mode {$payment['mode']} does not exist in the system.";
                            $isTransactionCreate = false;
                        } else {
                            $transaction['payment_details'][$key]['pd_mode'] = $paymentMode->id;
                        }
                    } else {
                        $transaction['payment_details'][$key]['pd_mode'] = null;
                    }
                }

                $isPaymentDetailsNull = true;
                foreach ($transaction['payment_details'] as $payment) {
                    if (! is_null($payment['ledger']) || ! is_null($payment['pd_date']) || $payment['pd_amount'] != 0) {
                        $isPaymentDetailsNull = false;
                        break;
                    }
                }
                if ($isPaymentDetailsNull) {
                    $transaction['payment_details'] = [];
                }

                if (! empty($transaction['payment_details'])) {
                    $totalPayment = collect($transaction['payment_details'] ?? [])->sum(fn ($pd) => $pd['pd_amount'] ?? 0);
                    $grandTotal = $transaction['grand_total'] ?? 0;

                    if (! empty($transaction['tds_details']) && is_array($transaction['tds_details'])) {
                        $totalTdsAmount = collect($transaction['tds_details'])->sum(fn ($tds) => $tds['tds_amount'] ?? 0);
                        $grandTotal -= $totalTdsAmount;
                    }

                    if (isset($transaction['grand_total']) && is_numeric($grandTotal)) {
                        if ($totalPayment > $transaction['grand_total']) {
                            $this->importErrors[$voucherNo][] = 'Payment amount cannot be greater than grand total.';
                            $isTransactionCreate = false;
                        }
                    } else {
                        $this->importErrors[$voucherNo][] = 'Grand total is missing or invalid.';
                        $isTransactionCreate = false;
                    }
                }

                if ($isTransactionCreate) {
                    $input = CheckValidationsAction::run($transaction, PurchaseTransaction::class);
                    $input['is_import'] = true;
                    $input['company'] = $this->company;
                    /** @var PurchaseTransactionRepository $purchaseTransactionRepository */
                    $purchaseTransactionRepository = App::make(PurchaseTransactionRepository::class);
                    PurchaseTransaction::withoutTimestamps(function () use ($purchaseTransactionRepository, $input) {
                        $purchaseTransactionRepository->store($input);
                    });
                }
            }
        }

        if (! isset($this->importErrors['missing_voucher_number']) && ! isset($this->importErrors['wrong_excel']) && ! isset($this->importErrors['empty_excel']) && ! isset($this->importErrors['field_missing']) && ! isset($this->importErrors['unsupported_formula'])) {
            $data = $collection->whereIn('voucher_number', array_keys($this->importErrors));

            foreach ($data as $key => $d) {
                $data[$key] = collect($d)->merge(['error' => implode(', ', array_unique($this->importErrors[$d['voucher_number']]))]);
                $data[$key]['voucher_date'] = parseExcelDateForImportTransaction($data[$key]['voucher_date']);
                $data[$key]['invoice_date'] = parseExcelDateForImportTransaction($data[$key]['invoice_date']);
                $data[$key]['document_date'] = parseExcelDateForImportTransaction($data[$key]['document_date']);
            }

            $this->totalInvoice = empty($transactions) ? 0 : count($transactions);
            $this->notImportedInvoice = count($this->importErrors);
            $this->importErrors = $data;
        }

        return $this->importErrors;
    }

    public function dataArray(Collection $collection, array $transactions)
    {
        $fieldMissing = checkMissingKeys($this->gstFields(), $collection);
        if (! empty($fieldMissing)) {
            $this->importErrors['field_missing'] = $fieldMissing;
        } else {
            foreach ($collection as $row) {
                removeExcelFileExtraFields($row);
                if ($row->has('item_name')) {
                    $this->importErrors['wrong_excel'] = 'You are importing wrong excel file.';
                    break;
                }
                foreach ($row as $cellKey => $cellValue) {
                    if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                        $this->importErrors['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey.'.';
                        break 2;
                    }
                }
                if (! empty($row['voucher_number'])) {
                    if (array_key_exists($row['voucher_number'], $transactions)) {
                        $transactions[$row['voucher_number']]['ledgers'][] = $this->getLedgerFields($row);
                    } else {
                        $transactions[$row['voucher_number']] = $this->getTransactionFields($row);
                    }
                } else {
                    $this->importErrors['missing_voucher_number'] = 'Voucher number does not exist in excel sheet.';
                }
            }
        }

        $result = CalculationForExpenseAccountingInvoiceAction::run($transactions, $this->importErrors, $this->fixDigit, $this->roundMethod);
        $transactions = $result['transactions'];
        $this->importErrors = $result['importErrors'];

        return $transactions;
    }

    public function dataArrayForNonGstApplicable(Collection $collection, array $transactions): array
    {
        $fieldMissing = checkMissingKeys($this->nonGstFields(), $collection);
        if (! empty($fieldMissing)) {
            $this->importErrors['field_missing'] = $fieldMissing;
        } else {
            foreach ($collection as $row) {
                removeExcelFileExtraFields($row);
                if ($row->has('item_name')) {
                    $this->importErrors['wrong_excel'] = 'You are importing wrong excel file.';
                    break;
                }
                foreach ($row as $cellKey => $cellValue) {
                    if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                        $this->importErrors['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey.'.';
                        break 2;
                    }
                }
                if (! empty($row['voucher_number'])) {
                    if (array_key_exists($row['voucher_number'], $transactions)) {
                        $transactions[$row['voucher_number']]['ledgers'][] = $this->getNonGstLedgerFields($row);
                    } else {
                        $transactions[$row['voucher_number']] = $this->getNonGstTransactionFields($row);
                    }
                } else {
                    $this->importErrors['missing_voucher_number'] = 'Voucher number does not exist in excel sheet.';
                }
            }
        }

        $result = CalculationForExpenseAccountingInvoiceAction::run($transactions, $this->importErrors, $this->fixDigit, $this->roundMethod);
        $transactions = $result['transactions'];
        $this->importErrors = $result['importErrors'];

        return $transactions;
    }

    private function getLedgerFields($row)
    {
        return [
            'ledger_name' => $row['ledger_name'] ?? '',
            'additional_description' => $row['additional_description_for_ledgers'] ?? '',
            'rpu' => isset($row['amount_without_gst']) && is_numeric($row['amount_without_gst']) ? $row['amount_without_gst'] : 0,
            'with_tax' => 0, //false
            'discount_type' => $row['discount_1_type'] == '%' ? 2 : 1,
            'discount_value' => isset($row['discount_1']) && is_numeric($row['discount_1']) ? (($row['discount_1_type'] ?? '') == '%' ? round($row['discount_1'], 3) : round($row['discount_1'], $this->fixDigit)) : 0,
            'discount_type_2' => $row['discount_2_type'] == '%' ? 2 : 1,
            'discount_value_2' => isset($row['discount_2']) && is_numeric($row['discount_2']) ? (($row['discount_2_type'] ?? '') == '%' ? round($row['discount_2'], 3) : round($row['discount_2'], $this->fixDigit)) : 0,
            'gst' => isset($row['gst']) ? $row['gst'] : null,
        ];
    }

    private function getNonGstLedgerFields($row)
    {
        return [
            'ledger_name' => $row['ledger_name'] ?? '',
            'additional_description' => $row['additional_description_for_ledgers'] ?? '',
            'rpu' => isset($row['amount']) && is_numeric($row['amount']) ? $row['amount'] : 0,
            'with_tax' => 0, //false
            'discount_type' => $row['discount_1_type'] == '%' ? 2 : 1,
            'discount_value' => isset($row['discount_1']) && is_numeric($row['discount_1']) ? (($row['discount_1_type'] ?? '') == '%' ? round($row['discount_1'], 3) : round($row['discount_1'], $this->fixDigit)) : 0,
            'discount_type_2' => $row['discount_2_type'] == '%' ? 2 : 1,
            'discount_value_2' => isset($row['discount_2']) && is_numeric($row['discount_2']) ? (($row['discount_2_type'] ?? '') == '%' ? round($row['discount_2'], 3) : round($row['discount_2'], $this->fixDigit)) : 0,
        ];
    }

    private function getTransactionFields($row)
    {
        return [
            'voucher_number' => $row['voucher_number'],
            'voucher_date' => ! empty($row['voucher_date']) ? getDateFromExcelDate($row['voucher_date']) : null,
            'sale_number' => $row['invoice_number'],
            'date_of_invoice' => ! empty($row['invoice_date']) ? getDateFromExcelDate($row['invoice_date']) : null,
            'supplier_name' => $row['supplier_name'] ?? '',
            'gstin' => $row['gstin'] ?? null,
            'billing_address' => [
                'address_1' => $row['billing_address_line_1'] ?? null,
                'address_2' => $row['billing_address_line_2'] ?? null,
                'city' => $row['billing_address_city'] ?? null,
                'state' => $row['billing_address_state'] ?? null,
                'country' => $row['billing_address_country'] ?? null,
                'pin_code' => $row['billing_address_pincode'] ?? null,
            ],
            'shipping_address' => [
                'shipping_name' => $row['shipping_name'] ?? null,
                'shipping_gstin' => $row['shipping_gstin'] ?? null,
                'address_1' => $row['shipping_address_line_1'] ?? null,
                'address_2' => $row['shipping_address_line_2'] ?? null,
                'city' => $row['shipping_address_city'] ?? null,
                'state' => $row['shipping_address_state'] ?? null,
                'country' => $row['shipping_address_country'] ?? null,
                'pin_code' => $row['shipping_address_pincode'] ?? null,
            ],
            'broker_details' => [
                'broker_name' => $row['broker_name'],
                'brokerage_for_sale' => $row['brokerage'],
                'brokerage_on_value_type' => ! empty($row['brokerage_on_value']) ? (strtolower($row['brokerage_on_value']) == 'invoice value' ? Broker::INVOICE_VALUE : Broker::TAXABLE_VALUE) : null,
            ],
            'transport_details' => [
                'transport_name' => $row['transport_name'],
                'transporter_document_number' => $row['document_number'],
                'transporter_document_date' => ! empty($row['document_date']) ? getDateFromExcelDate($row['document_date']) : null,
                'transporter_vehicle_number' => $row['vehicle_number'],
            ],
            'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
            'ledgers' => [$this->getLedgerFields($row)],
            'main_classification_nature_type' => $row['classification_nature_type'],
            'is_rcm_applicable' => strtolower($row['rcm_applicable']) == 'yes',
            'narration' => $row['note'] ?? '',
            'term_and_condition' => $row['terms_conditions'] ?? '',
            'additional_charges' => $this->getAdditionalCharges($row),
            'cess' => isset($row['cess']) && is_numeric($row['cess']) ? $row['cess'] : 0,
            'tcs_details' => [
                'tcs_ledger' => $row['tcs_ledger'],
                'tcs_rate' => isset($row['tcs_rate']) && is_numeric($row['tcs_rate']) ? $row['tcs_rate'] : 0,
            ],
            'add_less' => $this->getAddLess($row),
            'rounding_amount' => isset($row['round_off_amount']) && is_numeric($row['round_off_amount']) ? $row['round_off_amount'] : null,
            'tds_details' => [
                'tds_ledger' => $row['tds_ledger'],
                'tds_rate' => isset($row['tds_rate']) && is_numeric($row['tds_rate']) ? $row['tds_rate'] : 0,
            ],
            'payment_details' => $this->getPaymentDetails($row),
            'is_gst_enabled' => true,
            'is_round_off_not_changed' => false,
        ];
    }

    private function getNonGstTransactionFields($row)
    {
        return [
            'voucher_number' => $row['voucher_number'],
            'voucher_date' => ! empty($row['voucher_date']) ? getDateFromExcelDate($row['voucher_date']) : null,
            'sale_number' => $row['invoice_number'],
            'date_of_invoice' => ! empty($row['invoice_date']) ? getDateFromExcelDate($row['invoice_date']) : null,
            'supplier_name' => $row['supplier_name'] ?? '',
            'billing_address' => [
                'address_1' => $row['billing_address_line_1'] ?? null,
                'address_2' => $row['billing_address_line_2'] ?? null,
                'city' => $row['billing_address_city'] ?? null,
                'state' => $row['billing_address_state'] ?? null,
                'country' => $row['billing_address_country'] ?? null,
                'pin_code' => $row['billing_address_pincode'] ?? null,
            ],
            'shipping_address' => [
                'shipping_name' => $row['shipping_name'] ?? null,
                'shipping_gstin' => $row['shipping_gstin'] ?? null,
                'address_1' => $row['shipping_address_line_1'] ?? null,
                'address_2' => $row['shipping_address_line_2'] ?? null,
                'city' => $row['shipping_address_city'] ?? null,
                'state' => $row['shipping_address_state'] ?? null,
                'country' => $row['shipping_address_country'] ?? null,
                'pin_code' => $row['shipping_address_pincode'] ?? null,
            ],
            'broker_details' => [
                'broker_name' => $row['broker_name'],
                'brokerage_for_sale' => $row['brokerage'],
                'brokerage_on_value_type' => ! empty($row['brokerage_on_value']) ? (strtolower($row['brokerage_on_value']) == 'invoice value' ? Broker::INVOICE_VALUE : Broker::TAXABLE_VALUE) : null,
            ],
            'transport_details' => [
                'transport_name' => $row['transport_name'],
                'transporter_document_number' => $row['document_number'],
                'transporter_document_date' => ! empty($row['document_date']) ? getDateFromExcelDate($row['document_date']) : null,
                'transporter_vehicle_number' => $row['vehicle_number'],
            ],
            'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
            'ledgers' => [$this->getNonGstLedgerFields($row)],
            'main_classification_nature_type' => null,
            'is_rcm_applicable' => null,
            'narration' => $row['note'] ?? '',
            'term_and_condition' => $row['terms_conditions'] ?? '',
            'additional_charges' => $this->getAdditionalChargesForNonGst($row),
            'cess' => isset($row['cess']) && is_numeric($row['cess']) ? $row['cess'] : 0,
            'tcs_details' => [
                'tcs_ledger' => $row['tcs_ledger'],
                'tcs_rate' => isset($row['tcs_rate']) && is_numeric($row['tcs_rate']) ? $row['tcs_rate'] : 0,
            ],
            'add_less' => $this->getAddLess($row),
            'rounding_amount' => isset($row['round_off_amount']) && is_numeric($row['round_off_amount']) ? $row['round_off_amount'] : null,
            'tds_details' => [
                'tds_ledger' => $row['tds_ledger'],
                'tds_rate' => isset($row['tds_rate']) && is_numeric($row['tds_rate']) ? $row['tds_rate'] : 0,
            ],
            'payment_details' => $this->getPaymentDetails($row),
            'is_gst_enabled' => false,
            'is_round_off_not_changed' => false,
        ];
    }

    private function getAdditionalCharges($row)
    {
        $data = [];

        if (! empty($row['additional_charges_1_ledger'])) {
            $data['additional_charges_1'] = [
                'ledger' => $row['additional_charges_1_ledger'],
                'ac_type' => ($row['additional_charges_1_type'] ?? '') == '%' ? 2 : 1,
                'ac_value' => isset($row['additional_charges_1_amount_without_gst']) && is_numeric($row['additional_charges_1_amount_without_gst']) ? (($row['additional_charges_1_type'] ?? '') == '%' ? round($row['additional_charges_1_amount_without_gst'], 3) : round($row['additional_charges_1_amount_without_gst'], $this->fixDigit)) : 0,
                'ac_gst' => isset($row['additional_charges_1_gst']) ? $row['additional_charges_1_gst'] : null,
            ];
        }

        if (! empty($row['additional_charges_2_ledger'])) {
            $data['additional_charges_2'] = [
                'ledger' => $row['additional_charges_2_ledger'],
                'ac_type' => ($row['additional_charges_2_type'] ?? '') == '%' ? 2 : 1,
                'ac_value' => isset($row['additional_charges_2_amount_without_gst']) && is_numeric($row['additional_charges_2_amount_without_gst']) ? (($row['additional_charges_2_type'] ?? '') == '%' ? round($row['additional_charges_2_amount_without_gst'], 3) : round($row['additional_charges_2_amount_without_gst'], $this->fixDigit)) : 0,
                'ac_gst' => isset($row['additional_charges_2_gst']) ? $row['additional_charges_2_gst'] : null,
            ];
        }

        return $data;
    }

    private function getAdditionalChargesForNonGst($row)
    {
        $data = [];

        if (! empty($row['additional_charges_1_ledger'])) {
            $data['additional_charges_1'] = [
                'ledger' => $row['additional_charges_1_ledger'],
                'ac_type' => ($row['additional_charges_1_type'] ?? '') == '%' ? 2 : 1,
                'ac_value' => isset($row['additional_charges_1_amount']) && is_numeric($row['additional_charges_1_amount']) ? (($row['additional_charges_1_type'] ?? '') == '%' ? round($row['additional_charges_1_amount'], 3) : round($row['additional_charges_1_amount'], $this->fixDigit)) : 0,
            ];
        }

        if (! empty($row['additional_charges_2_ledger'])) {
            $data['additional_charges_2'] = [
                'ledger' => $row['additional_charges_2_ledger'],
                'ac_type' => ($row['additional_charges_2_type'] ?? '') == '%' ? 2 : 1,
                'ac_value' => isset($row['additional_charges_2_amount']) && is_numeric($row['additional_charges_2_amount']) ? (($row['additional_charges_2_type'] ?? '') == '%' ? round($row['additional_charges_2_amount'], 3) : round($row['additional_charges_2_amount'], $this->fixDigit)) : 0,
            ];
        }

        return $data;
    }

    private function getAddLess($row)
    {
        $data = [];

        if (! empty($row['add_less_1_ledger'])) {
            $data['add_less_1'] = [
                'ledger' => $row['add_less_1_ledger'],
                'al_ledger_id' => null,
                'al_type' => $row['add_less_1_type'] == '%' ? 2 : 1,
                'al_value' => isset($row['add_less_1_amount']) && is_numeric($row['add_less_1_amount']) ? (($row['add_less_1_type'] ?? '') == '%' ? round($row['add_less_1_amount'], 3) : round($row['add_less_1_amount'], $this->fixDigit)) : 0,
                'al_is_show_in_print' => true,
            ];
        }

        if (! empty($row['add_less_2_ledger'])) {
            $data['add_less_2'] = [
                'ledger' => $row['add_less_2_ledger'],
                'al_ledger_id' => null,
                'al_type' => $row['add_less_2_type'] == '%' ? 2 : 1,
                'al_value' => isset($row['add_less_2_amount']) && is_numeric($row['add_less_2_amount']) ? (($row['add_less_2_type'] ?? '') == '%' ? round($row['add_less_2_amount'], 3) : round($row['add_less_2_amount'], $this->fixDigit)) : 0,
                'al_is_show_in_print' => true,
            ];
        }

        return $data;
    }

    private function getPaymentDetails($row)
    {
        $data = [];

        if (! empty($row['payment_1_ledger'])) {
            $data['payment_1'] = [
                'ledger' => $row['payment_1_ledger'],
                'pd_date' => ! empty($row['payment_1_date']) ? getDateFromExcelDate($row['payment_1_date']) : null,
                'pd_amount' => isset($row['payment_1_amount']) && is_numeric($row['payment_1_amount']) ? round($row['payment_1_amount'], $this->fixDigit) : 0,
                'mode' => $row['payment_1_mode'] ?? null,
                'pd_reference_number' => $row['payment_1_reference_number'] ?? null,
            ];
        }

        if (! empty($row['payment_2_ledger'])) {
            $data['payment_2'] = [
                'ledger' => $row['payment_2_ledger'],
                'pd_date' => ! empty($row['payment_2_date']) ? getDateFromExcelDate($row['payment_2_date']) : null,
                'pd_amount' => isset($row['payment_2_amount']) && is_numeric($row['payment_2_amount']) ? round($row['payment_2_amount'], $this->fixDigit) : 0,
                'mode' => $row['payment_2_mode'] ?? null,
                'pd_reference_number' => $row['payment_2_reference_number'] ?? null,
            ];
        }

        return $data;
    }

    public function gstFields()
    {
        return [
            'voucher_number',
            'voucher_date',
            'invoice_number',
            'invoice_date',
            'supplier_name',
            'gstin',
            'shipping_name',
            'shipping_gstin',
            'billing_address_line_1',
            'billing_address_line_2',
            'billing_address_country',
            'billing_address_state',
            'billing_address_city',
            'billing_address_pincode',
            'shipping_address_line_1',
            'shipping_address_line_2',
            'shipping_address_country',
            'shipping_address_state',
            'shipping_address_city',
            'shipping_address_pincode',
            'broker_name',
            'brokerage',
            'brokerage_on_value',
            'transport_name',
            'document_number',
            'vehicle_number',
            'document_date',
            'ledger_name',
            'additional_description_for_ledgers',
            'amount_without_gst',
            'gst',
            'discount_1_type',
            'discount_1',
            'discount_2_type',
            'discount_2',
            'classification_nature_type',
            'rcm_applicable',
            'itc_applicable',
            'additional_charges_1_ledger',
            'additional_charges_1_type',
            'additional_charges_1_amount_without_gst',
            'additional_charges_1_gst',
            'additional_charges_2_ledger',
            'additional_charges_2_type',
            'additional_charges_2_amount_without_gst',
            'additional_charges_2_gst',
            'cess',
            'tcs_ledger',
            'tcs_rate',
            'add_less_1_ledger',
            'add_less_1_type',
            'add_less_1_amount',
            'add_less_2_ledger',
            'add_less_2_type',
            'add_less_2_amount',
            'round_off_amount',
            'tds_ledger',
            'tds_rate',
            'note',
            'payment_1_ledger',
            'payment_1_date',
            'payment_1_amount',
            'payment_1_mode',
            'payment_1_reference_number',
            'payment_2_ledger',
            'payment_2_date',
            'payment_2_amount',
            'payment_2_mode',
            'payment_2_reference_number',
        ];
    }

    public function nonGstFields()
    {
        return [
            'voucher_number',
            'voucher_date',
            'invoice_number',
            'invoice_date',
            'supplier_name',
            'billing_address_line_1',
            'billing_address_line_2',
            'billing_address_country',
            'billing_address_state',
            'billing_address_city',
            'billing_address_pincode',
            'shipping_address_line_1',
            'shipping_address_line_2',
            'shipping_address_country',
            'shipping_address_state',
            'shipping_address_city',
            'shipping_address_pincode',
            'broker_name',
            'brokerage',
            'brokerage_on_value',
            'transport_name',
            'document_number',
            'vehicle_number',
            'document_date',
            'ledger_name',
            'additional_description_for_ledgers',
            'amount',
            'discount_1_type',
            'discount_1',
            'discount_2_type',
            'discount_2',
            'additional_charges_1_ledger',
            'additional_charges_1_type',
            'additional_charges_1_amount',
            'additional_charges_2_ledger',
            'additional_charges_2_type',
            'additional_charges_2_amount',
            'tcs_ledger',
            'tcs_rate',
            'add_less_1_ledger',
            'add_less_1_type',
            'add_less_1_amount',
            'add_less_2_ledger',
            'add_less_2_type',
            'add_less_2_amount',
            'round_off_amount',
            'tds_ledger',
            'tds_rate',
            'note',
            'payment_1_ledger',
            'payment_1_date',
            'payment_1_amount',
            'payment_1_mode',
            'payment_1_reference_number',
            'payment_2_ledger',
            'payment_2_date',
            'payment_2_amount',
            'payment_2_mode',
            'payment_2_reference_number',
        ];
    }
}
