<?php

namespace App\Imports;

use App\Actions\v1\LedgerMaster\StoreOrUpdateLedgerAction;
use App\Http\Controllers\CompanyController;
use App\Models\Address;
use App\Models\CessRate;
use App\Models\Company;
use App\Models\CompanyGroup;
use App\Models\EntityType;
use App\Models\GstTax;
use App\Models\Ledger;
use App\Models\Location;
use App\Models\Master\Bank;
use App\Models\Master\Customer;
use App\Models\Master\Expense;
use App\Models\Master\Income;
use App\Models\Master\Supplier;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

/**
 * Class SingleLedgerGroupImport
 */
class SingleLedgerGroupImport implements SkipsEmptyRows, ToCollection, WithHeadingRow
{
    use Importable;

    public mixed $groupId;

    public mixed $groupName;

    public mixed $totalLedgers;

    public mixed $unImportedLedgers;

    public mixed $importedLedgers;

    public array $ledgersErrorList = [];

    public array $commonError = [];

    protected bool $fetchGstDetails;

    public $company;

    protected int $processedCount = 0;

    protected int $totalCount = 0;

    protected int $lastProgressUpdate = 0;

    protected const PROGRESS_UPDATE_INTERVAL = 1;

    protected const API_CALL_DELAY = 1;

    public function __construct($groupName, bool $fetchGstDetails = true)
    {
        $this->company = getCurrentCompany();
        $group = CompanyGroup::whereCompanyId($this->company->id)->whereName($groupName)->first();
        $this->groupName = getParentGroup($group)->name;
        $this->groupId = $group?->id;
        $this->fetchGstDetails = $fetchGstDetails;
    }

    protected function updateProgress()
    {
        $this->processedCount++;
        $progress = ($this->processedCount / $this->totalCount) * 100;
        $progress = min(100, $progress);

        $currentProgress = Cache::get('ledger_import_progress_'.$this->company->id);
        if (! $currentProgress || $currentProgress['progress'] < $progress) {
            $progressData = [
                'progress' => round($progress, 1),
                'processed' => $this->processedCount,
                'total' => $this->totalCount,
            ];
            Cache::put('ledger_import_progress_'.$this->company->id, $progressData, now()->addMinutes(30));
        }
    }

    public function collection(Collection $collection)
    {
        $this->processedCount = 0;
        $this->lastProgressUpdate = 0;

        Cache::forget('ledger_import_progress_'.$this->company->id);

        $validRows = $collection->filter(function ($row) {
            return ! empty($row['ledger_name']);
        });

        $this->totalCount = $validRows->count();

        if ($this->totalCount === 0) {
            return [];
        }

        $initialProgress = [
            'progress' => 0,
            'processed' => 0,
            'total' => $this->totalCount,
        ];
        Cache::put('ledger_import_progress_'.$this->company->id, $initialProgress, now()->addMinutes(30));

        switch ($this->groupName) {
            case Ledger::FIXED_ASSET:
                $this->storeFixedAssetData($validRows);
                break;
            case Ledger::LOAN_AND_ADVANCE:
                $this->storeLoanAndAdvanceData($validRows);
                break;
            case Ledger::INVESTMENT:
                $this->storeInvestMentData($validRows);
                break;
            case Ledger::MISC_ASSETS:
                $this->storeMiscAssetData($validRows);
                break;
            case Ledger::OTHER_CURRENT_ASSETS:
                $this->storeOtherCurrentAssentData($validRows);
                break;
            case Ledger::CASH:
                $this->storeCashData($validRows);
                break;
            case Ledger::BANK:
                $this->storeBankData($validRows);
                break;
            case Ledger::CAPITAL:
                $this->storeCapitalData($validRows);
                break;
            case Ledger::RESERVE_AND_SURPLUS:
                $this->storeReserveAndSurplusData($validRows);
                break;
            case Ledger::SECURED_LOAN:
                $this->storeSecuredLoanData($validRows);
                break;
            case Ledger::UNSECURED_LOAN:
                $this->storeUnsecuredLoanData($validRows);
                break;
            case Ledger::TAXES_GST:
                $this->storeTaxesGstData($validRows);
                break;
            case Ledger::TAXES_TDS:
                $this->storeTaxesTdsData($validRows);
                break;
            case Ledger::TAXES_TCS:
                $this->storeTaxesTcsData($validRows);
                break;
            case Ledger::PROVISION:
                $this->storeProvisionData($validRows);
                break;
            case Ledger::OTHER_CURRENT_LIABILITIES:
                $this->storeOtherCurrentLiabilitiesData($validRows);
                break;
            case Ledger::INCOME:
                $this->storeIncomeData($validRows);
                break;
            case Ledger::EXPENSE:
                $this->storeExpenseData($validRows);
                break;
            case Ledger::STOCK_IN_HAND:
                $this->storeStockInHandData($validRows);
                break;
            case Ledger::CUSTOMER:
                $this->storeCustomerData($validRows);
                break;
            case Ledger::SUPPLIER:
                $this->storeSupplierData($validRows);
                break;
        }

        if (count($this->commonError) > 1) {
            return $this->commonError;
        }

        $this->totalLedgers = $validRows->count();
        $this->unImportedLedgers = count($this->ledgersErrorList);
        $this->importedLedgers = $this->totalLedgers - $this->unImportedLedgers;

        $finalProgress = [
            'progress' => 100,
            'processed' => $this->totalCount,
            'total' => $this->totalCount,
        ];

        Cache::put('ledger_import_progress_'.$this->company->id, $finalProgress, now()->addMinutes(30));

        return $this->ledgersErrorList;
    }

    public function storeFixedAssetData(Collection $collection)
    {
        $errors = [];
        $collection->toArray();
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if (! empty($data['location_of_asset'])) {
                    $locationOfAssetExists = Location::whereRaw('lower(name) = ? ', [strtolower($data['location_of_asset'])])->first();
                    if (! $locationOfAssetExists) {
                        $error[] = 'This location of asset does not exists in the system.';
                        $isCreate = false;
                    }
                }
                if ($isCreate) {
                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::FIXED_ASSET,
                        'location_of_asset_id' => $locationOfAssetExists['id'] ?? null,
                        'is_import' => true,
                    ];
                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeInvestmentData(Collection $collection)
    {
        $errors = [];
        $collection->toArray();
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if ($isCreate) {
                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::INVESTMENT,
                        'is_import' => true,
                    ];
                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeMiscAssetData(Collection $collection)
    {
        $errors = [];
        $collection->toArray();
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if ($isCreate) {
                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::MISC_ASSETS,
                        'is_import' => true,
                    ];
                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeOtherCurrentAssentData(Collection $collection)
    {
        $errors = [];
        $collection->toArray();
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if ($isCreate) {
                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::OTHER_CURRENT_ASSETS,
                        'is_import' => true,
                    ];
                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeBankData(Collection $collection)
    {
        $errors = [];
        $collection->toArray();
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if ($isCreate) {
                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::BANK,
                        'account_number' => $data['account_number'],
                        'account_type' => Bank::ACCOUNT_TYPE_ID[$data['account_type']] ?? null,
                        'bank_name' => $data['bank_name'],
                        'bank_branch' => $data['branch_name'],
                        'ifsc_code' => $data['ifsc_code'],
                        'swift_code' => $data['swift_code'] ?? null,
                        'upi_id' => $data['upi_id'] ?? null,
                        'is_import' => true,
                    ];
                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeLoanAndAdvanceData(Collection $collection)
    {
        $errors = [];
        $collection->toArray();
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if (! empty($data['pan']) && ! preg_match('/[A-Z]{5}[0-9]{4}[A-Z]{1}$/', $data['pan'])) {
                    $error[] = 'Pan card number is invalid.';
                    $isCreate = false;
                }

                if (! empty($data['mobile_number_1'])) {
                    if (! empty($data['country_code_for_mobile_number_1'])) {
                        $code_1 = explode(' - ', $data['country_code_for_mobile_number_1']);
                        if (count($code_1) == 2) {
                            $isoCode_1 = $code_1[0];
                            $phoneCode_1 = $code_1[1];
                        } else {
                            $error[] = 'Country should be in the format ISO Code - Country Code';
                            $isCreate = false;
                        }
                    }
                    if (empty($data['country_code_for_mobile_number_1'])) {
                        $error[] = 'Phone 1 code and ISO code are required';
                        $isCreate = false;
                    }
                    if (! empty($isoCode_1) && $isoCode_1 == '91' && strlen($data['mobile_number_1']) != 10) {
                        $error[] = 'Phone 1 number should be 10 digits';
                        $isCreate = false;
                    }
                }

                if (! empty($data['mobile_number_2'])) {
                    if (! empty($data['country_code_for_mobile_number_2'])) {
                        $code_2 = explode(' - ', $data['country_code_for_mobile_number_2']);
                        if (count($code_2) == 2) {
                            $isoCode_2 = $code_2[0];
                            $phoneCode_2 = $code_2[1];
                        } else {
                            $error[] = 'Country should be in the format ISO Code - Country Code';
                            $isCreate = false;
                        }
                    }
                    if (empty($data['country_code_for_mobile_number_2'])) {
                        $error[] = 'Phone 2 code and ISO code are required';
                        $isCreate = false;
                    }
                    if (! empty($isoCode_2) && $isoCode_2 == '91' && strlen($data['mobile_number_2']) != 10) {
                        $error[] = 'Phone 2 number should be 10 digits';
                        $isCreate = false;
                    }
                }

                if ($isCreate) {
                    $entityTypeId = getEntityTypeId($data['entity_type']);

                    $countryId = getCountryId($data['country']);
                    $stateId = getStateId($data['state'], $countryId);
                    $cityId = ! empty($data['city']) ? getCityId($data['city'], $stateId) : null;

                    $addressData = [
                        'address_1' => $data['address_1'],
                        'address_2' => $data['address_2'],
                        'country_id' => $countryId,
                        'state_id' => $stateId,
                        'city_id' => $cityId,
                        'pin_code' => $data['pin_code'],
                    ];
                    $contactDetails = [
                        'contact_person_name' => $data['contact_person_name'] ?? null,
                        'region_iso_1' => $isoCode_1 ?? 'in',
                        'region_code_1' => $phoneCode_1 ?? 91,
                        'contact_person_phone_1' => $data['mobile_number_1'] ?? null,
                        'region_iso_2' => $isoCode_2 ?? 'in',
                        'region_code_2' => $phoneCode_2 ?? 91,
                        'contact_person_phone_2' => $data['mobile_number_2'] ?? null,
                        'contact_person_email' => $data['email'] ?? null,
                        'upload_document' => null,
                        'contact_person_website' => null,
                    ];
                    $taxDetails = [
                        'pan' => $data['pan'] ?? null,
                        'type_of_entity' => $entityTypeId,
                    ];
                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::LOAN_AND_ADVANCE,
                        'address' => $addressData,
                        'contact_details' => $contactDetails,
                        'tax_details' => $taxDetails,
                        'is_import' => true,
                    ];

                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeCashData(Collection $collection)
    {
        $errors = [];
        $collection->toArray();
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if ($isCreate) {
                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::CASH,
                        'is_import' => true,
                    ];
                    StoreOrUpdateLedgerAction::run($input);

                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeCapitalData(Collection $collection)
    {
        $errors = [];
        $collection->toArray();
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();

                $entityType = $this->company->companyTax->entity_type ?? null;

                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if ($entityType != null) {
                    if (empty($data['name_of_proprietor']) && ! in_array($entityType, [EntityType::AOP, EntityType::TRUST, EntityType::OTHERS, EntityType::HUF, EntityType::PROPRIETORSHIP])) {
                        $error[] = 'Name of proprietor does not exist in excel file.';
                        $isCreate = false;
                    }
                    if (empty($data['holding']) && ! in_array($entityType, [EntityType::AOP, EntityType::TRUST, EntityType::OTHERS, EntityType::HUF, EntityType::PROPRIETORSHIP])) {
                        $error[] = 'Holding ratio does not exist in excel file.';
                        $isCreate = false;
                    }
                    if (! empty($data['pan']) && ! preg_match('/[A-Z]{5}[0-9]{4}[A-Z]{1}$/', $data['pan']) && ! in_array($entityType, [EntityType::AOP, EntityType::TRUST, EntityType::OTHERS])) {
                        $error[] = 'Pan card number is invalid.';
                        $isCreate = false;
                    }
                }
                if ($isCreate) {
                    $countryId = ! empty($data['country']) ? getCountryId($data['country']) : null;
                    $stateId = ! empty($data['state']) ? getStateId($data['state'], $countryId) : null;
                    $cityId = ! empty($data['city']) ? getCityId($data['city'], $stateId) : null;
                    $addressData = [
                        'address_1' => $data['address_1'],
                        'address_2' => $data['address_2'],
                        'country_id' => $countryId,
                        'state_id' => $stateId,
                        'city_id' => $cityId,
                        'pin_code' => $data['pin_code'],
                    ];
                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::CAPITAL,
                        'is_import' => true,
                        'name_of_proprietor' => $data['name_of_proprietor'] ?? null,
                        'holding_ratio' => $data['holding'] ?? null,
                        'profit_loss_sharing_ratio' => $data['profit_loss_sharing_ratio'] ?? null,
                        'pan_number' => $data['pan'] ?? null,
                        'aadhar_number' => $data['aadhar_number'],
                        'address' => $addressData,
                    ];
                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeReserveAndSurplusData(Collection $collection)
    {
        $errors = [];
        $collection->toArray();
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if ($isCreate) {
                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::RESERVE_AND_SURPLUS,
                        'is_import' => true,
                    ];
                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeSecuredLoanData(Collection $collection)
    {
        $errors = [];
        $collection->toArray();
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if ($isCreate) {
                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::SECURED_LOAN,
                        'secured_loan_type' => Ledger::LOAN_TYPE_VALUE[$data['loan_type']] ?? null,
                        'loan_account_number' => $data['loan_account_number'],
                        'name_of_financier' => $data['name_of_financier'],
                        'rate_of_annum' => $data['rate_of_interest'],
                        'is_import' => true,
                    ];
                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeUnsecuredLoanData(Collection $collection)
    {
        $errors = [];
        $collection->toArray();
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if (! empty($data['pan']) && ! preg_match('/[A-Z]{5}[0-9]{4}[A-Z]{1}$/', $data['pan'])) {
                    $error[] = 'Pan card number is invalid.';
                    $isCreate = false;
                }
                if ($isCreate) {
                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::UNSECURED_LOAN,
                        'unsecured_loan_type' => Ledger::LOAN_TYPE_VALUE[$data['loan_type']] ?? null,
                        'loan_account_number' => $data['loan_account_number'],
                        'name_of_financier' => $data['name_of_financier'],
                        'rate_of_annum' => $data['rate_of_interest'],
                        'pan' => $data['pan'],
                        'is_import' => true,
                    ];
                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeTaxesGstData(Collection $collection)
    {
        $errors = [];
        $collection->toArray();
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if (empty($data['tax_type'])) {
                    $error[] = 'Tax type does not exist in the excel file.';
                    $isCreate = false;
                }
                if ($isCreate) {
                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::TAXES_GST,
                        'is_import' => true,
                        'tax_type' => Ledger::GST_TAX_TYPE_VALUE[$data['tax_type']] ?? null,
                        'rounding_method' => Ledger::ROUNDING_METHOD_VALUE[$data['rounding_method']] ?? null,
                    ];
                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeTaxesTdsData(Collection $collection)
    {
        $errors = [];
        $collection->toArray();
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if (empty($data['tax_type'])) {
                    $error[] = 'Tax type does not exist in the excel file';
                    $isCreate = false;
                }
                if ($isCreate) {
                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::TAXES_TDS,
                        'is_import' => true,
                        'tax_type' => getTaxesTDSType($data['tax_type']),
                        'individual_huf' => $data['individual_huf_rate'],
                        'for_other' => $data['rate_for_others'],
                        'pan_not_given' => $data['rate_if_pan_not_given'],
                        'individual_bill_wise' => $data['individual_bill_wise'],
                        'yearly_total' => $data['yearly_total'],
                        'rounding_method' => Ledger::ROUNDING_METHOD_VALUE[$data['rounding_method']] ?? null,
                    ];
                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeTaxesTcsData(Collection $collection)
    {
        $errors = [];
        $collection->toArray();
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if (empty($data['tax_type'])) {
                    $error[] = 'Tax type does not exist in the excel file';
                    $isCreate = false;
                }
                if ($isCreate) {
                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::TAXES_TCS,
                        'is_import' => true,
                        'tax_type' => getTaxesTCSType($data['tax_type']),
                        'individual_huf' => $data['individual_huf_rate'],
                        'for_other' => $data['rate_for_others'],
                        'pan_not_given' => $data['rate_if_pan_not_given'],
                        'calculated_on' => isset($data['calculated_on']) ? Customer::CALCULATED_ON[$data['calculated_on']] : null,
                        'individual_bill_wise' => $data['individual_bill_wise'],
                        'yearly_total' => $data['yearly_total'],
                        'rounding_method' => Ledger::ROUNDING_METHOD_VALUE[$data['rounding_method']] ?? null,
                    ];
                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeProvisionData(Collection $collection)
    {
        $errors = [];
        $collection->toArray();
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if ($isCreate) {
                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::PROVISION,
                        'is_import' => true,
                    ];
                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeOtherCurrentLiabilitiesData(Collection $collection)
    {
        $errors = [];
        $collection->toArray();
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if ($isCreate) {
                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::OTHER_CURRENT_LIABILITIES,
                        'is_import' => true,
                    ];
                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeIncomeData(Collection $collection)
    {
        $errors = [];
        $collection->toArray();
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if (empty($data['income_type'])) {
                    $error[] = 'Income type does not exist in the excel file';
                    $isCreate = false;
                }

                $data['is_gst_applicable'] = isset($data['gst_applicable']) ? strtolower($data['gst_applicable']) == 'yes' : false;
                $data['hsn_sac_code'] = $data['is_gst_applicable'] ? $data['hsn_code'] : null;
                $data['description'] = $data['is_gst_applicable'] ? $data['description'] : null;
                if ($this->company->is_gst_applicable == 1) {
                    if ($data['is_gst_applicable']) {
                        if ($data['gst_rate'] != null || $data['gst_rate'] != '' || strtolower($data['gst_rate']) == 'na' || $data['gst_rate'] == 0) {
                            $data['gst_tax_id'] = GstTax::where('tax_rate', 'LIKE', $data['gst_rate'].'%')->first()?->id ?? null;
                            if (empty($data['gst_tax_id'])) {
                                $error[] = 'The GST rate is invalid.';
                                $isCreate = false;
                            }
                        } else {
                            $data['gst_tax_id'] = GstTax::whereRaw('lower(name) = ? ', ['na'])->first()->id;
                        }
                    }

                    if (! empty($data['gst_cess_rate'])) {
                        $gstCessRate = CessRate::where('rate', $data['gst_cess_rate'])->first() ?? null;
                        if (empty($gstCessRate)) {
                            $error[] = 'The GST cess rate is invalid.';
                            $isCreate = false;
                        } else {
                            $data['gst_cess_rate'] = $gstCessRate->id;
                        }
                    }
                }
                if ($isCreate) {
                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::INCOME,
                        'is_import' => true,
                        'income_type' => array_search($data['income_type'], Income::INCOME_TYPE) ?: 1,
                        'is_gst_applicable' => $data['is_gst_applicable'],
                        'gst_tax_id' => $data['gst_tax_id'] ?? null,
                        'gst_cess_rate' => $data['gst_cess_rate'] ?? null,
                        'hsn_sac_code' => $data['hsn_sac_code'] ?? null,
                        'description' => $data['description'] ?? null,
                    ];
                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeExpenseData(Collection $collection)
    {
        $errors = [];
        $collection->toArray();
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if (empty($data['expense_type'])) {
                    $error[] = 'Expense type does not exist in the excel file';
                    $isCreate = false;
                }
                $data['is_gst_applicable'] = isset($data['gst_applicable']) ? strtolower($data['gst_applicable']) == 'yes' : false;
                $data['hsn_sac_code'] = $data['is_gst_applicable'] ? $data['hsn_code'] : null;
                $data['description'] = $data['is_gst_applicable'] ? $data['description'] : null;
                if ($this->company->is_gst_applicable == 1) {
                    if ($data['is_gst_applicable']) {
                        if ($data['gst_rate'] != null || $data['gst_rate'] != '' || strtolower($data['gst_rate']) == 'na' || $data['gst_rate'] == 0) {
                            $data['gst_tax_id'] = GstTax::where('tax_rate', 'LIKE', $data['gst_rate'].'%')->first()?->id ?? null;
                            if (empty($data['gst_tax_id'])) {
                                $error[] = 'The GST rate is invalid.';
                                $isCreate = false;
                            }
                        } else {
                            $data['gst_tax_id'] = GstTax::whereRaw('lower(name) = ? ', ['na'])->first()->id;
                        }
                    }

                    if (! empty($data['gst_cess_rate'])) {
                        $gstCessRate = CessRate::where('rate', $data['gst_cess_rate'])->first() ?? null;
                        if (empty($gstCessRate)) {
                            $error[] = 'The GST cess rate is invalid.';
                            $isCreate = false;
                        } else {
                            $data['gst_cess_rate'] = $gstCessRate->id;
                        }
                    }
                }
                if ($isCreate) {
                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::EXPENSE,
                        'is_import' => true,
                        'expense_type' => array_search($data['expense_type'], Expense::EXPENSE_TYPE) ?: 1,
                        'is_gst_applicable' => $data['is_gst_applicable'],
                        'gst_tax_id' => $data['gst_tax_id'] ?? null,
                        'gst_cess_rate' => $data['gst_cess_rate'] ?? null,
                        'hsn_sac_code' => $data['hsn_sac_code'] ?? null,
                        'description' => $data['description'] ?? null,
                    ];
                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeStockInHandData(Collection $collection)
    {
        $errors = [];
        $collection->toArray();
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if ($isCreate) {
                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::STOCK_IN_HAND,
                        'is_import' => true,
                    ];
                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeCustomerData(Collection $collection)
    {
        $errors = [];
        $isCompanyGstApplicable = isCompanyGstApplicable();
        $collection->toArray();
        $this->company->load(['billingAddress']);
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if (! empty($data['pan']) && ! preg_match('/[A-Z]{5}[0-9]{4}[A-Z]{1}$/', $data['pan'])) {
                    $error[] = 'Pan card number is invalid.';
                    $isCreate = false;
                }
                if (! empty($data['gstin']) && ! preg_match('/[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z][a-zA-Z0-9]{3}$/', $data['gstin'])) {
                    $error[] = 'Gst number is invalid.';
                    $isCreate = false;
                }
                if (empty($data['billing_country'])) {
                    $data['billing_country'] = $this->company->billingAddress->country_id;
                }
                if (empty($data['billing_state'])) {
                    $data['billing_state'] = $this->company->billingAddress->state->name;
                }
                if (empty($data['shipping_country'])) {
                    $data['shipping_country'] = $this->company->billingAddress->country_id;
                }
                if (empty($data['shipping_state'])) {
                    $data['shipping_state'] = $this->company->billingAddress->state->name;
                }
                if ($this->fetchGstDetails && ! empty($data['gstin'])) {
                    /** @var CompanyController $companyController */
                    $companyController = App::make(CompanyController::class);
                    $gstResponse = $companyController->getGstInformation($data['gstin']);
                    $gstInformation = $gstResponse->getData();

                    if ($gstInformation->success) {
                        $decodedData = json_decode($gstInformation->data);
                        $result = $decodedData->result ?? null;

                        if (! empty($result)) {
                            // Build address components
                            $address1 = trim(($result->pradr->addr->bno ?? '').
                                (! empty($result->pradr->addr->flno) ? ', '.$result->pradr->addr->flno : '').
                                (! empty($result->pradr->addr->bnm) ? ', '.$result->pradr->addr->bnm : ''));

                            $address2 = trim(($result->pradr->addr->st ?? '').
                                (! empty($result->pradr->addr->landMark) ? ', '.$result->pradr->addr->landMark : '').
                                (! empty($result->pradr->addr->locality) ? ', '.$result->pradr->addr->locality : ''));

                            $data['billing_address_1'] = $address1;
                            $data['billing_address_2'] = $address2;
                            $data['billing_country'] = Company::INDIA_COUNTRY_ID;
                            $data['billing_state'] = $result->pradr->addr->stcd;
                            $data['billing_city'] = $result->pradr->addr->dst;
                            $data['billing_pincode'] = $result->pradr->addr->pncd;
                            $data['shipping_address_1'] = $address1;
                            $data['shipping_address_2'] = $address2;
                            $data['shipping_country'] = Company::INDIA_COUNTRY_ID;
                            $data['shipping_state'] = $result->pradr->addr->stcd;
                            $data['shipping_city'] = $result->pradr->addr->dst;
                            $data['shipping_pincode'] = $result->pradr->addr->pncd;
                        } else {
                            $error[] = 'GSTIN is invalid.';
                            $isCreate = false;
                        }
                    }
                }
                if (! empty($data['credit_period']) && ! is_numeric($data['credit_period'])) {
                    $error[] = 'Credit limit period should be a number of days or months';
                    $isCreate = false;
                }
                if (! empty($data['phone_1'])) {
                    if (! empty($data['country_code_for_phone_number_1'])) {
                        $code_1 = explode(' - ', $data['country_code_for_phone_number_1']);
                        if (count($code_1) == 2) {
                            $isoCode_1 = $code_1[0];
                            $phoneCode_1 = $code_1[1];
                        } else {
                            $error[] = 'Country should be in the format ISO Code - Country Code';
                            $isCreate = false;
                        }
                    }
                    if (empty($data['country_code_for_phone_number_1'])) {
                        $error[] = 'Phone_1 code and ISO code are required';
                        $isCreate = false;
                    }
                    if (! empty($isoCode_1) && $isoCode_1 == '91' && strlen($data['phone_1']) != 10) {
                        $error[] = 'Phone 1 number should be 10 digits';
                        $isCreate = false;
                    }
                }
                if (! empty($data['phone_2'])) {
                    if (! empty($data['country_code_for_phone_number_2'])) {
                        $code_2 = explode(' - ', $data['country_code_for_phone_number_2']);
                        if (count($code_2) == 2) {
                            $isoCode_2 = $code_2[0];
                            $phoneCode_2 = $code_2[1];
                        } else {
                            $error[] = 'Country should be in the format ISO Code - Country Code';
                            $isCreate = false;
                        }
                    }
                    if (empty($data['country_code_for_phone_number_2'])) {
                        $error[] = 'Phone_2 code and ISO code are required';
                        $isCreate = false;
                    }
                    if (! empty($isoCode_2) && $isoCode_2 == '91' && strlen($data['phone_2']) != 10) {
                        $error[] = 'Phone 2 number should be 10 digits';
                        $isCreate = false;
                    }
                }

                if ($isCreate) {
                    // billing address
                    if (is_string($data['billing_country'])) {
                        $billingCountryId = getCountryId($data['billing_country']);
                    } else {
                        $billingCountryId = $data['billing_country'] ?? Company::INDIA_COUNTRY_ID;
                    }
                    $billingStateId = getStateId($data['billing_state'], $billingCountryId);
                    $billingCityId = ! empty($data['billing_city']) ? getCityId($data['billing_city'], $billingStateId) : null;

                    $billingAddressData = [
                        'address_1' => $data['billing_address_1'],
                        'address_2' => $data['billing_address_2'],
                        'country_id' => $billingCountryId,
                        'state_id' => $billingStateId,
                        'city_id' => $billingCityId,
                        'pin_code' => $data['billing_pincode'],
                    ];

                    // shipping address
                    if (is_string($data['shipping_country'])) {
                        $shippingCountryId = getCountryId($data['shipping_country']);
                    } else {
                        $shippingCountryId = $data['shipping_country'] ?? Company::INDIA_COUNTRY_ID;
                    }
                    $shippingStateId = getStateId($data['shipping_state'], $shippingCountryId);
                    $shippingCityId = ! empty($data['shipping_city']) ? getCityId($data['shipping_city'], $shippingStateId) : null;
                    $shippingAddressData = [
                        'address_1' => $data['shipping_address_1'],
                        'address_2' => $data['shipping_address_2'],
                        'country_id' => $shippingCountryId,
                        'state_id' => $shippingStateId,
                        'city_id' => $shippingCityId,
                        'pin_code' => $data['shipping_pincode'],
                        'shipping_gstin' => $data['shipping_gstin'] ?? null,
                        'shipping_name' => $data['shipping_name'] ?? null,
                    ];

                    // party details
                    $partyDetails = [
                        'gstin' => $data['gstin'] ?? null,
                        'billing_address' => $billingAddressData,
                        'same_as_billing_address' => false,
                        'shipping_address' => $shippingAddressData,
                        'contact_person_name' => $data['person_name'] ?? null,
                        'contact_person_email' => $data['person_email'] ?? null,
                        'region_iso_1' => $isoCode_1 ?? 'in',
                        'region_code_1' => $phoneCode_1 ?? 91,
                        'contact_person_phone_1' => $data['phone_1'] ?? null,
                        'region_iso_2' => $isoCode_2 ?? 'in',
                        'region_code_2' => $phoneCode_2 ?? 91,
                        'contact_person_phone_2' => $data['phone_2'] ?? null,
                    ];

                    // tax details
                    $taxDetails = [
                        'pan' => $data['pan'] ?? null,
                        'type_of_entity' => getEntityTypeId($data['entity_type']),
                        'gst_registration_type' => Customer::GST_REGISTRATION_VALUE[$data['gst_registration_type'] ?? 1] ?? null,
                        'tan' => $data['tan'] ?? null,
                        'cin_number' => $data['cin'] ?? null,
                    ];

                    // other details
                    $otherDetails = [
                        'credit_period' => $data['credit_period'] ?? null,
                        'credit_period_type' => Customer::CREDIT_PERIOD_TYPE_VALUE[$data['credit_period_type']] ?? Customer::CREDIT_PERIOD_TYPE_MONTH,
                        'allow_credit_limit' => strtolower($data['credit_limit']) == 'yes' ? 1 : 0,
                        'credit_limit' => $data['credit_limit_amount'] ?? null,
                        'credit_limit_action' => Customer::CREDIT_LIMIT_ACTION_VALUE[$data['credit_limit_action']] ?? null,
                        'broker' => getBrokerMasterId($data['broker_master'] ?? null),
                        'brokerage_percentage' => $data['brokerage'] ?? null,
                        'brokerage_on_value' => Customer::BROKERAGE_TYPE_VALUE[$data['brokerage_on_value']] ?? Customer::BROKERAGE_ON_VALUE_NONE,
                        'transporter' => getTransportMasterId($data['transporter_name'] ?? null),
                    ];

                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::CUSTOMER,
                        'is_import' => true,
                        'party_details' => $partyDetails,
                        'tax_details' => $taxDetails,
                        'other_details' => $otherDetails,
                    ];
                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function storeSupplierData(Collection $collection)
    {
        $errors = [];
        $isCompanyGstApplicable = isCompanyGstApplicable();
        $collection->toArray();
        $this->company->load(['billingAddress']);
        foreach ($collection as $data) {
            removeExcelFileExtraFields($data);
            foreach ($data as $cellKey => $cellValue) {
                if (is_string($cellValue) && strpos($cellValue, '=') === 0) {
                    return $this->commonError['unsupported_formula'] = 'The file contains unsupported formulas in cell '.$cellKey;
                }
            }
            try {
                DB::beginTransaction();
                $isCreate = true;
                $error = [];
                $ledgerExists = Ledger::whereRaw('lower(name) = ? ', [strtolower($data['ledger_name'])])->first();
                if ($ledgerExists) {
                    $error[] = 'This ledger already exists in the system.';
                    $isCreate = false;
                }
                if (! empty($data['pan']) && ! preg_match('/[A-Z]{5}[0-9]{4}[A-Z]{1}$/', $data['pan'])) {
                    $error[] = 'Pan card number is invalid.';
                    $isCreate = false;
                }
                if (! empty($data['gstin']) && ! preg_match('/[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z][a-zA-Z0-9]{3}$/', $data['gstin'])) {
                    $error[] = 'Gst number is invalid.';
                    $isCreate = false;
                }
                if (empty($data['billing_country'])) {
                    $data['billing_country'] = $this->company->billingAddress->country_id;
                }
                if (empty($data['billing_state'])) {
                    $data['billing_state'] = $this->company->billingAddress->state->name;
                }
                if (empty($data['shipping_country'])) {
                    $data['shipping_country'] = $this->company->billingAddress->country_id;
                }
                if (empty($data['shipping_state'])) {
                    $data['shipping_state'] = $this->company->billingAddress->state->name;
                }
                if ($this->fetchGstDetails && ! empty($data['gstin'])) {
                    /** @var CompanyController $companyController */
                    $companyController = App::make(CompanyController::class);
                    $gstResponse = $companyController->getGstInformation($data['gstin']);
                    $gstInformation = $gstResponse->getData();
                    if ($gstInformation->success) {
                        $decodedData = json_decode($gstInformation->data);
                        $result = $decodedData->result ?? null;
                        if (! empty($result)) {
                            $address1 = trim(($result->pradr->addr->bno ?? '').
                                (! empty($result->pradr->addr->flno) ? ', '.$result->pradr->addr->flno : '').
                                (! empty($result->pradr->addr->bnm) ? ', '.$result->pradr->addr->bnm : ''));

                            $address2 = trim(($result->pradr->addr->st ?? '').
                                (! empty($result->pradr->addr->landMark) ? ', '.$result->pradr->addr->landMark : '').
                                (! empty($result->pradr->addr->locality) ? ', '.$result->pradr->addr->locality : ''));

                            $data['billing_address_1'] = $address1;
                            $data['billing_address_2'] = $address2;
                            $data['billing_country'] = Company::INDIA_COUNTRY_ID;
                            $data['billing_state'] = $result->pradr->addr->stcd;
                            $data['billing_city'] = $result->pradr->addr->dst;
                            $data['billing_pincode'] = $result->pradr->addr->pncd;
                            $data['shipping_address_1'] = $address1;
                            $data['shipping_address_2'] = $address2;
                            $data['shipping_country'] = Company::INDIA_COUNTRY_ID;
                            $data['shipping_state'] = $result->pradr->addr->stcd;
                            $data['shipping_city'] = $result->pradr->addr->dst;
                            $data['shipping_pincode'] = $result->pradr->addr->pncd;
                        } else {
                            $error[] = 'GSTIN is invalid.';
                            $isCreate = false;
                        }
                    }
                }
                if (! empty($data['credit_period']) && ! is_numeric($data['credit_period'])) {
                    $error[] = 'Credit limit period should be a number of days or months';
                    $isCreate = false;
                }
                if (! empty($data['phone_1'])) {
                    if (! empty($data['country_code_for_phone_number_1'])) {
                        $code_1 = explode(' - ', $data['country_code_for_phone_number_1']);
                        if (count($code_1) == 2) {
                            $isoCode_1 = $code_1[0];
                            $phoneCode_1 = $code_1[1];
                        } else {
                            $error[] = 'Country should be in the format ISO Code - Country Code';
                            $isCreate = false;
                        }
                    }
                    if (empty($data['country_code_for_phone_number_1'])) {
                        $error[] = 'Phone_1 code and ISO code are required';
                        $isCreate = false;
                    }
                    if (! empty($isoCode_1) && $isoCode_1 == '91' && strlen($data['phone_1']) != 10) {
                        $error[] = 'Phone 1 number should be 10 digits';
                        $isCreate = false;
                    }
                }
                if (! empty($data['phone_2'])) {
                    if (! empty($data['country_code_for_phone_number_2'])) {
                        $code_2 = explode(' - ', $data['country_code_for_phone_number_2']);
                        if (count($code_2) == 2) {
                            $isoCode_2 = $code_2[0];
                            $phoneCode_2 = $code_2[1];
                        } else {
                            $error[] = 'Country should be in the format ISO Code - Country Code';
                            $isCreate = false;
                        }
                    }
                    if (empty($data['country_code_for_phone_number_2'])) {
                        $error[] = 'Phone_2 code and ISO code are required';
                        $isCreate = false;
                    }
                    if (! empty($isoCode_2) && $isoCode_2 == '91' && strlen($data['phone_2']) != 10) {
                        $error[] = 'Phone 2 number should be 10 digits';
                        $isCreate = false;
                    }
                }

                if ($isCreate) {
                    // billing address
                    if (is_string($data['billing_country'])) {
                        $billingCountryId = getCountryId($data['billing_country']);
                    } else {
                        $billingCountryId = $data['billing_country'] ?? Company::INDIA_COUNTRY_ID;
                    }
                    $billingStateId = getStateId($data['billing_state'], $billingCountryId);
                    $billingCityId = ! empty($data['billing_city']) ? getCityId($data['billing_city'], $billingStateId) : null;
                    $billingAddressData = [
                        'address_1' => $data['billing_address_1'],
                        'address_2' => $data['billing_address_2'],
                        'country_id' => $billingCountryId,
                        'state_id' => $billingStateId,
                        'city_id' => $billingCityId,
                        'pin_code' => $data['billing_pincode'],
                    ];

                    // shipping address
                    if (is_string($data['shipping_country'])) {
                        $shippingCountryId = getCountryId($data['shipping_country']);
                    } else {
                        $shippingCountryId = $data['shipping_country'] ?? Company::INDIA_COUNTRY_ID;
                    }
                    $shippingStateId = getStateId($data['shipping_state'], $shippingCountryId);
                    $shippingCityId = ! empty($data['shipping_city']) ? getCityId($data['shipping_city'], $shippingStateId) : null;
                    $shippingAddressData = [
                        'address_1' => $data['shipping_address_1'],
                        'address_2' => $data['shipping_address_2'],
                        'country_id' => $shippingCountryId,
                        'state_id' => $shippingStateId,
                        'city_id' => $shippingCityId,
                        'pin_code' => $data['shipping_pincode'],
                        'shipping_gstin' => $data['shipping_gstin'] ?? null,
                        'shipping_name' => $data['shipping_name'] ?? null,
                    ];

                    // party details
                    $partyDetails = [
                        'gstin' => $data['gstin'] ?? null,
                        'billing_address' => $billingAddressData,
                        'same_as_billing_address' => false,
                        'shipping_address' => $shippingAddressData,
                        'contact_person_name' => $data['person_name'] ?? null,
                        'contact_person_email' => $data['person_email'] ?? null,
                        'region_iso_1' => $isoCode_1 ?? 'in',
                        'region_code_1' => $phoneCode_1 ?? 91,
                        'contact_person_phone_1' => $data['phone_1'] ?? null,
                        'region_iso_2' => $isoCode_2 ?? 'in',
                        'region_code_2' => $phoneCode_2 ?? 91,
                        'contact_person_phone_2' => $data['phone_2'] ?? null,
                    ];

                    // tax details
                    $taxDetails = [
                        'pan' => $data['pan'] ?? null,
                        'type_of_entity' => getEntityTypeId($data['entity_type']),
                        'gst_registration_type' => Supplier::GST_REGISTRATION_VALUE[$data['gst_registration_type'] ?? 1] ?? null,
                        'tan' => $data['tan'] ?? null,
                        'cin_number' => $data['cin'] ?? null,
                        'is_tds_applicable' => strtolower($data['tds_applicable']) == 'yes' ? 1 : 0,
                        'gst_return_status' => $data['gst_return_status'] ?? null,
                    ];

                    // other details
                    $otherDetails = [
                        'credit_period' => $data['credit_period'] ?? null,
                        'credit_period_type' => Customer::CREDIT_PERIOD_TYPE_VALUE[$data['credit_period_type']] ?? Customer::CREDIT_PERIOD_TYPE_MONTH,
                        'allow_credit_limit' => false,
                        'credit_limit' => $data['credit_limit_amount'] ?? null,
                        'credit_limit_action' => $data['credit_limit_action'] ?? null,
                        'broker' => getBrokerMasterId($data['broker_master'] ?? null),
                        'brokerage_percentage' => $data['brokerage'] ?? null,
                        'brokerage_on_value' => Customer::BROKERAGE_TYPE_VALUE[$data['brokerage_on_value']] ?? Customer::BROKERAGE_ON_VALUE_NONE,
                        'transporter' => getTransportMasterId($data['transporter_name'] ?? null),
                    ];

                    $input = [
                        'name' => $data['ledger_name'],
                        'group_id' => $this->groupId,
                        'opening_balance_details' => [
                            'opening_balance' => removeCommaSeparator($data['opening_balance']),
                            'opening_balance_dr_cr' => getOpeningBalanceType($data['opening_balance_type']),
                        ],
                        'action' => Ledger::SUPPLIER,
                        'is_import' => true,
                        'party_details' => $partyDetails,
                        'tax_details' => $taxDetails,
                        'other_details' => $otherDetails,
                    ];
                    StoreOrUpdateLedgerAction::run($input);
                } else {
                    $errors[] = collect($data)->merge(['error' => implode(', ', $error)]);
                }
                DB::commit();
                $this->updateProgress();
            } catch (Exception $e) {
                DB::rollBack();
                $errors[] = collect($data)->merge(['error' => $e->getMessage()]);
                Log::info($e->getMessage());
            }
        }
        $this->ledgersErrorList = $errors;
    }

    public function startRow(): int
    {
        return 2;
    }
}
