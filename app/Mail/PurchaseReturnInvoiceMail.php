<?php

namespace App\Mail;

use App\Models\Master\Supplier;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class PurchaseReturnInvoiceMail extends Mailable
{
    use Queueable, SerializesModels;

    public $data;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Build the message.
     */
    public function build(): static
    {
        $companyDetails = $this->data['currentCompany'];
        $supplierName = $this->data['transaction']['supplier']['name'];
        $voucherNumber = $this->data['transaction']['sale_number'];
        $invoiceNumber = $this->data['transaction']['voucher_number'];
        $grandAmount = $this->data['transaction']['grand_total'];
        $invoiceDate = $this->data['transaction']->date_of_invoice;
        $taxInvoice = $this->data['taxInvoice'];
        $pdf = $this->data['purchasePdf'];
        $subject = $this->data['subject'];
        $body = $this->data['body'];
        $regards = $this->data['regards'];
        $isAttachments = $this->data['is_attachment'];
        $dueDate = '';
        $creditPeriodType = ! empty($this->data['transaction']['supplier']['model']['credit_period_type']) ? $this->data['transaction']['supplier']['model']['credit_period_type'] : '';
        $creditPeriod = ! empty($this->data['transaction']['supplier']['model']['credit_limit_period']) ? $this->data['transaction']['supplier']['model']['credit_limit_period'] : '';
        if (! empty($creditPeriodType) && ! empty($creditPeriod)) {
            if ($creditPeriodType == Supplier::CREDIT_PERIOD_TYPE_MONTH) {
                $dueDate = Carbon::parse($this->data['transaction']['date_of_invoice'])->addMonths($creditPeriod);
            } elseif ($creditPeriodType == Supplier::CREDIT_PERIOD_TYPE_DAY) {
                $dueDate = Carbon::parse($this->data['transaction']['date_of_invoice'])->addDays($creditPeriod);
            }
        } else {
            $dueDate = $this->data['transaction']['date_of_invoice'];
        }
        $pdf = $this->data['purchasePdf'];

        $email = $this->view('emails.purchase_invoice_mail',
            compact('supplierName', 'companyDetails', 'voucherNumber', 'grandAmount', 'invoiceDate', 'body', 'regards', 'taxInvoice', 'invoiceNumber'))
            ->markdown('emails.purchase_invoice_mail')
            ->subject($subject);

        if (! empty($this->data['transaction']['invoice_attachment'])) {
            foreach ($this->data['transaction']['invoice_attachment'] as $file) {
                $email->attach($file);
            }
        }

        $email->attachData($pdf,
            'purchase-return-transaction'.$this->data['transaction']->voucher_number.'.pdf');

        return $email;
    }
}
