<?php

namespace App\Http\Controllers;

use App\Actions\DownloadPdf\DownloadBulkPdf;
use App\Actions\DownloadPdf\DownloadPdfStatus;
use App\Actions\Expense\Purchase\CheckExistPurchaseTransactionData;
use App\Actions\Expense\Purchase\DeletePurchaseTransaction;
use App\Actions\Expense\Purchase\GetInvoicePdfDataForPurchase;
use App\Actions\Expense\Purchase\GetPurchaseVoucherNumber;
use App\Actions\Expense\Purchase\PurchaseStoreTransaction;
use App\Actions\Expense\Purchase\PurchaseUpdateTransaction;
use App\Actions\Expense\PurchaseOrder\GetMultiplePurchaseOrderTransaction;
use App\Actions\Income\GstCalculationForPdf;
use App\Exports\PurchaseTransactionExport;
use App\Http\Requests\PurchaseTransactionRequest;
use App\Imports\PurchaseAccountingInvoiceImport;
use App\Imports\PurchaseItemInvoiceImport;
use App\Jobs\BulkPdfExportJob;
use App\Jobs\DownloadInvoiceJob;
use App\Jobs\InvoiceMailJob;
use App\Models\Company;
use App\Models\CompanyGroup;
use App\Models\CompanySetting;
use App\Models\Configuration\PurchaseConfiguration;
use App\Models\ExpenseCreditNoteTransaction;
use App\Models\ExpenseDebitNoteTransaction;
use App\Models\GstTax;
use App\Models\Ledger;
use App\Models\Location;
use App\Models\Master\Broker;
use App\Models\Master\Customer;
use App\Models\Master\ExpenseTransactionMaster;
use App\Models\Master\PurchaseOrderTransactionMaster;
use App\Models\Master\Supplier;
use App\Models\Notification;
use App\Models\PaymentTransaction;
use App\Models\PdfDownloadingStatus;
use App\Models\PurchaseItemTransaction;
use App\Models\PurchaseOrderTransaction;
use App\Models\PurchaseReturnTransaction;
use App\Models\PurchaseTransaction;
use App\Models\SaleTransaction;
use App\Repositories\PurchaseTransactionRepositories;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\HigherOrderBuilderProxy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Laracasts\Flash\Flash;
use Maatwebsite\Excel\Facades\Excel;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use ZipArchive;

/**
 * Class PurchaseTransactionController
 */
class PurchaseTransactionController extends AppBaseController
{
    public PurchaseTransactionRepositories $purchaseTransactionRepositories;

    public function __construct(PurchaseTransactionRepositories $purchaseTransactionRepositories)
    {
        $this->purchaseTransactionRepositories = $purchaseTransactionRepositories;
    }

    /**
     * Display a listing of the resource.
     *
     * @return View
     */
    public function index(): \Illuminate\View\View
    {
        $paymentStatusArr = purchaseTransaction::PAYMENT_STATUS_ARR;

        return view('company.purchase.index', compact('paymentStatusArr'));
    }

    /**
     * @return View
     */
    public function create(): \Illuminate\View\View
    {
        $data = [];

        $getCurrentCompany = getCurrentCompany();
        $ledgerCustomer = CompanyGroup::whereCompanyId($getCurrentCompany?->id);
        $data['groupLists'] = getGroupLists();
        $data['groupListsId'] = $ledgerCustomer->where('name', Ledger::SUPPLIER)->first()->id;
        $data['countries'] = getCountries();
        $data['indiaStates'] = getStates(Company::INDIA_COUNTRY_ID);
        $data['updateLedger'] = false;
        $data['locationOfAssets'] = Location::pluck('name', 'id')->toArray();
        $data['brokerMasters'] = Broker::whereCompanyId($getCurrentCompany?->id)->pluck('broker_name', 'id');
        $data['purchaseConfiguration'] = PurchaseConfiguration::first();
        $data['companyItemMasterGroups'] = getParentGroupsValue(Ledger::ITEM_DEFAULT_GROUP);
        $data['defaultGroup'] = CompanyGroup::whereName(Ledger::ITEM_DEFAULT_GROUP)->whereName('Default Group')
            ->whereCompanyId(getCurrentCompany()->id)
            ->value('id');
        $data['unitOfMeasurement'] = getItemMasterUnit();
        $data['purchaseTransaction'] = ExpenseTransactionMaster::whereCompanyId($getCurrentCompany?->id)->first();
        $data['saleInvoiceNumbers'] = SaleTransaction::pluck('full_invoice_number', 'id')->toArray();
        $TdsGroups = getParentGroupsValue(Ledger::TAXES_TDS);
        $groupIds = array_merge(array_keys($TdsGroups));
        $data['ledgerOfTds'] = Ledger::whereIn('group_id', $groupIds)->pluck('name', 'id')->toArray();
        $data['lastVoucherNumber'] = GetPurchaseVoucherNumber::run();
        $data['methodVoucherType'] = $data['purchaseTransaction']->voucher_number ?? ExpenseTransactionMaster::AUTOMATIC;
        $data['paymentMode'] = $data['purchaseTransaction']->payment_mode ?? PurchaseTransaction::CREDIT_MODE;
        $data['previousBillId'] = PurchaseTransaction::orderBy('created_at', 'DESC')->first()?->id;

        $lastTransactionId = request()->query('lastTransactionId');
        if (! empty($lastTransactionId)) {
            $data['lastTransaction'] = PurchaseTransaction::where('id', $lastTransactionId)->first();
        }

        return view('company.purchase.create')->with($data);
    }

    public function checkUniqueVoucherNumber($voucherNumber, $configuration)
    {
        /* Whenever the code is used, add a fiscal year date condition to the query. */
        /* if (is_numeric($voucherNumber)) {
            $data['lastVoucherNumber'] = sprintf('%0'.strlen($voucherNumber).'d', ++$voucherNumber);
        } else {
            $data['lastVoucherNumber'] = $voucherNumber !== null ? ++$voucherNumber : 1;
        }
        $purchaseTransaction = PurchaseTransaction::whereVoucherNumber($data['lastVoucherNumber'])->first();
        if (! empty($purchaseTransaction)) {
            return $this->checkUniqueVoucherNumber($purchaseTransaction->voucher_number, $configuration);
        } */

        $data['lastVoucherNumber'] = genNextInvNo($voucherNumber);

        return $data['lastVoucherNumber'] ?? 1;
    }

    public function store(PurchaseTransactionRequest $request): JsonResponse
    {
        $input = $request->all();

        if ($input['is_cgst_sgst_igst_calculated'] && $input['is_gst_na'] && getCurrentCompany()->is_gst_applicable) {
            Log::error([
                'is_cgst_sgst_igst_calculated and is_gst_na both are' => true,
                'companyId' => getCurrentCompany()->id,
                'input' => $input,
            ]);
        }

        $data = PurchaseStoreTransaction::run($input);

        if ($input['submit_button_value'] == PurchaseTransaction::SAVE_AND_NEW_BUTTON) {
            $data['lastPurchaseTransactionDate'] = $input['voucher_date'];
            $data['lastTransactionId'] = $data['purchaseTransaction']['id'];
            $data['actionName'] = PurchaseTransaction::SAVE_AND_NEW_BUTTON;
        } elseif ($input['submit_button_value'] == PurchaseTransaction::SAVE_AND_PRINT_BUTTON) {
            $data['actionName'] = PurchaseTransaction::SAVE_AND_PRINT_BUTTON;
            $data['purchase_id'] = $data['purchaseTransaction']['id'];
            // $pdf = $this->getPurchasePDF($data['purchaseTransaction']['id']);
            // $content = json_decode($pdf->getContent(), true);
            // $data['pdf_status'] = $content['data']['status'];
            // $data['pdf_view_route'] = $data['pdf_status'] == 'completed' ? $content['data']['viewRoute'] : null;
        } else {
            $data['actionName'] = PurchaseTransaction::SAVE_BUTTON;
        }

        return $this->sendResponse($data, 'Purchase transaction created successfully');
    }

    public function show(PurchaseTransaction $purchase): JsonResponse
    {
        $data = $purchase->load('purchaseTransactionItems', 'supplier', 'tdsLedger');
        $transactionsData = [];
        $transactionsData['paymentTransactions'] = [];
        $transactionsData['purchaseReturnTransactions'] = [];
        $transactionsData['expenseDebitNoteTransactions'] = [];
        $transactionsData['expenseCreditNoteTransactions'] = [];
        $paymentMapping = PaymentTransaction::whereHas('paymentTransactionItem', function ($q) use ($purchase) {
            $q->where('purchase_id', $purchase->id)->where('paid_amount', '>', 0);
        })->get();
        $paymentMappingCount = $paymentMapping->count();
        if ($paymentMappingCount) {
            $transactionsData['paymentTransactions'] = $paymentMapping;
        }
        $purchaseReturnAssociated = PurchaseReturnTransaction::whereOriginalInvNo($purchase->id)->get();
        $purchaseReturnAssociatedCount = $purchaseReturnAssociated->count();
        if ($purchaseReturnAssociatedCount) {
            $transactionsData['purchaseReturnTransactions'] = $purchaseReturnAssociated;
        }
        $expenseDebitAssociated = ExpenseDebitNoteTransaction::whereOriginalInvNo($purchase->id)->get();
        $expenseDebitAssociatedCount = $expenseDebitAssociated->count();
        if ($expenseDebitAssociatedCount) {
            $transactionsData['expenseDebitNoteTransactions'] = $expenseDebitAssociated;
        }
        $expenseCreditAssociated = ExpenseCreditNoteTransaction::whereOriginalInvNo($purchase->id)->get();
        $expenseCreditAssociatedCount = $expenseCreditAssociated->count();
        if ($expenseCreditAssociatedCount) {
            $transactionsData['expenseCreditNoteTransactions'] = $expenseCreditAssociated;
        }

        $data['checkTransactionExists'] = $paymentMappingCount || $purchaseReturnAssociatedCount || $expenseDebitAssociatedCount || $expenseCreditAssociatedCount;
        $data['html'] = view('company.sale.view_sale_associated_transaction_list')->with($transactionsData)->render();

        return $this->sendResponse($data, 'Purchase transaction retrieved successfully');
    }

    /**
     * @return Application|Factory|View
     */
    public function edit(PurchaseTransaction $purchase)
    {
        $lockDate = getTransactionsLockDate()['expense'] ?? null;
        $isLocked = ! empty($lockDate) && Carbon::parse($lockDate)->greaterThanOrEqualTo($purchase->voucher_date);
        if ($isLocked) {
            Flash::error('These Transactions Is locked');

            return redirect()->back();
        }
        $data = [];
        $getCurrentCompany = getCurrentCompany();
        $data['purchase'] = $purchase->load([
            'addresses',
            'purchaseTransactionItems.items',
            'purchaseTransactionItems.gst',
            'purchaseTransactionLedger.gst',
            'supplier.model',
            'tcsLedger.model',
        ]);
        $ledgerCustomer = CompanyGroup::whereCompanyId($getCurrentCompany?->id);
        $ledger = Ledger::whereId($purchase->supplier_ledger_id)->whereIn('model_type', [Supplier::class, Customer::class])->first();
        if (! empty($ledger)) {
            $supplier = Supplier::whereId($ledger->model_id)->first();
        } else {
            Flash::error('Something went wrong! please try again after some time.');
            Log::error('Supplier ledger not found in purchase transaction Purchase supplier id = '.$purchase->supplier_ledger_id.' Purchase record id ='.$purchase->id);

            return redirect()->route('company.purchases.index');
        }
        $data['isTdsApplicable'] = $supplier?->is_tds_applicable;
        $TdsGroups = getParentGroupsValue(Ledger::TAXES_TDS);
        $groupIds = array_merge(array_keys($TdsGroups));
        $data['ledgerOfTds'] = Ledger::whereIn('group_id', $groupIds)->pluck('name', 'id')->toArray();
        $data['groupLists'] = getGroupLists();
        $data['groupListsId'] = $ledgerCustomer->where('name', Ledger::SUPPLIER)->first()->id;
        $data['countries'] = getCountries();
        $data['indiaStates'] = getStates(Company::INDIA_COUNTRY_ID);
        $data['updateLedger'] = false;
        $data['locationOfAssets'] = Location::pluck('name', 'id')->toArray();
        $data['brokerMasters'] = Broker::whereCompanyId($getCurrentCompany?->id)->pluck('broker_name', 'id');
        $data['purchaseConfiguration'] = PurchaseConfiguration::whereCompanyId($getCurrentCompany?->id)->toBase()->first();
        $data['companyItemMasterGroups'] = getParentGroupsValue(Ledger::ITEM_DEFAULT_GROUP);
        $data['defaultGroup'] = CompanyGroup::whereName(Ledger::ITEM_DEFAULT_GROUP)->whereName('Default Group')
            ->whereCompanyId(getCurrentCompany()->id)
            ->value('id');
        $data['unitOfMeasurement'] = getItemMasterUnit();
        $data['purchaseTransaction'] = ExpenseTransactionMaster::whereCompanyId($getCurrentCompany?->id)->first();
        $data['saleInvoiceNumbers'] = SaleTransaction::pluck('full_invoice_number', 'id')->toArray();
        $data['billingAddress'] = $data['purchase']->addresses->firstWhere(
            'address_type',
            PurchaseTransaction::BILLING_ADDRESS
        );
        $data['billingStates'] = getStates($data['billingAddress']?->country_id);
        $data['billingCities'] = getCities($data['billingAddress']?->state_id);
        $data['shippingAddress'] = $data['purchase']->addresses->firstWhere(
            'address_type',
            PurchaseTransaction::SHIPPING_ADDRESS
        );
        if ($data['shippingAddress']) {
            $data['shippingStates'] = getStates($data['shippingAddress']->country_id);
            $data['shippingCities'] = getCities($data['shippingAddress']->state_id);
        } else {
            $data['shippingStates'] = null;
            $data['shippingCities'] = null;
        }
        $data['previousBillId'] = PurchaseTransaction::where('id', '<', $purchase->id)->orderByDesc('id')->value('id');
        $data['nextBillId'] = PurchaseTransaction::where('id', '>', $purchase->id)->orderBy('id')->value('id');
        $selectedIds = explode(',', $data['purchase']->purchase_order_no) ?? [];
        $data['purchaseOrderNumbers'] = getPurchaseOrderNumbers($purchase->supplier_ledger_id, $selectedIds);

        return view('company.purchase.edit')->with($data);
    }

    public function update(PurchaseTransactionRequest $request, PurchaseTransaction $purchase): JsonResponse
    {
        $input = $request->all();

        if ($input['is_cgst_sgst_igst_calculated'] && $input['is_gst_na'] && getCurrentCompany()->is_gst_applicable) {
            Log::error([
                'is_cgst_sgst_igst_calculated and is_gst_na both are' => true,
                'transactionId' => $purchase->id,
                'companyId' => $purchase->company_id,
                'input' => $input,
                'old_input' => $purchase,
            ]);
        }

        $data = PurchaseUpdateTransaction::run($input, $purchase);

        if ($input['submit_button_value'] == PurchaseTransaction::SAVE_AND_PRINT_BUTTON) {
            $data['actionName'] = PurchaseTransaction::SAVE_AND_PRINT_BUTTON;
            $data['isTdsApplicable'] = false;
            $data['purchase_id'] = $data['id'];
            // $pdf = $this->getPurchasePDF($data['id']);
            // $content = json_decode($pdf->getContent(), true);
            // $data['pdf_status'] = $content['data']['status'];
            // $data['pdf_view_route'] = $data['pdf_status'] == 'completed' ? $content['data']['viewRoute'] : null;
        }

        return $this->sendResponse($data, 'Purchase updated successfully');
    }

    public function destroy(PurchaseTransaction $purchase)
    {
        DeletePurchaseTransaction::run($purchase);

        return $this->sendSuccess('Purchase deleted successfully');
    }

    public function getPurchaseTransactionItemType($itemType): JsonResponse
    {
        $html = null;
        $purchaseConfiguration = PurchaseConfiguration::whereCompanyId(getCurrentCompany()?->id)->first();
        if ($itemType == PurchaseTransaction::ACCOUNTING_INVOICE) {
            $html = view('company.purchase.append.ledgers_fields', compact('purchaseConfiguration'))
                ->render();
        }
        if ($itemType == PurchaseTransaction::ITEM_INVOICE) {
            $html = view('company.purchase.append.items_fields', compact('purchaseConfiguration'))
                ->render();
        }

        return $this->sendResponse(
            ['html' => $html, 'isGstApplicable' => isCompanyGstApplicable()],
            'Purchase Transaction screen retrieved successfully'
        );
    }

    /**
     * @return float|HigherOrderBuilderProxy|mixed|null
     */
    public function getPurchaseGstRatePercentage($gstTaxId)
    {

        return GstTax::whereId($gstTaxId)->first()->tax_rate ?? null;
    }

    public function getTdsTaxValue($tdsTaxId): JsonResponse
    {
        $taxDetails = Ledger::whereId($tdsTaxId)->with('model')->first();

        return $this->sendResponse($taxDetails, 'Tds Tax retrieved successfully');
    }

    public function getSupplierDetail(Ledger $supplier): JsonResponse
    {
        $data = [];
        $supplier = Ledger::whereIn('model_type', [Customer::class, Supplier::class])->whereId($supplier->id)->firstOrFail();
        $data['supplierDetail'] = $supplier->model->load('addresses');
        $data['shippingAddress'] = $data['supplierDetail']->addresses->where(
            'address_type',
            PurchaseTransaction::SHIPPING_ADDRESS
        )->first();
        $data['billingAddress'] = $data['supplierDetail']->addresses->where(
            'address_type',
            PurchaseTransaction::BILLING_ADDRESS
        )->first();

        return $this->sendResponse($data, 'Supplier Master retrieved successfully');
    }

    public function getPurchaseTransaction($purchaseId): JsonResponse
    {
        $data = [];
        $data['purchaseTransaction'] = PurchaseTransaction::whereId($purchaseId)->whereCompanyId(getCurrentCompany()->id)
            ->with('addresses', 'purchaseTransactionItems', 'purchaseTransactionLedger')->firstOrFail();

        $data['billingAddress'] = $data['purchaseTransaction']->addresses->where(
            'address_type',
            PurchaseTransaction::BILLING_ADDRESS
        )->first();
        $data['shippingAddress'] = $data['purchaseTransaction']->addresses->where(
            'address_type',
            PurchaseTransaction::SHIPPING_ADDRESS
        )->first();
        $data['classificationNatureType'] = null;
        $data['isRcmApplicable'] = 0;
        if ($data['purchaseTransaction']->purchase_item_type == PurchaseTransaction::ITEM_INVOICE) {
            $data['purchaseTransaction']->purchaseTransactionItems->load('classificationNatureType');
            $data['classificationNatureType'] = $data['purchaseTransaction']->purchaseTransactionItems[0]?->classificationNatureType?->name;
            $data['isRcmApplicable'] = $data['purchaseTransaction']->purchaseTransactionItems[0]?->classification_is_rcm_applicable;
        }
        if ($data['purchaseTransaction']->purchase_item_type == PurchaseTransaction::ACCOUNTING_INVOICE) {
            $data['purchaseTransaction']->purchaseTransactionLedger->load('classificationNatureType');
            $data['classificationNatureType'] = $data['purchaseTransaction']->purchaseTransactionLedger[0]?->classificationNatureType?->name;
            $data['isRcmApplicable'] = $data['purchaseTransaction']->purchaseTransactionLedger[0]?->classification_is_rcm_applicable;
        }

        return $this->sendResponse($data, 'Purchase transaction retrieved successfully');
    }

    public function getPurchasePdfPreview($purchasePdfPreviewId): JsonResponse
    {
        enableDeletedScope();
        $data = GetInvoicePdfDataForPurchase::run($purchasePdfPreviewId);
        $data = GstCalculationForPdf::run($data, PurchaseTransaction::class);
        $data['preview_enabled'] = true;
        $data['isA5Pdf'] = false;
        if (getCompanyExpensePDFFormat() == CompanySetting::PDF_FORMAT[CompanySetting::A5]) {
            $data['isA5Pdf'] = true;
        }

        $html = view('company.pdf.purchase_pdf.download_purchase', $data)->render();
        $fileName = 'Purchase_'.$data['transaction']->voucher_number.'_'.$data['transaction']['supplier']['name'];
        $data = [];
        // $data['emailRoute'] = route('company.purchases-email', ['purchase' => $purchasePdfPreviewId]);
        $data['downloadRoute'] = route('company.purchase-preview-pdf-download', ['purchaseId' => $purchasePdfPreviewId]);
        if (getCompanyExpensePDFFormat() == CompanySetting::PDF_FORMAT[CompanySetting::A5]) {
            $data['isA5Pdf'] = true;
        }
        disableDeletedScope();

        return $this->sendResponse(['html' => $html, 'fileName' => $fileName, 'data' => $data], 'Purchase pdf generated successfully');
    }

    public function getPurchasePDF($purchasePdfPreviewId)
    {
        $pdfStatus = DownloadPdfStatus::run(PdfDownloadingStatus::PURCHASE);
        $currentCompany = getCurrentCompany();
        $data = [];

        DownloadInvoiceJob::dispatch($purchasePdfPreviewId, $pdfStatus, $currentCompany, PurchaseTransaction::class);

        while ($pdfStatus->status != PdfDownloadingStatus::COMPLETED && $pdfStatus->status != PdfDownloadingStatus::FAILED) {
            $pdfStatus->refresh();
            usleep(50000);
        }

        if ($pdfStatus->status == PdfDownloadingStatus::COMPLETED) {
            $data['status'] = 'completed';
            $data['downloadRoute'] = route('company.purchase-pdf-download', [
                'purchasePdfDownloadId' => $pdfStatus->id,
                'isView' => 0,
            ]);
            // $data['emailRoute'] = route('company.purchases-email', ['purchase' => $purchasePdfPreviewId]);
            $data['viewRoute'] = $pdfStatus->link;
            $data['html'] = view('company.pdf.purchase_pdf.preview')
                ->with($data)
                ->render();
        }

        if ($pdfStatus->status == PdfDownloadingStatus::FAILED) {
            $data['status'] = 'failed';
            $data['message'] = 'Something went wrong. Please try again later.';
        }

        return $this->sendResponse($data, 'Purchase PDF Preview screen retrieved successfully');
    }

    public function getPurchasePdfDownload($purchasePdfDownloadId, $isView = false)
    {
        if ($isView) {
            $transaction = PurchaseTransaction::whereId($purchasePdfDownloadId)->withTrashed()->firstOrFail();
            $currentCompany = Company::with('billingAddress', 'companyTax', 'user', 'mailConfiguration')->findOrFail($transaction->company_id);
            session(['current_company' => $currentCompany]);
            $pdfStatus = DownloadPdfStatus::run(PdfDownloadingStatus::PURCHASE);

            if (dockerEnabled()) {
                $job = new DownloadInvoiceJob($purchasePdfDownloadId, $pdfStatus, $currentCompany, PurchaseTransaction::class);
                $job->dispatchSync($purchasePdfDownloadId, $pdfStatus, $currentCompany, PurchaseTransaction::class);
                $fileName = $job->getFileName();

                $pdfStatus->refresh();

                if ($pdfStatus->status == PdfDownloadingStatus::COMPLETED && $pdfStatus->link == null) {
                    return response($job->getPdfContents(), 200, [
                        'Content-Type' => 'application/pdf',
                        'Content-Disposition' => 'attachment; filename="'.$fileName.'"',
                    ]);
                }
            } else {
                DownloadInvoiceJob::dispatch($purchasePdfDownloadId, $pdfStatus, $currentCompany, PurchaseTransaction::class);
            }
            $pdfStatus->refresh();

            while ($pdfStatus->status != PdfDownloadingStatus::COMPLETED && $pdfStatus->status != PdfDownloadingStatus::FAILED) {
                $currentTime = Carbon::now();
                while ($pdfStatus->status != PdfDownloadingStatus::COMPLETED && $pdfStatus->status != PdfDownloadingStatus::FAILED) {
                    $pdfStatus->refresh();
                    usleep(50000);
                    $runningTime = Carbon::now();
                    if ($runningTime->diffInSeconds($currentTime) > 60) {
                        $pdfStatus->update([
                            'status' => PdfDownloadingStatus::FAILED,
                        ]);
                        Log::error('Pdf download failed after 60 seconds '.$pdfStatus->id);
                    }
                }
            }

            if ($pdfStatus->status == PdfDownloadingStatus::FAILED) {
                $data['status'] = 'failed';
                $data['message'] = 'Something went wrong. Please try again later.';

                Flash::error('Something went wrong. Please try again later.');

                return redirect()->back()->with('error', $data['message']);
            }

            $client = new Client();
            $response = $client->get($pdfStatus->link, ['stream' => true]);
            $fileContent = $response->getBody()->getContents();

            $headers = [
                'Content-Type' => $response->getHeaderLine('Content-Type'),
                'Content-Disposition' => 'attachment; filename="'.$pdfStatus->name.'"',
            ];

            return response($fileContent, 200, $headers);
        }

        $data = PdfDownloadingStatus::find($purchasePdfDownloadId);
        $client = new Client();
        $response = $client->get($data->link, ['stream' => true]);
        $fileContent = $response->getBody()->getContents();
        $headers = [
            'Content-Type' => $response->getHeaderLine('Content-Type'),
            'Content-Disposition' => 'attachment; filename="'.$data->name.'"',
        ];

        return response($fileContent, 200, $headers);
    }

    public function getPurchasePreviewPdfDownload($purchaseId)
    {
        enableDeletedScope();
        $transaction = PurchaseTransaction::whereId($purchaseId)->firstOrFail();
        $currentCompany = Company::with('billingAddress', 'companyTax', 'user', 'mailConfiguration')->findOrFail($transaction->company_id);
        session(['current_company' => $currentCompany]);
        $pdfStatus = DownloadPdfStatus::run(PdfDownloadingStatus::PURCHASE);

        if (dockerEnabled()) {
            $job = new DownloadInvoiceJob($purchaseId, $pdfStatus, $currentCompany, PurchaseTransaction::class);
            $job->dispatchSync($purchaseId, $pdfStatus, $currentCompany, PurchaseTransaction::class);
            $fileName = $job->getFileName();

            $pdfStatus->refresh();

            if ($pdfStatus->status == PdfDownloadingStatus::COMPLETED && $pdfStatus->link == null) {
                return response($job->getPdfContents(), 200, [
                    'Content-Type' => 'application/pdf',
                    'Content-Disposition' => 'attachment; filename="'.$fileName.'"',
                ]);
            }
        } else {
            DownloadInvoiceJob::dispatch($purchaseId, $pdfStatus, $currentCompany, PurchaseTransaction::class);
        }
        $pdfStatus->refresh();

        while ($pdfStatus->status != PdfDownloadingStatus::COMPLETED && $pdfStatus->status != PdfDownloadingStatus::FAILED) {
            $pdfStatus->refresh();
            usleep(50000);
        }

        $client = new Client();
        $response = $client->get($pdfStatus->link, ['stream' => true]);
        $fileContent = $response->getBody()->getContents();

        $headers = [
            'Content-Type' => $response->getHeaderLine('Content-Type'),
            'Content-Disposition' => 'attachment; filename="'.$pdfStatus->name.'"',
        ];
        disableDeletedScope();

        return response($fileContent, 200, $headers);
    }

    /**
     * @return View
     */
    public function purchaseCreateDuplicate(PurchaseTransaction $purchase): \Illuminate\View\View
    {
        $data = [];
        $getCurrentCompany = getCurrentCompany();
        $data['purchase'] = $purchase->load([
            'addresses',
            'purchaseTransactionItems.items',
            'purchaseTransactionItems.gst',
            'purchaseTransactionLedger.gst',
            'supplier.model',
            'tcsLedger.model',
        ]);
        $ledgerCustomer = CompanyGroup::whereCompanyId($getCurrentCompany?->id);
        $data['groupLists'] = getGroupLists();
        $data['groupListsId'] = $ledgerCustomer->where('name', Ledger::SUPPLIER)->first()->id;
        $data['countries'] = getCountries();
        $data['indiaStates'] = getStates(Company::INDIA_COUNTRY_ID);
        $data['updateLedger'] = false;
        $data['locationOfAssets'] = Location::pluck('name', 'id')->toArray();
        $data['brokerMasters'] = Broker::whereCompanyId($getCurrentCompany?->id)->pluck('broker_name', 'id');
        $data['purchaseConfiguration'] = PurchaseConfiguration::whereCompanyId($getCurrentCompany?->id)->toBase()->first();
        $data['companyItemMasterGroups'] = getParentGroupsValue(Ledger::ITEM_DEFAULT_GROUP);
        $data['defaultGroup'] = CompanyGroup::whereName(Ledger::ITEM_DEFAULT_GROUP)->whereName('Default Group')
            ->whereCompanyId(getCurrentCompany()->id)
            ->value('id');
        $data['unitOfMeasurement'] = getItemMasterUnit();
        $data['purchaseTransaction'] = ExpenseTransactionMaster::whereCompanyId($getCurrentCompany?->id)->first();
        $data['saleInvoiceNumbers'] = SaleTransaction::pluck('full_invoice_number', 'id')->toArray();
        $TdsGroups = getParentGroupsValue(Ledger::TAXES_TDS);
        $groupIds = array_merge(array_keys($TdsGroups));
        $data['ledgerOfTds'] = Ledger::whereIn('group_id', $groupIds)->pluck('name', 'id')->toArray();
        $voucherNumber = PurchaseTransaction::orderBy('created_at', 'desc')->first();
        $data['lastVoucherNumber'] = GetPurchaseVoucherNumber::run();
        $data['methodVoucherType'] = $data['purchaseTransaction']->voucher_number ?? ExpenseTransactionMaster::AUTOMATIC;
        $data['billingAddress'] = $data['purchase']->addresses->firstWhere(
            'address_type',
            PurchaseTransaction::BILLING_ADDRESS
        );
        $data['billingStates'] = getStates($data['billingAddress']?->country_id);
        $data['billingCities'] = getCities($data['billingAddress']?->state_id);
        $data['shippingAddress'] = $data['purchase']->addresses->firstWhere(
            'address_type',
            PurchaseTransaction::SHIPPING_ADDRESS
        );
        if ($data['shippingAddress']) {
            $data['shippingStates'] = getStates($data['shippingAddress']->country_id);
            $data['shippingCities'] = getCities($data['shippingAddress']->state_id);
        } else {
            $data['shippingStates'] = null;
            $data['shippingCities'] = null;
        }
        $data['previousBillId'] = PurchaseTransaction::where('id', '<', $purchase->id)->orderByDesc('id')->value('id');
        $data['nextBillId'] = PurchaseTransaction::where('id', '>', $purchase->id)->orderBy('id')->value('id');
        $data['purchaseOrderNumbers'] = getPurchaseOrderNumbers($data['purchase']->supplier_ledger_id);

        return view('company.purchase.duplicate')->with($data);
    }

    /**
     * @return View
     */
    public function purchaseEmail($purchase): \Illuminate\View\View
    {
        enableDeletedScope();
        $purchase = PurchaseTransaction::findOrFail($purchase);
        $data['purchase'] = $purchase->load([
            'addresses',
            'purchaseTransactionItems',
            'purchaseTransactionLedger',
            'supplier',
        ]);
        disableDeletedScope();

        return view('company.purchase.send-email')->with($data);
    }

    /**
     * @throws FileDoesNotExist
     * @throws FileIsTooBig
     */
    public function sendEmail(Request $request, $purchase)
    {
        enableDeletedScope();
        $purchase = PurchaseTransaction::findOrFail($purchase);
        $data = GetInvoicePdfDataForPurchase::run($purchase->id);
        $data = GstCalculationForPdf::run($data, PurchaseTransaction::class);

        $input = $request->all();

        if (isset($input['removed_attachment']) && ! empty($input['removed_attachment'][0])) {
            $cleanedFileArray = explode(',', $input['removed_attachment'][0]);
            $input['attachments'] = array_filter($input['attachments'], function ($file) use ($cleanedFileArray) {
                return ! in_array($file->getClientOriginalName(), $cleanedFileArray);
            });
        }

        if (isset($input['attachments']) && ! empty($input['attachments'])) {
            foreach ($input['attachments'] as $file) {
                $purchase->addMedia($file)->toMediaCollection(
                    PurchaseTransaction::INVOICE_ATTACHMENT,
                    config('app.media_disc')
                );
            }
        }

        $data['to'] = $input['to'] ?? null;
        $data['cc'] = $input['cc'] ?? null;
        $data['body'] = $input['body'] ?? null;
        $data['subject'] = $input['subject'] ?? null;
        $data['regards'] = $input['regards'] ?? null;
        $data['is_attachment'] = $input['is_attachment'] ?? 1;
        $data['customPaperSize'] = [0, 0, 700, 900];
        $data['fromName'] = getCompanySettings()['from_name'];
        $data['replayToEmail'] = getCompanySettings()['replay_to_email'];

        InvoiceMailJob::dispatch($data, PurchaseTransaction::class);

        Flash::success('Mail Sent Successfully.');
        disableDeletedScope();

        // return redirect()->route('company.purchases.index');
    }

    public function getInvoicePDFData($purchaseId): array
    {
        $data = [];
        $data['taxInvoice'] = 'Voucher No';
        $data['configuration'] = PurchaseConfiguration::first();
        $data['currentCompany'] = getCurrentCompany()?->load('addresses', 'companyTax', 'user', 'mailConfiguration', 'media');
        $data['companyBillingAddress'] = $data['currentCompany']->addresses
            ->where('address_type', Company::BILLING_ADDRESS)->first();
        $data['transaction'] = PurchaseTransaction::with(
            'addresses',
            'supplier',
            'transport',
            'purchaseTransactionItems',
            'purchaseTransactionLedger',
            'paymentTransactionItem'
        )->whereId($purchaseId)->firstOrFail();
        $data['transactionItems'] = $data['transaction']->purchaseTransactionItems->load('items');
        $data['transactionLedgers'] = $data['transaction']->purchaseTransactionLedger->load('ledgers');
        $data['customerDetail'] = $data['transaction']->supplier->load('model');
        $data['ledgerShippingAddress'] = $data['customerDetail']->model->shippingAddress;
        $addresses = $data['transaction']->addresses;
        $data['fromNameExists'] = CompanySetting::where('key', 'from_name')->exists();
        $data['replyToEmailExists'] = CompanySetting::where('key', 'replay_to_email')->exists();
        $data['billingAddress'] = $addresses->firstWhere('address_type', PurchaseTransaction::BILLING_ADDRESS);
        $data['shippingAddress'] = $addresses->firstWhere('address_type', PurchaseTransaction::SHIPPING_ADDRESS);
        $data['invoiceDate'] = Carbon::Parse($data['transaction']->date_of_invoice)->format('d-m-Y');
        $data['voucherDate'] = Carbon::Parse($data['transaction']->voucher_date)->format('d-m-Y');
        $data['invoiceNo'] = $data['transaction']->sale_number;
        $data['itemType'] = $data['transaction']->purchase_item_type;
        $data['dueDate'] = null;
        $data['creditPeriod'] = null;
        if (! empty($data['transaction']->credit_period)) {

            $data['creditPeriod'] = $data['transaction']->credit_period.' '.SaleTransaction::CREDIT_PERIOD_TYPE[$data['transaction']->credit_period_type];

            if ($data['transaction']->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_MONTH) {
                $data['dueDate'] = Carbon::parse($data['transaction']->voucher_date)->addMonths($data['transaction']->credit_period)->format('d-m-Y');
            } elseif ($data['transaction']->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_DAY) {
                $data['dueDate'] = Carbon::parse($data['transaction']->voucher_date)->addDays($data['transaction']->credit_period)->format('d-m-Y');
            }
        } elseif (! empty($data['customerDetail']->model->credit_limit_period)) {

            $data['creditPeriod'] = $data['customerDetail']->model->credit_limit_period.' '.SaleTransaction::CREDIT_PERIOD_TYPE[$data['customerDetail']->model->credit_period_type];

            if ($data['customerDetail']->model->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_MONTH) {
                $data['dueDate'] = Carbon::parse($data['transaction']->voucher_date)->addMonths($data['customerDetail']->model->credit_limit_period)->format('d-m-Y');
            } elseif ($data['customerDetail']->model->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_DAY) {
                $data['dueDate'] = Carbon::parse($data['transaction']->voucher_date)->addDays($data['customerDetail']->model->credit_limit_period)->format('d-m-Y');
            }
        }

        return $data;
    }

    public function getTdsLedger(Request $request): JsonResponse
    {
        $ledgerId = $request->ledgerTdsId;
        $data = Ledger::whereId($ledgerId)->with('model')->first();

        return $this->sendResponse($data, 'Ledger Retrieved Successfully');
    }

    public function storePurchaseTdsEntry(Request $request): JsonResponse
    {
        $input = $request->all();
        $transactionId = $request->purchaseTransactionId;
        $passTdsEntry = (int) ($input['pass_tds_entry'] ?? 0);

        $tdsEntry = [
            'pass_tds_entry' => $passTdsEntry,
            'tds_pan' => $passTdsEntry != 0 ? $input['tds_pan'] : null,
            'tds_taxable_value' => $passTdsEntry != 0 ? $input['tds_taxable_value'] : null,
            'ledger_of_tds' => $passTdsEntry != 0 ? $input['ledger_of_tds'] : null,
            'tds_rate' => $passTdsEntry != 0 ? $input['tds_rate'] : null,
            'tds_amount' => $passTdsEntry != 0 ? $input['tds_amount'] : null,
        ];

        $data = PurchaseTransaction::whereId($transactionId)->first();
        $data->update($tdsEntry);

        updateFieldsValue($data);

        return $this->sendResponse($data, 'TDS Entry store Successfully');
    }

    public function checkPurchaseExists(PurchaseTransaction $purchase)
    {
        $transactionsData = CheckExistPurchaseTransactionData::run($purchase);

        if (isset($transactionsData['transactionsIsCashMode']) && $transactionsData['transactionsIsCashMode']) {
            return $transactionsData;
        }

        $data['html'] = view('company.sale.delete_sale_associated_transaction_list')->with($transactionsData)->render();
        $data['checkTransactionExists'] = $transactionsData['checkTransactionExists'];
        $data['transactionName'] = $transactionsData['transactionName'];

        return $data;
    }

    /**
     * @return Response|BinaryFileResponse
     */
    public function export(Request $request)
    {
        $input = $request->all();
        $type = $input['type'];
        // $purchaseTransactionCacheKey = generateCacheKey('purchase_transactions');
        $purchaseTransactionFilterCacheKey = generateCacheKey('purchase_transactions_filter');
        $company = getCurrentCompany();
        $data = [];
        // $data['data'] = Cache::get($purchaseTransactionCacheKey);
        $filters = Cache::get($purchaseTransactionFilterCacheKey);
        $data = $filters;

        $queryData = PurchaseTransaction::select('purchase_transactions.*')
            ->whereBetween('purchase_transactions.voucher_date', [$data['data']['startDate'], $data['data']['endDate']])
            ->when(! empty($data['data']['partyName']), function ($q) use ($data) {
                $q->where('supplier_ledger_id', $data['data']['partyName']);
            })
            ->when(! empty($data['data']['paymentStatus']), function ($q) use ($data) {
                $q->where('payment_status', $data['data']['paymentStatus']);
            });

        $queryData = $queryData->sorting($data['data']['defaultSorting'])->get();

        /** @var PurchaseTransactionRepositories $purchaseTransactionRepo */
        $purchaseTransactionRepo = App::make(PurchaseTransactionRepositories::class);
        $data['data']['allData'] = $purchaseTransactionRepo->prepareDataForTable($queryData);

        $data['company'] = $company;
        $data['companyAddress'] = $company?->addresses->where('address_type', Company::BILLING_ADDRESS)->first();
        $data['durationDate'] = Carbon::parse($input['start_date'])->format('d-m-Y').' to '.Carbon::parse($input['end_date'])->format('d-m-Y');

        if ($type == 'excel') {
            $fileName = 'purchaseTransaction'.Carbon::now()->format('d-m-Y').'.xlsx';

            $response = Excel::download(new PurchaseTransactionExport($data), $fileName);
            ob_end_clean();

            return $response;

            // return (new PurchaseTransactionExport($data))->download($fileName);
        }

        $fileName = 'purchaseTransaction'.Carbon::now()->format('d-m-Y').'.pdf';

        return Pdf::loadView('pdf.purchase-transaction', $data)
            ->setPaper('a4', 'landscape')
            ->download($fileName);
    }

    public function importPurchase(Request $request): JsonResponse
    {
        ini_set('max_execution_time', 3600000000);
        ini_set('memory_limit', '500M'); // or you could use nM

        $request->validate([
            'purchase_excel_file' => 'required|mimes:xlsx,xls,csv',
        ]);

        $type = 'purchase';
        $transactionErrorCacheKey = generateCacheKey($type.'_transaction_import_error');
        Cache::forget($transactionErrorCacheKey);

        $input = $request->all();
        if ($input['transaction_type'] == PurchaseTransaction::ITEM_INVOICE) {
            $import = new PurchaseItemInvoiceImport();
        } else {
            $import = new PurchaseAccountingInvoiceImport();
        }
        $import->import($input['purchase_excel_file']);
        $data = $import;

        if (! empty($import->importErrors['missing_voucher_number'])) {
            return $this->sendError($import->importErrors['missing_voucher_number']);
        }
        if (! empty($import->importErrors['field_missing'])) {
            return $this->sendError($import->importErrors['field_missing']);
        }
        if (! empty($import->importErrors['empty_excel'])) {
            return $this->sendError($import->importErrors['empty_excel']);
        }
        if (! empty($import->importErrors['wrong_excel'])) {
            return $this->sendError($import->importErrors['wrong_excel']);
        }
        if (! empty($import->importErrors['unsupported_formula'])) {
            return $this->sendError($import->importErrors['unsupported_formula']);
        }
        $modalIsOpen = false;
        if ($import->importErrors) {
            $modalIsOpen = true;
        }
        Cache::put($transactionErrorCacheKey, $data);
        $html = view('company.import-transaction-error-modal.import_error_list', compact('data', 'type'))->render();

        $importedInvoice = $data->totalInvoice - $data->notImportedInvoice;
        $message = '';
        if ($importedInvoice != 0) {
            $message = 'Purchase transaction imported successfully.';
        }

        return $this->sendResponse(['modalIsOpen' => $modalIsOpen, 'html' => $html], $message);
    }

    public function downloadPurchaseInvoice(PurchaseTransaction $purchase)
    {
        $mediaCollection = $purchase->media;
        if ($mediaCollection->count() > 0) {
            $mediaItem = $mediaCollection->first();
            $mediaPath = $mediaItem->getPath();

            return response()->download($mediaPath);
        }
    }

    public function bulkDelete(Request $request)
    {
        $input = $request->all();

        $purchases = PurchaseTransaction::whereIn('id', $input['ids'])->get();
        $showMode = true;
        $data = [];
        $data['alreadyUsed'] = [];
        $data['lockedTransaction'] = [];
        $lockDate = getTransactionsLockDate()['expense'] ?? null;

        foreach ($purchases as $purchase) {
            $isLocked = ! empty($lockDate) && Carbon::parse($lockDate)->greaterThanOrEqualTo($purchase->voucher_date);
            $transactionsData = CheckExistPurchaseTransactionData::run($purchase);
            if ($isLocked) {
                $data['lockedTransaction'][] = $purchase->voucher_number;
            } elseif ($transactionsData['checkTransactionExists']) {
                $data['alreadyUsed'][] = $transactionsData['purchase']->voucher_number;
            } else {
                DeletePurchaseTransaction::run($purchase);
            }
        }

        if (empty($data['alreadyUsed']) && empty($data['lockedTransaction'])) {
            $showMode = false;
        }

        $html = '';
        if (! empty($data)) {
            $html = view('company.item-master.list-view', compact('data'))->render();
        }

        return $this->sendResponse(['html' => $html, 'showMode' => $showMode], 'Purchase deleted successfully');
    }

    public function downloadAttachment(PurchaseTransaction $purchase): Response|BinaryFileResponse
    {
        $mediaCollection = $purchase->media;

        if ($mediaCollection->isEmpty()) {
            abort(404, 'No documents found.');
        }

        $tempDir = storage_path('app/temp');
        if (! File::exists($tempDir)) {
            File::makeDirectory($tempDir, 0755, true);
        }

        if ($mediaCollection->count() === 1) {
            $mediaItem = $mediaCollection->first();
            $mediaPath = $mediaItem->getPath();

            if (config('app.media_disc') === 'public') {
                $mediaPath = Str::after($mediaItem->getUrl(), '/uploads');
            }

            $file = Storage::disk(config('app.media_disc'))->get($mediaPath);

            return response($file, 200, [
                'Content-Type' => $mediaItem->mime_type,
                'Content-Description' => 'File Transfer',
                'Content-Disposition' => "attachment; filename={$mediaItem->file_name}",
                'filename' => $mediaItem->file_name,
            ]);
        }

        $zipFileName = 'documents_'.time().'.zip';
        $zipPath = storage_path("app/temp/{$zipFileName}");

        $zip = new ZipArchive();
        if ($zip->open($zipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== true) {
            abort(500, 'Could not create zip file.');
        }

        foreach ($mediaCollection as $mediaItem) {
            $mediaPath = $mediaItem->getPath();
            if (config('app.media_disc') === 'public') {
                $mediaPath = Str::after($mediaItem->getUrl(), '/uploads');
            }

            $fileContent = Storage::disk(config('app.media_disc'))->get($mediaPath);
            $fileName = pathinfo($mediaItem->file_name, PATHINFO_FILENAME)
                .'_'.uniqid()
                .'.'.pathinfo($mediaItem->file_name, PATHINFO_EXTENSION);
            $zip->addFromString($fileName, $fileContent);
        }

        $zip->close();

        return response()->download($zipPath)->deleteFileAfterSend(true);
    }

    public function getPurchasesItemList(Request $request)
    {
        $input = $request->all();
        $input['purchaseConfiguration'] = PurchaseConfiguration::toBase()->first();
        $html = view('company.purchase.item-invoice-list')->with($input)->render();

        return $this->sendResponse(['html' => $html], 'Item list generated successfully.');
    }

    public function getPurchasesAccountingLedgerList(Request $request)
    {
        $input = $request->all();

        $html = view('company.purchase.item-accounting-ledger-list')->with($input)->render();

        return $this->sendResponse(['html' => $html], 'Ledger list generated successfully.');
    }

    public function purchaseBulkDownload(Request $request)
    {
        $input = $request->all();

        $fileName = 'purchase-transaction-'.strtotime('now').'.pdf';
        $notification = DownloadBulkPdf::run(Notification::DOWNLOAD, $fileName);
        $currentCompany = getCurrentCompany();

        BulkPdfExportJob::dispatch($input['ids'], $notification, $currentCompany, PurchaseTransaction::class);

        return true;
    }

    public function getPartyPurchaseOrderNumbers($partyId)
    {
        $purchaseOrderNumbers = getPurchaseOrderNumbers($partyId);

        return $this->sendResponse($purchaseOrderNumbers, 'Party purchase order number retrieved successfully');
    }

    public function manageMultiplePurchaseOrderTransaction(Request $request)
    {
        $input = $request->all();

        $data = GetMultiplePurchaseOrderTransaction::run($input['purchaseOrderTransactionTds'], $input['invoiceType'], $input['invoiceNumber']);

        return $this->sendResponse($data, 'Purchase Order transaction retrieved successfully');
    }

    public function getPurchaseOrderTransaction(PurchaseOrderTransaction $order)
    {
        $data = $order->load('transactionItems.items', 'transactionItems.classificationNatureType', 'transactionItems.gst', 'transactionLedgers.gst', 'billingAddress', 'shippingAddress', 'party');

        return $this->sendResponse($data, 'Purchase order transaction retrieved successfully');
    }

    public function purchaseCreatePurchaseOrder(PurchaseOrderTransaction $order)
    {
        $data = [];
        $getCurrentCompany = getCurrentCompany();
        $data['purchase'] = $order->load(['party', 'transactionItems.items', 'transactionLedgers', 'billingAddress', 'shippingAddress']);
        $data['purchase']->purchase_item_type = $data['purchase']->order_type;
        $data['purchase']->purchaseTransactionLedger = $data['purchase']->transactionLedgers;

        // Calculate remaining quantity in invoive type is items
        $data['purchase']->purchaseTransactionItems = collect($data['purchase']->transactionItems)->filter(function ($item) {
            $purchaseOrderId = $item['transactions_id'];
            $purchaseItemQty = PurchaseItemTransaction::whereHas('purchaseTransaction', function ($query) use ($purchaseOrderId) {
                $query->whereRaw('FIND_IN_SET(?, purchase_order_no)', [$purchaseOrderId])
                    ->whereNull('deleted_at');
            })
                ->where('item_id', $item['item_id'])
                ->sum('quantity');

            if (! empty($purchaseItemQty)) {
                $qty = max(0, $item['quantity'] - $purchaseItemQty);
                $total = $qty * $item['rpu_without_gst'];

                if ($item['discount_value'] == PurchaseTransaction::DISCOUNT_TYPE_AMOUNT) {
                    $total -= $item['discount_value'];
                } else {
                    $total -= ($total * $item['discount_value'] / 100);
                }

                $item['quantity'] = round($qty, 2);
                $item['total'] = round($total, 2);
            }

            return $item['quantity'] > 0;
        })->values();

        $data['purchase']->setRelation('transactionItems', $data['purchase']->purchaseTransactionItems);

        $data['groupLists'] = getGroupLists();
        $data['groupListsId'] = CompanyGroup::whereCompanyId($getCurrentCompany->id)->where('name', Ledger::SUPPLIER)->value('id');
        $data['countries'] = getCountries();
        $data['indiaStates'] = getStates(Company::INDIA_COUNTRY_ID);
        $data['billingStates'] = getStates($data['purchase']->billingAddress->country_id);
        $data['billingCities'] = getCities($data['purchase']->billingAddress->state_id);
        if ($data['purchase']->shippingAddress) {
            $data['shippingStates'] = getStates($data['purchase']->shippingAddress->country_id);
            $data['shippingCities'] = getCities($data['purchase']->shippingAddress->state_id);
        }
        $data['expenseTransaction'] = ExpenseTransactionMaster::whereCompanyId(getCurrentCompany()->id)->first();
        $data['purchaseConfiguration'] = PurchaseConfiguration::toBase()->first();
        $data['lastVoucherNumber'] = GetPurchaseVoucherNumber::run();
        $data['methodVoucherType'] = $data['expenseTransaction']->method_of_voucher_number ?? PurchaseOrderTransactionMaster::AUTOMATIC;
        $data['companyItemMasterGroups'] = getParentGroupsValue(Ledger::ITEM_DEFAULT_GROUP);
        $data['defaultGroup'] = CompanyGroup::whereName(Ledger::ITEM_DEFAULT_GROUP)
            ->whereCompanyId(getCurrentCompany()->id)
            ->value('id');
        $data['unitOfMeasurement'] = getItemMasterUnit();
        $data['previousBillId'] = PurchaseOrderTransaction::orderBy('created_at', 'DESC')->first()?->id;
        $data['paymentMode'] = $data['purchase']->payment_mode ?? PurchaseTransaction::CREDIT_MODE;
        $TdsGroups = getParentGroupsValue(Ledger::TAXES_TDS);
        $groupIds = array_merge(array_keys($TdsGroups));
        $data['ledgerOfTds'] = Ledger::whereIn('group_id', $groupIds)->pluck('name', 'id')->toArray();
        $data['purchaseOrderNumbers'] = getPurchaseOrderNumbers($data['purchase']->party_ledger_id);
        $data['billingAddress'] = $data['purchase']->addresses->firstWhere(
            'address_type',
            PurchaseTransaction::BILLING_ADDRESS
        );
        $data['shippingAddress'] = $data['purchase']->addresses->firstWhere(
            'address_type',
            PurchaseTransaction::SHIPPING_ADDRESS
        );

        return view('company.purchase.create-invoice')->with($data);
    }

    public function getPurchaseOrderTransactionItems($order): JsonResponse
    {
        $purchaseTransaction = PurchaseOrderTransaction::whereId($order)->with(
            'transactionItems',
            'transactionLedgers'
        )
            ->firstOrFail();

        $isCompanyGstApplicable = isCompanyGstApplicable();
        $purchaseConfiguration = PurchaseConfiguration::toBase()->first();

        $html = view(
            'company.purchase.append.items_fields',
            compact('purchaseConfiguration', 'purchaseTransaction')
        )->render();

        return $this->sendResponse($html, 'Purchase Item Type retrieved successfully');
    }
}
