<?php

namespace App\Http\Controllers;

use App\Actions\AuditTrail\CreateAuditTrailEvent;
use App\Actions\Reports\Gstr1\GenerateGstr1Summary;
use App\Actions\Reports\Gstr1\PrepareGstr1Data;
use App\Exports\GSTR1ErrorExport;
use App\Exports\Gstr1ReportExport;
use App\Models\Company;
use App\Models\GstDashboardData;
use App\Models\GstFiling;
use App\Models\GstrLogin;
use App\Models\IncomeCreditNoteItemTransaction;
use App\Models\IncomeCreditNoteLedgerTransaction;
use App\Models\IncomeCreditNoteTransaction;
use App\Models\IncomeDebitNoteItemTransaction;
use App\Models\IncomeDebitNoteLedgerTransaction;
use App\Models\IncomeDebitNoteTransaction;
use App\Models\SaleReturnItemTransaction;
use App\Models\SaleReturnLedgerTransaction;
use App\Models\SaleReturnTransaction;
use App\Models\SaleTransaction;
use App\Models\SaleTransactionItem;
use App\Models\SaleTransactionLedger;
use App\Models\TaxClassificationDetails;
use App\Services\FileGstService;
use Barryvdh\DomPDF\Facade\Pdf as PDF;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response as FacadesResponse;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

/**
 * Class Gstr1ReportController
 */
class Gstr1ReportController extends AppBaseController
{
    public $intrastateSalesTaxable;

    public $intrastateSalesExempt;

    public $intrastateSalesNilRated;

    public $interstateSalesTaxable;

    public $interstateSalesExempt;

    public $interstateSalesNilRated;

    public $exportSalesTaxable;

    public $exportSalesExempt;

    public $exportSalesNilRated;

    public $exportSalesunderLUT;

    public $salestoSEZTaxable;

    public $salestoSEZExempt;

    public $salestoSEZNilRated;

    public $salestoSEZunderLUT;

    public $deemedExportIntrastate;

    public $deemedExportInterstate;

    public $nonGSTSupplyIntraState;

    public $nonGSTSupplyInterState;

    public $B2BLB2CSCutoffAmount = 100000;

    /** @var FileGstService */
    public mixed $fileGstService;

    public function __construct()
    {
        $this->fileGstService = new FileGstService();
    }

    public function loadData()
    {
        $taxClassificationDetails = DB::table('tax_classification_details')->select('name', 'id')->get();

        $this->intrastateSalesTaxable = $taxClassificationDetails->firstWhere(
            'name',
            TaxClassificationDetails::SALE_INTRA_STATE_TAXABLE
        )->id;
        $this->intrastateSalesExempt = $taxClassificationDetails->firstWhere(
            'name',
            TaxClassificationDetails::SALE_INTRA_STATE_EXEMPT
        )->id;
        $this->intrastateSalesNilRated = $taxClassificationDetails->firstWhere(
            'name',
            TaxClassificationDetails::SALE_INTRA_STATE_NIL_RATED
        )->id;
        $this->interstateSalesTaxable = $taxClassificationDetails->firstWhere(
            'name',
            TaxClassificationDetails::SALE_INTER_STATE_TAXABLE
        )->id;
        $this->interstateSalesExempt = $taxClassificationDetails->firstWhere(
            'name',
            TaxClassificationDetails::SALE_INTER_STATE_EXEMPT
        )->id;
        $this->interstateSalesNilRated = $taxClassificationDetails->firstWhere(
            'name',
            TaxClassificationDetails::SALE_INTER_STATE_NIL_RATED
        )->id;
        $this->exportSalesTaxable = $taxClassificationDetails->firstWhere(
            'name',
            TaxClassificationDetails::SALE_EXPORT_TAXABLE
        )->id;
        $this->exportSalesExempt = $taxClassificationDetails->firstWhere(
            'name',
            TaxClassificationDetails::SALE_EXPORT_EXEMPT
        )->id;
        $this->exportSalesNilRated = $taxClassificationDetails->firstWhere(
            'name',
            TaxClassificationDetails::SALE_EXPORT_NIL_RATED
        )->id;
        $this->exportSalesunderLUT = $taxClassificationDetails->firstWhere(
            'name',
            TaxClassificationDetails::SALE_EXPORT_LUT_BOND
        )->id;
        $this->salestoSEZTaxable = $taxClassificationDetails->firstWhere(
            'name',
            TaxClassificationDetails::SALES_TO_SEZ_TAXABLE
        )->id;
        $this->salestoSEZExempt = $taxClassificationDetails->firstWhere(
            'name',
            TaxClassificationDetails::SALES_TO_SEZ_EXEMPT
        )->id;
        $this->salestoSEZNilRated = $taxClassificationDetails->firstWhere(
            'name',
            TaxClassificationDetails::SALES_TO_SEZ_NIL_RATED
        )->id;
        $this->salestoSEZunderLUT = $taxClassificationDetails->firstWhere(
            'name',
            TaxClassificationDetails::SALES_TO_SEZ_LUT_BOND
        )->id;
        $this->deemedExportIntrastate = $taxClassificationDetails->firstWhere(
            'name',
            TaxClassificationDetails::DEEMED_EXPORT_INTRASTATE
        )->id;
        $this->deemedExportInterstate = $taxClassificationDetails->firstWhere(
            'name',
            TaxClassificationDetails::DEEMED_EXPORT_INTER_STATE
        )->id;
        $this->nonGSTSupplyIntraState = $taxClassificationDetails->firstWhere(
            'name',
            TaxClassificationDetails::NON_GST_SUPPLY_INTRASTATE
        )->id;
        $this->nonGSTSupplyInterState = $taxClassificationDetails->firstWhere(
            'name',
            TaxClassificationDetails::NON_GST_SUPPLY_INTERSTATE
        )->id;
    }

    /**
     * @return View
     */
    public function index(Request $request): \Illuminate\View\View
    {
        if (! getCurrentCompany()->is_gst_applicable) {
            return abort(404);
        }
        $gstLogin = GstrLogin::latest()->first();
        $input = $request->all();
        $gstrCacheKey = generateCacheKey('gstr1_report');
        Cache::forget($gstrCacheKey);
        $gstrData = $this->gstr1ExcelReportData($input, true);

        $data = [];
        $data['data'] = $gstrData;
        $data['b2bSezdeTotal'] = sumOfGstr1ExcelSheet($gstrData['b2bSezde']);
        $data['b2cl'] = sumOfGstr1ExcelSheet($gstrData['b2cl']);
        $data['b2cs'] = sumOfGstr1ExcelSheet($gstrData['b2cs']);
        $data['cdnr'] = sumOfGstr1ExcelSheet($gstrData['cdnr']);
        $data['cdnur'] = sumOfGstr1ExcelSheet($gstrData['cdnur']);
        $data['exp'] = sumOfGstr1ExcelSheet($gstrData['exp']);
        $data['nillRated'] = sumOfGstr1ExcelSheet($gstrData['exemp']);
        $data['hsnB2b'] = sumOfGstr1hsnExcelSheet($gstrData['hsnB2b']);
        $data['hsnB2c'] = sumOfGstr1hsnExcelSheet($gstrData['hsnB2c']);
        $data['gstLogin'] = $gstLogin;
        $data['filterType'] = $input['filter_type'] ?? 3;
        $filingPeriod = ! empty($input['end_date'])
        ? Carbon::parse($input['end_date'])->format('mY')
        : null;
        $data['gstStatus'] = GstFiling::whereReturnPeriod($filingPeriod)->latest()->first();
        $data['gstFiling'] = GstDashboardData::where('month', $filingPeriod)->latest()->first();

        Cache::put($gstrCacheKey, $data);

        return view('company.gst-reports.gstr-1.index')->with($data);
    }

    /**
     * @return Response|BinaryFileResponse
     */
    public function gstr1ExcelReport(Request $request)
    {
        ini_set('max_execution_time', '300'); // 5 minutes
        ini_set('memory_limit', '1024M'); // 1024 MB

        $input = $request->all();
        $company = getCurrentCompany();
        $data['data'] = $this->gstr1ExcelReportData($input);

        $startDate = isset($input['start_date']) ? Carbon::parse($input['start_date'])->format('Y-m-d') : Carbon::now()->startOfMonth()->format('Y-m-d');
        $endDate = isset($input['end_date']) ? Carbon::parse($input['end_date'])->format('Y-m-d') : Carbon::now()->endOfMonth()->format('Y-m-d');
        $data['fileName'] = 'GSTR1_'.$company->trade_name.'_'.$startDate.'_to_'.$endDate;
        $data['fileName'] = replaceSpecialCharacters($data['fileName']);

        $user = getLoginUser();
        CreateAuditTrailEvent::run($user, 'Export', '<b>GSTR-1 Report</b> Excel was exported.');
        // if (ob_end_clean() > 0) {
        //     ob_end_clean();
        // }
        $response = Excel::download(new Gstr1ReportExport($data), $data['fileName'].'.xlsx');

        return $response;

        // return (new Gstr1ReportExport($data))->download($data['fileName'].'.xlsx');
    }

    public function gstr1ExcelReportData(array $input, $indexPage = false): array
    {
        $this->loadData();
        $data = [];
        $data['start_date'] = $input['start_date'] ?? Carbon::now()->startOfMonth()->format('Y-m-d');
        $data['end_date'] = $input['end_date'] ?? Carbon::now()->endOfMonth()->format('Y-m-d');

        $data['b2bSezde'] = $this->getb2bSezdeTransactionsData($data['start_date'], $data['end_date']);
        $data['b2ba'] = [];
        $data['b2cl'] = $this->getb2clTransactionData($data['start_date'], $data['end_date']);
        $data['b2cla'] = [];
        $data['b2cs'] = $this->getb2csTransactionData($data['start_date'], $data['end_date'], $indexPage);
        $data['b2csa'] = [];
        $data['cdnr'] = $this->getCdnrTransactionData($data['start_date'], $data['end_date']);
        $data['cdnra'] = [];
        $data['cdnur'] = $this->getCdnurTransactionData($data['start_date'], $data['end_date']);
        $data['cdnura'] = [];
        $data['exp'] = $this->getExpTransactionData($data['start_date'], $data['end_date']);
        $data['expa'] = [];
        $data['at'] = [];
        $data['ata'] = [];
        $data['atadj'] = [];
        $data['atadja'] = [];
        $data['docs'] = $this->getDocsTransactionData($data['start_date'], $data['end_date']);

        if ($indexPage) {
            $data['exemp'] = $this->getExempTransactionDataIndexPage($data['start_date'], $data['end_date']);
        } else {
            $data['exemp'] = $this->getExempTransactionDataExcelSheet($data['start_date'], $data['end_date']);
        }

        $data['hsnB2b'] = $this->getHsnB2bTransactionData($data['start_date'], $data['end_date']);
        $data['hsnB2c'] = $this->getHsnB2cTransactionData($data['start_date'], $data['end_date']);
        $data['eco'] = [];
        $data['ecoa'] = [];
        $data['ecob2b'] = [];
        $data['ecob2c'] = [];
        $data['ecourp2b'] = [];
        $data['ecourp2c'] = [];
        $data['ecoab2b'] = [];
        $data['ecoab2c'] = [];
        $data['ecoaurp2b'] = [];
        $data['ecoaurp2c'] = [];

        // also use in API
        return $data;
    }

    public function getb2bSezdeTransactionsData($startDate, $endDate): array
    {
        $transactionData = [];
        //intrastateSalesTaxable type
        $intrastateSalesTaxable = $this->intrastateSalesTaxable;
        $saleIntrastateSaleTaxableTra = SaleTransaction::with([
            'saleItems.classificationNatureType', 'saleLedgers.classificationNatureType',
            'receiptTransactionItem', 'journalTransactionItem', 'saleReturn', 'creditNote', 'addresses', 'customer',
        ])
            ->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($intrastateSalesTaxable) {
                $q->whereHas('saleItems', function ($q) use ($intrastateSalesTaxable) {
                    $q->where('classification_nature_type', $intrastateSalesTaxable);
                })->orWhereHas('saleLedgers', function ($q) use ($intrastateSalesTaxable) {
                    $q->where('classification_nature_type', $intrastateSalesTaxable);
                });
            })
            ->get();
        foreach ($saleIntrastateSaleTaxableTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $intrastateSalesExempt = $this->intrastateSalesExempt;
        //Intrastate Sales Exempt
        $saleIntrastateTra = SaleTransaction::with([
            'saleItems.classificationNatureType', 'saleLedgers.classificationNatureType',
            'receiptTransactionItem', 'journalTransactionItem', 'saleReturn', 'creditNote', 'addresses', 'customer',
        ])
            ->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($intrastateSalesExempt) {
                $q->whereHas('saleItems', function ($q) use ($intrastateSalesExempt) {
                    $q->where('classification_nature_type', $intrastateSalesExempt)
                        ->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($intrastateSalesExempt) {
                        $q->where('classification_nature_type', $intrastateSalesExempt)
                            ->where('classification_is_rcm_applicable', false);
                    });
            })
            ->get();
        foreach ($saleIntrastateTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $intrastateSalesNilRated = $this->intrastateSalesNilRated;
        //Intrastate Sales Nil Rated
        $saleIntrastateNilRatedTra = SaleTransaction::with([
            'saleItems.classificationNatureType', 'saleLedgers.classificationNatureType',
            'receiptTransactionItem', 'journalTransactionItem', 'saleReturn', 'creditNote', 'addresses', 'customer',
        ])
            ->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($intrastateSalesNilRated) {
                $q->whereHas('saleItems', function ($q) use ($intrastateSalesNilRated) {
                    $q->where('classification_nature_type', $intrastateSalesNilRated)
                        ->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($intrastateSalesNilRated) {
                        $q->where('classification_nature_type', $intrastateSalesNilRated)
                            ->where('classification_is_rcm_applicable', false);
                    });
            })
            ->get();
        foreach ($saleIntrastateNilRatedTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $interstateSalesTaxable = $this->interstateSalesTaxable;
        //Interstate Sales Taxable
        $saleInterstateTaxableTra = SaleTransaction::with([
            'saleItems.classificationNatureType', 'saleLedgers.classificationNatureType',
            'receiptTransactionItem', 'journalTransactionItem', 'saleReturn', 'creditNote', 'addresses', 'customer',
        ])
            ->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($interstateSalesTaxable) {
                $q->whereHas('saleItems', function ($q) use ($interstateSalesTaxable) {
                    $q->where('classification_nature_type', $interstateSalesTaxable);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($interstateSalesTaxable) {
                        $q->where('classification_nature_type', $interstateSalesTaxable);
                    });
            })
            ->get();
        foreach ($saleInterstateTaxableTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $interstateSalesExempt = $this->interstateSalesExempt;
        //Interstate Sales Exempt
        $saleInterstateExempt = SaleTransaction::with([
            'saleItems.classificationNatureType', 'saleLedgers.classificationNatureType',
            'receiptTransactionItem', 'journalTransactionItem', 'saleReturn', 'creditNote', 'addresses',
        ])
            ->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($interstateSalesExempt) {
                $q->whereHas('saleItems', function ($q) use ($interstateSalesExempt) {
                    $q->where('classification_nature_type', $interstateSalesExempt)
                        ->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($interstateSalesExempt) {
                        $q->where('classification_nature_type', $interstateSalesExempt)
                            ->where('classification_is_rcm_applicable', false);
                    });
            })
            ->get();
        foreach ($saleInterstateExempt as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $interstateSalesNilRated = $this->interstateSalesNilRated;
        //Interstate Sales Nil Rated
        $interStateNilRated = SaleTransaction::with([
            'saleItems.classificationNatureType', 'saleLedgers.classificationNatureType',
            'receiptTransactionItem', 'journalTransactionItem', 'saleReturn', 'creditNote', 'addresses',
        ])
            ->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($interstateSalesNilRated) {
                $q->whereHas(
                    'saleItems',
                    function ($q) use ($interstateSalesNilRated) {
                        $q->where('classification_nature_type', $interstateSalesNilRated)
                            ->where('classification_is_rcm_applicable', false);
                    }
                )
                    ->orwhereHas('saleLedgers', function ($q) use ($interstateSalesNilRated) {
                        $q->where('classification_nature_type', $interstateSalesNilRated)
                            ->where('classification_is_rcm_applicable', false);
                    });
            })
            ->get();
        foreach ($interStateNilRated as $transaction) {
            $getData = $transaction->getb2bSezdeTransactionsData();
            foreach ($getData as $data) {
                $transactionData[] = $data;
            }
        }
        $taxes = [
            $this->salestoSEZTaxable,
            $this->salestoSEZExempt,
            $this->salestoSEZNilRated,
            $this->salestoSEZunderLUT,
            $this->deemedExportIntrastate,
            $this->deemedExportInterstate,
        ];
        //11,12,13,14,15,16 taxes types
        $saleOtherTaxTra = SaleTransaction::with([
            'saleItems.classificationNatureType', 'saleLedgers.classificationNatureType',
            'receiptTransactionItem', 'journalTransactionItem', 'saleReturn', 'creditNote',
        ])
            ->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($query) use ($taxes) {
                $query->whereHas('saleItems', function ($q) use ($taxes) {
                    $q->whereIn('classification_nature_type', $taxes);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($taxes) {
                        $q->whereIn('classification_nature_type', $taxes);
                    });
            })
            ->get();
        foreach ($saleOtherTaxTra as $transaction) {
            $getData = $transaction->getb2bSezdeTransactionsData();
            foreach ($getData as $data) {
                $transactionData[] = $data;
            }
        }
        usort($transactionData, function ($a, $b) {
            return strtotime($a['invoice_date']) - strtotime($b['invoice_date']);
        });

        return $transactionData;
    }

    public function getb2clTransactionData($startDate, $endDate): array
    {
        $transactionData = [];
        //interstateSalesTaxable type
        $grandTotalValueDate = '2024-08-01';
        $interstateSalesTaxable = $this->interstateSalesTaxable;
        $saleInterstateSaleTaxableTra = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', null);
            // })
            ->where(function ($q) use ($grandTotalValueDate) {
                $q->where(function ($query) use ($grandTotalValueDate) {
                    $query->whereDate('date', '>', Carbon::parse($grandTotalValueDate))->where('grand_total', '>=', 250000);
                })->orWhere(function ($query) use ($grandTotalValueDate) {
                    $query->where('date', '>=', $grandTotalValueDate)->where('grand_total', '>=', $this->B2BLB2CSCutoffAmount);
                });
            })
            ->where(function ($q) use ($interstateSalesTaxable) {
                $q->whereHas('saleItems', function ($query) use ($interstateSalesTaxable) {
                    $query->where('classification_nature_type', $interstateSalesTaxable);
                })->orWhereHas('saleLedgers', function ($query) use ($interstateSalesTaxable) {
                    $query->where('classification_nature_type', $interstateSalesTaxable);
                });
            })
            ->get();
        foreach ($saleInterstateSaleTaxableTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        // intrastateSalesTaxable type
        // $intrastateSalesTaxable = $this->intrastateSalesTaxable;
        // $saleIntrastateSaleTaxableTra = SaleTransaction::whereBetween('date', [$startDate, $endDate])
        //     ->where('is_gst_na', '=', 0)
        //     ->whereNull('gstin')
        //     ->whereHas('customer.model', function ($q) {
        //         $q->where('gstin', null);
        //     })
        //     ->where(function ($q) use ($grandTotalValueDate) {
        //         $q->where(function ($query) use ($grandTotalValueDate) {
        //             $query->where('date', '<', $grandTotalValueDate)->where('grand_total', '>=', 250000);
        //         })->orWhere(function ($query) use ($grandTotalValueDate) {
        //             $query->where('date', '>=', $grandTotalValueDate)->where('grand_total', '>=', $this->B2BLB2CSCutoffAmount);
        //         });
        //     })
        //     ->where(function ($q) use ($intrastateSalesTaxable) {
        //         $q->whereHas('saleItems', function ($query) use ($intrastateSalesTaxable) {
        //             $query->where('classification_nature_type', $intrastateSalesTaxable);
        //         })->orWhereHas('saleLedgers', function ($query) use ($intrastateSalesTaxable) {
        //             $query->where('classification_nature_type', $intrastateSalesTaxable);
        //         });
        //     })
        //     ->get();
        // foreach ($saleIntrastateSaleTaxableTra as $transaction) {
        //     foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
        //         $transactionData[] = $data;
        //     }
        // }

        usort($transactionData, function ($a, $b) {
            return strtotime($a['invoice_date']) - strtotime($b['invoice_date']);
        });

        return $transactionData;
    }

    public function getb2csTransactionData($startDate, $endDate, $indexPage = false): array
    {
        /* Commented out the `where('grand_total', '<=', 250000)` line following the discussion on 02-09-2024 regarding the GSTR-1 and GSTR-3B amount mismatch issue. */
        $transactionData = [];
        //intrastateSalesTaxable type
        $intrastateSalesTaxable = $this->intrastateSalesTaxable;
        $saleIntrastateSaleTaxableTra = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            // ->where('grand_total', '<=', $this->B2BLB2CSCutoffAmount)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', null);
            // })
            ->where(function ($q) use ($intrastateSalesTaxable) {
                $q->whereHas('saleItems', function ($q) use ($intrastateSalesTaxable) {
                    $q->where('classification_nature_type', $intrastateSalesTaxable);
                })->orwhereHas('saleLedgers', function ($q) use ($intrastateSalesTaxable) {
                    $q->where('classification_nature_type', $intrastateSalesTaxable);
                });
            })
            ->get();
        foreach ($saleIntrastateSaleTaxableTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $saleReturnIntrastateSaleTaxableTra = SaleReturnTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            // ->where('grand_total', '<=', $this->B2BLB2CSCutoffAmount)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', null);
            // })
            ->where(function ($q) use ($intrastateSalesTaxable) {
                $q->whereHas('saleReturnItems', function ($q) use ($intrastateSalesTaxable) {
                    $q->where('classification_nature_type', $intrastateSalesTaxable);
                })->orwhereHas('saleReturnLedgers', function ($q) use ($intrastateSalesTaxable) {
                    $q->where('classification_nature_type', $intrastateSalesTaxable);
                });
            })
            ->get();
        foreach ($saleReturnIntrastateSaleTaxableTra as $transaction) {
            foreach ($transaction->getb2csTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $creditNoteIntrastateSaleTaxableTra = IncomeCreditNoteTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            // ->where('grand_total', '<=', $this->B2BLB2CSCutoffAmount)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', null);
            // })
            ->where(function ($q) use ($intrastateSalesTaxable) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($intrastateSalesTaxable) {
                    $q->where('classification_nature_type', $intrastateSalesTaxable);
                })->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($intrastateSalesTaxable) {
                    $q->where('classification_nature_type', $intrastateSalesTaxable);
                });
            })
            ->get();
        foreach ($creditNoteIntrastateSaleTaxableTra as $transaction) {
            foreach ($transaction->getb2csTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $debitNoteIntrastateSaleTaxableTra = IncomeDebitNoteTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            // ->where('grand_total', '<=', $this->B2BLB2CSCutoffAmount)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', null);
            // })
            ->where(function ($q) use ($intrastateSalesTaxable) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($intrastateSalesTaxable) {
                    $q->where('classification_nature_type', $intrastateSalesTaxable);
                })->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($intrastateSalesTaxable) {
                    $q->where('classification_nature_type', $intrastateSalesTaxable);
                });
            })
            ->get();
        foreach ($debitNoteIntrastateSaleTaxableTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $interstateSalesTaxable = $this->interstateSalesTaxable;
        $saleInterstateSalesTaxable = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->where('grand_total', '<=', $this->B2BLB2CSCutoffAmount)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', null);
            // })
            ->where(function ($q) use ($interstateSalesTaxable) {
                $q->whereHas('saleItems', function ($q) use ($interstateSalesTaxable) {
                    $q->where('classification_nature_type', $interstateSalesTaxable);
                })->orwhereHas('saleLedgers', function ($q) use ($interstateSalesTaxable) {
                    $q->where('classification_nature_type', $interstateSalesTaxable);
                });
            })
            ->get();
        foreach ($saleInterstateSalesTaxable as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $saleReturnInterstateSalesTaxable = SaleReturnTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->where('grand_total', '<=', $this->B2BLB2CSCutoffAmount)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', null);
            // })
            ->where(function ($q) use ($interstateSalesTaxable) {
                $q->whereHas('saleReturnItems', function ($q) use ($interstateSalesTaxable) {
                    $q->where('classification_nature_type', $interstateSalesTaxable);
                })->orwhereHas('saleReturnLedgers', function ($q) use ($interstateSalesTaxable) {
                    $q->where('classification_nature_type', $interstateSalesTaxable);
                });
            })
            ->get();
        foreach ($saleReturnInterstateSalesTaxable as $transaction) {
            foreach ($transaction->getb2csTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $debitNoteInterstateSalesTaxable = IncomeDebitNoteTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->where('grand_total', '<=', $this->B2BLB2CSCutoffAmount)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', null);
            // })
            ->where(function ($q) use ($interstateSalesTaxable) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($interstateSalesTaxable) {
                    $q->where('classification_nature_type', $interstateSalesTaxable);
                })->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($interstateSalesTaxable) {
                    $q->where('classification_nature_type', $interstateSalesTaxable);
                });
            })->get();
        foreach ($debitNoteInterstateSalesTaxable as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $creditNoteNoteInterstateSalesTaxable = IncomeCreditNoteTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->where('grand_total', '<=', $this->B2BLB2CSCutoffAmount)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', null);
            // })
            ->where(function ($q) use ($interstateSalesTaxable) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($interstateSalesTaxable) {
                    $q->where('classification_nature_type', $interstateSalesTaxable);
                })->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($interstateSalesTaxable) {
                    $q->where('classification_nature_type', $interstateSalesTaxable);
                });
            })
            ->get();
        foreach ($creditNoteNoteInterstateSalesTaxable as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        if ($indexPage) {
            return $transactionData;
        }

        $prepareStateWiseArray = [];
        foreach ($transactionData as $data) {
            if (isset($prepareStateWiseArray[$data['place_of_supplier']][$data['gst_rate']])) {
                $prepareStateWiseArray[$data['place_of_supplier']][$data['gst_rate']]['gst'] = $data['gst'];
                $prepareStateWiseArray[$data['place_of_supplier']][$data['gst_rate']]['name'] = $data['name'];
                $prepareStateWiseArray[$data['place_of_supplier']][$data['gst_rate']]['invoice_number'] = $data['invoice_number'];
                $prepareStateWiseArray[$data['place_of_supplier']][$data['gst_rate']]['invoice_date'] = $data['invoice_date'];
                $prepareStateWiseArray[$data['place_of_supplier']][$data['gst_rate']]['invoice_amount'] += $data['invoice_amount'];
                $prepareStateWiseArray[$data['place_of_supplier']][$data['gst_rate']]['place_of_supplier'] = $data['place_of_supplier'];
                $prepareStateWiseArray[$data['place_of_supplier']][$data['gst_rate']]['rcm'] = $data['rcm'];
                $prepareStateWiseArray[$data['place_of_supplier']][$data['gst_rate']]['invoice_type'] = $data['invoice_type'];
                $prepareStateWiseArray[$data['place_of_supplier']][$data['gst_rate']]['e_commerce_gstin'] = $data['e_commerce_gstin'];
                $prepareStateWiseArray[$data['place_of_supplier']][$data['gst_rate']]['gst_rate'] = $data['gst_rate'];
                $prepareStateWiseArray[$data['place_of_supplier']][$data['gst_rate']]['taxable_value'] += $data['taxable_value'];
                $prepareStateWiseArray[$data['place_of_supplier']][$data['gst_rate']]['cess'] += $data['cess'];
                $prepareStateWiseArray[$data['place_of_supplier']][$data['gst_rate']]['igst'] += $data['igst'];
                $prepareStateWiseArray[$data['place_of_supplier']][$data['gst_rate']]['cgst'] += $data['cgst'];
                $prepareStateWiseArray[$data['place_of_supplier']][$data['gst_rate']]['sgst'] += $data['sgst'];
            } else {
                $prepareStateWiseArray[$data['place_of_supplier']][$data['gst_rate']] = [
                    'gst' => $data['gst'],
                    'name' => $data['name'],
                    'invoice_number' => $data['invoice_number'],
                    'invoice_date' => $data['invoice_date'],
                    'invoice_amount' => $data['invoice_amount'],
                    'place_of_supplier' => $data['place_of_supplier'],
                    'rcm' => $data['rcm'],
                    'invoice_type' => $data['invoice_type'],
                    'e_commerce_gstin' => $data['e_commerce_gstin'],
                    'gst_rate' => $data['gst_rate'],
                    'taxable_value' => $data['taxable_value'],
                    'cess' => $data['cess'],
                    'cgst' => $data['cgst'],
                    'sgst' => $data['sgst'],
                    'igst' => $data['igst'],
                ];
            }
        }
        $newData = [];

        foreach ($prepareStateWiseArray as $stateWiseData) {
            foreach ($stateWiseData as $data) {
                $newData[] = $data;
            }
        }
        usort($newData, function ($a, $b) {
            return strtotime($a['invoice_date']) - strtotime($b['invoice_date']);
        });

        return $newData;
    }

    public function getCdnrTransactionData($startDate, $endDate): array
    {
        $transactionData = [];
        //intrastateSalesTaxable type
        $intrastateSalesTaxable = $this->intrastateSalesTaxable;
        $saleReturnIntrastateSaleTaxableTra = SaleReturnTransaction::with(['saleReturnItems', 'saleReturnLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($intrastateSalesTaxable) {
                $q->whereHas('saleReturnItems', function ($q) use ($intrastateSalesTaxable) {
                    $q->where('classification_nature_type', $intrastateSalesTaxable);
                })->orwhereHas('saleReturnLedgers', function ($q) use ($intrastateSalesTaxable) {
                    $q->where('classification_nature_type', $intrastateSalesTaxable);
                });
            })
            ->get();
        foreach ($saleReturnIntrastateSaleTaxableTra as $transaction) {
            $getData = $transaction->getb2bSezdeTransactionsData();
            foreach ($getData as $data) {
                $transactionData[] = $data;
            }
        }
        $creditNoteIntrastateSaleTaxableTra = IncomeCreditNoteTransaction::with(['incomeCreditNoteItems', 'incomeCreditNoteLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($intrastateSalesTaxable) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($intrastateSalesTaxable) {
                    $q->where('classification_nature_type', $intrastateSalesTaxable);
                })->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($intrastateSalesTaxable) {
                    $q->where('classification_nature_type', $intrastateSalesTaxable);
                });
            })
            ->get();
        foreach ($creditNoteIntrastateSaleTaxableTra as $transaction) {
            $getData = $transaction->getb2bSezdeTransactionsData();
            foreach ($getData as $data) {
                $transactionData[] = $data;
            }
        }
        $debitNoteIntrastateSaleTaxableTra = IncomeDebitNoteTransaction::with(['incomeDebitNoteItems', 'incomeDebitNoteLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($intrastateSalesTaxable) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($intrastateSalesTaxable) {
                    $q->where('classification_nature_type', $intrastateSalesTaxable);
                })->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($intrastateSalesTaxable) {
                    $q->where('classification_nature_type', $intrastateSalesTaxable);
                });
            })
            ->get();
        foreach ($debitNoteIntrastateSaleTaxableTra as $transaction) {
            $getData = $transaction->getb2bSezdeTransactionsData();
            foreach ($getData as $data) {
                $transactionData[] = $data;
            }
        }

        $intrastateSalesExempt = $this->intrastateSalesExempt;
        //Intrastate Sales Exempt
        $saleRetrunIntrastateTra = SaleReturnTransaction::with(['saleReturnItems', 'saleReturnLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($intrastateSalesExempt) {
                $q->whereHas('saleReturnItems', function ($q) use ($intrastateSalesExempt) {
                    $q->where('classification_nature_type', $intrastateSalesExempt)
                        ->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($intrastateSalesExempt) {
                        $q->where('classification_nature_type', $intrastateSalesExempt)
                            ->where('classification_is_rcm_applicable', false);
                    });
            })
            ->get();
        foreach ($saleRetrunIntrastateTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $creditNoteIntrastateTra = IncomeCreditNoteTransaction::with(['incomeCreditNoteItems', 'incomeCreditNoteLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($intrastateSalesExempt) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($intrastateSalesExempt) {
                    $q->where('classification_nature_type', $intrastateSalesExempt)
                        ->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($intrastateSalesExempt) {
                        $q->where('classification_nature_type', $intrastateSalesExempt)
                            ->where('classification_is_rcm_applicable', false);
                    });
            })
            ->get();
        foreach ($creditNoteIntrastateTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $debitNoteIntrastateTra = IncomeDebitNoteTransaction::with(['incomeDebitNoteItems', 'incomeDebitNoteLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($intrastateSalesExempt) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($intrastateSalesExempt) {
                    $q->where('classification_nature_type', $intrastateSalesExempt)
                        ->where('classification_is_rcm_applicable', false);
                })->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($intrastateSalesExempt) {
                    $q->where('classification_nature_type', $intrastateSalesExempt)
                        ->where('classification_is_rcm_applicable', false);
                });
            })
            ->get();
        foreach ($debitNoteIntrastateTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $intrastateSalesNilRated = $this->intrastateSalesNilRated;
        //Intrastate Sales Nil Rated
        $saleReturnIntrastateNilRatedTra = SaleReturnTransaction::with(['saleReturnItems', 'saleReturnLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($intrastateSalesNilRated) {
                $q->whereHas('saleReturnItems', function ($q) use ($intrastateSalesNilRated) {
                    $q->where('classification_nature_type', $intrastateSalesNilRated)
                        ->where('classification_is_rcm_applicable', false);
                })->orwhereHas('saleReturnLedgers', function ($q) use ($intrastateSalesNilRated) {
                    $q->where('classification_nature_type', $intrastateSalesNilRated)
                        ->where('classification_is_rcm_applicable', false);
                });
            })->get();
        foreach ($saleReturnIntrastateNilRatedTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $creditNoteIntrastateNilRatedTra = IncomeCreditNoteTransaction::with(['incomeCreditNoteItems', 'incomeCreditNoteLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($intrastateSalesNilRated) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($intrastateSalesNilRated) {
                    $q->where('classification_nature_type', $intrastateSalesNilRated)
                        ->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($intrastateSalesNilRated) {
                        $q->where('classification_nature_type', $intrastateSalesNilRated)
                            ->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($creditNoteIntrastateNilRatedTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $debitNoteIntrastateNilRatedTra = IncomeDebitNoteTransaction::with(['incomeDebitNoteItems', 'incomeDebitNoteLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($intrastateSalesNilRated) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($intrastateSalesNilRated) {
                    $q->where('classification_nature_type', $intrastateSalesNilRated)
                        ->where('classification_is_rcm_applicable', false);
                })->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($intrastateSalesNilRated) {
                    $q->where('classification_nature_type', $intrastateSalesNilRated)
                        ->where('classification_is_rcm_applicable', false);
                });
            })->get();
        foreach ($debitNoteIntrastateNilRatedTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $interstateSalesTaxable = $this->interstateSalesTaxable;
        //Interstate Sales Taxable
        $saleReturnInterstateTaxableTra = SaleReturnTransaction::with(['saleReturnItems', 'saleReturnLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($interstateSalesTaxable) {
                $q->whereHas('saleReturnItems', function ($q) use ($interstateSalesTaxable) {
                    $q->where('classification_nature_type', $interstateSalesTaxable);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($interstateSalesTaxable) {
                        $q->where('classification_nature_type', $interstateSalesTaxable);
                    });
            })->get();
        foreach ($saleReturnInterstateTaxableTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $creditNoteInterstateTaxableTra = IncomeCreditNoteTransaction::with(['incomeCreditNoteItems', 'incomeCreditNoteLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($interstateSalesTaxable) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($interstateSalesTaxable) {
                    $q->where('classification_nature_type', $interstateSalesTaxable);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($interstateSalesTaxable) {
                        $q->where('classification_nature_type', $interstateSalesTaxable);
                    });
            })->get();
        foreach ($creditNoteInterstateTaxableTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $debitNoteInterstateTaxableTra = IncomeDebitNoteTransaction::with(['incomeDebitNoteItems', 'incomeDebitNoteLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($interstateSalesTaxable) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($interstateSalesTaxable) {
                    $q->where('classification_nature_type', $interstateSalesTaxable);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($interstateSalesTaxable) {
                        $q->where('classification_nature_type', $interstateSalesTaxable);
                    });
            })
            ->get();
        foreach ($debitNoteInterstateTaxableTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $interstateSalesExempt = $this->interstateSalesExempt;
        //Interstate Sales Exempt
        $saleReturnInterstateExempt = SaleReturnTransaction::with(['saleReturnItems', 'saleReturnLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($interstateSalesExempt) {
                $q->whereHas('saleReturnItems', function ($q) use ($interstateSalesExempt) {
                    $q->where('classification_nature_type', $interstateSalesExempt)
                        ->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($interstateSalesExempt) {
                        $q->where('classification_nature_type', $interstateSalesExempt)
                            ->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($saleReturnInterstateExempt as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $creditNoteInterstateExempt = IncomeCreditNoteTransaction::with(['incomeCreditNoteItems', 'incomeCreditNoteLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($interstateSalesExempt) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($interstateSalesExempt) {
                    $q->where('classification_nature_type', $interstateSalesExempt)
                        ->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($interstateSalesExempt) {
                        $q->where('classification_nature_type', $interstateSalesExempt)
                            ->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($creditNoteInterstateExempt as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $debitNoteInterstateExempt = IncomeDebitNoteTransaction::with(['incomeDebitNoteItems', 'incomeDebitNoteLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($interstateSalesExempt) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($interstateSalesExempt) {
                    $q->where('classification_nature_type', $interstateSalesExempt)
                        ->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($interstateSalesExempt) {
                        $q->where('classification_nature_type', $interstateSalesExempt)
                            ->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($debitNoteInterstateExempt as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $interstateSalesNilRated = $this->interstateSalesNilRated;
        //Interstate Sales Nil Rated
        $saleRetrunInterStateNilRated = SaleReturnTransaction::with(['saleReturnItems', 'saleReturnLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($interstateSalesNilRated) {
                $q->whereHas('saleReturnItems', function ($q) use ($interstateSalesNilRated) {
                    $q->where('classification_nature_type', $interstateSalesNilRated)
                        ->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($interstateSalesNilRated) {
                        $q->where('classification_nature_type', $interstateSalesNilRated)
                            ->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($saleRetrunInterStateNilRated as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $creditNoteInterStateNilRated = IncomeCreditNoteTransaction::with(['incomeCreditNoteItems', 'incomeCreditNoteLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($interstateSalesNilRated) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($interstateSalesNilRated) {
                    $q->where('classification_nature_type', $interstateSalesNilRated)
                        ->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($interstateSalesNilRated) {
                        $q->where('classification_nature_type', $interstateSalesNilRated)
                            ->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($creditNoteInterStateNilRated as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $debitNoteInterStateNilRated = IncomeDebitNoteTransaction::with(['incomeDebitNoteItems', 'incomeDebitNoteLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($interstateSalesNilRated) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($interstateSalesNilRated) {
                    $q->where('classification_nature_type', $interstateSalesNilRated)
                        ->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($interstateSalesNilRated) {
                        $q->where('classification_nature_type', $interstateSalesNilRated)
                            ->where('classification_is_rcm_applicable', false);
                    });
            })
            ->get();
        foreach ($debitNoteInterStateNilRated as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $taxes = [
            $this->salestoSEZTaxable,
            $this->salestoSEZExempt,
            $this->salestoSEZNilRated,
            $this->salestoSEZunderLUT,
            $this->deemedExportIntrastate,
            $this->deemedExportInterstate,
        ];
        //11,12,13,14,15,16 taxes types
        $saleReturnOtherTaxTra = SaleReturnTransaction::with(['saleReturnItems', 'saleReturnLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($taxes) {
                $q->whereHas('saleReturnItems', function ($q) use ($taxes) {
                    $q->whereIn('classification_nature_type', $taxes);
                })->orwhereHas('saleReturnLedgers', function ($q) use ($taxes) {
                    $q->whereIn('classification_nature_type', $taxes);
                });
            })
            ->get();
        foreach ($saleReturnOtherTaxTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $creditNoteOtherTaxTra = IncomeCreditNoteTransaction::with(['incomeCreditNoteItems', 'incomeCreditNoteLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($taxes) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($taxes) {
                    $q->whereIn('classification_nature_type', $taxes);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($taxes) {
                        $q->whereIn('classification_nature_type', $taxes);
                    });
            })
            ->get();
        foreach ($creditNoteOtherTaxTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $debitNoteOtherTaxTra = IncomeDebitNoteTransaction::with(['incomeDebitNoteItems', 'incomeDebitNoteLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($taxes) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($taxes) {
                    $q->whereIn('classification_nature_type', $taxes);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($taxes) {
                        $q->whereIn('classification_nature_type', $taxes);
                    });
            })
            ->get();
        foreach ($debitNoteOtherTaxTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        usort($transactionData, function ($a, $b) {
            return strtotime($a['invoice_date']) - strtotime($b['invoice_date']);
        });

        return $transactionData;
    }

    public function getCdnurTransactionData($startDate, $endDate): array
    {
        $transactionData = [];
        //interstateSalesTaxable type
        $grandTotalValueDate = '2024-08-01';
        $interstateSalesTaxable = $this->interstateSalesTaxable;
        $saleReturnIntrastateSaleTaxableTra = SaleReturnTransaction::with(['saleReturnItems', 'saleReturnLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', null);
            // })
            ->where(function ($q) use ($grandTotalValueDate) {
                $q->where(function ($query) use ($grandTotalValueDate) {
                    $query->where('date', '<', $grandTotalValueDate)->where('grand_total', '>=', 250000);
                })->orWhere(function ($query) use ($grandTotalValueDate) {
                    $query->where('date', '>=', $grandTotalValueDate)->where('grand_total', '>=', $this->B2BLB2CSCutoffAmount);
                });
            })
            ->where(function ($q) use ($interstateSalesTaxable) {
                $q->whereHas('saleReturnItems', function ($query) use ($interstateSalesTaxable) {
                    $query->where('classification_nature_type', $interstateSalesTaxable);
                })->orWhereHas('saleReturnLedgers', function ($query) use ($interstateSalesTaxable) {
                    $query->where('classification_nature_type', $interstateSalesTaxable);
                });
            })
            ->get();

        foreach ($saleReturnIntrastateSaleTaxableTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $debitNoteIntrastateSaleTaxableTra = IncomeDebitNoteTransaction::with(['incomeDebitNoteItems', 'incomeDebitNoteLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', null);
            // })
            ->where(function ($q) use ($grandTotalValueDate) {
                $q->where(function ($query) use ($grandTotalValueDate) {
                    $query->where('date', '<', $grandTotalValueDate)->where('grand_total', '>=', 250000);
                })->orWhere(function ($query) use ($grandTotalValueDate) {
                    $query->where('date', '>=', $grandTotalValueDate)->where('grand_total', '>=', $this->B2BLB2CSCutoffAmount);
                });
            })
            ->where(function ($q) use ($interstateSalesTaxable) {
                $q->whereHas('incomeDebitNoteItems', function ($query) use ($interstateSalesTaxable) {
                    $query->where('classification_nature_type', $interstateSalesTaxable);
                })->orWhereHas('incomeDebitNoteLedgers', function ($query) use ($interstateSalesTaxable) {
                    $query->where('classification_nature_type', $interstateSalesTaxable);
                });
            })
            ->get();
        foreach ($debitNoteIntrastateSaleTaxableTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $creditNoteIntrastateSaleTaxableTra = IncomeCreditNoteTransaction::with(['incomeCreditNoteItems', 'incomeCreditNoteLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            ->where(function ($q) use ($grandTotalValueDate) {
                $q->where(function ($query) use ($grandTotalValueDate) {
                    $query->where('date', '<', $grandTotalValueDate)->where('grand_total', '>=', 250000);
                })->orWhere(function ($query) use ($grandTotalValueDate) {
                    $query->where('date', '>=', $grandTotalValueDate)->where('grand_total', '>=', $this->B2BLB2CSCutoffAmount);
                });
            })
            ->where(function ($q) use ($interstateSalesTaxable) {
                $q->whereHas('incomeCreditNoteItems', function ($query) use ($interstateSalesTaxable) {
                    $query->where('classification_nature_type', $interstateSalesTaxable);
                })->orWhereHas('incomeCreditNoteLedgers', function ($query) use ($interstateSalesTaxable) {
                    $query->where('classification_nature_type', $interstateSalesTaxable);
                });
            })
            ->get();
        foreach ($creditNoteIntrastateSaleTaxableTra as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $taxType = [
            $this->exportSalesTaxable, $this->exportSalesExempt, $this->exportSalesNilRated, $this->exportSalesunderLUT,
        ];
        $saleReturnTransactions = SaleReturnTransaction::with(['saleReturnItems', 'saleReturnLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereHas('saleReturnItems', function ($q) use ($taxType) {
                $q->whereIn('classification_nature_type', $taxType);
            })->orwhereHas('saleReturnLedgers', function ($q) use ($taxType) {
                $q->whereIn('classification_nature_type', $taxType);
            })->get();
        foreach ($saleReturnTransactions as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $debitNoteTransactions = IncomeDebitNoteTransaction::with(['incomeDebitNoteItems', 'incomeDebitNoteLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereHas('incomeDebitNoteItems', function ($q) use ($taxType) {
                $q->whereIn('classification_nature_type', $taxType);
            })->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($taxType) {
                $q->whereIn('classification_nature_type', $taxType);
            })->get();
        foreach ($debitNoteTransactions as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $creditNoteTransactions = IncomeCreditNoteTransaction::with(['incomeCreditNoteItems', 'incomeCreditNoteLedgers'])->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereHas('incomeCreditNoteItems', function ($q) use ($taxType) {
                $q->whereIn('classification_nature_type', $taxType);
            })->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($taxType) {
                $q->whereIn('classification_nature_type', $taxType);
            })->get();
        foreach ($creditNoteTransactions as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        usort($transactionData, function ($a, $b) {
            return strtotime($a['invoice_date']) - strtotime($b['invoice_date']);
        });

        return $transactionData;
    }

    public function getExpTransactionData($startDate, $endDate): array
    {
        $transactionData = [];
        $taxType = [
            $this->exportSalesTaxable, $this->exportSalesExempt, $this->exportSalesNilRated, $this->exportSalesunderLUT,
        ];

        $saleTransactions = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->where(function ($query) use ($taxType) {
                $query->whereHas('saleItems', function ($q) use ($taxType) {
                    $q->whereIn('classification_nature_type', $taxType);
                })
                    ->orWhereHas('saleLedgers', function ($q) use ($taxType) {
                        $q->whereIn('classification_nature_type', $taxType);
                    });
            })->get();

        foreach ($saleTransactions as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        return $transactionData;
    }

    public function getDocsTransactionData($startDate, $endDate): array
    {
        $data = [];
        $saleTransactions = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->get(['invoice_number', 'full_invoice_number'])
            // ->sortBy('invoice_number')
            ->pluck('full_invoice_number')
            ->toArray();

        $saleReturnTransactions = SaleReturnTransaction::whereBetween('date', [$startDate, $endDate])
            ->get(['credit_note_number', 'full_invoice_number'])
            // ->sortBy('credit_note_number')
            ->pluck('full_invoice_number')
            ->toArray();

        $incomeCreditTransactions = IncomeCreditNoteTransaction::whereBetween('date', [$startDate, $endDate])
            ->get(['credit_note_number', 'full_invoice_number'])
            // ->sortBy('credit_note_number')
            ->pluck('full_invoice_number')
            ->toArray();

        $incomeDebitTransactions = IncomeDebitNoteTransaction::whereBetween('date', [$startDate, $endDate])
            ->get(['debit_note_number', 'full_invoice_number'])
            // ->sortBy('debit_note_number')
            ->pluck('full_invoice_number')
            ->toArray();

        $data['invoice_for_outward_supply'] = getInvoiceSeriesCountArr($saleTransactions);
        $data['sale_credit_note'] = getInvoiceSeriesCountArr($incomeCreditTransactions);
        $data['income_credit_note'] = getInvoiceSeriesCountArr($saleReturnTransactions);
        $data['income_debit_note'] = getInvoiceSeriesCountArr($incomeDebitTransactions);

        return $data;
    }

    public function getExempTransactionDataExcelSheet($startDate, $endDate): array
    {
        $transactionData = [];
        //Intrastate Sales Exempt, Intrastate Sales Nil Rated tax type
        $intraStateNilRated = $this->intrastateSalesNilRated;
        $intraStateNilRatedSaleAmount = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($intraStateNilRated) {
                $q->whereHas('saleItems', function ($q) use ($intraStateNilRated) {
                    $q->where(
                        'classification_nature_type',
                        $intraStateNilRated
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($intraStateNilRated) {
                        $q->where(
                            'classification_nature_type',
                            $intraStateNilRated
                        )->where('classification_is_rcm_applicable', false);
                    });
            })
            ->sum('grand_total');
        $intraStateNilRatedDebitNoteAmount = IncomeDebitNoteTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($intraStateNilRated) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($intraStateNilRated) {
                    $q->where(
                        'classification_nature_type',
                        $intraStateNilRated
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($intraStateNilRated) {
                        $q->where(
                            'classification_nature_type',
                            $intraStateNilRated
                        )->where('classification_is_rcm_applicable', false);
                    });
            })
            ->sum('grand_total');
        $intraStateNilRatedSaleReturnAmount = SaleReturnTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($intraStateNilRated) {
                $q->whereHas('saleReturnItems', function ($q) use ($intraStateNilRated) {
                    $q->where(
                        'classification_nature_type',
                        $intraStateNilRated
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($intraStateNilRated) {
                        $q->where(
                            'classification_nature_type',
                            $intraStateNilRated
                        )->where('classification_is_rcm_applicable', false);
                    });
            })
            ->sum('grand_total');
        $intraStateNilRatedCreditNoteAmount = IncomeCreditNoteTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($intraStateNilRated) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($intraStateNilRated) {
                    $q->where(
                        'classification_nature_type',
                        $intraStateNilRated
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($intraStateNilRated) {
                        $q->where(
                            'classification_nature_type',
                            $intraStateNilRated
                        )->where('classification_is_rcm_applicable', false);
                    });
            })
            ->sum('grand_total');
        $transactionData['intraStateNilRatedB_4'] = $intraStateNilRatedSaleAmount + $intraStateNilRatedDebitNoteAmount - $intraStateNilRatedSaleReturnAmount - $intraStateNilRatedCreditNoteAmount;

        $intraStateExempt = $this->intrastateSalesExempt;
        $intraStateExemptedRatedSaleAmount = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($intraStateExempt) {
                $q->whereHas('saleItems', function ($q) use ($intraStateExempt) {
                    $q->where(
                        'classification_nature_type',
                        $intraStateExempt
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($intraStateExempt) {
                        $q->where(
                            'classification_nature_type',
                            $intraStateExempt
                        )->where('classification_is_rcm_applicable', false);
                    });
            })
            ->sum('grand_total');
        $intraStateExemptedRatedDebitNoteAmount = IncomeDebitNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($intraStateExempt) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($intraStateExempt) {
                    $q->where(
                        'classification_nature_type',
                        $intraStateExempt
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($intraStateExempt) {
                        $q->where(
                            'classification_nature_type',
                            $intraStateExempt
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');
        $intraStateExemptedRatedSaleReturnAmount = SaleReturnTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($intraStateExempt) {
                $q->whereHas('saleReturnItems', function ($q) use ($intraStateExempt) {
                    $q->where(
                        'classification_nature_type',
                        $intraStateExempt
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($intraStateExempt) {
                        $q->where(
                            'classification_nature_type',
                            $intraStateExempt
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');
        $intraStateExemptedRatedCreditNoteAmount = IncomeCreditNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($intraStateExempt) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($intraStateExempt) {
                    $q->where(
                        'classification_nature_type',
                        $intraStateExempt
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($intraStateExempt) {
                        $q->where(
                            'classification_nature_type',
                            $intraStateExempt
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');
        $transactionData['intraStateExemptedC_4'] = $intraStateExemptedRatedSaleAmount + $intraStateExemptedRatedDebitNoteAmount - $intraStateExemptedRatedSaleReturnAmount - $intraStateExemptedRatedCreditNoteAmount;

        $interStateExempt = $this->interstateSalesExempt;
        $interStateExemptedRatedSaleAmount = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($interStateExempt) {
                $q->whereHas('saleItems', function ($q) use ($interStateExempt) {
                    $q->where(
                        'classification_nature_type',
                        $interStateExempt
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($interStateExempt) {
                        $q->where(
                            'classification_nature_type',
                            $interStateExempt
                        )->where('classification_is_rcm_applicable', false);
                    });
            })
            ->sum('grand_total');
        $interStateExemptedRatedDebitNoteAmount = IncomeDebitNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($interStateExempt) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($interStateExempt) {
                    $q->where(
                        'classification_nature_type',
                        $interStateExempt
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($interStateExempt) {
                        $q->where(
                            'classification_nature_type',
                            $interStateExempt
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');
        $interStateExemptedRatedSaleReturnAmount = SaleReturnTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($interStateExempt) {
                $q->whereHas('saleReturnItems', function ($q) use ($interStateExempt) {
                    $q->where(
                        'classification_nature_type',
                        $interStateExempt
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($interStateExempt) {
                        $q->where(
                            'classification_nature_type',
                            $interStateExempt
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');
        $interStateExemptedRatedCreditNoteAmount = IncomeCreditNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($interStateExempt) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($interStateExempt) {
                    $q->where(
                        'classification_nature_type',
                        $interStateExempt
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($interStateExempt) {
                        $q->where(
                            'classification_nature_type',
                            $interStateExempt
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');
        $transactionData['interStateExemptedC_3'] = $interStateExemptedRatedSaleAmount + $interStateExemptedRatedDebitNoteAmount - $interStateExemptedRatedSaleReturnAmount - $interStateExemptedRatedCreditNoteAmount;

        $interStateNilRated = $this->interstateSalesNilRated;
        $interStateNilRatedSaleAmount = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($interStateNilRated) {
                $q->whereHas('saleItems', function ($q) use ($interStateNilRated) {
                    $q->where(
                        'classification_nature_type',
                        $interStateNilRated
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($interStateNilRated) {
                        $q->where(
                            'classification_nature_type',
                            $interStateNilRated
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');
        $interStateNilRatedDebitNoteAmount = IncomeDebitNoteTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($interStateNilRated) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($interStateNilRated) {
                    $q->where(
                        'classification_nature_type',
                        $interStateNilRated
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($interStateNilRated) {
                        $q->where(
                            'classification_nature_type',
                            $interStateNilRated
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');
        $interStateNilRatedSaleReturnAmount = SaleReturnTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($interStateNilRated) {
                $q->whereHas('saleReturnItems', function ($q) use ($interStateNilRated) {
                    $q->where(
                        'classification_nature_type',
                        $interStateNilRated
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($interStateNilRated) {
                        $q->where(
                            'classification_nature_type',
                            $interStateNilRated
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');
        $interStateNilRatedCreditNoteAmount = IncomeCreditNoteTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($interStateNilRated) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($interStateNilRated) {
                    $q->where(
                        'classification_nature_type',
                        $interStateNilRated
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($interStateNilRated) {
                        $q->where(
                            'classification_nature_type',
                            $interStateNilRated
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');
        $transactionData['interStateNilRatedB_3'] = $interStateNilRatedSaleAmount + $interStateNilRatedDebitNoteAmount - $interStateNilRatedSaleReturnAmount - $interStateNilRatedCreditNoteAmount;

        $nonGstIntraStateTax = $this->nonGSTSupplyIntraState;
        $nonGstIntraStateTaxB2BSaleAmount = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($nonGstIntraStateTax) {
                $q->whereHas('saleItems', function ($q) use ($nonGstIntraStateTax) {
                    $q->where('classification_nature_type', $nonGstIntraStateTax);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($nonGstIntraStateTax) {
                        $q->where('classification_nature_type', $nonGstIntraStateTax);
                    });
            })->sum('grand_total');

        $nonGstIntraStateTaxB2BDebitNoteAmount = IncomeDebitNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($nonGstIntraStateTax) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($nonGstIntraStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstIntraStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($nonGstIntraStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstIntraStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');

        $nonGstIntraStateTaxB2BCreditNoteAmount = IncomeCreditNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($nonGstIntraStateTax) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($nonGstIntraStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstIntraStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($nonGstIntraStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstIntraStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');

        $nonGstIntraStateTaxB2BSaleReturnAmount = SaleReturnTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($nonGstIntraStateTax) {
                $q->whereHas('saleReturnItems', function ($q) use ($nonGstIntraStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstIntraStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($nonGstIntraStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstIntraStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');

        $transactionData['noneGstIntraStateB2BD_2'] = $nonGstIntraStateTaxB2BSaleAmount + $nonGstIntraStateTaxB2BDebitNoteAmount - $nonGstIntraStateTaxB2BCreditNoteAmount - $nonGstIntraStateTaxB2BSaleReturnAmount;

        $nonGstIntraStateTaxB2CSaleAmount = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($nonGstIntraStateTax) {
                $q->whereHas('saleItems', function ($q) use ($nonGstIntraStateTax) {
                    $q->where('classification_nature_type', $nonGstIntraStateTax);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($nonGstIntraStateTax) {
                        $q->where('classification_nature_type', $nonGstIntraStateTax);
                    });
            })->sum('grand_total');

        $nonGstIntraStateTaxB2CDebitNoteAmount = IncomeDebitNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($nonGstIntraStateTax) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($nonGstIntraStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstIntraStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($nonGstIntraStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstIntraStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');

        $nonGstIntraStateTaxB2CCreditNoteAmount = IncomeCreditNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($nonGstIntraStateTax) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($nonGstIntraStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstIntraStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($nonGstIntraStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstIntraStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');

        $nonGstIntraStateTaxB2CSaleReturnAmount = SaleReturnTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($nonGstIntraStateTax) {
                $q->whereHas('saleReturnItems', function ($q) use ($nonGstIntraStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstIntraStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($nonGstIntraStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstIntraStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');
        $transactionData['noneGstIntraStateB2CD_4'] = $nonGstIntraStateTaxB2CSaleAmount + $nonGstIntraStateTaxB2CDebitNoteAmount - $nonGstIntraStateTaxB2CCreditNoteAmount - $nonGstIntraStateTaxB2CSaleReturnAmount;

        $nonGstInterStateTax = $this->nonGSTSupplyInterState;
        $nonGstInterStateTaxB2BSaleAmount = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($nonGstInterStateTax) {
                $q->whereHas('saleItems', function ($q) use ($nonGstInterStateTax) {
                    $q->where('classification_nature_type', $nonGstInterStateTax);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($nonGstInterStateTax) {
                        $q->where('classification_nature_type', $nonGstInterStateTax);
                    });
            })->sum('grand_total');

        $nonGstInterStateTaxB2BDebitNoteAmount = IncomeDebitNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($nonGstInterStateTax) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($nonGstInterStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstInterStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($nonGstInterStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstInterStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })
            ->sum('grand_total');

        $nonGstInterStateTaxB2BCreditNoteAmount = IncomeCreditNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($nonGstInterStateTax) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($nonGstInterStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstInterStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($nonGstInterStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstInterStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');

        $nonGstInterStateTaxB2BSaleReturnAmount = SaleReturnTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($nonGstInterStateTax) {
                $q->whereHas('saleReturnItems', function ($q) use ($nonGstInterStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstInterStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($nonGstInterStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstInterStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');

        $transactionData['noneGstInterStateB2BD_1'] = $nonGstInterStateTaxB2BSaleAmount + $nonGstInterStateTaxB2BDebitNoteAmount - $nonGstInterStateTaxB2BCreditNoteAmount - $nonGstInterStateTaxB2BSaleReturnAmount;

        $nonGstInterStateTax = $this->nonGSTSupplyInterState;
        $nonGstInterStateTaxB2CSaleAmount = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($nonGstInterStateTax) {
                $q->whereHas('saleItems', function ($q) use ($nonGstInterStateTax) {
                    $q->where('classification_nature_type', $nonGstInterStateTax);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($nonGstInterStateTax) {
                        $q->where('classification_nature_type', $nonGstInterStateTax);
                    });
            })->sum('grand_total');

        $nonGstInterStateTaxB2CDebitNoteAmount = IncomeDebitNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($nonGstInterStateTax) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($nonGstInterStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstInterStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($nonGstInterStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstInterStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');

        $nonGstInterStateTaxB2CCreditNoteAmount = IncomeCreditNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($nonGstInterStateTax) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($nonGstInterStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstInterStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($nonGstInterStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstInterStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');

        $nonGstInterStateTaxB2CSaleReturnAmount = SaleReturnTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($nonGstInterStateTax) {
                $q->whereHas('saleReturnItems', function ($q) use ($nonGstInterStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstInterStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($nonGstInterStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstInterStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->sum('grand_total');

        $transactionData['noneGstInterStateB2CD_3'] = $nonGstInterStateTaxB2CSaleAmount + $nonGstInterStateTaxB2CDebitNoteAmount - $nonGstInterStateTaxB2CCreditNoteAmount - $nonGstInterStateTaxB2CSaleReturnAmount;

        return $transactionData;
    }

    public function getExempTransactionDataIndexPage($startDate, $endDate): array
    {
        $transactionData = [];
        //Intrastate Sales Exempt, Intrastate Sales Nil Rated tax type
        $intraStateNilRated = $this->intrastateSalesNilRated;
        $intraStateNilRatedSaleAmount = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($intraStateNilRated) {
                $q->whereHas('saleItems', function ($q) use ($intraStateNilRated) {
                    $q->where(
                        'classification_nature_type',
                        $intraStateNilRated
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($intraStateNilRated) {
                        $q->where(
                            'classification_nature_type',
                            $intraStateNilRated
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($intraStateNilRatedSaleAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $intraStateNilRatedDebitNoteAmount = IncomeDebitNoteTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($intraStateNilRated) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($intraStateNilRated) {
                    $q->where(
                        'classification_nature_type',
                        $intraStateNilRated
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($intraStateNilRated) {
                        $q->where(
                            'classification_nature_type',
                            $intraStateNilRated
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($intraStateNilRatedDebitNoteAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $intraStateNilRatedSaleReturnAmount = SaleReturnTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($intraStateNilRated) {
                $q->whereHas('saleReturnItems', function ($q) use ($intraStateNilRated) {
                    $q->where(
                        'classification_nature_type',
                        $intraStateNilRated
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($intraStateNilRated) {
                        $q->where(
                            'classification_nature_type',
                            $intraStateNilRated
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($intraStateNilRatedSaleReturnAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $intraStateNilRatedCreditNoteAmount = IncomeCreditNoteTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($intraStateNilRated) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($intraStateNilRated) {
                    $q->where(
                        'classification_nature_type',
                        $intraStateNilRated
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($intraStateNilRated) {
                        $q->where(
                            'classification_nature_type',
                            $intraStateNilRated
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($intraStateNilRatedCreditNoteAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $intraStateExempt = $this->intrastateSalesExempt;
        $intraStateExemptedRatedSaleAmount = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($intraStateExempt) {
                $q->whereHas('saleItems', function ($q) use ($intraStateExempt) {
                    $q->where(
                        'classification_nature_type',
                        $intraStateExempt
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($intraStateExempt) {
                        $q->where(
                            'classification_nature_type',
                            $intraStateExempt
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($intraStateExemptedRatedSaleAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $intraStateExemptedRatedDebitNoteAmount = IncomeDebitNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($intraStateExempt) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($intraStateExempt) {
                    $q->where(
                        'classification_nature_type',
                        $intraStateExempt
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($intraStateExempt) {
                        $q->where(
                            'classification_nature_type',
                            $intraStateExempt
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($intraStateExemptedRatedDebitNoteAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $intraStateExemptedRatedSaleReturnAmount = SaleReturnTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($intraStateExempt) {
                $q->whereHas('saleReturnItems', function ($q) use ($intraStateExempt) {
                    $q->where(
                        'classification_nature_type',
                        $intraStateExempt
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($intraStateExempt) {
                        $q->where(
                            'classification_nature_type',
                            $intraStateExempt
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($intraStateExemptedRatedSaleReturnAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $intraStateExemptedRatedCreditNoteAmount = IncomeCreditNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($intraStateExempt) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($intraStateExempt) {
                    $q->where(
                        'classification_nature_type',
                        $intraStateExempt
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($intraStateExempt) {
                        $q->where(
                            'classification_nature_type',
                            $intraStateExempt
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($intraStateExemptedRatedCreditNoteAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $interStateExempt = $this->interstateSalesExempt;
        $interStateExemptedRatedSaleAmount = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($interStateExempt) {
                $q->whereHas('saleItems', function ($q) use ($interStateExempt) {
                    $q->where(
                        'classification_nature_type',
                        $interStateExempt
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($interStateExempt) {
                        $q->where(
                            'classification_nature_type',
                            $interStateExempt
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($interStateExemptedRatedSaleAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $interStateExemptedRatedDebitNoteAmount = IncomeDebitNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($interStateExempt) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($interStateExempt) {
                    $q->where(
                        'classification_nature_type',
                        $interStateExempt
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($interStateExempt) {
                        $q->where(
                            'classification_nature_type',
                            $interStateExempt
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($interStateExemptedRatedDebitNoteAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $interStateExemptedRatedSaleReturnAmount = SaleReturnTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($interStateExempt) {
                $q->whereHas('saleReturnItems', function ($q) use ($interStateExempt) {
                    $q->where(
                        'classification_nature_type',
                        $interStateExempt
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($interStateExempt) {
                        $q->where(
                            'classification_nature_type',
                            $interStateExempt
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($interStateExemptedRatedSaleReturnAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $interStateExemptedRatedCreditNoteAmount = IncomeCreditNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($interStateExempt) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($interStateExempt) {
                    $q->where(
                        'classification_nature_type',
                        $interStateExempt
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($interStateExempt) {
                        $q->where(
                            'classification_nature_type',
                            $interStateExempt
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($interStateExemptedRatedCreditNoteAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $interStateNilRated = $this->interstateSalesNilRated;
        $interStateNilRatedSaleAmount = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($interStateNilRated) {
                $q->whereHas('saleItems', function ($q) use ($interStateNilRated) {
                    $q->where(
                        'classification_nature_type',
                        $interStateNilRated
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($interStateNilRated) {
                        $q->where(
                            'classification_nature_type',
                            $interStateNilRated
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($interStateNilRatedSaleAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $interStateNilRatedDebitNoteAmount = IncomeDebitNoteTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($interStateNilRated) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($interStateNilRated) {
                    $q->where(
                        'classification_nature_type',
                        $interStateNilRated
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($interStateNilRated) {
                        $q->where(
                            'classification_nature_type',
                            $interStateNilRated
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($interStateNilRatedDebitNoteAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $interStateNilRatedSaleReturnAmount = SaleReturnTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($interStateNilRated) {
                $q->whereHas('saleReturnItems', function ($q) use ($interStateNilRated) {
                    $q->where(
                        'classification_nature_type',
                        $interStateNilRated
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($interStateNilRated) {
                        $q->where(
                            'classification_nature_type',
                            $interStateNilRated
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($interStateNilRatedSaleReturnAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $interStateNilRatedCreditNoteAmount = IncomeCreditNoteTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($interStateNilRated) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($interStateNilRated) {
                    $q->where(
                        'classification_nature_type',
                        $interStateNilRated
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($interStateNilRated) {
                        $q->where(
                            'classification_nature_type',
                            $interStateNilRated
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($interStateNilRatedCreditNoteAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $nonGstIntraStateTax = $this->nonGSTSupplyIntraState;
        $nonGstIntraStateTaxB2BSaleAmount = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($nonGstIntraStateTax) {
                $q->whereHas('saleItems', function ($q) use ($nonGstIntraStateTax) {
                    $q->where('classification_nature_type', $nonGstIntraStateTax);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($nonGstIntraStateTax) {
                        $q->where('classification_nature_type', $nonGstIntraStateTax);
                    });
            })->get();
        foreach ($nonGstIntraStateTaxB2BSaleAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $nonGstIntraStateTaxB2BDebitNoteAmount = IncomeDebitNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($nonGstIntraStateTax) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($nonGstIntraStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstIntraStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($nonGstIntraStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstIntraStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($nonGstIntraStateTaxB2BDebitNoteAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $nonGstIntraStateTaxB2BCreditNoteAmount = IncomeCreditNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($nonGstIntraStateTax) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($nonGstIntraStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstIntraStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($nonGstIntraStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstIntraStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($nonGstIntraStateTaxB2BCreditNoteAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $nonGstIntraStateTaxB2BSaleReturnAmount = SaleReturnTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($nonGstIntraStateTax) {
                $q->whereHas('saleReturnItems', function ($q) use ($nonGstIntraStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstIntraStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($nonGstIntraStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstIntraStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($nonGstIntraStateTaxB2BSaleReturnAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $nonGstIntraStateTaxB2CSaleAmount = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($nonGstIntraStateTax) {
                $q->whereHas('saleItems', function ($q) use ($nonGstIntraStateTax) {
                    $q->where('classification_nature_type', $nonGstIntraStateTax);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($nonGstIntraStateTax) {
                        $q->where('classification_nature_type', $nonGstIntraStateTax);
                    });
            })->get();
        foreach ($nonGstIntraStateTaxB2CSaleAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $nonGstIntraStateTaxB2CDebitNoteAmount = IncomeDebitNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($nonGstIntraStateTax) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($nonGstIntraStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstIntraStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($nonGstIntraStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstIntraStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($nonGstIntraStateTaxB2CDebitNoteAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $nonGstIntraStateTaxB2CCreditNoteAmount = IncomeCreditNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($nonGstIntraStateTax) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($nonGstIntraStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstIntraStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($nonGstIntraStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstIntraStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($nonGstIntraStateTaxB2CCreditNoteAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $nonGstIntraStateTaxB2CSaleReturnAmount = SaleReturnTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($nonGstIntraStateTax) {
                $q->whereHas('saleReturnItems', function ($q) use ($nonGstIntraStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstIntraStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($nonGstIntraStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstIntraStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($nonGstIntraStateTaxB2CSaleReturnAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $nonGstInterStateTax = $this->nonGSTSupplyInterState;
        $nonGstInterStateTaxB2BSaleAmount = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($nonGstInterStateTax) {
                $q->whereHas('saleItems', function ($q) use ($nonGstInterStateTax) {
                    $q->where('classification_nature_type', $nonGstInterStateTax);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($nonGstInterStateTax) {
                        $q->where('classification_nature_type', $nonGstInterStateTax);
                    });
            })->get();
        foreach ($nonGstInterStateTaxB2BSaleAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $nonGstInterStateTaxB2BDebitNoteAmount = IncomeDebitNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($nonGstInterStateTax) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($nonGstInterStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstInterStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($nonGstInterStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstInterStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($nonGstInterStateTaxB2BDebitNoteAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $nonGstInterStateTaxB2BCreditNoteAmount = IncomeCreditNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($nonGstInterStateTax) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($nonGstInterStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstInterStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($nonGstInterStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstInterStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($nonGstInterStateTaxB2BCreditNoteAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $nonGstInterStateTaxB2BSaleReturnAmount = SaleReturnTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '!=', null);
            // })
            ->where(function ($q) use ($nonGstInterStateTax) {
                $q->whereHas('saleReturnItems', function ($q) use ($nonGstInterStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstInterStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($nonGstInterStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstInterStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($nonGstInterStateTaxB2BSaleReturnAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        $nonGstInterStateTax = $this->nonGSTSupplyInterState;
        $nonGstInterStateTaxB2CSaleAmount = SaleTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($nonGstInterStateTax) {
                $q->whereHas('saleItems', function ($q) use ($nonGstInterStateTax) {
                    $q->where('classification_nature_type', $nonGstInterStateTax);
                })
                    ->orwhereHas('saleLedgers', function ($q) use ($nonGstInterStateTax) {
                        $q->where('classification_nature_type', $nonGstInterStateTax);
                    });
            })->get();
        foreach ($nonGstInterStateTaxB2CSaleAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $nonGstInterStateTaxB2CDebitNoteAmount = IncomeDebitNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($nonGstInterStateTax) {
                $q->whereHas('incomeDebitNoteItems', function ($q) use ($nonGstInterStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstInterStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeDebitNoteLedgers', function ($q) use ($nonGstInterStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstInterStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($nonGstInterStateTaxB2CDebitNoteAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $nonGstInterStateTaxB2CCreditNoteAmount = IncomeCreditNoteTransaction::whereBetween(
            'date',
            [$startDate, $endDate]
        )
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($nonGstInterStateTax) {
                $q->whereHas('incomeCreditNoteItems', function ($q) use ($nonGstInterStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstInterStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('incomeCreditNoteLedgers', function ($q) use ($nonGstInterStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstInterStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($nonGstInterStateTaxB2CCreditNoteAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }
        $nonGstInterStateTaxB2CSaleReturnAmount = SaleReturnTransaction::whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            // ->whereHas('customer.model', function ($q) {
            //     $q->where('gstin', '=', null);
            // })
            ->where(function ($q) use ($nonGstInterStateTax) {
                $q->whereHas('saleReturnItems', function ($q) use ($nonGstInterStateTax) {
                    $q->where(
                        'classification_nature_type',
                        $nonGstInterStateTax
                    )->where('classification_is_rcm_applicable', false);
                })
                    ->orwhereHas('saleReturnLedgers', function ($q) use ($nonGstInterStateTax) {
                        $q->where(
                            'classification_nature_type',
                            $nonGstInterStateTax
                        )->where('classification_is_rcm_applicable', false);
                    });
            })->get();
        foreach ($nonGstInterStateTaxB2CSaleReturnAmount as $transaction) {
            foreach ($transaction->getb2bSezdeTransactionsData() as $data) {
                $transactionData[] = $data;
            }
        }

        return $transactionData;
    }

    public function getHsnB2bTransactionData($startDate, $endDate): array
    {
        $input = [];
        $input['start_date'] = $startDate;
        $input['end_date'] = $endDate;

        return getGstr1HsnData($this->hsnSummaryOutWordB2BData($input));
    }

    public function getHsnB2cTransactionData($startDate, $endDate): array
    {
        $input = [];
        $input['start_date'] = $startDate;
        $input['end_date'] = $endDate;

        return getGstr1HsnData($this->hsnSummaryOutWordB2cData($input));
    }

    public function hsnSummaryOutWordB2BData($input)
    {
        $startDate = $input['start_date'] ?? null;
        $endDate = $input['end_date'] ?? null;
        $input['transaction_type'] = $input['transaction_type'] ?? 0;

        $currentCompanyId = getCurrentCompany()->id;

        if ($startDate == null && $endDate == null) {
            $startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
            $endDate = Carbon::now()->endOfMonth()->format('Y-m-d');
        }

        $data['durationDate'] = Carbon::parse($startDate)->format('d-M-Y').' to '.Carbon::parse($endDate)->format('d-M-Y');

        $data['saleItems'] = SaleTransactionItem::with(['saleTransaction', 'unit', 'items.model'])
            ->whereHas('saleTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)
                    ->where('is_gst_na', '=', 0)
                    ->whereNotNull('gstin')
                    ->where('gst_id', '!=', 12)
                    ->whereBetween('date', [$startDate, $endDate]);
            })->get();

        $data['saleTransactions'] = SaleTransaction::with(['saleItems', 'customer'])
            ->whereNotNull('gstin')
            ->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)->get();

        $data['saleLedgers'] = SaleTransactionLedger::with(['saleTransaction', 'ledgers.model'])
            ->whereHas('saleTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)
                    ->where('is_gst_na', '=', 0)
                    ->whereNotNull('gstin')
                    ->whereBetween('date', [$startDate, $endDate]);
            })->get();

        $data['saleTransactionAdditionalCharges'] = DB::table('additional_charges_for_sales_transactions as additional_charges')
            ->leftJoin('sales_transactions', 'additional_charges.sale_id', '=', 'sales_transactions.id')
            ->where('sales_transactions.company_id', $currentCompanyId)
            ->where('sales_transactions.is_gst_na', '=', 0)
            ->whereNotNull('sales_transactions.gstin')
            ->whereNull('sales_transactions.deleted_at')
            ->selectRaw('
                additional_charges.sale_id as id,
                SUM(additional_charges.value) as additional_charges_sum,
                SUM(CASE WHEN sales_transactions.cgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_cgst_sum,
                SUM(CASE WHEN sales_transactions.sgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_sgst_sum,
                SUM(CASE WHEN sales_transactions.igst != 0 THEN additional_charges.value * additional_charges.gst_percentage / 100 ELSE 0 END) as additional_charges_igst_sum
            ')

            ->whereBetween('sales_transactions.date', [$startDate, $endDate])
            ->groupBy('additional_charges.sale_id')
            ->get();

        $data['saleReturnItems'] = SaleReturnItemTransaction::with(['saleReturnTransaction', 'unit', 'items.model'])
            ->whereHas('saleReturnTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)->where('is_gst_na', '=', 0)
                    ->whereNotNull('gstin')
                    ->whereBetween('date', [$startDate, $endDate]);
            })->get();

        $data['saleReturnTransactions'] = SaleReturnTransaction::with(['saleReturnItems', 'customer'])
            ->whereNotNull('gstin')
            ->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)->get();
        $data['saleReturnLedgers'] = SaleReturnLedgerTransaction::with(['saleReturnTransaction', 'ledgers'])
            ->whereHas('saleReturnTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)
                    ->where('is_gst_na', '=', 0)
                    ->whereNotNull('gstin')
                    ->whereBetween('date', [$startDate, $endDate]);
            })->get();
        $data['saleReturnTransactionAdditionalCharges'] = DB::table('additional_charges_for_sales_return_transactions as additional_charges')
            ->leftJoin('sale_return_transactions', 'additional_charges.sale_return_id', '=', 'sale_return_transactions.id')
            ->where('sale_return_transactions.company_id', $currentCompanyId)
            ->where('sale_return_transactions.is_gst_na', '=', 0)
            ->whereNull('sale_return_transactions.deleted_at')
            ->whereNotNull('sale_return_transactions.gstin')
            ->selectRaw('
                additional_charges.sale_return_id as id,
                SUM(additional_charges.value) as additional_charges_sum,
                SUM(CASE WHEN sale_return_transactions.cgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_cgst_sum,
                SUM(CASE WHEN sale_return_transactions.sgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_sgst_sum,
                SUM(CASE WHEN sale_return_transactions.igst != 0 THEN additional_charges.value * additional_charges.gst_percentage / 100 ELSE 0 END) as additional_charges_igst_sum
            ')
            ->whereBetween('sale_return_transactions.date', [$startDate, $endDate])
            ->groupBy('additional_charges.sale_return_id')
            ->get();

        $data['incomeDebitNoteItems'] = IncomeDebitNoteItemTransaction::with(['incomeDebitNoteTransaction', 'unit', 'items.model'])
            ->whereHas('incomeDebitNoteTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)
                    ->where('is_gst_na', '=', 0)
                    ->whereNotNull('gstin')
                    ->whereBetween('date', [$startDate, $endDate]);
            })->get();

        $data['incomeDebitNoteLedgers'] = IncomeDebitNoteLedgerTransaction::with(['incomeDebitNoteTransaction', 'ledgers'])
            ->whereHas('incomeDebitNoteTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)
                    ->whereNotNull('gstin')
                    ->where('is_gst_na', '=', 0)
                    ->whereBetween('date', [$startDate, $endDate]);
            })->get();

        $data['incomeDebitTransactions'] = IncomeDebitNoteTransaction::with(['incomeDebitNoteItems', 'customer'])
            ->whereBetween('date', [$startDate, $endDate])
            ->whereNotNull('gstin')
            ->where('is_gst_na', '=', 0)->get();

        $data['incomeDrTransactionAdditionalCharges'] = DB::table('additional_charges_for_income_debit_note_transactions as additional_charges')
            ->leftJoin('income_debit_note_transactions', 'additional_charges.income_debit_note_id', '=', 'income_debit_note_transactions.id')
            ->where('income_debit_note_transactions.company_id', $currentCompanyId)
            ->where('income_debit_note_transactions.is_gst_na', '=', 0)
            ->whereNull('income_debit_note_transactions.deleted_at')
            ->whereNotNull('income_debit_note_transactions.gstin')
            ->selectRaw('
            additional_charges.income_debit_note_id as id,
            SUM(additional_charges.value) as additional_charges_sum,
            SUM(CASE WHEN income_debit_note_transactions.cgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_cgst_sum,
            SUM(CASE WHEN income_debit_note_transactions.sgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_sgst_sum,
            SUM(CASE WHEN income_debit_note_transactions.igst != 0 THEN additional_charges.value * additional_charges.gst_percentage / 100 ELSE 0 END) as additional_charges_igst_sum
            ')
            ->whereBetween('income_debit_note_transactions.date', [$startDate, $endDate])
            ->groupBy('additional_charges.income_debit_note_id')
            ->get();

        $data['incomeCreditNoteItems'] = IncomeCreditNoteItemTransaction::with(['incomeCreditNoteTransaction', 'unit', 'items.model'])
            ->whereHas('incomeCreditNoteTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)
                    ->where('is_gst_na', '=', 0)
                    ->whereNotNull('gstin')
                    ->whereBetween('date', [$startDate, $endDate]);
            })->get();

        $data['incomeCreditTransactions'] = IncomeCreditNoteTransaction::with(['incomeCreditNoteItems', 'customer'])
            ->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNotNull('gstin')
            ->get();

        $data['incomeCreditNoteLedgers'] = IncomeCreditNoteLedgerTransaction::with(['incomeCreditNoteTransaction', 'ledger'])
            ->whereHas('incomeCreditNoteTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)
                    ->where('is_gst_na', '=', 0)
                    ->whereNotNull('gstin')
                    ->whereBetween('date', [$startDate, $endDate]);
            })
            ->get();

        $data['incomeCrTransactionAdditionalCharges'] = DB::table('additional_charges_for_income_credit_note_transactions as additional_charges')
            ->leftJoin('income_credit_note_transactions', 'additional_charges.income_cn_id', '=', 'income_credit_note_transactions.id')
            ->where('income_credit_note_transactions.company_id', $currentCompanyId)
            ->where('income_credit_note_transactions.is_gst_na', '=', 0)
            ->whereNull('income_credit_note_transactions.deleted_at')
            ->whereNotNull('income_credit_note_transactions.gstin')
            ->selectRaw('
                    additional_charges.income_cn_id as id,
                    SUM(additional_charges.value) as additional_charges_sum,
                    SUM(CASE WHEN income_credit_note_transactions.cgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_cgst_sum,
                    SUM(CASE WHEN income_credit_note_transactions.sgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_sgst_sum,
                    SUM(CASE WHEN income_credit_note_transactions.igst != 0 THEN additional_charges.value * additional_charges.gst_percentage / 100 ELSE 0 END) as additional_charges_igst_sum
                ')
            ->whereBetween('income_credit_note_transactions.date', [$startDate, $endDate])
            ->groupBy('additional_charges.income_cn_id')
            ->get();

        $data['taxableAmountTotal'] = $data['saleItems']->sum('taxable_value')
            + $data['saleLedgers']->sum('taxable_value')
            - $data['saleReturnItems']->sum('taxable_value')
            - $data['saleReturnLedgers']->sum('taxable_value')
            + $data['incomeDebitNoteItems']->sum('taxable_value')
            + $data['incomeDebitNoteLedgers']->sum('taxable_value')
            - $data['incomeCreditNoteItems']->sum('taxable_value')
            - $data['incomeCreditNoteLedgers']->sum('taxable_value')
            + $data['saleTransactionAdditionalCharges']->sum('additional_charges_sum')
            - $data['saleReturnTransactionAdditionalCharges']->sum('additional_charges_sum')
            + $data['incomeDrTransactionAdditionalCharges']->sum('additional_charges_sum')
            - $data['incomeCrTransactionAdditionalCharges']->sum('additional_charges_sum');

        $data['cgstTaxTotal'] = $data['saleItems']->sum('classification_cgst_tax')
            + $data['saleLedgers']->sum('classification_cgst_tax')
            - $data['saleReturnItems']->sum('classification_cgst_tax')
            - $data['saleReturnLedgers']->sum('classification_cgst_tax')
            + $data['incomeDebitNoteItems']->sum('classification_cgst_tax')
            + $data['incomeDebitNoteLedgers']->sum('classification_cgst_tax')
            - $data['incomeCreditNoteItems']->sum('classification_cgst_tax')
            - $data['incomeCreditNoteLedgers']->sum('classification_cgst_tax')
            + $data['saleTransactionAdditionalCharges']->sum('additional_charges_cgst_sum')
            - $data['saleReturnTransactionAdditionalCharges']->sum('additional_charges_cgst_sum')
            + $data['incomeDrTransactionAdditionalCharges']->sum('additional_charges_cgst_sum')
            - $data['incomeCrTransactionAdditionalCharges']->sum('additional_charges_cgst_sum');

        $data['sgstTaxTotal'] = $data['saleItems']->sum('classification_sgst_tax')
            + $data['saleLedgers']->sum('classification_sgst_tax')
            - $data['saleReturnItems']->sum('classification_sgst_tax')
            - $data['saleReturnLedgers']->sum('classification_sgst_tax')
            + $data['incomeDebitNoteItems']->sum('classification_sgst_tax')
            + $data['incomeDebitNoteLedgers']->sum('classification_sgst_tax')
            - $data['incomeCreditNoteItems']->sum('classification_sgst_tax')
            - $data['incomeCreditNoteLedgers']->sum('classification_sgst_tax')
            + $data['saleTransactionAdditionalCharges']->sum('additional_charges_sgst_sum')
            - $data['saleReturnTransactionAdditionalCharges']->sum('additional_charges_sgst_sum')
            + $data['incomeDrTransactionAdditionalCharges']->sum('additional_charges_sgst_sum')
            - $data['incomeCrTransactionAdditionalCharges']->sum('additional_charges_sgst_sum');

        $data['igstTaxTotal'] = $data['saleItems']->sum('classification_igst_tax')
            + $data['saleLedgers']->sum('classification_igst_tax')
            - $data['saleReturnItems']->sum('classification_igst_tax')
            - $data['saleReturnLedgers']->sum('classification_igst_tax')
            + $data['incomeDebitNoteItems']->sum('classification_igst_tax')
            + $data['incomeDebitNoteLedgers']->sum('classification_igst_tax')
            - $data['incomeCreditNoteItems']->sum('classification_igst_tax')
            - $data['incomeCreditNoteLedgers']->sum('classification_igst_tax')
            + $data['saleTransactionAdditionalCharges']->sum('additional_charges_igst_sum')
            - $data['saleReturnTransactionAdditionalCharges']->sum('additional_charges_igst_sum')
            + $data['incomeDrTransactionAdditionalCharges']->sum('additional_charges_igst_sum')
            - $data['incomeCrTransactionAdditionalCharges']->sum('additional_charges_igst_sum');

        $data['cessTaxTotal'] = $data['saleItems']->sum('cess_amount')
            - $data['saleReturnItems']->sum('cess_amount')
            + $data['incomeDebitNoteItems']->sum('cess_amount')
            - $data['incomeCreditNoteItems']->sum('cess_amount');

        return $data;
    }

    public function hsnSummaryOutWordB2cData($input)
    {
        $startDate = $input['start_date'] ?? null;
        $endDate = $input['end_date'] ?? null;
        $input['transaction_type'] = $input['transaction_type'] ?? 0;

        $currentCompanyId = getCurrentCompany()->id;

        if ($startDate == null && $endDate == null) {
            $startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
            $endDate = Carbon::now()->endOfMonth()->format('Y-m-d');
        }

        $data['durationDate'] = Carbon::parse($startDate)->format('d-M-Y').' to '.Carbon::parse($endDate)->format('d-M-Y');

        $data['saleItems'] = SaleTransactionItem::with(['saleTransaction', 'unit', 'items.model'])
            ->whereHas('saleTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)
                    ->where('is_gst_na', '=', 0)
                    ->whereNull('gstin')
                    ->where('gst_id', '!=', 12)
                    ->whereBetween('date', [$startDate, $endDate]);
            })
            ->whereHas('items.model', function (Builder $query) {
                // $query->whereNotNull('hsn_sac_code');
            })
            ->get();

        $data['saleTransactions'] = SaleTransaction::with(['saleItems', 'customer'])
            ->whereNull('gstin')
            ->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)->get();

        $data['saleLedgers'] = SaleTransactionLedger::with(['saleTransaction', 'ledgers.model'])
            ->whereHas('saleTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)
                    ->where('is_gst_na', '=', 0)
                    ->whereNull('gstin')
                    ->whereBetween('date', [$startDate, $endDate]);
            })->get();

        $data['saleTransactionAdditionalCharges'] = DB::table('additional_charges_for_sales_transactions as additional_charges')
            ->leftJoin('sales_transactions', 'additional_charges.sale_id', '=', 'sales_transactions.id')
            ->where('sales_transactions.company_id', $currentCompanyId)
            ->where('sales_transactions.is_gst_na', '=', 0)
            ->whereNull('sales_transactions.gstin')
            ->whereNull('sales_transactions.deleted_at')
            ->selectRaw('
                additional_charges.sale_id as id,
                SUM(additional_charges.value) as additional_charges_sum,
                SUM(CASE WHEN sales_transactions.cgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_cgst_sum,
                SUM(CASE WHEN sales_transactions.sgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_sgst_sum,
                SUM(CASE WHEN sales_transactions.igst != 0 THEN additional_charges.value * additional_charges.gst_percentage / 100 ELSE 0 END) as additional_charges_igst_sum
            ')

            ->whereBetween('sales_transactions.date', [$startDate, $endDate])
            ->groupBy('additional_charges.sale_id')
            ->get();

        $data['saleReturnItems'] = SaleReturnItemTransaction::with(['saleReturnTransaction', 'unit', 'items.model'])
            ->whereHas('saleReturnTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)->where('is_gst_na', '=', 0)
                    ->whereNull('gstin')
                    ->whereBetween('date', [$startDate, $endDate]);
            })
            ->whereHas('items.model', function (Builder $query) {
                // $query->whereNotNull('hsn_sac_code');
            })->get();

        $data['saleReturnTransactions'] = SaleReturnTransaction::with(['saleReturnItems', 'customer'])
            ->whereNull('gstin')
            ->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)->get();
        $data['saleReturnLedgers'] = SaleReturnLedgerTransaction::with(['saleReturnTransaction', 'ledgers'])
            ->whereHas('saleReturnTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)
                    ->where('is_gst_na', '=', 0)
                    ->whereNull('gstin')
                    ->whereBetween('date', [$startDate, $endDate]);
            })->get();
        $data['saleReturnTransactionAdditionalCharges'] = DB::table('additional_charges_for_sales_return_transactions as additional_charges')
            ->leftJoin('sale_return_transactions', 'additional_charges.sale_return_id', '=', 'sale_return_transactions.id')
            ->where('sale_return_transactions.company_id', $currentCompanyId)
            ->where('sale_return_transactions.is_gst_na', '=', 0)
            ->whereNull('sale_return_transactions.deleted_at')
            ->whereNull('sale_return_transactions.gstin')
            ->selectRaw('
                additional_charges.sale_return_id as id,
                SUM(additional_charges.value) as additional_charges_sum,
                SUM(CASE WHEN sale_return_transactions.cgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_cgst_sum,
                SUM(CASE WHEN sale_return_transactions.sgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_sgst_sum,
                SUM(CASE WHEN sale_return_transactions.igst != 0 THEN additional_charges.value * additional_charges.gst_percentage / 100 ELSE 0 END) as additional_charges_igst_sum
            ')
            ->whereBetween('sale_return_transactions.date', [$startDate, $endDate])
            ->groupBy('additional_charges.sale_return_id')
            ->get();

        $data['incomeDebitNoteItems'] = IncomeDebitNoteItemTransaction::with(['incomeDebitNoteTransaction', 'unit', 'items.model'])
            ->whereHas('incomeDebitNoteTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)
                    ->where('is_gst_na', '=', 0)
                    ->whereNull('gstin')
                    ->whereBetween('date', [$startDate, $endDate]);
            })
            ->whereHas('items.model', function (Builder $query) {
                // $query->whereNotNull('hsn_sac_code');
            })->get();

        $data['incomeDebitNoteLedgers'] = IncomeDebitNoteLedgerTransaction::with(['incomeDebitNoteTransaction', 'ledgers'])
            ->whereHas('incomeDebitNoteTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)
                    ->whereNull('gstin')
                    ->where('is_gst_na', '=', 0)
                    ->whereBetween('date', [$startDate, $endDate]);
            })->get();

        $data['incomeDebitTransactions'] = IncomeDebitNoteTransaction::with(['incomeDebitNoteItems', 'customer'])
            ->whereBetween('date', [$startDate, $endDate])
            ->whereNull('gstin')
            ->where('is_gst_na', '=', 0)->get();

        $data['incomeDrTransactionAdditionalCharges'] = DB::table('additional_charges_for_income_debit_note_transactions as additional_charges')
            ->leftJoin('income_debit_note_transactions', 'additional_charges.income_debit_note_id', '=', 'income_debit_note_transactions.id')
            ->where('income_debit_note_transactions.company_id', $currentCompanyId)
            ->where('income_debit_note_transactions.is_gst_na', '=', 0)
            ->whereNull('income_debit_note_transactions.deleted_at')
            ->whereNull('income_debit_note_transactions.gstin')
            ->selectRaw('
            additional_charges.income_debit_note_id as id,
            SUM(additional_charges.value) as additional_charges_sum,
            SUM(CASE WHEN income_debit_note_transactions.cgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_cgst_sum,
            SUM(CASE WHEN income_debit_note_transactions.sgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_sgst_sum,
            SUM(CASE WHEN income_debit_note_transactions.igst != 0 THEN additional_charges.value * additional_charges.gst_percentage / 100 ELSE 0 END) as additional_charges_igst_sum
            ')
            ->whereBetween('income_debit_note_transactions.date', [$startDate, $endDate])
            ->groupBy('additional_charges.income_debit_note_id')
            ->get();

        $data['incomeCreditNoteItems'] = IncomeCreditNoteItemTransaction::with(['incomeCreditNoteTransaction', 'unit', 'items.model'])
            ->whereHas('incomeCreditNoteTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)
                    ->where('is_gst_na', '=', 0)
                    ->whereNull('gstin')
                    ->whereBetween('date', [$startDate, $endDate]);
            })
            ->whereHas('items.model', function (Builder $query) {
                // $query->whereNotNull('hsn_sac_code');
            })->get();

        $data['incomeCreditTransactions'] = IncomeCreditNoteTransaction::with(['incomeCreditNoteItems', 'customer'])
            ->whereBetween('date', [$startDate, $endDate])
            ->where('is_gst_na', '=', 0)
            ->whereNull('gstin')
            ->get();

        $data['incomeCreditNoteLedgers'] = IncomeCreditNoteLedgerTransaction::with(['incomeCreditNoteTransaction', 'ledger'])
            ->whereHas('incomeCreditNoteTransaction', function (Builder $query) use ($currentCompanyId, $startDate, $endDate) {
                $query->where('company_id', $currentCompanyId)
                    ->where('is_gst_na', '=', 0)
                    ->whereNull('gstin')
                    ->whereBetween('date', [$startDate, $endDate]);
            })
            ->get();

        $data['incomeCrTransactionAdditionalCharges'] = DB::table('additional_charges_for_income_credit_note_transactions as additional_charges')
            ->leftJoin('income_credit_note_transactions', 'additional_charges.income_cn_id', '=', 'income_credit_note_transactions.id')
            ->where('income_credit_note_transactions.company_id', $currentCompanyId)
            ->where('income_credit_note_transactions.is_gst_na', '=', 0)
            ->whereNull('income_credit_note_transactions.deleted_at')
            ->whereNull('income_credit_note_transactions.gstin')
            ->selectRaw('
                    additional_charges.income_cn_id as id,
                    SUM(additional_charges.value) as additional_charges_sum,
                    SUM(CASE WHEN income_credit_note_transactions.cgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_cgst_sum,
                    SUM(CASE WHEN income_credit_note_transactions.sgst != 0 THEN (additional_charges.value * additional_charges.gst_percentage / 100) / 2 ELSE 0 END) as additional_charges_sgst_sum,
                    SUM(CASE WHEN income_credit_note_transactions.igst != 0 THEN additional_charges.value * additional_charges.gst_percentage / 100 ELSE 0 END) as additional_charges_igst_sum
                ')
            ->whereBetween('income_credit_note_transactions.date', [$startDate, $endDate])
            ->groupBy('additional_charges.income_cn_id')
            ->get();

        $data['taxableAmountTotal'] = $data['saleItems']->sum('taxable_value')
            + $data['saleLedgers']->sum('taxable_value')
            - $data['saleReturnItems']->sum('taxable_value')
            - $data['saleReturnLedgers']->sum('taxable_value')
            + $data['incomeDebitNoteItems']->sum('taxable_value')
            + $data['incomeDebitNoteLedgers']->sum('taxable_value')
            - $data['incomeCreditNoteItems']->sum('taxable_value')
            - $data['incomeCreditNoteLedgers']->sum('taxable_value')
            + $data['saleTransactionAdditionalCharges']->sum('additional_charges_sum')
            - $data['saleReturnTransactionAdditionalCharges']->sum('additional_charges_sum')
            + $data['incomeDrTransactionAdditionalCharges']->sum('additional_charges_sum')
            - $data['incomeCrTransactionAdditionalCharges']->sum('additional_charges_sum');

        $data['cgstTaxTotal'] = $data['saleItems']->sum('classification_cgst_tax')
            + $data['saleLedgers']->sum('classification_cgst_tax')
            - $data['saleReturnItems']->sum('classification_cgst_tax')
            - $data['saleReturnLedgers']->sum('classification_cgst_tax')
            + $data['incomeDebitNoteItems']->sum('classification_cgst_tax')
            + $data['incomeDebitNoteLedgers']->sum('classification_cgst_tax')
            - $data['incomeCreditNoteItems']->sum('classification_cgst_tax')
            - $data['incomeCreditNoteLedgers']->sum('classification_cgst_tax')
            + $data['saleTransactionAdditionalCharges']->sum('additional_charges_cgst_sum')
            - $data['saleReturnTransactionAdditionalCharges']->sum('additional_charges_cgst_sum')
            + $data['incomeDrTransactionAdditionalCharges']->sum('additional_charges_cgst_sum')
            - $data['incomeCrTransactionAdditionalCharges']->sum('additional_charges_cgst_sum');

        $data['sgstTaxTotal'] = $data['saleItems']->sum('classification_sgst_tax')
            + $data['saleLedgers']->sum('classification_sgst_tax')
            - $data['saleReturnItems']->sum('classification_sgst_tax')
            - $data['saleReturnLedgers']->sum('classification_sgst_tax')
            + $data['incomeDebitNoteItems']->sum('classification_sgst_tax')
            + $data['incomeDebitNoteLedgers']->sum('classification_sgst_tax')
            - $data['incomeCreditNoteItems']->sum('classification_sgst_tax')
            - $data['incomeCreditNoteLedgers']->sum('classification_sgst_tax')
            + $data['saleTransactionAdditionalCharges']->sum('additional_charges_sgst_sum')
            - $data['saleReturnTransactionAdditionalCharges']->sum('additional_charges_sgst_sum')
            + $data['incomeDrTransactionAdditionalCharges']->sum('additional_charges_sgst_sum')
            - $data['incomeCrTransactionAdditionalCharges']->sum('additional_charges_sgst_sum');

        $data['igstTaxTotal'] = $data['saleItems']->sum('classification_igst_tax')
            + $data['saleLedgers']->sum('classification_igst_tax')
            - $data['saleReturnItems']->sum('classification_igst_tax')
            - $data['saleReturnLedgers']->sum('classification_igst_tax')
            + $data['incomeDebitNoteItems']->sum('classification_igst_tax')
            + $data['incomeDebitNoteLedgers']->sum('classification_igst_tax')
            - $data['incomeCreditNoteItems']->sum('classification_igst_tax')
            - $data['incomeCreditNoteLedgers']->sum('classification_igst_tax')
            + $data['saleTransactionAdditionalCharges']->sum('additional_charges_igst_sum')
            - $data['saleReturnTransactionAdditionalCharges']->sum('additional_charges_igst_sum')
            + $data['incomeDrTransactionAdditionalCharges']->sum('additional_charges_igst_sum')
            - $data['incomeCrTransactionAdditionalCharges']->sum('additional_charges_igst_sum');

        $data['cessTaxTotal'] = $data['saleItems']->sum('cess_amount')
            - $data['saleReturnItems']->sum('cess_amount')
            + $data['incomeDebitNoteItems']->sum('cess_amount')
            - $data['incomeCreditNoteItems']->sum('cess_amount');

        return $data;
    }

    public function downloadPDF(Request $request): Response
    {
        $input = $request->all();

        $gstrCacheKey = generateCacheKey('gstr1_report');
        $data = Cache::get($gstrCacheKey);
        if (empty($data)) {
            $gstrData = $this->gstr1ExcelReportData($input);
            $data['data'] = $gstrData;
            $data['b2bSezdeTotal'] = sumOfGstr1ExcelSheet($gstrData['b2bSezde']);
            $data['b2cl'] = sumOfGstr1ExcelSheet($gstrData['b2cl']);
            $data['b2cs'] = sumOfGstr1ExcelSheet($gstrData['b2cs']);
            $data['cdnr'] = sumOfGstr1ExcelSheet($gstrData['cdnr']);
            $data['cdnur'] = sumOfGstr1ExcelSheet($gstrData['cdnur']);
            $data['exp'] = sumOfGstr1ExcelSheet($gstrData['exp']);
            $data['nillRated'] = sumOfGstr1ExcelSheet([]);
            $data['hsnB2b'] = sumOfGstr1hsnExcelSheet($gstrData['hsnB2b']);
            $data['hsnB2c'] = sumOfGstr1hsnExcelSheet($gstrData['hsnB2c']);
        }

        $company = getCurrentCompany();
        $data['company'] = $company;
        $data['companyAddress'] = $company?->addresses->where('address_type', Company::BILLING_ADDRESS)->first();
        $data['durationDate'] = Carbon::parse($data['data']['start_date'])->format('d-m-Y').' to '.
            Carbon::parse($data['data']['end_date'])->format('d-m-Y');
        $data['fileName'] = 'GSTR1_'.$company->trade_name.'_'.Carbon::parse($data['data']['start_date'])->format('Y-m-d').'_to_'.Carbon::parse($data['data']['end_date'])->format('Y-m-d');
        $data['fileName'] = replaceSpecialCharacters($data['fileName']);
        $pdf = Pdf::loadView('pdf.gstr1_report', $data)->setPaper([0, 0, 700, 900]);

        $user = getLoginUser();
        CreateAuditTrailEvent::run($user, 'Export', '<b>GSTR-1 Report</b> PDF was exported.');

        if (isset($input['is_view'])) {
            return $pdf->stream($data['fileName'].'.pdf');
        }

        // also use in API
        return $pdf->download($data['fileName'].'.pdf');
    }

    public function prepareGstr1Data(Request $request)
    {
        $input = $request->all();
        $gstrData = $this->gstr1ExcelReportData($input);
        $body = PrepareGstr1Data::run($input, $gstrData);

        $gstLogin = GstrLogin::latest()->first();
        $otpISInValid = true;
        $company = getCurrentCompany();
        $gstin = $company->companyTax->gstin;
        $gstrCacheKey = generateCacheKey('gstr1_report');
        $cacheData = Cache::get($gstrCacheKey);
        $filingPeriod = ! empty($cacheData['data']['end_date']) ? Carbon::parse($cacheData['data']['end_date'])->format('mY') : null;

        if (! $body['status']) {
            return $this->sendError('Enter the Invoice number of invoices issued to  registered recipients. Ensure that the format is alpha-numeric with  allowed special characters of slash(/) and dash(-) .The total number of characters should not be more than 16.');
        }

        if (! isset($gstLogin->otp)) {
            $otpISInValid = false;

            return $this->sendResponse(['gstLogin' => $gstLogin, 'otpISInValid' => $otpISInValid], 'OTP sent to your registered mobile/email with GST Portal');
        }
        $statecd = substr($gstin, 0, 2);
        $headers = [
            'gstin' => $gstin,
            'username' => $gstLogin->username,
            'otp' => $gstLogin->otp,
            'statecd' => $statecd,
            'retperiod' => $filingPeriod,
        ];
        $json = asset('gstfilling.json');
        $jsonData = file_get_contents($json);
        $dataArray = json_decode($jsonData, true);
        $gstGetInvoice = [];
        $fileGst = $this->fileGstService;
        $data = $fileGst->fileGstr1($body['data'], $headers);
        // $data = $fileGst->fileGstr1($dataArray, $headers);
        if ($data['success']) {
            $headers = array_merge($headers, ['requestid' => $data['requestid']]);
            $filingStatus = $fileGst->checkGstFilingStatus($headers);

            if ($filingStatus['status_cd'] == 'ER') {
                Log::channel('gstr1')->error('Filing GSTR1 Report', [
                    'company_id' => $company->id,
                    'error' => $filingStatus['error_report'],
                    'requestId' => $data['requestid'],
                    'return_period' => $filingPeriod,
                ]);
            }
            $invoiceType = [
                'B2B',
                'B2CL',
                'B2CS',
                'CDNRU',
                'DOC ISSUES',
                'HSNSUM',
            ];

            foreach ($invoiceType as $key => $value) {
                $headers['action'] = $value;
                $gstGetInvoice[$value] = $fileGst->getGstr1Invoice($headers);
            }
            $gstr1File = GstFiling::where('return_period', $filingPeriod)->where('company_id', getCurrentCompany()->id)->first();
            if (! empty($gstr1File)) {
                $gstr1File->update([
                    'company_id' => $company->id,
                    'return_period' => $filingPeriod,
                    'requestId' => $data['requestid'],
                    'status' => GstFiling::STATUS[$filingStatus['status_cd']],
                    'meta_data' => json_encode($gstGetInvoice),
                ]);
            } else {
                GstFiling::create([
                    'company_id' => $company->id,
                    'return_period' => $filingPeriod,
                    'requestId' => $data['requestid'],
                    'status' => GstFiling::STATUS[$filingStatus['status_cd']],
                    'meta_data' => json_encode($gstGetInvoice),
                ]);
            }

        } else {
            Log::channel('gstr1')->alert('FilingGSTR1Report', $data);

            return $this->sendError($data['message']);
        }
        $showError = false;
        if ($filingStatus['status_cd'] == 'P') {
            GenerateGstr1Summary::run($input);

            return $this->sendResponse($showError, '🎉 Hurray! Your GSTR 1 for the period has been successfully filed! Great job!');
        } elseif ($filingStatus['status_cd'] == 'PE') {
            $showError = true;

            return $this->sendErrorResponse($showError, "GSTR 1 prepared with errors. Don't worry! Check the error details and fix them for a smooth filing.");
        } elseif ($filingStatus['status_cd'] == 'ER') {
            return $this->sendErrorResponse($showError, $filingStatus['error_report']['error_msg']);
        } else {
            return $this->sendResponse($showError, '🎉 Hurray! Your GSTR 1 for the period has been successfully filed! Great job!');
        }

    }

    public function jsonExport(Request $request)
    {

        $input = $request->all();
        $gstrData = $this->gstr1ExcelReportData($input);

        $gstr1JsonData = PrepareGstr1Data::run($input, $gstrData);

        $company = getCurrentCompany();
        $startDate = isset($input['start_date']) ? Carbon::parse($input['start_date'])->format('Y-m-d') : Carbon::now()->startOfMonth()->format('Y-m-d');
        $endDate = isset($input['end_date']) ? Carbon::parse($input['end_date'])->format('Y-m-d') : Carbon::now()->endOfMonth()->format('Y-m-d');
        $fileName = 'GSTR1_'.$company->trade_name.'_'.$startDate.'_to_'.$endDate;
        $fileName = replaceSpecialCharacters($fileName);
        $fileName = $fileName.'.json';
        if (! $gstr1JsonData['status']) {
            $response = Excel::download(new GSTR1ErrorExport($gstr1JsonData['data']), $fileName.'.xlsx');

            return $response;
        }
        ini_set('serialize_precision', 14);

        return FacadesResponse::make(json_encode($gstr1JsonData['data'], JSON_UNESCAPED_SLASHES), 200, [
            'Content-Type' => 'application/json',
            'Content-Disposition' => 'attachment; filename="'.$fileName.'"',
        ]);
    }

    public function sendEvcOtpForGSTR1Filing(Request $request)
    {
        $input = $request->all();

        $gstLogin = GstrLogin::first();
        $company = getCurrentCompany();
        $gstin = $company->companyTax->gstin;
        $filingPeriod = ! empty($input['endDate']) ? Carbon::parse($input['endDate'])->format('mY') : null;
        $statecd = substr($gstin, 0, 2);
        $gstr1File = GstFiling::where('return_period', $filingPeriod)->where('company_id', getCurrentCompany()->id)->first();

        $gstLogin->update([
            'pan_number' => $input['pan'],
        ]);

        $headers = [
            'gstin' => $gstin,
            'username' => $gstLogin->username,
            'otp' => $gstLogin->otp,
            'statecd' => $statecd,
            'retperiod' => $filingPeriod,
            'pan' => $input['pan'],
            'requestId' => $gstr1File->requestId,
        ];

        $fileGst = $this->fileGstService;

        $data = $fileGst->getEcvOtpForGSTR1Filling($headers);

        // $data = [
        //     'success' => true,
        //     'message' => 'An EVC sent to registered mobile number/email. Please provide EVC',
        //     'status' => '500',
        //     'errorCode' => 'EVCREQUEST',
        // ];

        Log::channel('gstr1')->alert('GSTR1EVC', [$data]);
        if (! $data['success'] && $data['errorCode'] == 'OTP0010') {
            return $this->sendError($data['message']);
        }

        if ($data['errorCode'] == 'EVCREQUEST') {
            return $this->sendSuccess($data['message']);
        }

        return $this->sendError($data['message']);
    }

    public function fileGstr1(Request $request)
    {
        $input = $request->all();

        $gstLogin = GstrLogin::first();
        $company = getCurrentCompany();
        $gstin = $company->companyTax->gstin;
        $gstrCacheKey = generateCacheKey('gstr1_report');
        $cacheData = Cache::get($gstrCacheKey);
        $filingPeriod = ! empty($cacheData['data']['end_date']) ? Carbon::parse($cacheData['data']['end_date'])->format('mY') : null;
        $statecd = substr($gstin, 0, 2);
        $gstr1File = GstFiling::where('return_period', $filingPeriod)->where('company_id', getCurrentCompany()->id)->first();
        $gstrLogin = GstrLogin::whereCompanyId($company->id)->first();
        $headers = [
            'username' => $gstrLogin->username,
            'otp' => $gstrLogin->otp,
            'gstin' => $gstin,
            'statecd' => $statecd,
            'retperiod' => $filingPeriod,
        ];
        $fileGst = $this->fileGstService;
        $summaryData = $fileGst->getGstR1Data($headers);

        if (! empty($summaryData) && isset($summaryData['errorCode'])) {
            $message = $summaryData['message'];

            return $this->sendError($message);
        }

        $headers = [
            'gstin' => $gstin,
            'username' => $gstLogin->username,
            'otp' => $gstLogin->otp,
            'statecd' => $statecd,
            'retperiod' => $filingPeriod,
            'pan' => $gstLogin->pan_number,
            'requestId' => $gstr1File->requestId,
            'evcotp' => $input['otp'],
        ];

        $data = $fileGst->fileGstr1UsingOTP($headers, $summaryData);
        // $data = [
        //     'success' => true,
        //     'message' => 'GSTR1 submitted successfully',
        //     'result' => [
        //         'stdClass' => [
        //             'ack_num' => 'AA240625573586S',
        //         ],
        //     ],
        //     'status' => 200,
        //     'errorCode' => '1000',
        // ];

        Log::channel('gstr1')->alert('GSTR1Submit', $data);

        if (! $data['success']) {
            return $this->sendError($data['message']);
        }

        $gstr1File->update([
            'status' => GstFiling::STATUS[GstFiling::FILED],
        ]);

        return $this->sendResponse($data, $data['message']);
    }

    public function resendVerificationOtp(Request $request)
    {
        $input = $request->all();

        $gstLogin = GstrLogin::first();
        $company = getCurrentCompany();
        $gstin = $company->companyTax->gstin;
        $filingPeriod = ! empty($input['endDate']) ? Carbon::parse($input['endDate'])->format('mY') : null;
        $statecd = substr($gstin, 0, 2);
        $gstr1File = GstFiling::where('return_period', $filingPeriod)->where('company_id', getCurrentCompany()->id)->first();

        $headers = [
            'gstin' => $gstin,
            'username' => $gstLogin->username,
            'otp' => $gstLogin->otp,
            'statecd' => $statecd,
            'retperiod' => $filingPeriod,
            'pan' => $gstLogin->pan_number,
            'requestId' => $gstr1File->requestId,
        ];

        $fileGst = $this->fileGstService;

        $data = $fileGst->getEcvOtpForGSTR1Filling($headers);

        Log::channel('gstr1')->alert('GSTR1EVC', [$data]);
        if (! $data['success'] && $data['errorCode'] == 'OTP0010') {
            return $this->sendError($data['message']);
        }

        return $this->sendSuccess($data['message']);
    }
}
