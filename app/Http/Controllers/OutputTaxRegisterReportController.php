<?php

namespace App\Http\Controllers;

use App\Actions\AuditTrail\CreateAuditTrailEvent;
use App\Exports\OutputTaxRegisterReportExport;
use App\Models\Company;
use App\Models\IncomeCreditNoteItemTransaction;
use App\Models\IncomeCreditNoteLedgerTransaction;
use App\Models\IncomeCreditNoteTransaction;
use App\Models\IncomeDebitNoteItemTransaction;
use App\Models\IncomeDebitNoteLedgerTransaction;
use App\Models\IncomeDebitNoteTransaction;
use App\Models\SaleReturnItemTransaction;
use App\Models\SaleReturnLedgerTransaction;
use App\Models\SaleReturnTransaction;
use App\Models\SaleTransaction;
use App\Models\SaleTransactionItem;
use App\Models\SaleTransactionLedger;
use Barryvdh\DomPDF\Facade\Pdf as PDF;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;
use Maatwebsite\Excel\Facades\Excel;

class OutputTaxRegisterReportController extends AppBaseController
{
    public function index(Request $request): View
    {
        return view('company.output-tax-register-report.index');
    }

    public function getOutputTaxRegisterData($input)
    {
        $company = getCurrentCompany();
        $data = [];

        // For Sale Transaction
        $data['saleTransactionItems'] = SaleTransactionItem::with('saleTransaction')
            ->whereHas('saleTransaction', function (Builder $query) use ($company, $input) {
                $query->where('company_id', $company->id)
                    // ->where('is_gst_na', '=', 0)
                    ->whereBetween('date', [$input['start_date'], $input['end_date']]);
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $query->where('customer_ledger_id', $input['party_name']);
                }
            })
            ->get();

        $data['saleTransactionLedgers'] = SaleTransactionLedger::with('saleTransaction')
            ->whereHas('saleTransaction', function (Builder $query) use ($company, $input) {
                $query->where('company_id', $company->id)
                    // ->where('is_gst_na', '=', 0)
                    ->whereBetween('date', [$input['start_date'], $input['end_date']]);
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $query->where('customer_ledger_id', $input['party_name']);
                }
            })->get();

        $data['saleTransactionAdditionalCharges'] = SaleTransaction::whereBetween('date',
            [$input['start_date'], $input['end_date']])
            ->with(['additionalCharges', 'customer'])
            ->whereHas('additionalCharges')
            ->where(function ($query) use ($input) {
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $query->where('customer_ledger_id', $input['party_name']);
                }
            })->get();

        // For SaleReturn Transaction
        $data['saleReturnTransactionItems'] = SaleReturnItemTransaction::with('saleReturnTransaction')
            ->whereHas('saleReturnTransaction', function (Builder $query) use ($company, $input) {
                $query->where('company_id', $company->id)
                    // ->where('is_gst_na', '=', 0)
                    ->whereBetween('date', [$input['start_date'], $input['end_date']]);
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $query->where('customer_ledger_id', $input['party_name']);
                }
            })
            ->get();

        $data['saleReturnTransactionAdditionalCharges'] = SaleReturnTransaction::whereBetween('date',
            [$input['start_date'], $input['end_date']])
            ->with(['additionalCharges', 'customer'])
            ->whereHas('additionalCharges')
            ->where(function ($query) use ($input) {
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $query->where('customer_ledger_id', $input['party_name']);
                }
            })->get();

        $data['saleReturnTransactionLedgers'] = SaleReturnLedgerTransaction::with('saleReturnTransaction')
            ->whereHas('saleReturnTransaction', function (Builder $query) use ($company, $input) {
                $query->where('company_id', $company->id)
                    // ->where('is_gst_na', '=', 0)
                    ->whereBetween('date', [$input['start_date'], $input['end_date']]);
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $query->where('customer_ledger_id', $input['party_name']);
                }
            })->get();

        // For DebitNote Transaction
        $data['incomeDebitNoteTransactionItems'] = IncomeDebitNoteItemTransaction::with('incomeDebitNoteTransaction')
            ->whereHas('incomeDebitNoteTransaction', function (Builder $query) use ($company, $input) {
                $query->where('company_id', $company->id)
                    // ->where('is_gst_na', '=', 0)
                    ->whereBetween('date', [$input['start_date'], $input['end_date']]);
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $query->where('customer_ledger_id', $input['party_name']);
                }
            })
            ->get();

        $data['incomeDebitNoteTransactionAdditionalCharges'] = IncomeDebitNoteTransaction::whereBetween('date',
            [$input['start_date'], $input['end_date']])
            ->with(['additionalCharges', 'customer'])
            ->whereHas('additionalCharges')
            ->where(function ($query) use ($input) {
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $query->where('customer_ledger_id', $input['party_name']);
                }
            })->get();

        $data['incomeDebitNoteTransactionLedgers'] = IncomeDebitNoteLedgerTransaction::with('incomeDebitNoteTransaction')
            ->whereHas('incomeDebitNoteTransaction', function (Builder $query) use ($company, $input) {
                $query->where('company_id', $company->id)
                    // ->where('is_gst_na', '=', 0)
                    ->whereBetween('date', [$input['start_date'], $input['end_date']]);
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $query->where('customer_ledger_id', $input['party_name']);
                }
            })->get();

        // For CreditNote Transaction
        $data['incomeCreditNoteTransactionItems'] = IncomeCreditNoteItemTransaction::with('incomeCreditNoteTransaction')
            ->whereHas('incomeCreditNoteTransaction', function (Builder $query) use ($company, $input) {
                $query->where('company_id', $company->id)
                    // ->where('is_gst_na', '=', 0)
                    ->whereBetween('date', [$input['start_date'], $input['end_date']]);
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $query->where('customer_ledger_id', $input['party_name']);
                }
            })
            ->get();
        $data['incomeCreditNoteTransactionAdditionalCharges'] = IncomeCreditNoteTransaction::whereBetween('date',
            [$input['start_date'], $input['end_date']])
            ->with(['additionalCharges', 'customer'])
            ->whereHas('additionalCharges')
            ->where(function ($query) use ($input) {
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $query->where('customer_ledger_id', $input['party_name']);
                }
            })->get();

        $data['incomeCreditNoteTransactionLedgers'] = IncomeCreditNoteLedgerTransaction::with('incomeCreditNoteTransaction')
            ->whereHas('incomeCreditNoteTransaction', function (Builder $query) use ($company, $input) {
                $query->where('company_id', $company->id)
                    ->whereBetween('date', [$input['start_date'], $input['end_date']]);
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $query->where('customer_ledger_id', $input['party_name']);
                }
            })->get();

        $dataItem = [];

        // Sale Item Transactions
        foreach ($data['saleTransactionItems'] as $item) {

            $saleTransaction = $item->saleTransaction;
            $gstRate = $item->gst_tax_percentage == 0 ? ($saleTransaction->is_gst_na ? 'NA' : 0) : $item->gst_tax_percentage;

            $dataItem[] = [
                'party_name' => $saleTransaction->customer->name,
                'ledger_id' => $saleTransaction->customer_ledger_id,
                'gstin' => ! empty($saleTransaction->gstin) ? $saleTransaction->gstin : '',
                'invoice_number' => $saleTransaction->full_invoice_number,
                'invoice_date' => Carbon::parse($saleTransaction->date)->format('d-m-Y'),
                'transaction_type' => 'Sale',
                'ledger_name' => ! empty($item->ledger_id) ? $item->ledger->name : '',
                'item_name' => $item->items->item_name,
                'quantity' => $item->quantity,
                'unit_of_measurement' => $item->unit->full_name,
                'rate_per_unit' => $item->rpu_without_gst,
                'hsn_code' => $item->hsn_code ?? $item->items->model->hsn_sac_code ?? '',
                'taxable_value' => $item->taxable_value,
                'rate_of_gst' => $gstRate,
                'cgst' => ! empty($item->classification_cgst_tax) ? $item->classification_cgst_tax : '0',
                'sgst' => ! empty($item->classification_sgst_tax) ? $item->classification_sgst_tax : '0',
                'igst' => ! empty($item->classification_igst_tax) ? $item->classification_igst_tax : '0',
                'cess_amount' => ! empty($item->cess_amount) ? $item->cess_amount : '0',
                'rcm_yes_or_no' => ! empty($item->classification_is_rcm_applicable) ? $item->classification_is_rcm_applicable : '0',
                'invoice_amount' => $saleTransaction->grand_total,
            ];
        }
        foreach ($data['saleTransactionAdditionalCharges'] as $transaction) {
            /** @var SaleTransaction $transaction */
            $additionalCharges = $transaction->prepareAdditionalCharges();
            foreach ($additionalCharges as $additionalCharge) {
                $dataItem[] = $additionalCharge;
            }
        }

        // Sale Ledger Transactions
        foreach ($data['saleTransactionLedgers'] as $ledger) {
            $saleTransaction = $ledger->saleTransaction;
            $gstRate = $ledger->gst_tax_percentage == 0 ? ($saleTransaction->is_gst_na ? 'NA' : 0) : $ledger->gst_tax_percentage;

            $dataItem[] = [
                'party_name' => $saleTransaction->customer->name,
                'ledger_id' => $saleTransaction->customer_ledger_id,
                'gstin' => ! empty($saleTransaction->gstin) ? $saleTransaction->gstin : '',
                'invoice_number' => $saleTransaction->full_invoice_number,
                'invoice_date' => Carbon::parse($saleTransaction->date)->format('d-m-Y'),
                'transaction_type' => 'Sale',
                'ledger_name' => ! empty($ledger->ledger_id) ? $ledger->ledgers->name : '',
                'item_name' => '',
                'quantity' => 0,
                'unit_of_measurement' => '',
                'rate_per_unit' => $ledger->rpu_without_gst,
                'hsn_code' => ! empty($ledger->items->model->hsn_sac_code) ? $ledger->items->model->hsn_sac_code : '',
                'taxable_value' => $ledger->taxable_value,
                'rate_of_gst' => $gstRate,
                'cgst' => ! empty($ledger->classification_cgst_tax) ? $ledger->classification_cgst_tax : '0',
                'sgst' => ! empty($ledger->classification_sgst_tax) ? $ledger->classification_sgst_tax : '0',
                'igst' => ! empty($ledger->classification_igst_tax) ? $ledger->classification_igst_tax : '0',
                'cess_amount' => ! empty($ledger->cess_amount) ? $ledger->cess_amount : '0',
                'rcm_yes_or_no' => ! empty($ledger->classification_is_rcm_applicable) ? $ledger->classification_is_rcm_applicable : '0',
                'invoice_amount' => $saleTransaction->grand_total,
            ];
        }
        // Sale Return Item Transactions
        foreach ($data['saleReturnTransactionItems'] as $item) {
            $saleReturnTransaction = $item->saleReturnTransaction;
            $gstRate = $item->gst_tax_percentage == 0 ? ($saleReturnTransaction->is_gst_na ? 'NA' : 0) : $item->gst_tax_percentage;

            $dataItem[] = [
                'party_name' => $saleReturnTransaction->customer->name,
                'ledger_id' => $saleReturnTransaction->customer_ledger_id,
                'gstin' => ! empty($saleReturnTransaction->gstin) ? $saleReturnTransaction->gstin : '',
                'invoice_number' => $saleReturnTransaction->full_invoice_number,
                'invoice_date' => Carbon::parse($saleReturnTransaction->date)->format('d-m-Y'),
                'transaction_type' => 'Sale Return',
                'ledger_name' => ! empty($item->ledger_id) ? $item->ledger->name : '',
                'item_name' => $item->items->item_name,
                'quantity' => $item->quantity,
                'unit_of_measurement' => $item->unit->full_name,
                'rate_per_unit' => $item->rpu_without_gst,
                'hsn_code' => $item->hsn_code ?? $item->items->model->hsn_sac_code ?? '',
                'taxable_value' => -$item->taxable_value,
                'rate_of_gst' => $gstRate,
                'cgst' => (! empty($item->classification_cgst_tax)) ? -$item->classification_cgst_tax : '0',
                'sgst' => (! empty($item->classification_sgst_tax)) ? -$item->classification_sgst_tax : '0',
                'igst' => (! empty($item->classification_igst_tax)) ? -$item->classification_igst_tax : '0',
                'cess_amount' => (! empty($item->cess_amount)) ? -$item->cess_amount : '0',
                'rcm_yes_or_no' => ! empty($item->classification_is_rcm_applicable) ? $item->classification_is_rcm_applicable : '0',
                'invoice_amount' => -$saleReturnTransaction->grand_total,
            ];
        }

        foreach ($data['saleReturnTransactionAdditionalCharges'] as $transaction) {
            /** @var SaleReturnTransaction $transaction */
            $additionalCharges = $transaction->prepareAdditionalCharges();
            foreach ($additionalCharges as $additionalCharge) {
                $dataItem[] = $additionalCharge;
            }
        }

        // Sale Return Ledger Transactions
        foreach ($data['saleReturnTransactionLedgers'] as $ledger) {

            $saleReturnTransaction = $ledger->saleReturnTransaction;
            $gstRate = $ledger->gst_tax_percentage == 0 ? ($saleReturnTransaction->is_gst_na ? 'NA' : 0) : $ledger->gst_tax_percentage;

            $dataItem[] = [
                'party_name' => $saleReturnTransaction->customer->name,
                'ledger_id' => $saleReturnTransaction->customer_ledger_id,
                'gstin' => ! empty($saleReturnTransaction->gstin) ? $saleReturnTransaction->gstin : '',
                'invoice_number' => $saleReturnTransaction->full_invoice_number,
                'invoice_date' => Carbon::parse($saleReturnTransaction->date)->format('d-m-Y'),
                'transaction_type' => 'Sale Return',
                'ledger_name' => ! empty($ledger->ledger_id) ? $ledger->ledger->name : '',
                'item_name' => '',
                'quantity' => 0,
                'unit_of_measurement' => '',
                'rate_per_unit' => $ledger->rpu_without_gst,
                'hsn_code' => ! empty($ledger->items->model->hsn_sac_code) ? $ledger->items->model->hsn_sac_code : '',
                'taxable_value' => -$ledger->taxable_value,
                'rate_of_gst' => $gstRate,
                'cgst' => (! empty($ledger->classification_cgst_tax)) ? -$ledger->classification_cgst_tax : '0',
                'sgst' => (! empty($ledger->classification_sgst_tax)) ? -$ledger->classification_sgst_tax : '0',
                'igst' => (! empty($ledger->classification_igst_tax)) ? -$ledger->classification_igst_tax : '0',
                'cess_amount' => (! empty($ledger->cess_amount)) ? -$ledger->cess_amount : '0',
                'rcm_yes_or_no' => ! empty($ledger->classification_is_rcm_applicable) ? $ledger->classification_is_rcm_applicable : '0',
                'invoice_amount' => -$saleReturnTransaction->grand_total,
            ];
        }

        // Income Dr. Note Item Transactions
        foreach ($data['incomeDebitNoteTransactionItems'] as $item) {
            $incomeDebitNoteTransaction = $item->incomeDebitNoteTransaction;
            $gstRate = $item->gst_tax_percentage == 0 ? ($incomeDebitNoteTransaction->is_gst_na ? 'NA' : 0) : $item->gst_tax_percentage;

            $dataItem[] = [
                'party_name' => $incomeDebitNoteTransaction->customer->name,
                'ledger_id' => $incomeDebitNoteTransaction->customer_ledger_id,
                'gstin' => ! empty($incomeDebitNoteTransaction->gstin) ? $incomeDebitNoteTransaction->gstin : '',
                'invoice_number' => $incomeDebitNoteTransaction->full_invoice_number,
                'invoice_date' => Carbon::parse($incomeDebitNoteTransaction->date)->format('d-m-Y'),
                'transaction_type' => 'Income Dr. Note',
                'ledger_name' => ! empty($item->ledger_id) ? $item->ledger->name : '',
                'item_name' => $item->items->item_name,
                'quantity' => $item->quantity,
                'unit_of_measurement' => $item->unit->full_name,
                'rate_per_unit' => $item->rpu_without_gst,
                'hsn_code' => $item->hsn_code ?? $item->items->model->hsn_sac_code ?? '',
                'taxable_value' => $item->taxable_value,
                'rate_of_gst' => $gstRate,
                'cgst' => ! empty($item->classification_cgst_tax) ? $item->classification_cgst_tax : '0',
                'sgst' => ! empty($item->classification_sgst_tax) ? $item->classification_sgst_tax : '0',
                'igst' => ! empty($item->classification_igst_tax) ? $item->classification_igst_tax : '0',
                'cess_amount' => ! empty($item->cess_amount) ? $item->cess_amount : '0',
                'rcm_yes_or_no' => ! empty($item->classification_is_rcm_applicable) ? $item->classification_is_rcm_applicable : '0',
                'invoice_amount' => $incomeDebitNoteTransaction->grand_total,
            ];
        }

        foreach ($data['incomeDebitNoteTransactionAdditionalCharges'] as $transaction) {
            /** @var IncomeDebitNoteTransaction $transaction */
            $additionalCharges = $transaction->prepareAdditionalCharges();
            foreach ($additionalCharges as $additionalCharge) {
                $dataItem[] = $additionalCharge;
            }
        }

        // Income Dr. Note Ledger Transactions
        foreach ($data['incomeDebitNoteTransactionLedgers'] as $ledger) {

            $incomeDebitNoteTransaction = $ledger->incomeDebitNoteTransaction;
            $gstRate = $ledger->gst_tax_percentage == 0 ? ($incomeDebitNoteTransaction->is_gst_na ? 'NA' : 0) : $ledger->gst_tax_percentage;

            $dataItem[] = [
                'party_name' => $incomeDebitNoteTransaction->customer->name,
                'ledger_id' => $incomeDebitNoteTransaction->customer_ledger_id,
                'gstin' => ! empty($incomeDebitNoteTransaction->gstin) ? $incomeDebitNoteTransaction->gstin : '',
                'invoice_number' => $incomeDebitNoteTransaction->full_invoice_number,
                'invoice_date' => Carbon::parse($incomeDebitNoteTransaction->date)->format('d-m-Y'),
                'transaction_type' => 'Income Dr. Note',
                'ledger_name' => ! empty($ledger->ledger_id) ? $ledger->ledger->name : '',
                'item_name' => '',
                'quantity' => 0,
                'unit_of_measurement' => '',
                'rate_per_unit' => $ledger->rpu_without_gst,
                'hsn_code' => ! empty($ledger->items->model->hsn_sac_code) ? $ledger->items->model->hsn_sac_code : '',
                'taxable_value' => $ledger->taxable_value,
                'rate_of_gst' => $gstRate,
                'cgst' => ! empty($ledger->classification_cgst_tax) ? $ledger->classification_cgst_tax : '0',
                'sgst' => ! empty($ledger->classification_sgst_tax) ? $ledger->classification_sgst_tax : '0',
                'igst' => ! empty($ledger->classification_igst_tax) ? $ledger->classification_igst_tax : '0',
                'cess_amount' => ! empty($ledger->cess_amount) ? $ledger->cess_amount : '0',
                'rcm_yes_or_no' => ! empty($ledger->classification_is_rcm_applicable) ? $ledger->classification_is_rcm_applicable : '0',
                'invoice_amount' => $incomeDebitNoteTransaction->grand_total,
            ];
        }

        // Income Cr. Note Item Transactions
        foreach ($data['incomeCreditNoteTransactionItems'] as $item) {
            $incomeCreditNoteTransaction = $item->incomeCreditNoteTransaction;
            $gstRate = $item->gst_tax_percentage == 0 ? ($incomeCreditNoteTransaction->is_gst_na ? 'NA' : 0) : $item->gst_tax_percentage;
            $dataItem[] = [
                'party_name' => $incomeCreditNoteTransaction->customer->name,
                'ledger_id' => $incomeCreditNoteTransaction->customer_ledger_id,
                'gstin' => ! empty($incomeCreditNoteTransaction->gstin) ? $incomeCreditNoteTransaction->gstin : '',
                'invoice_number' => $incomeCreditNoteTransaction->full_invoice_number,
                'invoice_date' => Carbon::parse($incomeCreditNoteTransaction->date)->format('d-m-Y'),
                'transaction_type' => 'Income Cr. Note',
                'ledger_name' => ! empty($item->ledger_id) ? $item->ledger->name : '',
                'item_name' => $item->items->item_name,
                'quantity' => $item->quantity,
                'unit_of_measurement' => $item->unit->full_name,
                'rate_per_unit' => $item->rpu_without_gst,
                'hsn_code' => $item->hsn_code ?? $item->items->model->hsn_sac_code ?? '',
                'taxable_value' => -$item->taxable_value,
                'rate_of_gst' => $gstRate,
                'cgst' => (! empty($item->classification_cgst_tax)) ? -$item->classification_cgst_tax : '0',
                'sgst' => (! empty($item->classification_sgst_tax)) ? -$item->classification_sgst_tax : '0',
                'igst' => (! empty($item->classification_igst_tax)) ? -$item->classification_igst_tax : '0',
                'cess_amount' => (! empty($item->cess_amount)) ? -$item->cess_amount : '0',
                'rcm_yes_or_no' => ! empty($item->classification_is_rcm_applicable) ? $item->classification_is_rcm_applicable : '0',
                'invoice_amount' => -$incomeCreditNoteTransaction->grand_total,
            ];
        }

        // Income Cr. Note Ledger Transactions
        foreach ($data['incomeCreditNoteTransactionLedgers'] as $ledger) {
            $incomeCreditNoteTransaction = $ledger->incomeCreditNoteTransaction;
            $gstRate = $ledger->gst_tax_percentage == 0 ? ($incomeCreditNoteTransaction->is_gst_na ? 'NA' : 0) : $ledger->gst_tax_percentage;

            $dataItem[] = [
                'party_name' => $incomeCreditNoteTransaction->customer->name,
                'ledger_id' => $incomeCreditNoteTransaction->customer_ledger_id,
                'gstin' => ! empty($incomeCreditNoteTransaction->gstin) ? $incomeCreditNoteTransaction->gstin : '',
                'invoice_number' => $incomeCreditNoteTransaction->full_invoice_number,
                'invoice_date' => Carbon::parse($incomeCreditNoteTransaction->date)->format('d-m-Y'),
                'transaction_type' => 'Income Cr. Note',
                'ledger_name' => ! empty($ledger->ledger_id) ? $ledger->ledger->name : '',
                'item_name' => '',
                'quantity' => 0,
                'unit_of_measurement' => '',
                'rate_per_unit' => $ledger->rpu_without_gst,
                'hsn_code' => ! empty($ledger->items->model->hsn_sac_code) ? $ledger->items->model->hsn_sac_code : '',
                'taxable_value' => -$ledger->taxable_value,
                'rate_of_gst' => $gstRate,
                'cgst' => (! empty($ledger->classification_cgst_tax)) ? -$ledger->classification_cgst_tax : '0',
                'sgst' => (! empty($ledger->classification_sgst_tax)) ? -$ledger->classification_sgst_tax : '0',
                'igst' => (! empty($ledger->classification_igst_tax)) ? -$ledger->classification_igst_tax : '0',
                'cess_amount' => (! empty($ledger->cess_amount)) ? -$ledger->cess_amount : '0',
                'rcm_yes_or_no' => ! empty($ledger->classification_is_rcm_applicable) ? $ledger->classification_is_rcm_applicable : '0',
                'invoice_amount' => -$incomeCreditNoteTransaction->grand_total,
            ];
        }

        foreach ($data['incomeCreditNoteTransactionAdditionalCharges'] as $transaction) {
            /** @var IncomeCreditNoteTransaction $transaction */
            $additionalCharges = $transaction->prepareAdditionalCharges();
            foreach ($additionalCharges as $additionalCharge) {
                $dataItem[] = $additionalCharge;
            }
        }

        if ($input['report_type'] == 1) {
            $dataItem = $this->getSummaryReports($dataItem);
        }

        return $dataItem;
    }

    public function getSummaryReports($data)
    {
        $dataItem = [];

        foreach ($data as $item) {
            $customerId = $item['ledger_id'];
            if (array_key_exists($customerId, $dataItem)) {
                if ($dataItem[$customerId]['invoice_number'] !== $item['invoice_number']) {
                    $dataItem[$customerId]['invoice_amount'] += $item['invoice_amount'];
                }
                $dataItem[$customerId]['party_name'] = $item['party_name'];
                $dataItem[$customerId]['ledger_id'] = $customerId;
                $dataItem[$customerId]['gstin'] = $item['gstin'];
                $dataItem[$customerId]['taxable_value'] += $item['taxable_value'];
                $dataItem[$customerId]['rateOfGst'] = $item['rate_of_gst'];
                $dataItem[$customerId]['cgst'] += $item['cgst'];
                $dataItem[$customerId]['sgst'] += $item['sgst'];
                $dataItem[$customerId]['igst'] += $item['igst'];
                $dataItem[$customerId]['cess_amount'] += $item['cess_amount'];
                $dataItem[$customerId]['invoice_number'] = $item['invoice_number'];
            } else {
                $dataItem[$customerId]['party_name'] = $item['party_name'];
                $dataItem[$customerId]['ledger_id'] = $customerId;
                $dataItem[$customerId]['gstin'] = $item['gstin'];
                $dataItem[$customerId]['taxable_value'] = $item['taxable_value'];
                $dataItem[$customerId]['rateOfGst'] = $item['rate_of_gst'];
                $dataItem[$customerId]['cgst'] = $item['cgst'];
                $dataItem[$customerId]['sgst'] = $item['sgst'];
                $dataItem[$customerId]['igst'] = $item['igst'];
                $dataItem[$customerId]['cess_amount'] = $item['cess_amount'];
                $dataItem[$customerId]['invoice_amount'] = $item['invoice_amount'];
                $dataItem[$customerId]['invoice_number'] = $item['invoice_number'];
            }
        }

        return array_values($dataItem);

    }

    public function export(Request $request)
    {
        $input = $request->all();
        $type = $input['type'];
        $outputTaxRegisterReportCacheKey = generateCacheKey('output_tax_register_report');
        $company = getCurrentCompany();
        $data = [];
        $data['data'] = Cache::get($outputTaxRegisterReportCacheKey);
        $data['company'] = $company;
        $data['companyAddress'] = $company?->addresses->where('address_type', Company::BILLING_ADDRESS)->first();

        $user = getLoginUser();
        $pdfOrExcel = $type == 'pdf' ? 'PDF' : 'Excel';
        CreateAuditTrailEvent::run($user, 'Export', '<b>Output Tax Register Report</b> '.$pdfOrExcel.' was exported.');

        if ($type === 'excel') {
            $data['report_type'] = $input['report_type'];

            $response = Excel::download(new OutputTaxRegisterReportExport($data), 'output-tax-register-report.xlsx');
            ob_end_clean();

            return $response;

            return (new OutputTaxRegisterReportExport($data))->download('output-tax-register-report.xlsx');
        }

        if ($input['report_type'] == 1) {
            $pdf = Pdf::loadView('pdf.output-tax-register-summery', $data)->setPaper('a4', 'landscape');
        } else {
            $pdf = Pdf::loadView('pdf.output-tax-register', $data)->setPaper('a4', 'landscape');
        }

        if (isset($input['is_view'])) {
            return $pdf->stream('output-tax-register-report.pdf');
        }

        return $pdf->download('output-tax-register-report.pdf');
    }
}
