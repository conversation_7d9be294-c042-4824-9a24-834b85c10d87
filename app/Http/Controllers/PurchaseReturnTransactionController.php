<?php

namespace App\Http\Controllers;

use App\Actions\DownloadPdf\DownloadBulkPdf;
use App\Actions\DownloadPdf\DownloadPdfStatus;
use App\Actions\Expense\PurchaseReturn\CheckExistPurchaseReturnTransactionData;
use App\Actions\Expense\PurchaseReturn\DeletePurchaseReturnTransaction;
use App\Actions\Expense\PurchaseReturn\GetInvoicePDFDataForPurchaseReturn;
use App\Actions\Expense\PurchaseReturn\GetPurchaseReturnVoucherNumber;
use App\Actions\Expense\PurchaseReturn\PurchaseReturnStoreTransaction;
use App\Actions\Expense\PurchaseReturn\PurchaseReturnUpdateTransaction;
use App\Actions\Income\GstCalculationForPdf;
use App\Exports\PurchaseReturnTransactionExport;
use App\Http\Requests\PurchaseReturnTransactionRequest;
use App\Imports\PurchaseReturnAccountingInvoiceImport;
use App\Imports\PurchaseReturnItemInvoiceImport;
use App\Jobs\BulkPdfExportJob;
use App\Jobs\DownloadInvoiceJob;
use App\Jobs\InvoiceMailJob;
use App\Models\Company;
use App\Models\CompanyGroup;
use App\Models\CompanySetting;
use App\Models\Configuration\PurchaseReturnConfiguration;
use App\Models\Ledger;
use App\Models\Location;
use App\Models\Master\Broker;
use App\Models\Master\Customer;
use App\Models\Master\ExpenseReturnTransactionMaster;
use App\Models\Master\Supplier;
use App\Models\Notification;
use App\Models\PdfDownloadingStatus;
use App\Models\PurchaseReturnTransaction;
use App\Models\PurchaseTransaction;
use App\Models\ReceiptTransaction;
use App\Models\SaleTransaction;
use App\Repositories\PurchaseReturnTransactionRepository;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Laracasts\Flash\Flash;
use Maatwebsite\Excel\Facades\Excel;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use ZipArchive;

/**
 * Class PurchaseReturnTransactionController
 */
class PurchaseReturnTransactionController extends AppBaseController
{
    public PurchaseReturnTransactionRepository $purchaseReturnTransactionRepository;

    public function __construct(PurchaseReturnTransactionRepository $purchaseReturnTransactionRepository)
    {
        $this->purchaseReturnTransactionRepository = $purchaseReturnTransactionRepository;
    }

    /**
     * @return View
     */
    public function index(): \Illuminate\View\View
    {
        return view('company.purchase-return.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return View
     */
    public function create(): \Illuminate\View\View
    {
        $data = [];
        $getCurrentCompany = getCurrentCompany();
        $ledgerSupplier = CompanyGroup::whereCompanyId($getCurrentCompany?->id);
        $data['groupLists'] = getGroupLists();
        $data['groupListsId'] = $ledgerSupplier->where('name', Ledger::SUPPLIER)->first()->id;
        $data['countries'] = getCountries();
        $data['indiaStates'] = getStates(Company::INDIA_COUNTRY_ID);
        $data['updateLedger'] = false;
        $TdsGroups = getParentGroupsValue(Ledger::TAXES_TDS);
        $groupIds = array_merge(array_keys($TdsGroups));
        $data['ledgerOfTds'] = Ledger::whereIn('group_id', $groupIds)->pluck('name', 'id')->toArray();
        $data['locationOfAssets'] = Location::pluck('name', 'id')->toArray();
        $data['brokerMasters'] = Broker::whereCompanyId($getCurrentCompany?->id)->pluck('broker_name', 'id');
        $data['purchaseReturnConfiguration'] = PurchaseReturnConfiguration::first();
        $data['companyItemMasterGroups'] = getParentGroupsValue(Ledger::ITEM_DEFAULT_GROUP);
        $data['defaultGroup'] = CompanyGroup::whereName(Ledger::ITEM_DEFAULT_GROUP)->whereName('Default Group')
            ->whereCompanyId(getCurrentCompany()->id)
            ->value('id');
        $data['unitOfMeasurement'] = getItemMasterUnit();
        $data['purchaseReturnTransaction'] = ExpenseReturnTransactionMaster::whereCompanyId($getCurrentCompany?->id)->first();
        $data['purchaseInvoiceNumbers'] = PurchaseTransaction::pluck('voucher_number', 'id')->toArray();
        $data['lastVoucherNumber'] = GetPurchaseReturnVoucherNumber::run();
        $data['methodVoucherType'] = $data['purchaseReturnTransaction']->voucher_number ?? ExpenseReturnTransactionMaster::AUTOMATIC;
        $data['paymentMode'] = $data['purchaseReturnTransaction']->payment_mode ?? PurchaseReturnTransaction::CREDIT_MODE;
        $data['previousBillId'] = PurchaseReturnTransaction::orderBy('created_at', 'DESC')->first()?->id;

        $lastTransactionId = request()->query('lastTransactionId');
        if (! empty($lastTransactionId)) {
            $data['lastTransaction'] = PurchaseReturnTransaction::where('id', $lastTransactionId)->first();
        }

        return view('company.purchase-return.create')->with($data);
    }

    public function checkUniqueVoucherNumber($voucherNumber, $configuration)
    {
        /* Whenever the code is used, add a fiscal year date condition to the query. */
        /* if (is_numeric($voucherNumber)) {
            $data['lastVoucherNumber'] = sprintf('%0'.strlen($voucherNumber).'d', ++$voucherNumber);
        } else {
            $data['lastVoucherNumber'] = $voucherNumber !== null ? ++$voucherNumber : 1;
        }
        $purchaseReturnTransaction = PurchaseReturnTransaction::whereVoucherNumber($data['lastVoucherNumber'])->first();
        if (! empty($purchaseReturnTransaction)) {
            return $this->checkUniqueVoucherNumber($purchaseReturnTransaction->voucher_number, $configuration);
        } */

        $data['lastVoucherNumber'] = genNextInvNo($voucherNumber);

        return $data['lastVoucherNumber'] ?? 1;
    }

    public function store(PurchaseReturnTransactionRequest $request): JsonResponse
    {
        $input = $request->all();

        if ($input['is_cgst_sgst_igst_calculated'] && $input['is_gst_na'] && getCurrentCompany()->is_gst_applicable) {
            Log::error([
                'is_cgst_sgst_igst_calculated and is_gst_na both are' => true,
                'companyId' => getCurrentCompany()->id,
                'input' => $input,
            ]);
        }

        $data = PurchaseReturnStoreTransaction::run($input);
        if ($input['submit_button_value'] == PurchaseReturnTransaction::SAVE_AND_NEW_BUTTON) {
            $data['lastPurchaseReturnTransactionDate'] = $input['voucher_date'];
            $data['lastTransactionId'] = $data['purchaseReturn']['id'];
            $data['actionName'] = PurchaseReturnTransaction::SAVE_AND_NEW_BUTTON;
        } elseif ($input['submit_button_value'] == PurchaseReturnTransaction::SAVE_AND_PRINT_BUTTON) {
            $data['actionName'] = PurchaseReturnTransaction::SAVE_AND_PRINT_BUTTON;
            $data['purchase_return_id'] = $data['purchaseReturn']['id'];
            // $pdf = $this->getPurchaseReturnPDF($data['purchaseReturn']['id']);
            // $content = json_decode($pdf->getContent(), true);
            // $data['pdf_status'] = $content['data']['status'];
            // $data['pdf_view_route'] = $data['pdf_status'] == 'completed' ? $content['data']['viewRoute'] : null;
        } else {
            $data['actionName'] = PurchaseReturnTransaction::SAVE_BUTTON;
        }

        return $this->sendResponse($data, 'Purchase return transaction created successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(PurchaseReturnTransaction $purchaseReturn): JsonResponse
    {
        $data = $purchaseReturn->load('supplier', 'purchaseReturnItems', 'tdsLedger');
        $purchaseReturnTransactions = ReceiptTransaction::whereHas(
            'receiptTransactionItem',
            function ($q) use ($purchaseReturn) {
                $q->where('purchase_return_id', $purchaseReturn->id)->where('received_amount', '>', 0);
            }
        )->get();
        $transactionsData['receiptTransactions'] = [];
        $transactionsData['purchaseTransaction'] = [];
        $purchaseReturnTransactionsCount = $purchaseReturnTransactions->count();
        if ($purchaseReturnTransactionsCount) {
            $transactionsData['receiptTransactions'] = $purchaseReturnTransactions;
        }
        $purchaseTransaction = $purchaseReturn->purchase;
        $purchaseTransactionCount = false;
        if (! empty($purchaseTransaction)) {
            $purchaseTransactionCount = true;
            $transactionsData['purchaseTransaction'] = $purchaseTransaction;
        }

        $data['checkTransactionExists'] = $purchaseReturnTransactionsCount || $purchaseTransactionCount;
        $data['html'] = view('company.sale.view_sale_associated_transaction_list')->with($transactionsData)->render();

        return $this->sendResponse($data, 'Purchase Return transaction retrieved successfully');
    }

    /**
     * @return Application|Factory|View
     */
    public function edit(PurchaseReturnTransaction $purchaseReturn)
    {
        $lockDate = getTransactionsLockDate()['expense'] ?? null;
        $isLocked = ! empty($lockDate) && Carbon::parse($lockDate)->greaterThanOrEqualTo($purchaseReturn->voucher_date);
        if ($isLocked) {
            Flash::error('These Transactions Is locked');

            return redirect()->back();
        }
        $data = [];
        $data['purchaseReturn'] = $purchaseReturn->load([
            'purchaseReturnItems.items',
            'purchaseReturnItems.gst',
            'purchaseReturnLedgers.gst',
            'addresses',
            'supplier.model',
            'tcsLedger.model',
        ]);
        $getCurrentCompany = getCurrentCompany();
        $ledgerSupplier = CompanyGroup::whereCompanyId($getCurrentCompany?->id);
        $data['groupLists'] = getGroupLists();
        $data['groupListsId'] = $ledgerSupplier->where('name', Ledger::SUPPLIER)->first()->id;
        $data['countries'] = getCountries();
        $data['indiaStates'] = getStates(Company::INDIA_COUNTRY_ID);
        $data['updateLedger'] = false;
        $ledger = Ledger::whereId($purchaseReturn->supplier_id)->whereIn('model_type', [Supplier::class, Customer::class])->first();
        $supplier = Supplier::whereId($ledger->model_id)->first();
        $data['isTdsApplicable'] = $supplier?->is_tds_applicable;
        $TdsGroups = getParentGroupsValue(Ledger::TAXES_TDS);
        $groupIds = array_merge(array_keys($TdsGroups));
        $data['ledgerOfTds'] = Ledger::whereIn('group_id', $groupIds)->pluck('name', 'id')->toArray();
        $data['locationOfAssets'] = Location::pluck('name', 'id')->toArray();
        $data['brokerMasters'] = Broker::whereCompanyId($getCurrentCompany?->id)->pluck('broker_name', 'id');
        $data['purchaseNumbers'] = getSupplierPurchaseInvoiceNumber($purchaseReturn->supplier_id);
        $data['purchaseReturnConfiguration'] = PurchaseReturnConfiguration::first();
        $data['purchaseReturnTransaction'] = ExpenseReturnTransactionMaster::whereCompanyId($getCurrentCompany?->id)->first();
        $data['companyItemMasterGroups'] = getParentGroupsValue(Ledger::ITEM_DEFAULT_GROUP);
        $data['defaultGroup'] = CompanyGroup::whereName(Ledger::ITEM_DEFAULT_GROUP)->whereName('Default Group')
            ->whereCompanyId(getCurrentCompany()->id)
            ->value('id');
        $data['unitOfMeasurement'] = getItemMasterUnit();
        $data['billingAddress'] = $data['purchaseReturn']->addresses->firstWhere(
            'address_type',
            PurchaseReturnTransaction::BILLING_ADDRESS
        );
        $data['billingStates'] = getStates($data['billingAddress']->country_id);
        $data['billingCities'] = getCities($data['billingAddress']->state_id);
        $data['shippingAddress'] = $data['purchaseReturn']->addresses->firstWhere(
            'address_type',
            PurchaseReturnTransaction::SHIPPING_ADDRESS
        );
        if ($data['shippingAddress']) {
            $data['shippingStates'] = getStates($data['shippingAddress']->country_id);
            $data['shippingCities'] = getCities($data['shippingAddress']->state_id);
        } else {
            $data['shippingStates'] = null;
            $data['shippingCities'] = null;
        }
        $data['previousBillId'] = PurchaseReturnTransaction::where('id', '<', $purchaseReturn->id)->orderByDesc('id')->value('id');
        $data['nextBillId'] = PurchaseReturnTransaction::where('id', '>', $purchaseReturn->id)->orderBy('id')->value('id');

        return view('company.purchase-return.edit')->with($data);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(PurchaseReturnTransactionRequest $request, PurchaseReturnTransaction $purchaseReturn): JsonResponse
    {
        $input = $request->all();

        if ($input['is_cgst_sgst_igst_calculated'] && $input['is_gst_na'] && getCurrentCompany()->is_gst_applicable) {
            Log::error([
                'is_cgst_sgst_igst_calculated and is_gst_na both are' => true,
                'transactionId' => $purchaseReturn->id,
                'companyId' => $purchaseReturn->company_id,
                'input' => $input,
                'old_input' => $purchaseReturn,
            ]);
        }

        $data = PurchaseReturnUpdateTransaction::run($input, $purchaseReturn);

        if ($input['submit_button_value'] == PurchaseReturnTransaction::SAVE_AND_PRINT_BUTTON) {
            $data['actionName'] = PurchaseReturnTransaction::SAVE_AND_PRINT_BUTTON;
            $data['purchase_return_id'] = $data['id'];
            // $pdf = $this->getPurchaseReturnPDF($data['id']);
            // $content = json_decode($pdf->getContent(), true);
            // $data['pdf_status'] = $content['data']['status'];
            // $data['pdf_view_route'] = $data['pdf_status'] == 'completed' ? $content['data']['viewRoute'] : null;
        }

        return $this->sendResponse($data, 'Purchase Return updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PurchaseReturnTransaction $purchaseReturn): JsonResponse
    {
        DeletePurchaseReturnTransaction::run($purchaseReturn);

        return $this->sendSuccess('Purchase Return deleted successfully');
    }

    public function getPurchaseReturnTransactionItemType($itemType): JsonResponse
    {
        $html = null;
        $purchaseReturnConfiguration = PurchaseReturnConfiguration::firstOrFail();
        if ($itemType == PurchaseReturnTransaction::ACCOUNTING_INVOICE) {
            $html = view('company.purchase-return.append.ledgers_fields', compact('purchaseReturnConfiguration'))
                ->render();
        }
        if ($itemType == PurchaseReturnTransaction::ITEM_INVOICE) {
            $html = view('company.purchase-return.append.items_fields', compact('purchaseReturnConfiguration'))
                ->render();
        }

        return $this->sendResponse(
            ['html' => $html, 'isGstApplicable' => isCompanyGstApplicable()],
            'Purchase Return Transaction screen retrieved successfully'
        );
    }

    public function getSaleTransactionItems($purchaseTransactionId): JsonResponse
    {
        $purchaseTransaction = PurchaseTransaction::whereId($purchaseTransactionId)->with(
            'purchaseTransactionItems',
            'purchaseTransactionLedger'
        )
            ->firstOrFail();

        $isCompanyGstApplicable = isCompanyGstApplicable();
        $purchaseReturnConfiguration = PurchaseReturnConfiguration::toBase()->first();
        $html = view(
            'company.purchase-return.append.purchase-items-fields',
            compact('purchaseReturnConfiguration', 'isCompanyGstApplicable', 'purchaseTransaction')
        )->render();

        return $this->sendResponse($html, 'Purchase Item Type retrieved successfully');
    }

    public function getSupplierPurchaseNumber($supplierId): JsonResponse
    {
        $purchaseNumbers = getSupplierPurchaseInvoiceNumber($supplierId);

        return $this->sendResponse($purchaseNumbers, 'Suppliers purchase number retrieved successfully');
    }

    public function getPurchaseReturnPdfPreview($purchaseReturnPdfPreviewId): JsonResponse
    {
        enableDeletedScope();
        $data = GetInvoicePDFDataForPurchaseReturn::run($purchaseReturnPdfPreviewId);
        $data = GstCalculationForPdf::run($data, PurchaseReturnTransaction::class);
        $data['preview_enabled'] = true;
        $data['isA5Pdf'] = false;
        if (getCompanyExpensePDFFormat() == CompanySetting::PDF_FORMAT[CompanySetting::A5]) {
            $data['isA5Pdf'] = true;
        }

        $html = view('company.pdf.purchase_pdf.download_purchase_return', $data)->render();
        $fileName = 'Purchase_return_'.$data['transaction']->voucher_number.'_'.$data['transaction']['supplier']['name'];
        $data = [];
        // $data['emailRoute'] = route('company.purchase-returns-email', ['purchase_return' => $purchaseReturnPdfPreviewId]);
        $data['downloadRoute'] = route('company.purchase-return-preview-pdf-download', ['purchaseReturnId' => $purchaseReturnPdfPreviewId]);
        if (getCompanyExpensePDFFormat() == CompanySetting::PDF_FORMAT[CompanySetting::A5]) {
            $data['isA5Pdf'] = true;
        }
        disableDeletedScope();

        return $this->sendResponse(['html' => $html, 'fileName' => $fileName, 'data' => $data], 'Purchase return pdf generated successfully');
    }

    public function getPurchaseReturnPDF($purchaseReturnPdfPreviewId)
    {
        $pdfStatus = DownloadPdfStatus::run(PdfDownloadingStatus::PURCHASE_RETURN);
        $currentCompany = getCurrentCompany();
        $data = [];

        DownloadInvoiceJob::dispatch($purchaseReturnPdfPreviewId, $pdfStatus, $currentCompany, PurchaseReturnTransaction::class);
        while ($pdfStatus->status != PdfDownloadingStatus::COMPLETED && $pdfStatus->status != PdfDownloadingStatus::FAILED) {
            $pdfStatus->refresh();
            usleep(50000);
        }

        if ($pdfStatus->status == PdfDownloadingStatus::COMPLETED) {
            $data['status'] = 'completed';
            $data['downloadRoute'] = route('company.purchase-return-pdf-download', [
                'purchaseReturnPdfDownloadId' => $pdfStatus->id,
                'isView' => 0,
            ]);
            $data['viewRoute'] = $pdfStatus->link;
            $data['emailRoute'] = route('company.purchase-returns-email', ['purchase_return' => $purchaseReturnPdfPreviewId]);
            $data['html'] = view('company.pdf.purchase_pdf.preview')
                ->with($data)
                ->render();
        }

        if ($pdfStatus->status == PdfDownloadingStatus::FAILED) {
            $data['status'] = 'failed';
            $data['message'] = 'Something went wrong. Please try again later.';
        }

        return $this->sendResponse($data, 'Purchase Return PDF Preview screen retrieved successfully');
    }

    public function getPurchaseReturnPdfDownload($purchaseReturnPdfDownloadId, $isView = false)
    {
        if ($isView) {
            $pdfStatus = DownloadPdfStatus::run(PdfDownloadingStatus::PURCHASE_RETURN);
            $currentCompany = getCurrentCompany();

            if (dockerEnabled()) {
                $job = new DownloadInvoiceJob($purchaseReturnPdfDownloadId, $pdfStatus, $currentCompany, PurchaseReturnTransaction::class);
                $job->dispatchSync($purchaseReturnPdfDownloadId, $pdfStatus, $currentCompany, PurchaseReturnTransaction::class);
                $fileName = $job->getFileName();

                $pdfStatus->refresh();

                if ($pdfStatus->status == PdfDownloadingStatus::COMPLETED && $pdfStatus->link == null) {
                    return response($job->getPdfContents(), 200, [
                        'Content-Type' => 'application/pdf',
                        'Content-Disposition' => 'attachment; filename="'.$fileName.'"',
                    ]);
                }
            } else {
                DownloadInvoiceJob::dispatch($purchaseReturnPdfDownloadId, $pdfStatus, $currentCompany, PurchaseReturnTransaction::class);
            }
            $pdfStatus->refresh();

            while ($pdfStatus->status != PdfDownloadingStatus::COMPLETED && $pdfStatus->status != PdfDownloadingStatus::FAILED) {
                $currentTime = Carbon::now();
                while ($pdfStatus->status != PdfDownloadingStatus::COMPLETED && $pdfStatus->status != PdfDownloadingStatus::FAILED) {
                    $pdfStatus->refresh();
                    usleep(50000);
                    $runningTime = Carbon::now();
                    if ($runningTime->diffInSeconds($currentTime) > 60) {
                        $pdfStatus->update([
                            'status' => PdfDownloadingStatus::FAILED,
                        ]);
                        Log::error('Pdf download failed after 60 seconds '.$pdfStatus->id);
                    }
                }
            }

            if ($pdfStatus->status == PdfDownloadingStatus::FAILED) {
                $data['status'] = 'failed';
                $data['message'] = 'Something went wrong. Please try again later.';

                Flash::error('Something went wrong. Please try again later.');

                return redirect()->back()->with('error', $data['message']);
            }

            $client = new Client();
            $response = $client->get($pdfStatus->link, ['stream' => true]);
            $fileContent = $response->getBody()->getContents();

            $headers = [
                'Content-Type' => $response->getHeaderLine('Content-Type'),
                'Content-Disposition' => 'attachment; filename="'.$pdfStatus->name.'"',
            ];

            return response($fileContent, 200, $headers);
        }

        $data = PdfDownloadingStatus::find($purchaseReturnPdfDownloadId);
        $client = new Client();
        $response = $client->get($data->link, ['stream' => true]);
        $fileContent = $response->getBody()->getContents();
        $headers = [
            'Content-Type' => $response->getHeaderLine('Content-Type'),
            'Content-Disposition' => 'attachment; filename="'.$data->name.'"',
        ];

        return response($fileContent, 200, $headers);
    }

    public function getPurchaseReturnPreviewPdfDownload($purchaseReturnId)
    {
        enableDeletedScope();
        $transaction = PurchaseReturnTransaction::whereId($purchaseReturnId)->firstOrFail();
        $currentCompany = Company::with('billingAddress', 'companyTax', 'user', 'mailConfiguration')->findOrFail($transaction->company_id);
        session(['current_company' => $currentCompany]);
        $pdfStatus = DownloadPdfStatus::run(PdfDownloadingStatus::PURCHASE_RETURN);

        if (dockerEnabled()) {
            $job = new DownloadInvoiceJob($purchaseReturnId, $pdfStatus, $currentCompany, PurchaseReturnTransaction::class);
            $job->dispatchSync($purchaseReturnId, $pdfStatus, $currentCompany, PurchaseReturnTransaction::class);
            $fileName = $job->getFileName();

            $pdfStatus->refresh();

            if ($pdfStatus->status == PdfDownloadingStatus::COMPLETED && $pdfStatus->link == null) {
                return response($job->getPdfContents(), 200, [
                    'Content-Type' => 'application/pdf',
                    'Content-Disposition' => 'attachment; filename="'.$fileName.'"',
                ]);
            }
        } else {
            DownloadInvoiceJob::dispatch($purchaseReturnId, $pdfStatus, $currentCompany, PurchaseReturnTransaction::class);
        }
        $pdfStatus->refresh();

        while ($pdfStatus->status != PdfDownloadingStatus::COMPLETED && $pdfStatus->status != PdfDownloadingStatus::FAILED) {
            $pdfStatus->refresh();
            usleep(50000);
        }

        $client = new Client();
        $response = $client->get($pdfStatus->link, ['stream' => true]);
        $fileContent = $response->getBody()->getContents();

        $headers = [
            'Content-Type' => $response->getHeaderLine('Content-Type'),
            'Content-Disposition' => 'attachment; filename="'.$pdfStatus->name.'"',
        ];
        disableDeletedScope();

        return response($fileContent, 200, $headers);
    }

    /**
     * @return View
     */
    public function purchaseReturnCreateDuplicate(PurchaseReturnTransaction $purchaseReturn): \Illuminate\View\View
    {
        $data = [];
        $data['purchaseReturn'] = $purchaseReturn->load([
            'purchaseReturnItems.items',
            'purchaseReturnItems.gst',
            'purchaseReturnLedgers.gst',
            'addresses',
            'supplier.model',
            'tcsLedger.model',
        ]);
        $getCurrentCompany = getCurrentCompany();
        $ledgerSupplier = CompanyGroup::whereCompanyId($getCurrentCompany?->id);
        $data['groupLists'] = getGroupLists();
        $data['groupListsId'] = $ledgerSupplier->where('name', Ledger::SUPPLIER)->first()->id;
        $data['countries'] = getCountries();
        $data['indiaStates'] = getStates(Company::INDIA_COUNTRY_ID);
        $data['updateLedger'] = false;
        $data['locationOfAssets'] = Location::pluck('name', 'id')->toArray();
        $data['brokerMasters'] = Broker::whereCompanyId($getCurrentCompany?->id)->pluck('broker_name', 'id');
        $data['purchaseNumbers'] = getSupplierPurchaseInvoiceNumber($purchaseReturn->supplier_id);
        $data['purchaseReturnConfiguration'] = PurchaseReturnConfiguration::first();
        $data['purchaseReturnTransaction'] = ExpenseReturnTransactionMaster::whereCompanyId($getCurrentCompany?->id)->first();
        $TdsGroups = getParentGroupsValue(Ledger::TAXES_TDS);
        $groupIds = array_merge(array_keys($TdsGroups));
        $data['ledgerOfTds'] = Ledger::whereIn('group_id', $groupIds)->pluck('name', 'id')->toArray();
        $voucherNumber = PurchaseReturnTransaction::orderBy('created_at', 'desc')->first();
        $data['lastVoucherNumber'] = GetPurchaseReturnVoucherNumber::run();
        $data['methodVoucherType'] = $data['purchaseReturnTransaction']->voucher_number ?? ExpenseReturnTransactionMaster::AUTOMATIC;
        $data['companyItemMasterGroups'] = getParentGroupsValue(Ledger::ITEM_DEFAULT_GROUP);
        $data['defaultGroup'] = CompanyGroup::whereName(Ledger::ITEM_DEFAULT_GROUP)->whereName('Default Group')
            ->whereCompanyId(getCurrentCompany()->id)
            ->value('id');
        $data['unitOfMeasurement'] = getItemMasterUnit();
        $data['billingAddress'] = $data['purchaseReturn']->addresses->firstWhere(
            'address_type',
            PurchaseReturnTransaction::BILLING_ADDRESS
        );
        $data['billingStates'] = getStates($data['billingAddress']->country_id);
        $data['billingCities'] = getCities($data['billingAddress']->state_id);
        $data['shippingAddress'] = $data['purchaseReturn']->addresses->firstWhere(
            'address_type',
            PurchaseReturnTransaction::SHIPPING_ADDRESS
        );
        if ($data['shippingAddress']) {
            $data['shippingStates'] = getStates($data['shippingAddress']->country_id);
            $data['shippingCities'] = getCities($data['shippingAddress']->state_id);
        } else {
            $data['shippingStates'] = null;
            $data['shippingCities'] = null;
        }
        $data['previousBillId'] = PurchaseReturnTransaction::where('id', '<', $purchaseReturn->id)->orderByDesc('id')->value('id');
        $data['nextBillId'] = PurchaseReturnTransaction::where('id', '>', $purchaseReturn->id)->orderBy('id')->value('id');

        return view('company.purchase-return.duplicate')->with($data);
    }

    /**
     * @return View
     */
    public function purchaseReturnEmail($purchaseReturn): \Illuminate\View\View
    {
        enableDeletedScope();
        $purchaseReturn = PurchaseReturnTransaction::findOrFail($purchaseReturn);
        $data['purchaseReturn'] = $purchaseReturn->load([
            'purchaseReturnItems',
            'purchaseReturnLedgers',
            'addresses',
            'supplier',
        ]);
        disableDeletedScope();

        return view('company.purchase-return.send-email')->with($data);
    }

    /**
     * @throws FileDoesNotExist
     * @throws FileIsTooBig
     */
    public function sendEmail(Request $request, $purchaseReturn)
    {
        enableDeletedScope();
        $purchaseReturn = PurchaseReturnTransaction::findOrFail($purchaseReturn);
        $data = GetInvoicePDFDataForPurchaseReturn::run($purchaseReturn->id);
        $data = GstCalculationForPdf::run($data, PurchaseReturnTransaction::class);
        // $data = $this->getInvoicePDFData($purchaseReturn->id);
        $input = $request->all();
        //   $data['mailSetting'] = $data['currentCompany']->mailConfiguration;
        //   if (empty($data['mailSetting'])) {

        //       Flash::error('Please enter mail credentials first.');

        //       return redirect()->route('company.purchase-returns.index');
        //   }

        if (isset($input['removed_attachment']) && ! empty($input['removed_attachment'][0])) {
            $cleanedFileArray = explode(',', $input['removed_attachment'][0]);
            $input['attachments'] = array_filter($input['attachments'], function ($file) use ($cleanedFileArray) {
                return ! in_array($file->getClientOriginalName(), $cleanedFileArray);
            });
        }

        if (isset($input['attachments']) && ! empty($input['attachments'])) {
            foreach ($input['attachments'] as $file) {
                $purchaseReturn->addMedia($file)->toMediaCollection(
                    PurchaseReturnTransaction::INVOICE_ATTACHMENT,
                    config('app.media_disc')
                );
            }
        }

        $data['to'] = $input['to'] ?? null;
        $data['cc'] = $input['cc'] ?? null;
        $data['body'] = $input['body'] ?? null;
        $data['subject'] = $input['subject'] ?? null;
        $data['regards'] = $input['regards'] ?? null;
        $data['is_attachment'] = $input['is_attachment'] ?? 1;
        $data['customPaperSize'] = [0, 0, 700, 900];
        $data['fromName'] = getCompanySettings()['from_name'];
        $data['replayToEmail'] = getCompanySettings()['replay_to_email'];

        InvoiceMailJob::dispatch($data, PurchaseReturnTransaction::class);

        Flash::success('Mail Sent Successfully.');
        disableDeletedScope();

        // return redirect()->route('company.purchase-returns.index');
    }

    public function getInvoicePDFData($purchaseReturnId): array
    {
        $data = [];
        $data['taxInvoice'] = 'Voucher No';
        $data['configuration'] = PurchaseReturnConfiguration::first();
        $data['currentCompany'] = getCurrentCompany()?->load('addresses', 'companyTax', 'user', 'mailConfiguration', 'media');
        $data['companyBillingAddress'] = $data['currentCompany']->addresses
            ->where('address_type', Company::BILLING_ADDRESS)->first();
        $data['transaction'] = PurchaseReturnTransaction::with(
            'addresses',
            'supplier',
            'transport',
            'purchaseReturnItems',
            'purchaseReturnLedgers'
        )->whereId($purchaseReturnId)->firstOrFail();
        $data['transactionItems'] = $data['transaction']->purchaseReturnItems->load('items');
        $data['transactionLedgers'] = $data['transaction']->purchaseReturnLedgers->load('ledgers');
        $data['customerDetail'] = $data['transaction']->supplier->load('model');
        $data['ledgerShippingAddress'] = $data['customerDetail']->model->shippingAddress ?? null;
        $addresses = $data['transaction']->addresses;
        $data['fromNameExists'] = CompanySetting::where('key', 'from_name')->exists();
        $data['replyToEmailExists'] = CompanySetting::where('key', 'replay_to_email')->exists();
        $data['billingAddress'] = $addresses->firstWhere('address_type', PurchaseReturnTransaction::BILLING_ADDRESS);
        $data['shippingAddress'] = $addresses->firstWhere('address_type', PurchaseReturnTransaction::SHIPPING_ADDRESS);
        $data['invoiceDate'] = Carbon::Parse($data['transaction']->original_inv_date)->format('d-m-Y');
        $data['voucherDate'] = Carbon::Parse($data['transaction']->voucher_date)->format('d-m-Y');
        $data['invoiceNo'] = $data['transaction']->supplier_purchase_return_number;
        $data['itemType'] = $data['transaction']->pr_item_type;
        $data['dueDate'] = null;
        $data['creditPeriod'] = null;
        if (! empty($data['transaction']->credit_period)) {

            $data['creditPeriod'] = $data['transaction']->credit_period.' '.SaleTransaction::CREDIT_PERIOD_TYPE[$data['transaction']->credit_period_type];

            if ($data['transaction']->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_MONTH) {
                $data['dueDate'] = Carbon::parse($data['transaction']->voucher_date)->addMonths($data['transaction']->credit_period)->format('d-m-Y');
            } elseif ($data['transaction']->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_DAY) {
                $data['dueDate'] = Carbon::parse($data['transaction']->voucher_date)->addDays($data['transaction']->credit_period)->format('d-m-Y');
            }
        } elseif (! empty($data['customerDetail']->model->credit_limit_period)) {

            $data['creditPeriod'] = $data['customerDetail']->model->credit_limit_period.' '.SaleTransaction::CREDIT_PERIOD_TYPE[$data['customerDetail']->model->credit_period_type];

            if ($data['customerDetail']->model->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_MONTH) {
                $data['dueDate'] = Carbon::parse($data['transaction']->voucher_date)->addMonths($data['customerDetail']->model->credit_limit_period)->format('d-m-Y');
            } elseif ($data['customerDetail']->model->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_DAY) {
                $data['dueDate'] = Carbon::parse($data['transaction']->voucher_date)->addDays($data['customerDetail']->model->credit_limit_period)->format('d-m-Y');
            }
        }

        return $data;
    }

    public function storePurchaseReturnTdsEntry(Request $request): JsonResponse
    {
        $input = $request->all();
        $transactionId = $request->purchaseReturnTransactionId;
        $passTdsEntry = (int) ($input['pass_tds_entry'] ?? 0);

        $tdsEntry = [
            'pass_tds_entry' => $passTdsEntry,
            'tds_pan' => $passTdsEntry != 0 ? $input['tds_pan'] : null,
            'tds_taxable_value' => $passTdsEntry != 0 ? $input['tds_taxable_value'] : null,
            'ledger_of_tds' => $passTdsEntry != 0 ? $input['ledger_of_tds'] : null,
            'tds_rate' => $passTdsEntry != 0 ? $input['tds_rate'] : null,
            'tds_amount' => $passTdsEntry != 0 ? $input['tds_amount'] : null,
        ];
        $data = PurchaseReturnTransaction::whereId($transactionId)->first();
        $data->update($tdsEntry);

        updateFieldsValue($data);

        return $this->sendResponse($data, 'TDS Entry store Successfully');
    }

    public function checkPurchaseReturnExists(PurchaseReturnTransaction $purchaseReturn)
    {
        $transactionsData = CheckExistPurchaseReturnTransactionData::run($purchaseReturn);

        if (isset($transactionsData['transactionsIsCashMode']) && $transactionsData['transactionsIsCashMode']) {
            return $transactionsData;
        }

        $data['checkTransactionExists'] = $transactionsData['checkTransactionExists'];
        $data['transactionName'] = $transactionsData['checkTransactionExists'];
        $data['html'] = view('company.sale.delete_sale_associated_transaction_list')->with($transactionsData)->render();

        return $data;
    }

    /**
     * @return Response|BinaryFileResponse
     */
    public function export(Request $request)
    {
        $input = $request->all();
        $type = $input['type'];
        // $purchaseReturnTransactionCacheKey = generateCacheKey('purchase_return_transactions');
        $purchaseReturnTransactionFilterCacheKey = generateCacheKey('purchase_return_transactions_filter');
        $company = getCurrentCompany();
        $data = [];
        // $data['data'] = Cache::get($purchaseReturnTransactionCacheKey);
        $filters = Cache::get($purchaseReturnTransactionFilterCacheKey);
        $data = $filters;

        $queryData = PurchaseReturnTransaction::select('purchase_return_transactions.*')
            ->whereBetween('voucher_date', [$data['data']['startDate'], $data['data']['endDate']])
            ->when(! empty($data['data']['partyName']), function ($q) use ($data) {
                $q->where('supplier_id', $data['data']['partyName']);
            })
            ->when(! empty($data['data']['paymentStatus']), function ($q) use ($data) {
                $q->where('payment_status', $data['data']['paymentStatus']);
            });

        $queryData = $queryData->sorting($data['data']['defaultSorting'])->get();

        /** @var PurchaseReturnTransactionRepository $purchaseReturnTranRepo */
        $purchaseReturnTranRepo = App::make(PurchaseReturnTransactionRepository::class);
        $data['data']['data'] = $purchaseReturnTranRepo->prepareDataForTable($queryData);

        $data['company'] = $company;
        $data['companyAddress'] = $company?->addresses->where('address_type', Company::BILLING_ADDRESS)->first();
        $data['durationDate'] = Carbon::parse($input['start_date'])->format('d-m-Y').' to '.Carbon::parse($input['end_date'])->format('d-m-Y');

        if ($type == 'excel') {
            $fileName = 'purchaseReturnTransaction'.Carbon::now()->format('d-m-Y').'.xlsx';

            $response = Excel::download(new PurchaseReturnTransactionExport($data), $fileName);
            ob_end_clean();

            return $response;

            // return (new PurchaseReturnTransactionExport($data))->download($fileName);
        }

        $fileName = 'purchaseReturnTransaction'.Carbon::now()->format('d-m-Y').'.pdf';

        return Pdf::loadView('pdf.purchase-return-transaction', $data)
            ->setPaper('a4', 'landscape')
            ->download($fileName);
    }

    public function importPurchaseReturn(Request $request)
    {
        ini_set('max_execution_time', 3600000000);
        ini_set('memory_limit', '500M'); // or you could use nM

        $request->validate([
            'purchase_return_excel_file' => 'required|mimes:xlsx,xls',
        ]);

        $type = 'purchase_return';
        $transactionErrorCacheKey = generateCacheKey($type.'_transaction_import_error');
        Cache::forget($transactionErrorCacheKey);

        $input = $request->all();
        if ($input['transaction_type'] == PurchaseReturnTransaction::ITEM_INVOICE) {
            $import = new PurchaseReturnItemInvoiceImport();
        } else {
            $import = new PurchaseReturnAccountingInvoiceImport();
        }
        $import->import($input['purchase_return_excel_file']);
        $data = $import;

        if (! empty($import->importErrors['missing_voucher_number'])) {
            return $this->sendError($import->importErrors['missing_voucher_number']);
        }
        if (! empty($import->importErrors['field_missing'])) {
            return $this->sendError($import->importErrors['field_missing']);
        }
        if (! empty($import->importErrors['empty_excel'])) {
            return $this->sendError($import->importErrors['empty_excel']);
        }
        if (! empty($import->importErrors['wrong_excel'])) {
            return $this->sendError($import->importErrors['wrong_excel']);
        }
        if (! empty($import->importErrors['unsupported_formula'])) {
            return $this->sendError($import->importErrors['unsupported_formula']);
        }
        $modalIsOpen = false;
        if ($import->importErrors) {
            $modalIsOpen = true;
        }
        Cache::put($transactionErrorCacheKey, $data);
        $html = view('company.import-transaction-error-modal.import_error_list', compact('data', 'type'))->render();

        $importedInvoice = $data->totalInvoice - $data->notImportedInvoice;
        $message = '';
        if ($importedInvoice != 0) {
            $message = 'Purchase return transaction imported successfully.';
        }

        return $this->sendResponse(['modalIsOpen' => $modalIsOpen, 'html' => $html], $message);
    }

    public function bulkDelete(Request $request)
    {
        $input = $request->all();

        $purchaseReturns = PurchaseReturnTransaction::whereIn('id', $input['ids'])->get();
        $showMode = true;
        $data = [];
        $data['alreadyUsed'] = [];
        $data['lockedTransaction'] = [];
        $lockDate = getTransactionsLockDate()['expense'] ?? null;
        foreach ($purchaseReturns as $purchaseReturn) {
            $isLocked = ! empty($lockDate) && Carbon::parse($lockDate)->greaterThanOrEqualTo($purchaseReturn->voucher_date);
            $transactionsData = CheckExistPurchaseReturnTransactionData::run($purchaseReturn);
            if ($isLocked) {
                $data['lockedTransaction'][] = $purchaseReturn->voucher_number;
            } elseif ($transactionsData['checkTransactionExists']) {
                $data['alreadyUsed'][] = $transactionsData['purchaseReturn']->voucher_number;
            } else {
                DeletePurchaseReturnTransaction::run($purchaseReturn);
            }
        }

        if (empty($data['alreadyUsed']) && empty($data['lockedTransaction'])) {
            $showMode = false;
        }

        $html = '';
        if (! empty($data)) {
            $html = view('company.item-master.list-view', compact('data'))->render();
        }

        return $this->sendResponse(['html' => $html, 'showMode' => $showMode], 'Purchase Return deleted successfully');
    }

    public function getPurchaseReturnItemList(Request $request)
    {
        $input = $request->all();
        $input['purchaseReturnConfiguration'] = PurchaseReturnConfiguration::toBase()->first();
        $html = view('company.purchase-return.item-invoice-list')->with($input)->render();

        return $this->sendResponse(['html' => $html], 'Item list generated successfully.');
    }

    public function getPurchaseReturnAccountingLedgerList(Request $request)
    {
        $input = $request->all();

        $html = view('company.purchase-return.item-accounting-ledger-list')->with($input)->render();

        return $this->sendResponse(['html' => $html], 'Ledger list generated successfully.');
    }

    public function purchaseReturnBulkDownload(Request $request)
    {
        $input = $request->all();

        $fileName = 'purchase-return-transaction-'.strtotime('now').'.pdf';
        $notification = DownloadBulkPdf::run(Notification::DOWNLOAD, $fileName);
        $currentCompany = getCurrentCompany();

        BulkPdfExportJob::dispatch($input['ids'], $notification, $currentCompany, PurchaseReturnTransaction::class);

        return true;

    }

    public function downloadAttachment(PurchaseReturnTransaction $purchaseReturn): Response|BinaryFileResponse
    {
        $mediaCollection = $purchaseReturn->media;

        if ($mediaCollection->isEmpty()) {
            abort(404, 'No documents found.');
        }

        $tempDir = storage_path('app/temp');
        if (! File::exists($tempDir)) {
            File::makeDirectory($tempDir, 0755, true);
        }

        if ($mediaCollection->count() === 1) {
            $mediaItem = $mediaCollection->first();
            $mediaPath = $mediaItem->getPath();

            if (config('app.media_disc') === 'public') {
                $mediaPath = Str::after($mediaItem->getUrl(), '/uploads');
            }

            $file = Storage::disk(config('app.media_disc'))->get($mediaPath);

            return response($file, 200, [
                'Content-Type' => $mediaItem->mime_type,
                'Content-Description' => 'File Transfer',
                'Content-Disposition' => "attachment; filename={$mediaItem->file_name}",
                'filename' => $mediaItem->file_name,
            ]);
        }

        $zipFileName = 'documents_'.time().'.zip';
        $zipPath = storage_path("app/temp/{$zipFileName}");

        $zip = new ZipArchive();
        if ($zip->open($zipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== true) {
            abort(500, 'Could not create zip file.');
        }

        foreach ($mediaCollection as $mediaItem) {
            $mediaPath = $mediaItem->getPath();
            if (config('app.media_disc') === 'public') {
                $mediaPath = Str::after($mediaItem->getUrl(), '/uploads');
            }

            $fileContent = Storage::disk(config('app.media_disc'))->get($mediaPath);
            $fileName = pathinfo($mediaItem->file_name, PATHINFO_FILENAME)
                .'_'.uniqid()
                .'.'.pathinfo($mediaItem->file_name, PATHINFO_EXTENSION);
            $zip->addFromString($fileName, $fileContent);
        }

        $zip->close();

        return response()->download($zipPath)->deleteFileAfterSend(true);
    }
}
