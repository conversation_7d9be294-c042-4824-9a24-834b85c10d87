<?php

namespace App\Http\Controllers\API\v2\CustomField;

use App\Actions\CustomFields\DeleteCustomFieldAction;
use App\Actions\CustomFields\EditDetailOfCustomFieldAction;
use App\Actions\CustomFields\GetEnumValueOfFieldFromTableAction;
use App\Actions\CustomFields\StoreCustomFieldAction;
use App\Actions\CustomFields\UpdateCustomFieldAction;
use App\Actions\CustomFields\UpdateStatusOfCustomFieldAction;
use App\Http\Controllers\API\v1\AppBaseAPIController;
use App\Http\Requests\ReactAPI\CustomFieldsAPIRequest;
use App\Models\TransactionCustomField;
use App\Models\TransactionCustomFieldOption;
use Illuminate\Http\Request;

class CustomFieldsAPIController extends AppBaseAPIController
{
    public function getInputTypes()
    {
        $tableName = TransactionCustomField::make()->getTable();

        $data = GetEnumValueOfFieldFromTableAction::run($tableName, 'input_type');

        $data = collect($data)->reject(function ($item) {
            return in_array($item, ['radio', 'textarea']);
        })->values()->all();

        return $this->sendResponse($data, 'Input Types retrieved successfully.');
    }

    public function store(CustomFieldsAPIRequest $request)
    {
        $input = $request->all();

        $customFields = StoreCustomFieldAction::run($input);

        $customFields['options'] = TransactionCustomFieldOption::where('custom_field_id', $customFields['id'])->get() ?? [];
        $customFields['types'] = $input['types'];
        $customFields['is_show_in_print'] = $input['is_show_in_print'];

        return $this->sendResponse($customFields, 'Custom Field created successfully.');
    }

    public function updateStatus(Request $request)
    {
        $request->validate([
            'custom_field_id' => 'required',
            'is_enabled' => 'required',
            'type' => 'required',
        ]);

        $input = $request->all();

        UpdateStatusOfCustomFieldAction::run($input);

        return $this->sendSuccess('Custom Field Updated successfully.');
    }

    public function edit(Request $request, $id)
    {
        $input = $request->all();

        $data = EditDetailOfCustomFieldAction::run($input, $id);

        return $this->sendResponse($data, 'Custom Field Get successfully.');
    }

    public function update(CustomFieldsAPIRequest $request, $id)
    {
        $input = $request->all();

        $customFields = UpdateCustomFieldAction::run($input, $id);

        $customFields['options'] = TransactionCustomFieldOption::where('custom_field_id', $customFields['id'])->get() ?? [];
        $customFields['types'] = $input['types'];
        $customFields['is_show_in_print'] = $input['is_show_in_print'];

        return $this->sendResponse($customFields, 'Custom Field Updated successfully.');
    }

    public function delete($id)
    {
        DeleteCustomFieldAction::run($id);

        return $this->sendSuccess('Custom Field Deleted successfully.');
    }
}
