<?php

namespace App\Http\Controllers\API\v2\Gstr2BReconciliation;

use App\Actions\GetGstr2BReconciliation\FetchGSTR2BData;
use App\Actions\GetGstr2BReconciliation\GetGstr2BReconciliationDetailsAction;
use App\Actions\GetGstr2BReconciliation\GetGstr2BReconciliationSummary;
use App\Exports\Gstr2BReconciliationExport;
use App\Http\Controllers\API\v1\AppBaseAPIController;
use App\Models\ExpenseDebitNoteTransaction;
use App\Models\Gstr2bSummaryTransaction;
use App\Models\GstrLogin;
use App\Models\NotificationTemplate;
use App\Models\PurchaseReturnTransaction;
use App\Models\PurchaseTransaction;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class Gstr2BReconciliationAPIController extends AppBaseAPIController
{
    public function index(Request $request)
    {
        $input = $request->all();
        $filingPeriod = ! empty($input['end_date']) ? Carbon::parse($input['end_date'])->format('mY') : null;
        $response = GetGstr2BReconciliationSummary::run($input);

        $getGstr2BReconciliationSummary = Gstr2bSummaryTransaction::where('return_period', $filingPeriod)->get();

        $showFetchingBtn = false;
        if (! empty($getGstr2BReconciliationSummary)) {
            $showFetchingBtn = true;
        }

        $lastRecord = Gstr2bSummaryTransaction::latest()->first();

        if ($lastRecord) {
            $updatedAt = $lastRecord->updated_at;
        } else {
            $updatedAt = null;
        }

        return $this->sendResponse(['response' => $response, 'showFetchingBtn' => $showFetchingBtn, 'updated_at' => $updatedAt], 'Gstr2B Reconciliation retrieved successfully.');
    }

    public function fetchData(Request $request)
    {
        $input = $request->all();

        FetchGSTR2BData::run($input);

        return $this->sendSuccess('Gstr2B Reconciliation Fetched successfully.');
    }

    public function reconciliation(Request $request)
    {
        $input = $request->all();

        $response = GetGstr2BReconciliationDetailsAction::run($input);

        return $this->sendResponse($response, 'Gstr2B Reconciliation retrieved successfully.');
    }

    public function sendMail($id, $type)
    {
        $notificationTemplate = NotificationTemplate::where('template_name', NotificationTemplate::GSTR2B_RECONCILIATION)->first();
        $fromName = isset(getCompanySettings()['from_name']) ? getCompanySettings()['from_name'] : '';
        $replyToEmail = isset(getCompanySettings()['replay_to_email']) ? getCompanySettings()['replay_to_email'] : '';

        if (empty($fromName) || empty($replyToEmail)) {

            return redirect()->route('company.email-and-whatsapp-configuration-setting.index', ['section' => 'emailConfiguration']);
        }

        $getCurrentCompany = getCurrentCompany();

        if ($type == 'purchase') {
            $transaction = PurchaseTransaction::whereId($id)->first();

            $data = $transaction->load('purchaseTransactionItems', 'purchaseTransactionLedger', 'supplier');
            $invoiceNumber = $data->sale_number;
        } elseif ($type == 'purchaseReturn') {
            $transaction = PurchaseReturnTransaction::whereId($id)->first();

            $data = $transaction->load('purchaseReturnItems', 'purchaseReturnLedgers', 'supplier');
            $invoiceNumber = $data->supplier_purchase_return_number;
        } elseif ($type == 'expenseDebitNote') {
            $transaction = ExpenseDebitNoteTransaction::whereId($id)->first();

            $data = $transaction->load('debitNoteItems', 'debitNoteLedgers', 'supplier');
            $invoiceNumber = $data->supplier_purchase_return_number;
        }

        $search = [
            '{ party_name }',
            '{ our_company_name }',
            '{ invoice_number }',
            '{ month_period }',
            '{ invoice_date }',
        ];

        $replace = [
            $data->supplier->name,
            $getCurrentCompany->trade_name,
            $invoiceNumber,
            Carbon::parse($data['date_of_invoice'])->format('mY'),
            Carbon::parse($data['date_of_invoice'])->format('d-m-Y'),
        ];

        $notificationTemplate['subject'] = str_replace($search, $replace, $notificationTemplate->subject);
        $notificationTemplate['body'] = str_replace($search, $replace, $notificationTemplate->body);
        $notificationTemplate['regards'] = str_replace($search, $replace, $notificationTemplate->regards);

        return view('company.Gstr2b.send-mail')->with(['sale' => $data, 'notificationTemplate' => $notificationTemplate, 'type' => $type]);
    }

    public function checkGstLogin()
    {
        $gstLoin = GstrLogin::first();

        return $this->sendResponse($gstLoin, 'Gstr Login data fetching successfully');
    }

    public function downloadExcel(Request $request)
    {
        $input = $request->all();
        $response = GetGstr2BReconciliationDetailsAction::run($input);

        return Excel::download(new Gstr2BReconciliationExport($response), 'Gstr2BReconciliation.xlsx');
    }
}
