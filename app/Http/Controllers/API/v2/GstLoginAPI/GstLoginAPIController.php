<?php

namespace App\Http\Controllers\API\v2\GstLoginAPI;

use App\Http\Controllers\API\v1\AppBaseAPIController;
use App\Models\GstrLogin;
use App\Services\FileGstService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class GstLoginAPIController extends AppBaseAPIController
{
    /** @var FileGstService */
    public mixed $fileGstService;

    public function __construct()
    {
        $this->fileGstService = new FileGstService();
    }

    public function login(Request $request)
    {
        $input = $request->all();
        $company = getCurrentCompany();
        $gstin = $company->companyTax->gstin;
        $gstrLogin = GstrLogin::whereCompanyId($company->id)->first();
        $statecd = substr($gstin, 0, 2);
        $headers = [
            'username' => $input['user_name'] ?? $gstrLogin->username ?? null,
            'otp' => $input['otp'] ?? '000000',
            'gstin' => $gstin,
            'action' => 'B2B',
            'statecd' => $statecd,
            'retperiod' => '032025',
        ];

        $fileGst = $this->fileGstService;

        $data = [];
        $data['hideModal'] = false;

        if (! empty($gstrLogin)) {
            if (! isset($input['resend'])) {
                if (! empty($input['otp'])) {
                    $data['api'] = $fileGst->getGstR1Data($headers);
                    if ($data['api']['errorCode'] == 'RETOTPREQUEST') {
                        $message = 'Invalid OTP. A new OTP has been sent to your registered mobile number or email.';

                        return $this->sendError($message);
                    }
                    $gstrLogin = $gstrLogin->update([
                        'username' => $input['user_name'],
                        'status' => GstrLogin::CONNECTED,
                        'otp' => $input['otp'],
                    ]);
                    $data['hideModal'] = true;
                }
            }
        } else {
            $gstrLogin = GstrLogin::create([
                'company_id' => $company->id,
                'username' => $input['user_name'],
                'status' => GstrLogin::DISCONNECTED,
            ]);
        }

        $data['api'] = $fileGst->getGstR1Data($headers);
        Log::channel('gstr1')->alert('GSTR1Login', $data['api']);
        if (isset($data['api']) && isset($data['api']['errorCode']) && ($data['api']['errorCode'] == 'RETOTPREQUEST')) {
            $message = 'OTP send to registered mobile/email';

            return $this->sendResponse($data, $message);
        }

        if (! empty($data['api']) && isset($data['api']['errorCode'])) {
            $message = $data['api']['message'];

            return $this->sendError($message);
        }

        if ($data['hideModal'] || ! empty($data['api'])) {
            $gstrLogin->update([
                'status' => GstrLogin::CONNECTED,
                'username' => $input['user_name'],
                'otp' => $input['otp'] ?? 000000,
            ]);

            $data['hideModal'] = true;
            $getCurrentFinancialYearDetails = getCurrentFinancialYearDetails();
            $fullYear = $getCurrentFinancialYearDetails['currentFinancialYear']; // e.g. "2025 - 2026"

            [$start, $end] = explode(' - ', $fullYear);
            $shortFormat = $start.'-'.substr($end, -2);
            $headers = [
                'gstin' => $gstin,
                'year' => $shortFormat,
            ];

            $fillingType = $fileGst->getGstr1Preference($headers);
            if ($fillingType['success'] == 'true') {
                $gstrLogin->update([
                    'filling_type' => $fillingType['result']['response'][0]['preference'],
                ]);
            }

            return $this->sendResponse($data, 'GST login successfully');
        }

        $message = $data['api']['message'];

        return $this->sendError($message);

        return $this->sendResponse($input, 'Login Successfully.');
    }
}
