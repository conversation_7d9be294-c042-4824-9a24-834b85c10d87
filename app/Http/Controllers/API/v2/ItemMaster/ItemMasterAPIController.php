<?php

namespace App\Http\Controllers\API\v2\ItemMaster;

use App\Actions\CommonAction\GetFinancialYearOpeningBalanceDetails;
use App\Actions\CommonActionMobile\GetItemListAction;
use App\Actions\v1\ItemMaster\StoreOrUpdateItemAction;
use App\Http\Controllers\API\v1\AppBaseAPIController;
use App\Http\Requests\ReactAPI\ItemMasterAPIRequest;
use App\Models\Master\ItemMaster;
use App\Models\User;
use Illuminate\Http\Request;

class ItemMasterAPIController extends AppBaseAPIController
{
    public function index(Request $request)
    {
        $input = $request->all();
        $response = GetItemListAction::run($input);

        return $this->sendResponse($response, 'Items Fetched Successfully.');
    }

    public function create(ItemMasterAPIRequest $request)
    {
        $input = $request->all();
        $input['via_api'] = User::VIA_API_MOBILE;

        $itemExist = ItemMaster::whereRaw('LOWER(`item_name`) = ? ', [trim(strtolower($input['item_name']))])->exists();

        if ($itemExist) {
            return $this->sendError('The item name has already been taken.');
        }

        if (! empty($input['sku'])) {
            $itemExist = ItemMaster::where('sku', $input['sku'])->exists();
            if ($itemExist) {
                return $this->sendError('The item sku has already been taken.');
            }
        }

        $response = StoreOrUpdateItemAction::run($input, StoreOrUpdateItemAction::STORE);

        return $this->sendResponse($response, 'Item Master Created Successfully.');
    }

    public function edit($id)
    {
        $itemMaster = ItemMaster::with('model.unitOfMeasurement')->findOrFail($id);
        // TO_COMMENT =>
        $response['itemMaster'] = $itemMaster;
        $response['item_master'] = $itemMaster;

        if ($itemMaster->item_type == ItemMaster::ITEM_MASTER_GOODS) {
            $openingBalanceDetails = GetFinancialYearOpeningBalanceDetails::run($itemMaster->id, true);
            // TO_COMMENT =>
            $response['openingBalanceDetails'] = $openingBalanceDetails;
            $response['opening_balance_details'] = $openingBalanceDetails;
        }

        return $this->sendResponse($response, 'Item Master Fetched Successfully.');
    }

    public function update(ItemMasterAPIRequest $request, $id)
    {
        $input = $request->all();
        $input['via_api'] = User::VIA_API_MOBILE;

        $itemMaster = ItemMaster::with('model')->findOrFail($id);

        $itemExist = ItemMaster::whereRaw('LOWER(`item_name`) = ? ', [trim(strtolower($input['item_name']))])
            ->whereNot('id', $itemMaster->id)
            ->exists();

        if ($itemExist) {
            return $this->sendError('The item name has already been taken.');
        }

        if (! empty($input['sku'])) {
            $itemExist = ItemMaster::where('sku', $input['sku'])->whereNot('id', $itemMaster->id)->exists();
            if ($itemExist) {
                return $this->sendError('The item sku has already been taken.');
            }
        }

        $response = StoreOrUpdateItemAction::run($input, StoreOrUpdateItemAction::UPDATE, $itemMaster);

        return $this->sendResponse($response, 'Item Master Updated Successfully.');
    }
}
