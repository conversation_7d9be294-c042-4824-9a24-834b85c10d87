<?php

namespace App\Http\Controllers\API\v1;

use App\Actions\AccountMaster\StoreCompanyGroup;
use App\Actions\AccountMaster\UpdateCompanyGroup;
use App\Actions\Groups\GetAllGroupsAction;
use App\Http\Resources\v1\CompanyGroupCollection;
use App\Http\Resources\v1\CompanyGroupResource;
use App\Models\CompanyGroup;
use App\Models\User;
use App\Repositories\API\v1\CompanyGroupRepository;
use App\Rules\CheckLedgerGroupRule;
use Illuminate\Http\Request;

class CompanyGroupAPIController extends AppBaseAPIController
{
    public CompanyGroupRepository $companyGroupRepository;

    /**
     * CompanyGroupRepository constructor.
     */
    public function __construct(CompanyGroupRepository $companyGroupRepository)
    {
        $this->companyGroupRepository = $companyGroupRepository;
    }

    public function index(Request $request)
    {
        $input = $request->all();

        $data = GetAllGroupsAction::run($input);

        CompanyGroupResource::usingWithCollection();

        return new CompanyGroupCollection($data, 'Company Group retrieved successfully.');
    }

    public function store(Request $request)
    {
        request()->validate([
            'name' => 'required|unique:company_groups,name,NULL,id,company_id,'.getCurrentCompany()->id,
            'parent_id' => ['required', new CheckLedgerGroupRule()],
        ]);

        $input = $request->all();
        $input['via_api'] = User::VIA_API_MOBILE;

        $data = StoreCompanyGroup::run($input);

        return $this->sendResponse($data, 'Company Group Created Successfully');
    }

    public function edit(Request $request)
    {
        $companyGroupId = $request->company_group;
        $companyGroup = $this->companyGroupRepository->whereId($companyGroupId)->get();

        CompanyGroupResource::usingWithCollection();

        return new CompanyGroupCollection($companyGroup, 'Company Group retrieved successfully.');
    }

    public function update(Request $request)
    {
        request()->validate([
            'name' => 'required|unique:company_groups,name,'.$request->company_group.',id,company_id,'.getCurrentCompany()->id,
            'parent_id' => ['required', new CheckLedgerGroupRule()],
        ]);

        $companyGroupId = $request->company_group;

        $companyParentGroupIds = CompanyGroup::whereParentId($companyGroupId)->pluck('id')->toArray();
        if ($companyGroupId == $request->parent_id || in_array($request->parent_id, $companyParentGroupIds)) {

            return $this->sendError('Parent group can not be same or child group.');
        }

        $input = $request->all();
        $input['via_api'] = User::VIA_API_MOBILE;

        UpdateCompanyGroup::run($input, CompanyGroup::findOrFail($companyGroupId));

        return $this->sendSuccess('Company Group updated successfully.');
    }

    public function destroy(Request $request)
    {
        $companyGroupId = $request->company_group;

        $group = CompanyGroup::whereId($companyGroupId)->first();

        if ($group->is_default_group) {

            return $this->sendError("Group can't be deleted.");
        }

        $recordExists = checkGroupIsExists($group->id);

        if ($recordExists) {
            return $this->sendError('This group can not be deleted because this group is used.');
        }

        $group->delete();

        return $this->sendSuccess('Company Group deleted successfully.');
    }

    public function getAccountMasterLedgerGroup()
    {

        $list = $this->companyGroupRepository->getAccountMasterLedgerGroup();

        CompanyGroupResource::usingWithOutJsonResourceful();

        return new CompanyGroupResource($list, 'Account Master Ledger retrieved successfully');
    }
}
