<?php

namespace App\Http\Controllers;

use App\Actions\AuditTrail\CreateAuditTrailEvent;
use App\Exports\InputTaxRegisterReportExport;
use App\Models\Company;
use App\Models\ExpenseCreditNoteItemTransaction;
use App\Models\ExpenseCreditNoteLedgerTransaction;
use App\Models\ExpenseCreditNoteTransaction;
use App\Models\ExpenseDebitNoteItemTransaction;
use App\Models\ExpenseDebitNoteLedgerTransaction;
use App\Models\ExpenseDebitNoteTransaction;
use App\Models\PurchaseItemTransaction;
use App\Models\PurchaseLedgerTransaction;
use App\Models\PurchaseReturnItemTransaction;
use App\Models\PurchaseReturnLedgerTransaction;
use App\Models\PurchaseReturnTransaction;
use App\Models\PurchaseTransaction;
use Barryvdh\DomPDF\Facade\Pdf as PDF;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class InputTaxRegisterReportController extends AppBaseController
{
    /**
     * @return View
     */
    public function index(Request $request): \Illuminate\View\View
    {
        return view('company.input-tax-register-report.index');
    }

    public function getInputTaxRegisterData($input): array
    {
        $company = getCurrentCompany();
        $data = [];

        // For Purchase Transactions
        $data['purchaseTransactionItems'] = PurchaseItemTransaction::with('purchaseTransaction')
            ->whereHas('purchaseTransaction', function (Builder $q) use ($company, $input) {
                $q->where('company_id', $company->id)
                    ->whereBetween('voucher_date', [$input['start_date'], $input['end_date']]);
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $q->where('supplier_ledger_id', $input['party_name']);
                }
            })->get();
        $data['purchaseTransactionAdditionalCharges'] = PurchaseTransaction::whereBetween('voucher_date',
            [$input['start_date'], $input['end_date']])
            ->with(['additionalCharges', 'supplier'])
            ->whereHas('additionalCharges')
            ->where(function ($query) use ($input) {
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $query->where('supplier_ledger_id', $input['party_name']);
                }
            })->get();

        $data['purchaseTransactionLedgers'] = PurchaseLedgerTransaction::with('purchaseTransaction')
            ->whereHas('purchaseTransaction', function (Builder $q) use ($company, $input) {
                $q->where('company_id', $company->id)
                    ->whereBetween('voucher_date', [$input['start_date'], $input['end_date']]);
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $q->where('supplier_ledger_id', $input['party_name']);
                }
            })->get();

        // For PurchaseReturn Transactions
        $data['purchaseReturnTransactionItems'] = PurchaseReturnItemTransaction::with('purchaseReturnTransaction')
            ->whereHas('purchaseReturnTransaction', function (Builder $q) use ($company, $input) {
                $q->where('company_id', $company->id)
                    ->whereBetween('voucher_date', [$input['start_date'], $input['end_date']]);
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $q->where('supplier_id', $input['party_name']);
                }
            })->get();
        $data['purchaseReturnTransactionAdditionalCharges'] = PurchaseReturnTransaction::whereBetween('voucher_date',
            [$input['start_date'], $input['end_date']])
            ->with(['additionalCharges', 'supplier'])
            ->whereHas('additionalCharges')
            ->where(function ($query) use ($input) {
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $query->where('supplier_id', $input['party_name']);
                }
            })->get();

        $data['purchaseReturnTransactionLedgers'] = PurchaseReturnLedgerTransaction::with('purchaseReturnTransaction')
            ->whereHas('purchaseReturnTransaction', function (Builder $q) use ($company, $input) {
                $q->where('company_id', $company->id)
                    ->whereBetween('voucher_date', [$input['start_date'], $input['end_date']]);
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $q->where('supplier_id', $input['party_name']);
                }
            })->get();

        // For Expense Debit Note Transactions
        $data['expenseDebitNoteTransactionItems'] = ExpenseDebitNoteItemTransaction::with('expenseDebitNotesTransaction')
            ->whereHas('expenseDebitNotesTransaction', function (Builder $q) use ($company, $input) {
                $q->where('company_id', $company->id)
                    ->whereBetween('voucher_date', [$input['start_date'], $input['end_date']]);
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $q->where('supplier_id', $input['party_name']);
                }
            })->get();
        $data['expenseDebitNoteTransactionAdditionalCharges'] = ExpenseDebitNoteTransaction::whereBetween('voucher_date',
            [$input['start_date'], $input['end_date']])
            ->with(['additionalCharges', 'supplier'])
            ->whereHas('additionalCharges')
            ->where(function ($query) use ($input) {
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $query->where('supplier_id', $input['party_name']);
                }
            })->get();

        $data['expenseDebitNoteTransactionLedgers'] = ExpenseDebitNoteLedgerTransaction::with('expenseDebitNotesTransaction')
            ->whereHas('expenseDebitNotesTransaction', function (Builder $q) use ($company, $input) {
                $q->where('company_id', $company->id)
                    ->whereBetween('voucher_date', [$input['start_date'], $input['end_date']]);
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $q->where('supplier_id', $input['party_name']);
                }
            })->get();

        // For Expense Credit Note Transactions
        $data['expenseCreditNoteTransactionItems'] = ExpenseCreditNoteItemTransaction::with('expenseCreditNotesTransaction')
            ->whereHas('expenseCreditNotesTransaction', function (Builder $q) use ($company, $input) {
                $q->where('company_id', $company->id)
                    ->whereBetween('voucher_date', [$input['start_date'], $input['end_date']]);
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $q->where('supplier_id', $input['party_name']);
                }
            })->get();
        $data['expenseCreditNoteTransactionAdditionalCharges'] = ExpenseCreditNoteTransaction::whereBetween('voucher_date',
            [$input['start_date'], $input['end_date']])
            ->with(['additionalCharges', 'supplier'])
            ->whereHas('additionalCharges')
            ->where(function ($query) use ($input) {
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $query->where('supplier_id', $input['party_name']);
                }
            })->get();

        $data['expenseCreditNoteTransactionLedgers'] = ExpenseCreditNoteLedgerTransaction::with('expenseCreditNotesTransaction')
            ->whereHas('expenseCreditNotesTransaction', function (Builder $q) use ($company, $input) {
                $q->where('company_id', $company->id)
                    // ->where('is_gst_na', '=', 0)
                    ->whereBetween('voucher_date', [$input['start_date'], $input['end_date']]);
                if (isset($input['party_name']) && $input['party_name'] != 0) {
                    $q->where('supplier_id', $input['party_name']);
                }
            })->get();

        $dataItem = [];

        /** @var PurchaseItemTransaction $item */
        // Purchase Item Transactions
        foreach ($data['purchaseTransactionItems'] as $item) {

            $purchaseTransaction = $item->purchaseTransaction;
            $gstRate = $item->gst_tax_percentage == 0 ? ($purchaseTransaction->is_gst_na ? 'NA' : 0) : $item->gst_tax_percentage;

            $dataItem[] = [
                'party_name' => $purchaseTransaction->supplier->name,
                'ledger_id' => $purchaseTransaction->supplier_ledger_id,
                'gstin' => ! empty($purchaseTransaction->gstin) ? $purchaseTransaction->gstin : '',
                'invoice_number' => $purchaseTransaction->sale_number,
                'invoice_date' => Carbon::parse($purchaseTransaction->date_of_invoice)->format('d-m-Y'),
                'voucher_number' => $purchaseTransaction->voucher_number,
                'voucher_date' => Carbon::parse($purchaseTransaction->voucher_date)->format('d-m-Y'),
                'transaction_type' => 'Purchase',
                'ledger_name' => ! empty($item->ledger_id) ? $item->ledger->name : '',
                'item_name' => $item->items->item_name,
                'quantity' => $item->quantity,
                'unit_of_measurement' => $item->unit->full_name,
                'rate_per_unit' => $item->rpu_without_gst,
                'hsn_code' => $item->hsn_code ?? $item->items->model->hsn_sac_code ?? '',
                'taxable_value' => $item->taxable_value,
                'gst_rate' => $gstRate,
                'cgst' => ! empty($item->classification_cgst_tax) ? $item->classification_cgst_tax : 0,
                'sgst' => ! empty($item->classification_sgst_tax) ? $item->classification_sgst_tax : 0,
                'igst' => ! empty($item->classification_igst_tax) ? $item->classification_igst_tax : 0,
                'cess_amount' => ! empty($item->cess_amount) ? $item->cess_amount : 0,
                'invoice_amount' => $purchaseTransaction->grand_total,
                'itc' => ! empty($item->classification_is_itc_applicable) ? $item->classification_is_itc_applicable : 0,
                'rcm_yes_or_no' => ! empty($item->classification_is_rcm_applicable) ? $item->classification_is_rcm_applicable : 0,
                'invoice_type' => PurchaseTransaction::PURCHASES,
            ];
        }
        foreach ($data['purchaseTransactionAdditionalCharges'] as $transaction) {
            /** @var PurchaseTransaction $transaction */
            $additionalCharges = $transaction->prepareAdditionalCharges();
            foreach ($additionalCharges as $additionalCharge) {
                $dataItem[] = $additionalCharge;
            }
        }

        // Purchase Ledger Transactions
        /** @var PurchaseLedgerTransaction $ledger */
        foreach ($data['purchaseTransactionLedgers'] as $ledger) {

            $purchaseTransaction = $ledger->purchaseTransaction;
            $gstRate = $ledger->gst_tax_percentage == 0 ? ($purchaseTransaction->is_gst_na ? 'NA' : 0) : $ledger->gst_tax_percentage;

            $dataItem[] = [
                'party_name' => $purchaseTransaction->supplier->name,
                'ledger_id' => $purchaseTransaction->supplier_ledger_id,
                'gstin' => ! empty($purchaseTransaction->gstin) ? $purchaseTransaction->gstin : '',
                'invoice_number' => $purchaseTransaction->sale_number,
                'voucher_number' => $purchaseTransaction->voucher_number,
                'invoice_date' => Carbon::parse($purchaseTransaction->date_of_invoice)->format('d-m-Y'),
                'voucher_date' => Carbon::parse($purchaseTransaction->voucher_date)->format('d-m-Y'),
                'transaction_type' => 'Purchase',
                'ledger_name' => ! empty($ledger->ledger_id) ? $ledger->ledger->name : '',
                'item_name' => '',
                'quantity' => 0,
                'unit_of_measurement' => '',
                'rate_per_unit' => $ledger->rpu_without_gst,
                'hsn_code' => '',
                'taxable_value' => $ledger->taxable_value,
                'gst_rate' => $gstRate,
                'cgst' => ! empty($ledger->classification_cgst_tax) ? $ledger->classification_cgst_tax : 0,
                'sgst' => ! empty($ledger->classification_sgst_tax) ? $ledger->classification_sgst_tax : 0,
                'igst' => ! empty($ledger->classification_igst_tax) ? $ledger->classification_igst_tax : 0,
                'cess_amount' => ! empty($ledger->cess_amount) ? $ledger->cess_amount : 0,
                'invoice_amount' => $purchaseTransaction->grand_total,
                'itc' => ! empty($ledger->classification_is_itc_applicable) ? $ledger->classification_is_itc_applicable : 0,
                'rcm_yes_or_no' => ! empty($ledger->classification_is_rcm_applicable) ? $ledger->classification_is_rcm_applicable : 0,
                'invoice_type' => PurchaseTransaction::PURCHASES,
            ];
        }

        /** @var PurchaseReturnItemTransaction $item */
        // Purchase Return Item Transactions
        foreach ($data['purchaseReturnTransactionItems'] as $item) {

            $purchaseReturnTransaction = $item->purchaseReturnTransaction;
            $gstRate = $item->gst_tax_percentage == 0 ? ($purchaseReturnTransaction->is_gst_na ? 'NA' : 0) : $item->gst_tax_percentage;

            $dataItem[] = [
                'party_name' => $purchaseReturnTransaction->supplier->name,
                'ledger_id' => $purchaseReturnTransaction->supplier_id,
                'gstin' => ! empty($purchaseReturnTransaction->gstin) ? $purchaseReturnTransaction->gstin : '',
                'invoice_number' => $purchaseReturnTransaction->supplier_purchase_return_number,
                'voucher_number' => $purchaseReturnTransaction->voucher_number,
                'invoice_date' => Carbon::parse($purchaseReturnTransaction->supplier_purchase_return_date)->format('d-m-Y'),
                'voucher_date' => Carbon::parse($purchaseReturnTransaction->voucher_date)->format('d-m-Y'),
                'transaction_type' => 'Purchase Return',
                'ledger_name' => ! empty($item->ledger_id) ? $item->ledger->name : '',
                'item_name' => $item->items->item_name,
                'quantity' => $item->quantity,
                'unit_of_measurement' => $item->unit->full_name,
                'rate_per_unit' => $item->rpu_without_gst,
                'hsn_code' => $item->hsn_code ?? $item->items->model->hsn_sac_code ?? '',
                'taxable_value' => -$item->taxable_value,
                'gst_rate' => $gstRate,
                'cgst' => (! empty($item->classification_cgst_tax)) ? -$item->classification_cgst_tax : 0,
                'sgst' => (! empty($item->classification_sgst_tax)) ? -$item->classification_sgst_tax : 0,
                'igst' => (! empty($item->classification_igst_tax)) ? -$item->classification_igst_tax : 0,
                'cess_amount' => (! empty($item->cess_amount)) ? -$item->cess_amount : 0,
                'invoice_amount' => -$purchaseReturnTransaction->grand_total,
                'itc' => ! empty($item->classification_is_itc_applicable) ? $item->classification_is_itc_applicable : 0,
                'rcm_yes_or_no' => ! empty($item->classification_is_rcm_applicable) ? $item->classification_is_rcm_applicable : 0,
                'invoice_type' => PurchaseTransaction::PURCHASES_RETURN,
            ];
        }
        foreach ($data['purchaseReturnTransactionAdditionalCharges'] as $transaction) {
            /** @var PurchaseReturnTransaction $transaction */
            $additionalCharges = $transaction->prepareAdditionalCharges();
            foreach ($additionalCharges as $additionalCharge) {
                $dataItem[] = $additionalCharge;
            }
        }

        /** @var PurchaseReturnLedgerTransaction $ledger */
        // Purchase Return Ledger Transactions
        foreach ($data['purchaseReturnTransactionLedgers'] as $ledger) {

            $purchaseReturnTransaction = $ledger->purchaseReturnTransaction;
            $gstRate = $ledger->gst_tax_percentage == 0 ? ($purchaseReturnTransaction->is_gst_na ? 'NA' : 0) : $ledger->gst_tax_percentage;

            $dataItem[] = [
                'party_name' => $purchaseReturnTransaction->supplier->name,
                'ledger_id' => $purchaseReturnTransaction->supplier_id,
                'gstin' => ! empty($purchaseReturnTransaction->gstin) ? $purchaseReturnTransaction->gstin : '',
                'invoice_number' => $purchaseReturnTransaction->supplier_purchase_return_number,
                'voucher_number' => $purchaseReturnTransaction->voucher_number,
                'invoice_date' => Carbon::parse($purchaseReturnTransaction->supplier_purchase_return_date)->format('d-m-Y'),
                'voucher_date' => Carbon::parse($purchaseReturnTransaction->voucher_date)->format('d-m-Y'),
                'transaction_type' => 'Purchase Return',
                'ledger_name' => ! empty($ledger->ledger_id) ? $ledger->ledger->name : '',
                'item_name' => '',
                'quantity' => 0,
                'unit_of_measurement' => '',
                'rate_per_unit' => $ledger->rpu_without_gst,
                'hsn_code' => '',
                'taxable_value' => -$ledger->taxable_value,
                'gst_rate' => $gstRate,
                'cgst' => (! empty($ledger->classification_cgst_tax)) ? -$ledger->classification_cgst_tax : 0,
                'sgst' => (! empty($ledger->classification_sgst_tax)) ? -$ledger->classification_sgst_tax : 0,
                'igst' => (! empty($ledger->classification_igst_tax)) ? -$ledger->classification_igst_tax : 0,
                'cess_amount' => (! empty($ledger->cess_amount)) ? -$ledger->cess_amount : 0,
                'invoice_amount' => -$purchaseReturnTransaction->grand_total,
                'itc' => ! empty($ledger->classification_is_itc_applicable) ? $ledger->classification_is_itc_applicable : 0,
                'rcm_yes_or_no' => ! empty($ledger->classification_is_rcm_applicable) ? $ledger->classification_is_rcm_applicable : 0,
                'invoice_type' => PurchaseTransaction::PURCHASES_RETURN,
            ];
        }

        /** @var ExpenseDebitNoteItemTransaction $item */
        // Expense Dr. Note Item Transactions
        foreach ($data['expenseDebitNoteTransactionItems'] as $item) {

            $expenseDebitNotesTransaction = $item->expenseDebitNotesTransaction;
            $gstRate = $item->gst_tax_percentage == 0 ? ($expenseDebitNotesTransaction->is_gst_na ? 'NA' : 0) : $item->gst_tax_percentage;

            $dataItem[] = [
                'party_name' => $expenseDebitNotesTransaction->supplier->name,
                'ledger_id' => $expenseDebitNotesTransaction->supplier_id,
                'gstin' => ! empty($expenseDebitNotesTransaction->gstin) ? $expenseDebitNotesTransaction->gstin : '',
                'invoice_number' => $expenseDebitNotesTransaction->supplier_purchase_return_number,
                'voucher_number' => $expenseDebitNotesTransaction->voucher_number,
                'invoice_date' => Carbon::parse($expenseDebitNotesTransaction->supplier_purchase_return_date)->format('d-m-Y'),
                'voucher_date' => Carbon::parse($expenseDebitNotesTransaction->voucher_date)->format('d-m-Y'),
                'transaction_type' => 'Expense Dr. Note',
                'ledger_name' => ! empty($item->ledger_id) ? $item->ledger->name : '',
                'item_name' => $item->items->item_name,
                'quantity' => $item->quantity,
                'unit_of_measurement' => $item->unit->full_name,
                'rate_per_unit' => $item->rpu_without_gst,
                'hsn_code' => $item->hsn_code ?? $item->items->model->hsn_sac_code ?? '',
                'taxable_value' => -$item->taxable_value,
                'gst_rate' => $gstRate,
                'cgst' => ! empty($item->classification_cgst_tax) ? -$item->classification_cgst_tax : 0,
                'sgst' => ! empty($item->classification_sgst_tax) ? -$item->classification_sgst_tax : 0,
                'igst' => ! empty($item->classification_igst_tax) ? -$item->classification_igst_tax : 0,
                'cess_amount' => ! empty($item->cess_amount) ? -$item->cess_amount : 0,
                'invoice_amount' => -$expenseDebitNotesTransaction->grand_total,
                'itc' => ! empty($item->classification_is_itc_applicable) ? $item->classification_is_itc_applicable : 0,
                'rcm_yes_or_no' => ! empty($item->classification_is_rcm_applicable) ? $item->classification_is_rcm_applicable : 0,
                'invoice_type' => PurchaseTransaction::DEBIT_NOTES,
            ];
        }
        foreach ($data['expenseDebitNoteTransactionAdditionalCharges'] as $transaction) {
            /** @var ExpenseDebitNoteTransaction $transaction */
            $additionalCharges = $transaction->prepareAdditionalCharges();
            foreach ($additionalCharges as $additionalCharge) {
                $dataItem[] = $additionalCharge;
            }
        }

        /** @var ExpenseDebitNoteLedgerTransaction $ledger */
        // Expense Dr. Note Ledger Transactions
        foreach ($data['expenseDebitNoteTransactionLedgers'] as $ledger) {

            $expenseDebitNotesTransaction = $ledger->expenseDebitNotesTransaction;
            $gstRate = $ledger->gst_tax_percentage == 0 ? ($expenseDebitNotesTransaction->is_gst_na ? 'NA' : 0) : $ledger->gst_tax_percentage;

            $dataItem[] = [
                'party_name' => $expenseDebitNotesTransaction->supplier->name,
                'ledger_id' => $expenseDebitNotesTransaction->supplier_id,
                'gstin' => ! empty($expenseDebitNotesTransaction->gstin) ? $expenseDebitNotesTransaction->gstin : '',
                'invoice_number' => $expenseDebitNotesTransaction->supplier_purchase_return_number,
                'voucher_number' => $expenseDebitNotesTransaction->voucher_number,
                'invoice_date' => Carbon::parse($expenseDebitNotesTransaction->supplier_purchase_return_date)->format('d-m-Y'),
                'voucher_date' => Carbon::parse($expenseDebitNotesTransaction->voucher_date)->format('d-m-Y'),
                'transaction_type' => 'Expense Dr. Note',
                'ledger_name' => ! empty($ledger->ledger_id) ? $ledger->ledger->name : '',
                'item_name' => '',
                'quantity' => 0,
                'unit_of_measurement' => '',
                'rate_per_unit' => $ledger->rpu_without_gst,
                'hsn_code' => '',
                'taxable_value' => -$ledger->taxable_value,
                'gst_rate' => $gstRate,
                'cgst' => ! empty($ledger->classification_cgst_tax) ? -$ledger->classification_cgst_tax : 0,
                'sgst' => ! empty($ledger->classification_sgst_tax) ? -$ledger->classification_sgst_tax : 0,
                'igst' => ! empty($ledger->classification_igst_tax) ? -$ledger->classification_igst_tax : 0,
                'cess_amount' => ! empty($ledger->cess_amount) ? -$ledger->cess_amount : 0,
                'invoice_amount' => -$expenseDebitNotesTransaction->grand_total,
                'itc' => ! empty($ledger->classification_is_itc_applicable) ? $ledger->classification_is_itc_applicable : 0,
                'rcm_yes_or_no' => ! empty($ledger->classification_is_rcm_applicable) ? $ledger->classification_is_rcm_applicable : 0,
                'invoice_type' => PurchaseTransaction::DEBIT_NOTES,
            ];
        }

        /** @var ExpenseCreditNoteItemTransaction $item */
        // Expense Cr. Note Item Transactions
        foreach ($data['expenseCreditNoteTransactionItems'] as $item) {

            $expenseCreditNotesTransaction = $item->expenseCreditNotesTransaction;
            $gstRate = $item->gst_tax_percentage == 0 ? ($expenseCreditNotesTransaction->is_gst_na ? 'NA' : 0) : $item->gst_tax_percentage;

            $dataItem[] = [
                'party_name' => $expenseCreditNotesTransaction->supplier->name,
                'ledger_id' => $expenseCreditNotesTransaction->supplier_id,
                'gstin' => ! empty($expenseCreditNotesTransaction->gstin) ? $expenseCreditNotesTransaction->gstin : '',
                'invoice_number' => $expenseCreditNotesTransaction->supplier_purchase_return_number,
                'voucher_number' => $expenseCreditNotesTransaction->voucher_number,
                'invoice_date' => Carbon::parse($expenseCreditNotesTransaction->supplier_purchase_return_date)->format('d-m-Y'),
                'voucher_date' => Carbon::parse($expenseCreditNotesTransaction->voucher_date)->format('d-m-Y'),
                'transaction_type' => 'Expense Cr. Note',
                'ledger_name' => ! empty($item->ledger_id) ? $item->ledger->name : '',
                'item_name' => $item->items->item_name,
                'quantity' => $item->quantity,
                'unit_of_measurement' => $item->unit->full_name,
                'rate_per_unit' => $item->rpu_without_gst,
                'hsn_code' => $item->hsn_code ?? $item->items->model->hsn_sac_code ?? '',
                'taxable_value' => $item->taxable_value,
                'gst_rate' => $gstRate,
                'cgst' => ! empty($item->classification_cgst_tax) ? $item->classification_cgst_tax : 0,
                'sgst' => ! empty($item->classification_sgst_tax) ? $item->classification_sgst_tax : 0,
                'igst' => ! empty($item->classification_igst_tax) ? $item->classification_igst_tax : 0,
                'cess_amount' => ! empty($item->cess_amount) ? $item->cess_amount : 0,
                'invoice_amount' => $expenseCreditNotesTransaction->grand_total,
                'itc' => ! empty($item->classification_is_itc_applicable) ? $item->classification_is_itc_applicable : 0,
                'rcm_yes_or_no' => ! empty($item->classification_is_rcm_applicable) ? $item->classification_is_rcm_applicable : 0,
                'invoice_type' => PurchaseTransaction::CREDIT_NOTES,
            ];
        }

        foreach ($data['expenseCreditNoteTransactionAdditionalCharges'] as $transaction) {
            /** @var ExpenseCreditNoteTransaction $transaction */
            $additionalCharges = $transaction->prepareAdditionalCharges();
            foreach ($additionalCharges as $additionalCharge) {
                $dataItem[] = $additionalCharge;
            }
        }

        /** @var ExpenseCreditNoteLedgerTransaction $ledger */
        // Expense Cr. Note Ledger Transactions
        foreach ($data['expenseCreditNoteTransactionLedgers'] as $ledger) {

            $expenseCreditNotesTransaction = $ledger->expenseCreditNotesTransaction;
            $gstRate = $ledger->gst_tax_percentage == 0 ? ($expenseCreditNotesTransaction->is_gst_na ? 'NA' : 0) : $ledger->gst_tax_percentage;

            $dataItem[] = [
                'party_name' => $expenseCreditNotesTransaction->supplier->name,
                'ledger_id' => $expenseCreditNotesTransaction->supplier_id,
                'gstin' => ! empty($expenseCreditNotesTransaction->gstin) ? $expenseCreditNotesTransaction->gstin : '',
                'invoice_number' => $expenseCreditNotesTransaction->supplier_purchase_return_number,
                'voucher_number' => $expenseCreditNotesTransaction->voucher_number,
                'invoice_date' => Carbon::parse($expenseCreditNotesTransaction->supplier_purchase_return_date)->format('d-m-Y'),
                'voucher_date' => Carbon::parse($expenseCreditNotesTransaction->voucher_date)->format('d-m-Y'),
                'transaction_type' => 'Expense Cr. Note',
                'ledger_name' => ! empty($ledger->ledger_id) ? $ledger->ledger->name : '',
                'item_name' => '',
                'quantity' => 0,
                'unit_of_measurement' => '',
                'rate_per_unit' => $ledger->rpu_without_gst,
                'hsn_code' => '',
                'taxable_value' => $ledger->taxable_value,
                'gst_rate' => $gstRate,
                'cgst' => ! empty($ledger->classification_cgst_tax) ? $ledger->classification_cgst_tax : 0,
                'sgst' => ! empty($ledger->classification_sgst_tax) ? $ledger->classification_sgst_tax : 0,
                'igst' => ! empty($ledger->classification_igst_tax) ? $ledger->classification_igst_tax : 0,
                'cess_amount' => ! empty($ledger->cess_amount) ? $ledger->cess_amount : 0,
                'invoice_amount' => $expenseCreditNotesTransaction->grand_total,
                'itc' => ! empty($item->classification_is_itc_applicable) ? $item->classification_is_itc_applicable : 0,
                'rcm_yes_or_no' => ! empty($ledger->classification_is_rcm_applicable) ? $ledger->classification_is_rcm_applicable : 0,
                'invoice_type' => PurchaseTransaction::CREDIT_NOTES,
            ];
        }

        if ($input['report_type'] == 1) {
            $dataItem = $this->getSummaryReports($dataItem);
        }

        return $dataItem;
    }

    public function getSummaryReports($data): array
    {
        $dataItem = [];

        foreach ($data as $item) {
            $customerId = $item['ledger_id'];
            if (array_key_exists($customerId, $dataItem)) {
                if ($dataItem[$customerId]['voucher_number'] !== $item['voucher_number']) {
                    $dataItem[$customerId]['invoice_amount'] += $item['invoice_amount'];
                }
                $dataItem[$customerId]['party_name'] = $item['party_name'];
                $dataItem[$customerId]['ledger_id'] = $customerId;
                $dataItem[$customerId]['gstin'] = $item['gstin'];
                $dataItem[$customerId]['taxable_value'] += $item['taxable_value'];
                $dataItem[$customerId]['cgst'] += $item['cgst'];
                $dataItem[$customerId]['sgst'] += $item['sgst'];
                $dataItem[$customerId]['igst'] += $item['igst'];
                $dataItem[$customerId]['cess_amount'] += $item['cess_amount'];
                $dataItem[$customerId]['voucher_number'] = $item['voucher_number'];
            } else {
                $dataItem[$customerId]['party_name'] = $item['party_name'];
                $dataItem[$customerId]['ledger_id'] = $customerId;
                $dataItem[$customerId]['gstin'] = $item['gstin'];
                $dataItem[$customerId]['taxable_value'] = $item['taxable_value'];
                $dataItem[$customerId]['cgst'] = $item['cgst'];
                $dataItem[$customerId]['sgst'] = $item['sgst'];
                $dataItem[$customerId]['igst'] = $item['igst'];
                $dataItem[$customerId]['cess_amount'] = $item['cess_amount'];
                $dataItem[$customerId]['invoice_amount'] = $item['invoice_amount'];
                $dataItem[$customerId]['voucher_number'] = $item['voucher_number'];
            }
        }

        return array_values($dataItem);
    }

    /**
     * @return Response|BinaryFileResponse
     */
    public function export(Request $request)
    {
        $input = $request->all();
        $type = $input['type'];
        $inputTaxRegisterReportCacheKey = generateCacheKey('input_tax_register_report');
        $company = getCurrentCompany();
        $data = [];
        $data['data'] = Cache::get($inputTaxRegisterReportCacheKey);
        $data['company'] = $company;
        $data['companyAddress'] = $company?->addresses->where('address_type', Company::BILLING_ADDRESS)->first();

        $user = getLoginUser();
        $pdfOrExcel = $type == 'pdf' ? 'PDF' : 'Excel';
        CreateAuditTrailEvent::run($user, 'Export', '<b>Input Tax Register Report</b> '.$pdfOrExcel.' was exported.');

        if ($type === 'excel') {
            $data['report_type'] = $input['report_type'];

            $response = Excel::download(new InputTaxRegisterReportExport($data), 'input-tax-register-report.xlsx');
            ob_end_clean();

            return $response;

            // return (new InputTaxRegisterReportExport($data))->download('input-tax-register-report.xlsx');
        }

        $customPaperSize = [0, 0, 700, 900];

        if ($input['report_type'] == 1) {
            $pdf = PDF::loadView('pdf.input-tax-register-summery', $data)->setPaper('a4', 'landscape');
        } else {
            $pdf = PDF::loadView('pdf.input-tax-register', $data)->setPaper('a4', 'landscape');
        }

        if (isset($input['is_view'])) {
            return $pdf->stream('input-tax-register-report.pdf');
        }

        return $pdf->download('input-tax-register-report.pdf');
    }
}
