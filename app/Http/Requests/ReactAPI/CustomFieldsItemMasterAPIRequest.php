<?php

namespace App\Http\Requests\ReactAPI;

use App\Models\ItemCustomField;
use Illuminate\Foundation\Http\FormRequest;

class CustomFieldsItemMasterAPIRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'label_name' => 'required|string|max:255',
            'types' => 'required|array',
            'types.*.type' => 'required|integer',
            'types.*.is_show_in_print' => 'required|boolean',
            'custom_field_type' => 'required|integer|in:' . implode(',', array_keys(ItemCustomField::CUSTOM_FIELD_ARRAY_WITH_INPUT_TYPE)),
            'options' => 'required_if:custom_field_type,'.ItemCustomField::CF_TYPE_DROPDOWN.'|array',
            'options.*.value' => 'required_if:custom_field_type,'.ItemCustomField::CF_TYPE_DROPDOWN,
            'enable_for_all' => 'required|boolean',
            'default_value' => 'nullable',
            'default_formula' => 'string|required_with:used_cf_ids_for_formula',
            'used_cf_ids_for_formula' => 'nullable|string',
            'field_type' => 'integer|in:1,2|required_if:custom_field_type,'.ItemCustomField::CF_TYPE_WARRANTY,
        ];
    }

    public function messages()
    {
        return [
            'label_name.required' => 'Label name is required.',
            'types.*.type.required' => 'Please select a transaction type.',
            'types.*.is_show_in_print.required' => 'Please select a print option.',
            'custom_field_type.required' => 'Please select a custom field type.',
            'options.required_if' => 'Please enter a value for each option.',
            'options.*.value.required_if' => 'Please enter a value for each option.',
            'enable_for_all.boolean' => 'Is show in print must be a boolean.',
            'default_formula.required_with' => 'Default Formula is required.',
            'field_type.required_if' => 'Field Type is required when Custom Field Type is Warranty.',
        ];
    }
}
