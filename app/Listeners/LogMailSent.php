<?php

namespace App\Listeners;

use App\Actions\AuditTrail\CreateAuditTrailEvent;
use App\Models\EmailTrackingLog;
use Illuminate\Mail\Events\MessageSent;
use Illuminate\Support\Facades\Log;

class LogMailSent
{
    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(MessageSent $event)
    {
        $messageID = $event->sent->getSymfonySentMessage()->getMessageId();
        $data = $event->data;
        // $user = $event->user;

        if (isset($data['data']['invoiceMailType']) && ! empty($data['data']['invoiceMailType'])) {
            // if (! isset($data['data']['taxInvoice']) && ! isset($data['taxInvoice'])) {
            //     Log::error('No taxInvoice in data : ', $data);

            //     return;
            // }
            $meta['invoiceMailType'] = $data['data']['invoiceMailType'];
            $meta['taxInvoice'] = $data['data']['subject'] ?? $data['taxInvoice'];
            $meta['invoiceName'] = $data['data']['invoiceName'] ?? 0;

            if (! empty($messageID)) {
                EmailTrackingLog::create([
                    'message_id' => $messageID,
                    'user_id' => $data['companyDetails']['user_id'] ?? null,
                    'send_to_email' => $data['data']['to'],
                    'company_id' => $data['companyDetails']['id'] ?? null,
                    'invoice_id' => $data['invoiceNumber'] ?? null,
                    'meta' => $meta,
                ]);
            }
        }

        // CreateAuditTrailEvent::run($user, 'login', '<b>'.$user->first_name.'</b> logged in.');
    }
}
