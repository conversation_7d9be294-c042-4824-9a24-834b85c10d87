<?php

namespace App\Actions\GetGstr2BReconciliation;

use App\Models\ExpenseDebitNoteTransaction;
use App\Models\Gstr2bSummaryTransaction;
use App\Models\PurchaseReturnTransaction;
use App\Models\PurchaseTransaction;
use Carbon\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

class GetGstr2BReconciliationDetailsAction
{
    use AsAction;

    public function handle($input)
    {
        $input = [
            'start_date' => '2025-06-01',
            'end_date' => '2025-06-30',
        ];
        $startDate = ! empty($input['start_date']) ? Carbon::parse($input['start_date'])->format('Y-m-d') : Carbon::now()->startOfMonth()->format('Y-m-d');
        $endDate = ! empty($input['end_date']) ? Carbon::parse($input['end_date'])->format('Y-m-d') : Carbon::now()->endOfMonth()->format('Y-m-d');
        $filingPeriod = ! empty($input['end_date']) ? Carbon::parse($input['end_date'])->format('mY') : null;

        return $this->getBooksTransaction($startDate, $endDate, $filingPeriod);

    }

    private function getBooksTransaction($startDate, $endDate, $filingPeriod)
    {
        $purchase = PurchaseTransaction::with('supplier')->whereBetween('voucher_date', [$startDate, $endDate])->whereNotNull('gstin')->get();
        $purchaseReturn = PurchaseReturnTransaction::whereBetween('voucher_date', [$startDate, $endDate])->whereNotNull('gstin')->get();
        $expenseDebitNote = ExpenseDebitNoteTransaction::whereBetween('voucher_date', [$startDate, $endDate])->whereNotNull('gstin')->get();

        $getGstr2BReconciliationSummary = Gstr2bSummaryTransaction::where('return_period', $filingPeriod)->get();

        $books = [];
        foreach ($purchase as $value) {
            $books[] = [
                'gstin' => $value->gstin,
                'name' => $value->supplier->name,
                'party_id' => $value->supplier_id,
                'type' => 'B2B',
                'invoice_number' => $value->sale_number,
                'date' => $value->date_of_invoice,
                'taxable_value' => $value->taxable_value,
                'cgst' => $value->cgst,
                'sgst' => $value->sgst,
                'igst' => $value->igst,
                'cess' => $value->cess,
                'transaction_type' => 'purchase',
                'transaction_id' => $value->id,
            ];
        }

        foreach ($purchaseReturn as $value) {
            $books[] = [
                'gstin' => $value->gstin,
                'name' => $value->supplier->name,
                'party_id' => $value->supplier_id,
                'type' => 'CDNR',
                'invoice_number' => $value->supplier_purchase_return_number,
                'date' => $value->supplier_purchase_return_date,
                'taxable_value' => $value->taxable_value,
                'cgst' => $value->cgst,
                'sgst' => $value->sgst,
                'igst' => $value->igst,
                'cess' => $value->cess,
                'transaction_type' => 'purchaseReturn',
                'transaction_id' => $value->id,
            ];
        }

        foreach ($expenseDebitNote as $value) {
            $books[] = [
                'gstin' => $value->gstin,
                'name' => $value->supplier->name,
                'party_id' => $value->supplier_id,
                'type' => 'CDNR',
                'invoice_number' => $value->supplier_purchase_return_number,
                'date' => $value->supplier_purchase_return_date,
                'taxable_value' => $value->taxable_value,
                'cgst' => $value->cgst,
                'sgst' => $value->sgst,
                'igst' => $value->igst,
                'cess' => $value->cess,
                'transaction_type' => 'expenseDebitNote',
                'transaction_id' => $value->id,
            ];
        }

        $portal = [];
        foreach ($getGstr2BReconciliationSummary as $value) {
            $portal[] = [
                'gstin' => $value->gstin,
                'type' => $value->transaction_type == 1 ? 'B2B' : 'CDNR',
                'invoice_number' => $value->invoice_number,
                'date' => $value->invoice_date,
                'taxable_value' => $value->taxable_value,
                'cgst' => $value->cgst,
                'sgst' => $value->sgst,
                'igst' => $value->igst,
                'cess' => $value->cess,
            ];
        }

        $data = [];

        // Step 1: Build portal index for fast lookup
        $portalByKey = [];
        $portalLooseMatch = [];

        foreach ($portal as $p) {
            $key = $p['gstin'].'|'.$p['invoice_number'];
            $portalByKey[$key] = $p;

            // Add loose matching key for partial matching (excluding invoice number)
            $looseKey = $p['gstin'].'|'.$p['taxable_value'].'|'.$p['cgst'].'|'.$p['sgst'].'|'.$p['igst'].'|'.$p['cess'].'|'.$p['date'];
            $portalLooseMatch[$looseKey][] = $p;
        }

        $matchedPortalKeys = [];

        foreach ($books as $b) {
            $key = $b['gstin'].'|'.$b['invoice_number'];

            if (isset($portalByKey[$key])) {
                // Exact invoice match
                $p = $portalByKey[$key];
                $matchedPortalKeys[] = $key;

                $isFullMatch = (
                    $b['taxable_value'] == $p['taxable_value'] &&
                    $b['cgst'] == $p['cgst'] &&
                    $b['sgst'] == $p['sgst'] &&
                    $b['igst'] == $p['igst'] &&
                    $b['cess'] == $p['cess'] &&
                    Carbon::parse($b['date'])->format('Y-m-d') == $p['date']
                );

                $color = $isFullMatch ? '#cff7d3' : '#fff1c2';
                $action = $isFullMatch ? 'matched' : 'pending';

                $data[] = [
                    'gstin' => $b['gstin'],
                    'name' => $b['name'],
                    'transaction_id' => $b['transaction_id'],
                    'books_doc' => $b['type'],
                    'books_doc_no' => $b['invoice_number'],
                    'books_doc_date' => Carbon::parse($b['date'])->format('Y-m-d'),
                    'books_taxable_value' => $b['taxable_value'],
                    'books_igst' => $b['igst'],
                    'books_cgst' => $b['cgst'],
                    'books_sgst' => $b['sgst'],
                    'books_cess' => $b['cess'],
                    'portal_doc_no' => $p['invoice_number'],
                    'portal_doc_date' => $p['date'],
                    'portal_taxable_value' => $p['taxable_value'],
                    'portal_igst' => $p['igst'],
                    'portal_cgst' => $p['cgst'],
                    'portal_sgst' => $p['sgst'],
                    'portal_cess' => $p['cess'],
                    'difference_taxable_value' => $p['taxable_value'] - $b['taxable_value'],
                    'difference_igst' => $p['igst'] - $b['igst'],
                    'difference_cgst' => $p['cgst'] - $b['cgst'],
                    'difference_sgst' => $p['sgst'] - $b['sgst'],
                    'difference_cess' => $p['cess'] - $b['cess'],
                    'color' => $color,
                    'action' => $action,
                    'transaction_type' => $b['transaction_type'],
                    'party_id' => $b['party_id'],
                ];
            } else {
                // Check partial match: all values match except invoice number
                $looseKey = $b['gstin'].'|'.$b['taxable_value'].'|'.$b['cgst'].'|'.$b['sgst'].'|'.$b['igst'].'|'.$b['cess'].'|'.$b['date'];
                $partialMatch = $portalLooseMatch[$looseKey][0] ?? null;

                if ($partialMatch) {
                    $matchedPortalKeys[] = $partialMatch['gstin'].'|'.$partialMatch['invoice_number'];

                    $data[] = [
                        'gstin' => $b['gstin'],
                        'name' => $b['name'],
                        'transaction_id' => $b['transaction_id'],
                        'books_doc' => $b['type'],
                        'books_doc_no' => $b['invoice_number'],
                        'books_doc_date' => Carbon::parse($b['date'])->format('Y-m-d'),
                        'books_taxable_value' => $b['taxable_value'],
                        'books_igst' => $b['igst'],
                        'books_cgst' => $b['cgst'],
                        'books_sgst' => $b['sgst'],
                        'books_cess' => $b['cess'],
                        'portal_doc_no' => $partialMatch['invoice_number'],
                        'portal_doc_date' => $partialMatch['date'],
                        'portal_taxable_value' => $partialMatch['taxable_value'],
                        'portal_igst' => $partialMatch['igst'],
                        'portal_cgst' => $partialMatch['cgst'],
                        'portal_sgst' => $partialMatch['sgst'],
                        'portal_cess' => $partialMatch['cess'],
                        'difference_taxable_value' => 0,
                        'difference_igst' => 0,
                        'difference_cgst' => 0,
                        'difference_sgst' => 0,
                        'difference_cess' => 0,
                        'color' => '#fff1c2',
                        'action' => 'pending',
                        'transaction_type' => $b['transaction_type'],
                        'party_id' => $b['party_id'],
                    ];
                } else {
                    // No match at all → Missing in Portal
                    $data[] = [
                        'gstin' => $b['gstin'],
                        'name' => $b['name'],
                        'transaction_id' => $b['transaction_id'],
                        'books_doc' => $b['type'],
                        'books_doc_no' => $b['invoice_number'],
                        'books_doc_date' => Carbon::parse($b['date'])->format('Y-m-d'),
                        'books_taxable_value' => $b['taxable_value'],
                        'books_igst' => $b['igst'],
                        'books_cgst' => $b['cgst'],
                        'books_sgst' => $b['sgst'],
                        'books_cess' => $b['cess'],
                        'portal_doc_no' => null,
                        'portal_doc_date' => null,
                        'portal_taxable_value' => null,
                        'portal_igst' => null,
                        'portal_cgst' => null,
                        'portal_sgst' => null,
                        'portal_cess' => null,
                        'difference_taxable_value' => null,
                        'difference_igst' => null,
                        'difference_cgst' => null,
                        'difference_sgst' => null,
                        'difference_cess' => null,
                        'color' => '#e6e6e6',
                        'action' => 'missingInGST',
                        'transaction_type' => $b['transaction_type'],
                        'party_id' => $b['party_id'],
                    ];
                }
            }
        }

        // Step 3: Entries in Portal but not matched → RED
        foreach ($portal as $p) {

            $key = $p['gstin'].'|'.$p['invoice_number'];
            if (! in_array($key, $matchedPortalKeys)) {
                $data[] = [
                    'gstin' => $p['gstin'],
                    'name' => null,
                    'books_doc' => $p['type'],
                    'books_doc_no' => null,
                    'books_doc_date' => null,
                    'books_taxable_value' => null,
                    'books_igst' => null,
                    'books_cgst' => null,
                    'books_sgst' => null,
                    'books_cess' => null,
                    'portal_doc_no' => $p['invoice_number'],
                    'portal_doc_date' => $p['date'],
                    'portal_taxable_value' => $p['taxable_value'],
                    'portal_igst' => $p['igst'],
                    'portal_cgst' => $p['cgst'],
                    'portal_sgst' => $p['sgst'],
                    'portal_cess' => $p['cess'],
                    'difference_taxable_value' => null,
                    'difference_igst' => null,
                    'difference_cgst' => null,
                    'difference_sgst' => null,
                    'difference_cess' => null,
                    'color' => '#fdd3d0',
                    'action' => 'missingInBook',
                    'transaction_type' => $p['type'] == 'B2B' ? 'purchase' : 'purchaseReturn',
                ];
            }
        }

        return $data;
    }
}
