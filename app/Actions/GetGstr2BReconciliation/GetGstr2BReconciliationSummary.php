<?php

namespace App\Actions\GetGstr2BReconciliation;

use App\Models\ExpenseDebitNoteTransaction;
use App\Models\Gstr2bSummaryTransaction;
use App\Models\PurchaseReturnTransaction;
use App\Models\PurchaseTransaction;
use Carbon\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

class GetGstr2BReconciliationSummary
{
    use AsAction;

    public function handle($input)
    {
        $filingPeriod = ! empty($input['end_date']) ? Carbon::parse($input['end_date'])->format('mY') : null;

        [$b2bBooks,$booksTransaction] = $this->b2bBooks($input);
        [$b2bPortal, $portalTransaction] = $this->b2bPortal($filingPeriod, 1);
        $difference = $this->b2bDifference($booksTransaction, $portalTransaction);

        [$cdnrBooks, $cdnrTransaction] = $this->cdnrBooks($input);
        [$cdnrPortal, $cdnrPortalTransaction] = $this->b2bPortal($filingPeriod, 2);
        $cdnrDifference = $this->b2bDifference($cdnrTransaction, $cdnrPortalTransaction);

        $data = [
            'b2b' => [
                'books' => $b2bBooks,
                'portal' => $b2bPortal,
                'matched' => $difference['matched'],
                'mismatched' => $difference['mismatched'],
                'missing_in_books' => $difference['missing_in_books'],
                'missing_in_portal' => $difference['missing_in_portal'],
            ],
            'cdnr' => [
                'books' => $cdnrBooks,
                'portal' => $cdnrPortal,
                'matched' => $cdnrDifference['matched'],
                'mismatched' => $cdnrDifference['mismatched'],
                'missing_in_books' => $cdnrDifference['missing_in_books'],
                'missing_in_portal' => $cdnrDifference['missing_in_portal'],
            ],
        ];

        return $data;

    }

    public function b2bBooks($input)
    {
        $startDate = $input['start_date'];
        $endDate = $input['end_date'];
        $purchase = PurchaseTransaction::whereBetween('voucher_date', [$startDate, $endDate])->whereNotNull('gstin')->get();

        $numberOfInvoices = $purchase->count();
        $taxableValue = $purchase->sum('grand_total');
        $totalIgst = $purchase->sum('igst');
        $totalCgst = $purchase->sum('cgst');
        $totalSgst = $purchase->sum('sgst');
        $totalCess = $purchase->sum('cess');
        $totalTex = $totalIgst + $totalCgst + $totalSgst + $totalCess;
        $totalInvoiceValue = $purchase->sum('taxable_value');

        $data = [
            'invoices' => $numberOfInvoices,
            'taxable_value' => $taxableValue,
            'total_igst' => $totalIgst,
            'total_cgst' => $totalCgst,
            'total_sgst' => $totalSgst,
            'total_cess' => $totalCess,
            'total_tax' => $totalTex,
            'total_invoice_value' => $totalInvoiceValue,
        ];

        return [$data, $purchase];

    }

    public function b2bPortal($filingPeriod, $transactionType)
    {
        $gstData = Gstr2bSummaryTransaction::where('return_period', $filingPeriod)->where('transaction_type', $transactionType)->get();

        $numberOfInvoices = $gstData->count();
        $taxableValue = $gstData->sum('invoice_amount');
        $totalIgst = $gstData->sum('igst');
        $totalCgst = $gstData->sum('cgst');
        $totalSgst = $gstData->sum('sgst');
        $totalCess = $gstData->sum('cess');
        $totalTex = $totalIgst + $totalCgst + $totalSgst + $totalCess;
        $totalInvoiceValue = $gstData->sum('taxable_value');

        $data = [
            'invoices' => $numberOfInvoices,
            'taxable_value' => $taxableValue,
            'total_igst' => $totalIgst,
            'total_cgst' => $totalCgst,
            'total_sgst' => $totalSgst,
            'total_cess' => $totalCess,
            'total_tax' => $totalTex,
            'total_invoice_value' => $totalInvoiceValue,
        ];

        return [$data, $gstData];
    }

    public function b2bDifference($booksTransaction, $portalTransaction)
    {
        $books = [];
        $portal = [];
        foreach ($booksTransaction as $transaction) {
            $books[] = [
                'sale_number' => $transaction->sale_number,
                'taxable_value' => $transaction->taxable_value,
                'igst' => $transaction->igst,
                'cgst' => $transaction->cgst,
                'sgst' => $transaction->sgst,
                'cess' => $transaction->cess,
                'invoice_amount' => $transaction->grand_total,
                'date_of_invoice' => $transaction->date_of_invoice,
                'gstin' => $transaction->gstin,
            ];
        }
        foreach ($portalTransaction as $transaction) {
            $portal[] = [
                'invoice_number' => $transaction->invoice_number,
                'taxable_value' => $transaction->taxable_value,
                'igst' => $transaction->igst,
                'cgst' => $transaction->cgst,
                'sgst' => $transaction->sgst,
                'cess' => $transaction->cess,
                'invoice_amount' => $transaction->invoice_amount,
                'invoice_date' => $transaction->invoice_date,
                'gstin' => $transaction->gstin,
            ];
        }
        $booksData = collect($books);    // from your first array
        $portalData = collect($portal);  // from your second array

        $normalize = fn ($val) => preg_replace('/[^a-z0-9]/i', '', strtolower($val));

        // Convert both arrays to associative maps by normalized invoice number
        $booksMap = collect($booksData)->mapWithKeys(function ($item) use ($normalize) {
            return [$normalize($item['sale_number']) => $item];
        });

        $portalMap = collect($portalData)->mapWithKeys(function ($item) use ($normalize) {
            return [$normalize($item['invoice_number']) => $item];
        });

        $matched = [];
        $mismatched = [];
        $missingInPortal = [];
        $missingInBooks = [];

        $compareFields = ['taxable_value', 'igst', 'cgst', 'sgst', 'cess'];

        // Step 1: Compare Books → Portal
        foreach ($booksMap as $key => $bookItem) {
            if (! isset($portalMap[$key])) {
                $missingInPortal[] = $bookItem;

                continue;
            }

            $portalItem = $portalMap[$key];
            $isMatch = true;

            foreach ($compareFields as $field) {
                $bookVal = round(floatval($bookItem[$field]), 2);
                $portalVal = round(floatval($portalItem[$field]), 2);
                if (abs($bookVal - $portalVal) > 1) {
                    $isMatch = false;
                    break;
                }
            }

            if ($isMatch) {
                $matched[] = $bookItem;
            } else {
                $mismatched[] = ['book' => $bookItem, 'portal' => $portalItem];
            }
        }

        // Step 2: Compare Portal → Books
        foreach ($portalMap as $key => $portalItem) {
            if (! isset($booksMap[$key])) {
                $missingInBooks[] = $portalItem;
            }
        }

        $matchedData = collect($matched);
        $mismatchedData = collect($mismatched);
        $missingInBooksData = collect($missingInBooks);
        $missingInPortalData = collect($missingInPortal);

        $data['matched'] = [
            'invoices' => $matchedData->count(),
            'taxable_value' => $matchedData->sum('taxable_value'),
            'total_igst' => $matchedData->sum('igst'),
            'total_cgst' => $matchedData->sum('cgst'),
            'total_sgst' => $matchedData->sum('sgst'),
            'total_cess' => $matchedData->sum('cess'),
            'total_tax' => $matchedData->sum('taxable_value') + $matchedData->sum('igst') + $matchedData->sum('cgst') + $matchedData->sum('sgst') + $matchedData->sum('cess'),
            'total_invoice_value' => $matchedData->sum('invoice_amount'),
        ];

        $data['mismatched'] = [
            'invoices' => $mismatchedData->count(),
            'taxable_value' => $mismatchedData->sum('taxable_value'),
            'total_igst' => $mismatchedData->sum('igst'),
            'total_cgst' => $mismatchedData->sum('cgst'),
            'total_sgst' => $mismatchedData->sum('sgst'),
            'total_cess' => $mismatchedData->sum('cess'),
            'total_tax' => $mismatchedData->sum('taxable_value') + $mismatchedData->sum('igst') + $mismatchedData->sum('cgst') + $mismatchedData->sum('sgst') + $mismatchedData->sum('cess'),
            'total_invoice_value' => $mismatchedData->sum('invoice_amount'),
        ];
        $data['missing_in_books'] = [
            'invoices' => $missingInBooksData->count(),
            'taxable_value' => $missingInBooksData->sum('taxable_value'),
            'total_igst' => $missingInBooksData->sum('igst'),
            'total_cgst' => $missingInBooksData->sum('cgst'),
            'total_sgst' => $missingInBooksData->sum('sgst'),
            'total_cess' => $missingInBooksData->sum('cess'),
            'total_tax' => $missingInBooksData->sum('taxable_value') + $missingInBooksData->sum('igst') + $missingInBooksData->sum('cgst') + $missingInBooksData->sum('sgst') + $missingInBooksData->sum('cess'),
            'total_invoice_value' => $missingInBooksData->sum('invoice_amount'),
        ];
        $data['missing_in_portal'] = [
            'invoices' => $missingInPortalData->count(),
            'taxable_value' => $missingInPortalData->sum('taxable_value'),
            'total_igst' => $missingInPortalData->sum('igst'),
            'total_cgst' => $missingInPortalData->sum('cgst'),
            'total_sgst' => $missingInPortalData->sum('sgst'),
            'total_cess' => $missingInPortalData->sum('cess'),
            'total_tax' => $missingInPortalData->sum('taxable_value') + $missingInPortalData->sum('igst') + $missingInPortalData->sum('cgst') + $missingInPortalData->sum('sgst') + $missingInPortalData->sum('cess'),
            'total_invoice_value' => $missingInPortalData->sum('invoice_amount'),
        ];

        return $data;
    }

    public function cdnrBooks($input)
    {
        $startDate = $input['start_date'];
        $endDate = $input['end_date'];
        $purchaseReturn = PurchaseReturnTransaction::whereBetween('voucher_date', [$startDate, $endDate])->whereNotNull('gstin')->get();
        $expense = ExpenseDebitNoteTransaction::whereBetween('voucher_date', [$startDate, $endDate])->whereNotNull('gstin')->get();

        $purchaseReturn->merge($expense);

        $numberOfInvoices = $purchaseReturn->count();
        $taxableValue = $purchaseReturn->sum('grand_total');
        $totalIgst = $purchaseReturn->sum('igst');
        $totalCgst = $purchaseReturn->sum('cgst');
        $totalSgst = $purchaseReturn->sum('sgst');
        $totalCess = $purchaseReturn->sum('cess');
        $totalTex = $totalIgst + $totalCgst + $totalSgst + $totalCess;
        $totalInvoiceValue = $purchaseReturn->sum('taxable_value');

        $data = [
            'invoices' => $numberOfInvoices,
            'taxable_value' => $taxableValue,
            'total_igst' => $totalIgst,
            'total_cgst' => $totalCgst,
            'total_sgst' => $totalSgst,
            'total_cess' => $totalCess,
            'total_tax' => $totalTex,
            'total_invoice_value' => $totalInvoiceValue,
        ];

        return [$data, $purchaseReturn];
    }
}
