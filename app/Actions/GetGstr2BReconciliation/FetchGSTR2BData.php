<?php

namespace App\Actions\GetGstr2BReconciliation;

use App\Actions\Report\GstDashboard\API\GetApiForGSTR2BData;
use App\Models\GstDashboardData;
use App\Models\Gstr2bSummaryTransaction;
use App\Models\GstrLogin;
use Carbon\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

class FetchGSTR2BData
{
    use AsAction;

    public function handle($input)
    {
        $filingPeriod = ! empty($input['end_date']) ? Carbon::parse($input['end_date'])->format('mY') : null;
        $gstDashboardData = GstDashboardData::with('saleGstR2BData')->where('month', $filingPeriod)->first();

        if (isset($gstDashboardData) && isset($gstDashboardData->saleGstR2BData)) {
            $gstr2bData = json_decode($gstDashboardData->saleGstR2BData->gstr_2b);

            $storeGstr2BData = $this->gstr2BDataStore($gstr2bData, $filingPeriod);
        } else {
            $company = getCurrentCompany();
            $gstin = $company->companyTax->gstin;

            $filingPeriod = ! empty($input['end_date']) ? Carbon::parse($input['end_date'])->format('mY') : null;

            $dashboardData = [
                'company_id' => $company->id,
                'gstin' => $gstin,
            ];
            $gstLogin = GstrLogin::whereCompanyId($company->id)->first()->toArray();
            $month['ret_prd'] = $filingPeriod;
            $data = GetApiForGSTR2BData::run($dashboardData, $gstLogin, $month);

            $gstDashboardData = GstDashboardData::with('saleGstR2BData')->where('month', $filingPeriod)->first();

            if (isset($gstDashboardData) && isset($gstDashboardData->saleGstR2BData)) {
                $gstr2bData = json_decode($gstDashboardData->saleGstR2BData->gstr_2b);
                $storeGstr2BData = $this->gstr2BDataStore($gstr2bData, $filingPeriod);
            }
        }

    }

    public function gstr2BDataStore($gstr2bData, $filingPeriod)
    {

        $cdnrData = $gstr2bData->data->docdata->cdnr ?? [];
        $b2bData = $gstr2bData->data->docdata->b2b;

        $currentCompany = getCurrentCompany();
        $data = [];
        foreach ($b2bData as $b2b) {
            $gstin = $b2b->ctin;
            foreach ($b2b->inv as $invoice) {
                if (isset($invoice->items)) {
                    $itemsData = $invoice->items;
                    $taxableValue = collect($itemsData)->sum('txval');
                    $igst = collect($itemsData)->sum('igst');
                    $cgst = collect($itemsData)->sum('cgst');
                    $sgst = collect($itemsData)->sum('sgst');
                    $cess = collect($itemsData)->sum('cess');
                    $totalTax = $igst + $cgst + $sgst + $cess;
                } else {
                    $taxableValue = $invoice->txval;
                    $igst = $invoice->igst;
                    $cgst = $invoice->cgst;
                    $sgst = $invoice->sgst;
                    $cess = $invoice->cess;
                    $totalTax = $igst + $cgst + $sgst + $cess;
                }
                $invoiceNum = $invoice->inum;
                $invoiceAmount = $invoice->val;
                $date = $invoice->dt;
                $data[] = [
                    'company_id' => $currentCompany->id,
                    'return_period' => $filingPeriod,
                    'transaction_type' => 1,
                    'gstin' => $gstin,
                    'invoice_number' => $invoiceNum,
                    'taxable_value' => $taxableValue,
                    'igst' => $igst,
                    'cgst' => $cgst,
                    'sgst' => $sgst,
                    'cess' => $cess,
                    'total_tax' => $totalTax,
                    'invoice_amount' => $invoiceAmount,
                    'invoice_date' => Carbon::parse($date),
                ];
            }
        }
        foreach ($cdnrData as $cdnr) {
            $gstin = $cdnr->ctin;
            foreach ($cdnr->nt as $invoice) {
                if (isset($invoice->items)) {
                    $itemsData = $invoice->items;
                    $taxableValue = collect($itemsData)->sum('txval');
                    $igst = collect($itemsData)->sum('igst');
                    $cgst = collect($itemsData)->sum('cgst');
                    $sgst = collect($itemsData)->sum('sgst');
                    $cess = collect($itemsData)->sum('cess');
                    $totalTax = $igst + $cgst + $sgst + $cess;
                } else {
                    $taxableValue = $invoice->txval;
                    $igst = $invoice->igst;
                    $cgst = $invoice->cgst;
                    $sgst = $invoice->sgst;
                    $cess = $invoice->cess;
                    $totalTax = $igst + $cgst + $sgst + $cess;
                }
                $invoiceNum = $invoice->ntnum;
                $invoiceAmount = $invoice->val;
                $date = $invoice->dt;

                $data[] = [
                    'company_id' => $currentCompany->id,
                    'return_period' => $filingPeriod,
                    'transaction_type' => 2,
                    'gstin' => $gstin,
                    'invoice_number' => $invoiceNum,
                    'taxable_value' => $taxableValue,
                    'igst' => $igst,
                    'cgst' => $cgst,
                    'sgst' => $sgst,
                    'cess' => $cess,
                    'total_tax' => $totalTax,
                    'invoice_amount' => $invoiceAmount,
                    'invoice_date' => Carbon::parse($date),
                ];
            }
        }

        if (! empty($data)) {
            Gstr2bSummaryTransaction::upsert(
                $data,
                ['company_id', 'gstin', 'invoice_number', 'invoice_date'],
                [
                    'return_period',
                    'transaction_type',
                    'taxable_value',
                    'igst',
                    'cgst',
                    'sgst',
                    'cess',
                    'total_tax',
                    'invoice_amount',
                ]
            );
        }

    }
}
