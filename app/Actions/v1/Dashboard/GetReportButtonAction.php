<?php

namespace App\Actions\v1\Dashboard;

use App\Models\CompanyDashboardButton;
use Carbon\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

class GetReportButtonAction
{
    use AsAction;

    public function handle($user, $company)
    {
        $buttons = [];
        if ($user->can('company_view_trading_profit_loss_report')) {
            $buttons[] = $this->createButtonData(
                'Trading P & L',
                route('company.trading-profit-loss', [
                    'start_date' => getDateCompanyFilter('trading_profit_loss_report_date')[0],
                    'end_date' => getDateCompanyFilter('trading_profit_loss_report_date')[1],
                    'details' => false,
                    'hideZeroBalance' => false,
                ]),
                'Shortcut Key : Shift + 2'
            );
        }

        if ($user->can('company_view_balance_sheet_report')) {
            $buttons[] = $this->createButtonData(
                'Balance Sheet',
                route('company.balance-sheet-report', [
                    'date' => getCompanyFilters()['balance_sheet_date'] ?? Carbon::now()->format('d-m-Y'),
                ]),
                'Shortcut Key : Shift + 3'
            );
        }

        if ($user->can('company_view_ledger_report')) {
            $buttons[] = $this->createButtonData(
                'Ledger',
                route('company.ledger-report'),
                'Shortcut Key : Shift + 1'
            );
        }

        // GST Reports
        if ($company->is_gst_applicable && $this->userHasGSTPermissions($user)) {
            $gstButtons = $this->getGSTButtons($user);
            $buttons[] = [
                'name' => 'GST Report',
                'url' => '',
                'tooltip' => '',
                'sub_buttons' => $gstButtons,
            ];
        }

        // TDS Reports
        if ($company->is_tds_applicable && $this->userHasTDSPermissions($user)) {
            $tdsButtons = $this->getTDSButtons($user);
            $buttons[] = [
                'name' => 'TDS Report',
                'url' => '',
                'tooltip' => '',
                'sub_buttons' => $tdsButtons,
            ];
        }

        $companyDashboardButton = $this->getCompanyDashboardButton($company->id);
        foreach ($companyDashboardButton as $buttonData) {
            $buttons[] = [
                'button_id' => $buttonData->button_id,
                'name' => CompanyDashboardButton::REPORT_ARR[$buttonData->button_id],
                'url' => $this->dynamicButtonUrl($buttonData->button_id),
                'tooltip' => $this->dynamicButtonTooltip($buttonData->button_id),
                'is_remove' => true,
            ];
        }

        return $buttons;
    }

    private function createButtonData($name, $url, $tooltip)
    {
        return [
            'button_id' => null,
            'name' => $name,
            'url' => $url,
            'tooltip' => $tooltip,
            'is_remove' => false,
        ];
    }

    private function getGSTButtons($user)
    {
        $gstButtons = [];

        if ($user->can('company_view_gstr_1_report')) {
            $gstButtons[] = $this->createButtonData(
                'GSTR 1',
                route('company.reports.gstr-1', [
                    'start_date' => getDateCompanyFilter('gstr_1_report_date')[0],
                    'end_date' => getDateCompanyFilter('gstr_1_report_date')[1],
                    'filter_type' => 3,
                ]),
                'Shortcut Key : Shift + 8'
            );
        }

        if ($user->can('company_view_gstr_3b_report')) {
            $gstButtons[] = $this->createButtonData(
                'GSTR 3B Summary',
                route('company.reports.gstr-3b-summary', [
                    'start_date' => getDateCompanyFilter('gstr_3b_report_date')[0],
                    'end_date' => getDateCompanyFilter('gstr_3b_report_date')[1],
                ]),
                'Shortcut Key : Shift + 7'
            );
        }

        if ($user->can('company_view_input_tax_report')) {
            $gstButtons[] = $this->createButtonData(
                'Input Tax Register',
                route('company.input-tax-register-report'),
                ''
            );
        }

        if ($user->can('company_view_output_tax_report')) {
            $gstButtons[] = $this->createButtonData(
                'Output Tax Register',
                route('company.output-tax-register-report'),
                ''
            );
        }

        if ($user->can('company_view_hsn_outward_report')) {
            $gstButtons[] = $this->createButtonData(
                'HSN Summary Outward',
                route('company.hsn-summary-outward-report'),
                ''
            );
        }

        if ($user->can('company_view_hsn_inward_report')) {
            $gstButtons[] = $this->createButtonData(
                'HSN Summary Inward',
                route('company.hsn-summary-inward-report'),
                ''
            );
        }

        return $gstButtons;
    }

    private function getTDSButtons($user)
    {
        $tdsButtons = [];

        if ($user->can('company_view_tds_liability_report')) {
            $tdsButtons[] = $this->createButtonData(
                'TDS Liability Statement',
                route('company.tds-liability-report'),
                'Shortcut Key : Shift + 9'
            );
        }

        if ($user->can('company_view_tcs_liability_report')) {
            $tdsButtons[] = $this->createButtonData(
                'TCS Liability Statement',
                route('company.tcs-liability-report'),
                ''
            );
        }

        if ($user->can('company_view_tds_return_report')) {
            $tdsButtons[] = $this->createButtonData(
                'TDS Return Statement',
                route('company.tds-return-report'),
                ''
            );
        }

        if ($user->can('company_view_tcs_return_report')) {
            $tdsButtons[] = $this->createButtonData(
                'TCS Return Statement',
                route('company.tcs-return-report'),
                ''
            );
        }

        return $tdsButtons;
    }

    private function userHasGSTPermissions($user)
    {
        return $user->can('company_view_gstr_1_report') ||
               $user->can('company_view_gstr_3b_report') ||
               $user->can('company_view_input_tax_report') ||
               $user->can('company_view_output_tax_report') ||
               $user->can('company_view_hsn_outward_report') ||
               $user->can('company_view_hsn_inward_report');
    }

    private function userHasTDSPermissions($user)
    {
        return $user->can('company_view_tds_liability_report') ||
               $user->can('company_view_tcs_liability_report') ||
               $user->can('company_view_tds_return_report') ||
               $user->can('company_view_tcs_return_report');
    }

    private function getCompanyDashboardButton($companyId)
    {
        $companyDashboardButton = CompanyDashboardButton::whereCompanyId($companyId)
            ->whereNotIn('button_id', [
                CompanyDashboardButton::TREND_ANALYSIS,
                CompanyDashboardButton::GSTR_2A,
                CompanyDashboardButton::GSTR_2B,
                CompanyDashboardButton::GSTR_3B_VS_1,
                CompanyDashboardButton::GSTR_9,
                CompanyDashboardButton::GSTR_9C,
            ])
            ->take(5)->get();

        return $companyDashboardButton;
    }

    private function dynamicButtonTooltip($buttonId)
    {
        switch ($buttonId) {
            case CompanyDashboardButton::TRIAL_BALANCE:
                return 'Shortcut Key : Shift + 4';

            case CompanyDashboardButton::CASH_FLOW:
                return 'Shortcut Key : Shift + 5';

            case CompanyDashboardButton::STOCK_REPORT:
                return 'Shortcut Key : Shift + 6';

            case CompanyDashboardButton::GSTR_3B_SUMMARY:
                return 'Shortcut Key : Shift + 7';

            case CompanyDashboardButton::GSTR_1:
                return 'Shortcut Key : Shift + 8';

            default:
                return null;
        }
    }

    private function dynamicButtonUrl($buttonId)
    {
        switch ($buttonId) {
            case CompanyDashboardButton::OUTSTANDING:
                return route('company.outstanding-report');

            case CompanyDashboardButton::AGEING_REPORT:
                return route('company.ageing-report');

            case CompanyDashboardButton::SALE_REPORT:
                return route('company.sale-report');

            case CompanyDashboardButton::PURCHASE_REPORT:
                return route('company.purchase-report');

            case CompanyDashboardButton::DAY_BOOK_REPORT:
                return route('company.day-book-report');

            case CompanyDashboardButton::CASH_BANK_REPORT:
                return route('company.cash-bank-report');

            case CompanyDashboardButton::STOCK_REPORT:
                return route('company.report-stock', [
                    'start_date' => getDateCompanyFilter('stock_report_date')[0],
                    'end_date' => getDateCompanyFilter('stock_report_date')[1],
                ]);

            case CompanyDashboardButton::BROKER_REPORT:
                return route('company.broker-report');

            case CompanyDashboardButton::TREND_ANALYSIS:
                return '';

            case CompanyDashboardButton::TRIAL_BALANCE:
                return route('company.trial-balance-report');

            case CompanyDashboardButton::CASH_FLOW:
                return route('company.cash-flow-report');

            case CompanyDashboardButton::GSTR_1:
                return route('company.reports.gstr-1', [
                    'start_date' => getDateCompanyFilter('gstr_1_report_date')[0],
                    'end_date' => getDateCompanyFilter('gstr_1_report_date')[1],
                    'filter_type' => 3,
                ]);

            case CompanyDashboardButton::GSTR_3B_SUMMARY:
                return route('company.reports.gstr-3b-summary', [
                    'start_date' => getDateCompanyFilter('gstr_3b_report_date')[0],
                    'end_date' => getDateCompanyFilter('gstr_3b_report_date')[1],
                ]);

            case CompanyDashboardButton::INPUT_TAX_REGISTER:
                return route('company.input-tax-register-report');

            case CompanyDashboardButton::OUTPUT_TAX_REGISTER:
                return route('company.output-tax-register-report');

            case CompanyDashboardButton::HSN_SUMMARY_INWARD:
                return route('company.hsn-summary-outward-report');

            case CompanyDashboardButton::HSN_SUMMARY_OUTWARD:
                return route('company.hsn-summary-inward-report');

            case CompanyDashboardButton::TDS_RETURN_STATEMENT:
                return route('company.tds-return-report');

            case CompanyDashboardButton::TCS_LIABILITY_STATEMENT:
                return route('company.tcs-liability-report');

            case CompanyDashboardButton::TCS_RETURN_STATEMENT:
                return route('company.tcs-return-report');

            default:
                return '#';
        }
    }
}
