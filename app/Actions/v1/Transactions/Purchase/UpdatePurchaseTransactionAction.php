<?php

namespace App\Actions\v1\Transactions\Purchase;

use App\Actions\CommonAction\CheckGrandTotalWithPaymentAction;
use App\Actions\Subscription\StartTrialPeriod;
use App\Models\PurchaseTransaction;
use App\Repositories\v1\PurchaseTransactionRepository;
use Illuminate\Support\Facades\App;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class UpdatePurchaseTransactionAction
{
    use AsAction;

    public function handle($input, $purchaseTransaction)
    {
        $data = [];
        StartTrialPeriod::run();

        $input['company'] = getCurrentCompany();

        enableDeletedScope();
        $purchaseTransactionExist = PurchaseTransaction::whereVoucherNumber($input['voucher_number'])
            ->financialYearDate()
            ->whereCompanyId($input['company']->id)
            ->whereNot('id', $purchaseTransaction->id)
            ->exists();

        if ($purchaseTransactionExist) {
            throw new UnprocessableEntityHttpException('The voucher number has already been taken.');
        }
        disableDeletedScope();

        // Check grand total with payment
        CheckGrandTotalWithPaymentAction::run($input);

        /** @var PurchaseTransactionRepository $purchaseTransactionRepository */
        $purchaseTransactionRepository = App::make(PurchaseTransactionRepository::class);
        $purchaseTransactionUpdate = $purchaseTransactionRepository->update($input, $purchaseTransaction);

        // TO_COMMENT =>
        $data['purchaseTransaction'] = $purchaseTransactionUpdate;
        $data['purchase_transaction'] = $purchaseTransactionUpdate;

        return $data;
    }
}
