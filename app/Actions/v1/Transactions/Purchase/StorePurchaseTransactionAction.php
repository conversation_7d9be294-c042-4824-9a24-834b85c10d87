<?php

namespace App\Actions\v1\Transactions\Purchase;

use App\Actions\CommonAction\CheckGrandTotalWithPaymentAction;
use App\Actions\Expense\Purchase\GetPurchaseVoucherNumber;
use App\Actions\Subscription\StartTrialPeriod;
use App\Models\Ledger;
use App\Models\Master\Customer;
use App\Models\Master\ExpenseTransactionMaster;
use App\Models\Master\Supplier;
use App\Models\PurchaseTransaction;
use App\Repositories\v1\PurchaseTransactionRepository;
use Illuminate\Support\Facades\App;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class StorePurchaseTransactionAction
{
    use AsAction;

    public function handle($input)
    {
        $data = [];
        StartTrialPeriod::run();

        $input['company'] = getCurrentCompany();

        $expenseTransactionMaster = ExpenseTransactionMaster::whereCompanyId($input['company']->id)->first();

        enableDeletedScope();
        $purchaseTransactionExist = PurchaseTransaction::whereVoucherNumber($input['voucher_number'])
            ->financialYearDate()
            ->whereCompanyId($input['company']->id)
            ->exists();

        if ($purchaseTransactionExist) {
            if ($expenseTransactionMaster && $expenseTransactionMaster->voucher_number == ExpenseTransactionMaster::AUTOMATIC) {
                $nextVoucherNumber = GetPurchaseVoucherNumber::run();
                $input['voucher_number'] = $nextVoucherNumber;
                $purchaseTransactionExist = PurchaseTransaction::whereVoucherNumber($input['voucher_number'])
                    ->financialYearDate()
                    ->whereCompanyId($input['company']->id)
                    ->exists();
                if ($purchaseTransactionExist) {
                    throw new UnprocessableEntityHttpException('Unable to generate unique voucher number. Please try again.');
                }
            } else {
                throw new UnprocessableEntityHttpException('The voucher number has already been taken.');
            }
        }
        disableDeletedScope();

        // Check grand total with payment
        CheckGrandTotalWithPaymentAction::run($input);

        /** @var PurchaseTransactionRepository $purchaseTransactionRepository */
        $purchaseTransactionRepository = App::make(PurchaseTransactionRepository::class);
        $purchaseTransaction = $purchaseTransactionRepository->store($input);

        // TO_COMMENT =>
        $data['purchaseTransaction'] = $purchaseTransaction;

        $ledger = Ledger::whereId($input['supplier_ledger_id'])->whereIn('model_type', [Supplier::class, Customer::class])->first();
        $supplier = Supplier::whereId($ledger?->model_id ?? 0)->first();
        // TO_COMMENT =>
        $data['isTdsApplicable'] = $supplier?->is_tds_applicable ?? false;

        $data['purchase_transaction'] = $purchaseTransaction;
        $data['is_tds_applicable'] = $supplier?->is_tds_applicable ?? false;

        return $data;
    }
}
