<?php

namespace App\Actions\v1\Transactions\ExpenseCreditNote;

use App\Actions\CommonAction\CheckGrandTotalWithPaymentAction;
use App\Models\ExpenseCreditNoteTransaction;
use App\Repositories\v1\ExpenseCNTransactionRepository;
use Illuminate\Support\Facades\App;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class UpdateExpenseCNTransactionAction
{
    use AsAction;

    public function handle($input, $expenseCNTransaction)
    {
        $input['company'] = getCurrentCompany();

        enableDeletedScope();
        $expenseCNTransactionExist = ExpenseCreditNoteTransaction::whereVoucherNumber($input['voucher_number'])
            ->financialYearDate()
            ->whereCompanyId($input['company']->id)
            ->whereNot('id', $expenseCNTransaction->id)
            ->exists();

        if ($expenseCNTransactionExist) {
            throw new UnprocessableEntityHttpException('The voucher number has already been taken.');
        }

        $data = [];

        if (isset($input['original_inv_no']) && ! empty($input['original_inv_no'])) {
            $response = CheckUpdateTransactionItemsQuantityAction::run($input, $expenseCNTransaction);
            if (isset($response['status']) && $response['status']) {
                return $response;
            }
        }
        disableDeletedScope();

        // Check grand total with payment
        CheckGrandTotalWithPaymentAction::run($input);

        /** @var ExpenseCNTransactionRepository $expenseCNTransactionRepository */
        $expenseCNTransactionRepository = App::make(ExpenseCNTransactionRepository::class);
        $expenseCNTransactionUpdate = $expenseCNTransactionRepository->update($input, $expenseCNTransaction);

        // TO_COMMENT =>
        $data['expenseCNTransaction'] = $expenseCNTransactionUpdate;

        $data['expense_credit_note_transaction'] = $expenseCNTransactionUpdate;

        return $data;
    }
}
