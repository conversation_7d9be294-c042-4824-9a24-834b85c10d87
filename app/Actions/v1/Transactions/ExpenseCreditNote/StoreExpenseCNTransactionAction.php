<?php

namespace App\Actions\v1\Transactions\ExpenseCreditNote;

use App\Actions\CommonAction\CheckGrandTotalWithPaymentAction;
use App\Actions\Expense\CreditNote\GetExpenseCreditNoteInvoiceNumber;
use App\Actions\Subscription\StartTrialPeriod;
use App\Models\ExpenseCreditNoteTransaction;
use App\Models\Ledger;
use App\Models\Master\Customer;
use App\Models\Master\ExpenseCreditNoteTransactionMaster;
use App\Models\Master\Supplier;
use App\Repositories\v1\ExpenseCNTransactionRepository;
use Illuminate\Support\Facades\App;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class StoreExpenseCNTransactionAction
{
    use AsAction;

    public function handle($input)
    {
        StartTrialPeriod::run();

        $input['company'] = getCurrentCompany();

        $expenseCreditNoteTransactionMaster = ExpenseCreditNoteTransactionMaster::whereCompanyId($input['company']->id)->first();

        enableDeletedScope();
        $purchaseTransactionExist = ExpenseCreditNoteTransaction::whereVoucherNumber($input['voucher_number'])
            ->financialYearDate()
            ->whereCompanyId($input['company']->id)
            ->exists();

        if ($purchaseTransactionExist) {
            if ($expenseCreditNoteTransactionMaster && $expenseCreditNoteTransactionMaster->voucher_number == ExpenseCreditNoteTransactionMaster::AUTOMATIC) {
                $nextVoucherNumber = GetExpenseCreditNoteInvoiceNumber::run();
                $input['voucher_number'] = $nextVoucherNumber;
                $purchaseTransactionExist = ExpenseCreditNoteTransaction::whereVoucherNumber($input['voucher_number'])
                    ->financialYearDate()
                    ->whereCompanyId($input['company']->id)
                    ->exists();
                if ($purchaseTransactionExist) {
                    throw new UnprocessableEntityHttpException('Unable to generate unique voucher number. Please try again.');
                }
            } else {
                throw new UnprocessableEntityHttpException('The voucher number has already been taken.');
            }
        }
        disableDeletedScope();

        $data = [];

        if (isset($input['original_inv_no']) && ! empty($input['original_inv_no'])) {
            $response = CheckTransactionItemsQuantityAction::run($input);
            if (isset($response['status']) && $response['status']) {
                return $response;
            }
        }

        // Check grand total with payment
        CheckGrandTotalWithPaymentAction::run($input);

        /** @var ExpenseCNTransactionRepository $expenseCNTransactionRepository */
        $expenseCNTransactionRepository = App::make(ExpenseCNTransactionRepository::class);
        $expenseCNTransaction = $expenseCNTransactionRepository->store($input);
        // TO_COMMENT =>
        $data['expenseCNTransaction'] = $expenseCNTransaction;

        $ledger = Ledger::whereId($input['supplier_id'])->whereIn('model_type', [Supplier::class, Customer::class])->firstOrFail();
        $supplier = Supplier::whereId($ledger->model_id)->first();
        $isTdsApplicable = false;
        if (! empty($supplier)) {
            $isTdsApplicable = $supplier?->is_tds_applicable;
        } else {
            $isTdsApplicable = false;
        }
        // TO_COMMENT =>
        $data['isTdsApplicable'] = $isTdsApplicable;

        $data['expense_credit_note_transaction'] = $expenseCNTransaction;
        $data['is_tds_applicable'] = $isTdsApplicable;

        return $data;
    }
}
