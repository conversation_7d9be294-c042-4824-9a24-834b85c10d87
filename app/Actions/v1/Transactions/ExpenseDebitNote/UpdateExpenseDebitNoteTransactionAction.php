<?php

namespace App\Actions\v1\Transactions\ExpenseDebitNote;

use App\Actions\CommonAction\CheckGrandTotalWithPaymentAction;
use App\Models\ExpenseDebitNoteTransaction;
use App\Models\PurchaseTransaction;
use App\Repositories\v1\ExpenseDebitNoteTransactionRepository;
use Illuminate\Support\Facades\App;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class UpdateExpenseDebitNoteTransactionAction
{
    use AsAction;

    public function handle($input, $expenseDebitNoteTransaction)
    {
        $input['company'] = getCurrentCompany();

        enableDeletedScope();
        $expenseDebitNoteTransactionExist = ExpenseDebitNoteTransaction::whereVoucherNumber($input['voucher_number'])
            ->financialYearDate()
            ->whereCompanyId($input['company']->id)
            ->whereNot('id', $expenseDebitNoteTransaction->id)
            ->exists();

        if ($expenseDebitNoteTransactionExist) {
            throw new UnprocessableEntityHttpException('The debit note number has already been taken.');
        }

        $data = [];

        $isOriginalInvoiceSet = isset($input['original_inv_no']) && ! empty($input['original_inv_no']);

        // if ($isOriginalInvoiceSet) {
        //     $response = CheckUpdateTransactionItemsQuantityAction::run($input, $expenseDebitNoteTransaction);
        //     if (isset($response['status']) && $response['status']) {
        //         return $response;
        //     }
        // }
        disableDeletedScope();

        // Check grand total with payment
        CheckGrandTotalWithPaymentAction::run($input);

        /** @var ExpenseDebitNoteTransactionRepository ExpenseDebitNoteTransactionRepository */
        $expenseDebitNoteTransactionRepository = App::make(ExpenseDebitNoteTransactionRepository::class);
        $expenseDebitNoteTransactionUpdate = $expenseDebitNoteTransactionRepository->update($input, $expenseDebitNoteTransaction);

        // TO_COMMENT =>
        $data['expenseDebitNoteTransaction'] = $expenseDebitNoteTransactionUpdate;
        // TO_COMMENT =>
        $data['transactionId'] = $data['expenseDebitNoteTransaction']->id;

        $data['expense_debit_note_transaction'] = $expenseDebitNoteTransactionUpdate;
        $data['transaction_id'] = $expenseDebitNoteTransaction->id;

        if ($isOriginalInvoiceSet) {
            $purchaseTransaction = PurchaseTransaction::findOrFail($expenseDebitNoteTransactionUpdate->original_inv_no);

            /* Update Taxable Value, Payment Status And Due Amount Column Value For Purchase Transaction */
            updateFieldsValue($purchaseTransaction);
        }

        return $data;
    }
}
