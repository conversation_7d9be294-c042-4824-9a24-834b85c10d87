<?php

namespace App\Actions\v1\Transactions\ExpenseDebitNote;

use App\Actions\CommonAction\CheckGrandTotalWithPaymentAction;
use App\Actions\Expense\DebitNote\GetExpenseDebitNoteInvoiceNumber;
use App\Actions\Subscription\StartTrialPeriod;
use App\Models\ExpenseDebitNoteTransaction;
use App\Models\Master\ExpenseDebitNoteTransactionMaster;
use App\Models\PurchaseTransaction;
use App\Repositories\v1\ExpenseDebitNoteTransactionRepository;
use Illuminate\Support\Facades\App;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class StoreExpenseDebitNoteTransactionAction
{
    use AsAction;

    public function handle($input)
    {
        StartTrialPeriod::run();

        $input['company'] = getCurrentCompany();

        $expenseDebitNoteTransactionMaster = ExpenseDebitNoteTransactionMaster::whereCompanyId($input['company']->id)->first();

        enableDeletedScope();
        $expenseDebitNoteTransactionExist = ExpenseDebitNoteTransaction::whereVoucherNumber($input['voucher_number'])
            ->financialYearDate()
            ->whereCompanyId($input['company']->id)
            ->exists();

        if ($expenseDebitNoteTransactionExist) {
            if ($expenseDebitNoteTransactionMaster && $expenseDebitNoteTransactionMaster->voucher_number == ExpenseDebitNoteTransactionMaster::AUTOMATIC) {
                $nextVoucherNumber = GetExpenseDebitNoteInvoiceNumber::run();
                $input['voucher_number'] = $nextVoucherNumber;
                $expenseDebitNoteTransactionExist = ExpenseDebitNoteTransaction::whereVoucherNumber($input['voucher_number'])
                    ->financialYearDate()
                    ->whereCompanyId($input['company']->id)
                    ->exists();
                if ($expenseDebitNoteTransactionExist) {
                    throw new UnprocessableEntityHttpException('Unable to generate unique voucher number. Please try again.');
                }
            } else {
                throw new UnprocessableEntityHttpException('The debit note number has already been taken.');
            }
        }

        $data = [];
        $isOriginalInvoiceSet = isset($input['original_inv_no']) && ! empty($input['original_inv_no']);

        // if ($isOriginalInvoiceSet) {
        //     $response = CheckTransactionItemsQuantityAction::run($input);
        //     if (isset($response['status']) && $response['status']) {
        //         return $response;
        //     }
        // }
        disableDeletedScope();

        // Check grand total with payment
        CheckGrandTotalWithPaymentAction::run($input);

        /** @var ExpenseDebitNoteTransactionRepository ExpenseDebitNoteTransactionRepository */
        $expenseDebitNoteTransactionRepository = App::make(ExpenseDebitNoteTransactionRepository::class);
        $expenseDebitNoteTransaction = $expenseDebitNoteTransactionRepository->store($input);

        // TO_COMMENT =>
        $data['expenseDebitNoteTransaction'] = $expenseDebitNoteTransaction;
        // TO_COMMENT =>
        $data['transactionId'] = $data['expenseDebitNoteTransaction']->id;

        $data['expense_debit_note_transaction'] = $expenseDebitNoteTransaction;
        $data['transaction_id'] = $expenseDebitNoteTransaction->id;

        if ($isOriginalInvoiceSet) {
            $purchaseTransaction = PurchaseTransaction::findOrFail($expenseDebitNoteTransaction->original_inv_no);

            /* Update Taxable Value, Payment Status And Due Amount Column Value For Purchase Transaction */
            updateFieldsValue($purchaseTransaction);
        }

        return $data;
    }
}
