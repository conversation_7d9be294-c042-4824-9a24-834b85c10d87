<?php

namespace App\Actions\v1\Transactions\PurchaseReturn;

use App\Actions\CommonAction\CheckGrandTotalWithPaymentAction;
use App\Models\PurchaseReturnTransaction;
use App\Models\PurchaseTransaction;
use App\Repositories\v1\PurchaseReturnTransactionRepository;
use Illuminate\Support\Facades\App;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class UpdatePurchaseReturnTransactionAction
{
    use AsAction;

    public function handle($input, $purchaseReturnTransaction)
    {
        $input['company'] = getCurrentCompany();

        enableDeletedScope();
        $purchaseReturnTransactionExist = PurchaseReturnTransaction::whereVoucherNumber($input['voucher_number'])
            ->financialYearDate()
            ->whereCompanyId($input['company']->id)
            ->whereNot('id', $purchaseReturnTransaction->id)
            ->exists();

        if ($purchaseReturnTransactionExist) {
            throw new UnprocessableEntityHttpException('The invoice number has already been taken.');
        }

        $data = [];

        $isOriginalInvoiceSet = isset($input['original_inv_no']) && ! empty($input['original_inv_no']);

        if ($isOriginalInvoiceSet) {
            $response = CheckUpdateTransactionItemsQuantityAction::run($input, $purchaseReturnTransaction);
            if (isset($response['status']) && $response['status']) {
                return $response;
            }
        }
        disableDeletedScope();

        // Check grand total with payment
        CheckGrandTotalWithPaymentAction::run($input);

        /** @var PurchaseReturnTransactionRepository $purchaseReturnTransactionRepository */
        $purchaseReturnTransactionRepository = App::make(PurchaseReturnTransactionRepository::class);
        $purchaseReturnTransactionUpdate = $purchaseReturnTransactionRepository->update($input, $purchaseReturnTransaction);

        // TO_COMMENT =>
        $data['purchaseReturnTransaction'] = $purchaseReturnTransactionUpdate;
        // TO_COMMENT =>
        $data['transactionId'] = $data['purchaseReturnTransaction']->id;

        $data['purchase_return_transaction'] = $purchaseReturnTransactionUpdate;
        $data['transaction_id'] = $purchaseReturnTransaction->id;

        if ($isOriginalInvoiceSet) {
            $purchaseTransaction = PurchaseTransaction::findOrFail($purchaseReturnTransactionUpdate->original_inv_no);

            /* Update Taxable Value, Payment Status And Due Amount Column Value For Purchase Transaction */
            updateFieldsValue($purchaseTransaction);
        }

        return $data;
    }
}
