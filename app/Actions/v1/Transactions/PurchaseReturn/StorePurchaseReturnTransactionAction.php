<?php

namespace App\Actions\v1\Transactions\PurchaseReturn;

use App\Actions\CommonAction\CheckGrandTotalWithPaymentAction;
use App\Actions\Expense\PurchaseReturn\GetPurchaseReturnVoucherNumber;
use App\Actions\Subscription\StartTrialPeriod;
use App\Models\Master\ExpenseReturnTransactionMaster;
use App\Models\PurchaseReturnTransaction;
use App\Models\PurchaseTransaction;
use App\Repositories\v1\PurchaseReturnTransactionRepository;
use Illuminate\Support\Facades\App;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class StorePurchaseReturnTransactionAction
{
    use AsAction;

    public function handle($input)
    {
        StartTrialPeriod::run();

        $input['company'] = getCurrentCompany();

        $expenseReturnTransactionMaster = ExpenseReturnTransactionMaster::whereCompanyId($input['company']->id)->first();

        enableDeletedScope();
        $purchaseReturnTransactionExist = PurchaseReturnTransaction::whereVoucherNumber($input['voucher_number'])
            ->financialYearDate()
            ->whereCompanyId($input['company']->id)
            ->exists();

        if ($purchaseReturnTransactionExist) {
            if ($expenseReturnTransactionMaster && $expenseReturnTransactionMaster->voucher_number == ExpenseReturnTransactionMaster::AUTOMATIC) {
                $nextVoucherNumber = GetPurchaseReturnVoucherNumber::run();
                $input['voucher_number'] = $nextVoucherNumber;
                $purchaseReturnTransactionExist = PurchaseReturnTransaction::whereVoucherNumber($input['voucher_number'])
                    ->financialYearDate()
                    ->whereCompanyId($input['company']->id)
                    ->exists();
                if ($purchaseReturnTransactionExist) {
                    throw new UnprocessableEntityHttpException('Unable to generate unique voucher number. Please try again.');
                }
            } else {
                throw new UnprocessableEntityHttpException('The invoice number has already been taken.');
            }
        }
        disableDeletedScope();

        $data = [];

        $isOriginalInvoiceSet = isset($input['original_inv_no']) && ! empty($input['original_inv_no']);

        if ($isOriginalInvoiceSet) {
            $response = CheckTransactionItemsQuantityAction::run($input);
            if (isset($response['status']) && $response['status']) {
                return $response;
            }
        }

        // Check grand total with payment
        CheckGrandTotalWithPaymentAction::run($input);

        /** @var PurchaseReturnTransactionRepository $purchaseReturnTransactionRepository */
        $purchaseReturnTransactionRepository = App::make(PurchaseReturnTransactionRepository::class);
        $purchaseReturnTransaction = $purchaseReturnTransactionRepository->store($input);

        // TO_COMMENT =>
        $data['purchaseReturnTransaction'] = $purchaseReturnTransaction;
        // TO_COMMENT =>
        $data['transactionId'] = $data['purchaseReturnTransaction']->id;

        $data['purchase_return_transaction'] = $purchaseReturnTransaction;
        $data['transaction_id'] = $purchaseReturnTransaction->id;

        if ($isOriginalInvoiceSet) {
            $purchaseTransaction = PurchaseTransaction::findOrFail($purchaseReturnTransaction->original_inv_no);

            /* Update Taxable Value, Payment Status And Due Amount Column Value For Purchase Transaction */
            updateFieldsValue($purchaseTransaction);
        }

        return $data;
    }
}
