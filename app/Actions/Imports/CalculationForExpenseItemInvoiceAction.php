<?php

namespace App\Actions\Imports;

use App\Models\GstTax;
use App\Models\Ledger;
use Lorisleiva\Actions\Concerns\AsAction;

class CalculationForExpenseItemInvoiceAction
{
    use AsAction;

    public function handle($transactions, $importErrors, $fixDigit, $roundMethod)
    {
        foreach ($transactions as $invoiceNo => &$transaction) {
            $isRcmApplicable = $transaction['is_rcm_applicable'];
            $gstData = [];
            $transaction['is_gst_na'] = false;
            $transaction['is_cgst_sgst_igst_calculated'] = true;
            $gstArray = array_filter(array_column($transaction['items'], 'gst'), function ($value) {
                return $value !== null && $value !== '' && strtolower($value) !== 'na' && strtolower($value) !== 'n/a';
            });

            if ($gstArray != null) {
                foreach ($gstArray as $gstRate) {
                    if (strtolower($gstRate) == 'na') {
                        $transaction['is_gst_na'] = true;
                        $transaction['is_cgst_sgst_igst_calculated'] = false;
                    }
                }

                if ($transaction['is_gst_na'] && ! (count(array_unique($gstArray, SORT_REGULAR)) === 1)) {
                    $importErrors[$invoiceNo][] = 'You have not entered same GST rate for NA rate.';
                    unset($transaction[$invoiceNo]);

                    continue;
                }
            } else {
                $transaction['is_gst_na'] = true;
                $transaction['is_cgst_sgst_igst_calculated'] = false;
            }

            if ($transaction['is_gst_enabled']) {
                $gstData = $this->getGSTCalculationDetails($transaction['main_classification_nature_type'], $isRcmApplicable, $invoiceNo, $transaction);
            }

            $transaction['taxable_value'] = 0;
            foreach ($transaction['items'] as &$item) {

                $item['total'] = $item['rpu'] * $item['quantity'];

                if (Ledger::DISCOUNT_TYPE[$item['discount_type']] == Ledger::DISCOUNT_TYPE_PERCENTAGE) {
                    $item['discount_amount_1'] = ($item['rpu'] * $item['discount_value']) / 100;
                    $item['total_discount_amount'] = $item['discount_amount_1'] * $item['quantity'];
                    $rpuWithoutGSTAfterDiscount = round($item['rpu'] - $item['discount_amount_1'], $fixDigit);
                } elseif (Ledger::DISCOUNT_TYPE[$item['discount_type']] == Ledger::DISCOUNT_TYPE_AMOUNT) {
                    $item['total_discount_amount'] = $item['discount_value'] * $item['quantity'];
                    $rpuWithoutGSTAfterDiscount = round($item['rpu'] - $item['discount_value'], $fixDigit);
                } else {
                    $item['total_discount_amount'] = 0;
                    $rpuWithoutGSTAfterDiscount = 0;
                }

                if (Ledger::DISCOUNT_TYPE[$item['discount_type_2']] == Ledger::DISCOUNT_TYPE_PERCENTAGE) {
                    $totalDiscount2Amount = ($rpuWithoutGSTAfterDiscount * $item['discount_value_2']) / 100;
                    $totalDiscount2Amount = $totalDiscount2Amount * $item['quantity'];
                } elseif (Ledger::DISCOUNT_TYPE[$item['discount_type_2']] == Ledger::DISCOUNT_TYPE_AMOUNT) {
                    $totalDiscount2Amount = $item['discount_value_2'] * $item['quantity'];
                } else {
                    $totalDiscount2Amount = 0;
                }

                $item['total_discount_amount'] += $totalDiscount2Amount;
                $item['total_discount_amount'] = round($item['total_discount_amount'], 2);
                $item['total'] = round($item['total'] - $item['total_discount_amount'], $fixDigit);
                $transaction['taxable_value'] += $item['total'];

                if ($transaction['is_gst_enabled']) {
                    if ($gstData['isGSTCalculated'] && ! $transaction['is_gst_na'] && ! in_array(strtolower($item['gst']), ['na', 'n/a', '0', 'exempt'])) {
                        $item['rate_per_unit_with_gst'] = round($item['rpu'] * (1 + ($item['gst'] / 100)), $fixDigit);

                        if ($gstData['gstType'] == 'CGST') {
                            $item['classification_igst_tax'] = 0;
                            $item['classification_cgst_tax'] = round(($item['total'] * $item['gst']) / 200, $fixDigit);
                            $item['classification_sgst_tax'] = round(($item['total'] * $item['gst']) / 200, $fixDigit);
                        } else {
                            $item['classification_igst_tax'] = round(($item['total'] * $item['gst']) / 100, $fixDigit);
                            $item['classification_cgst_tax'] = 0;
                            $item['classification_sgst_tax'] = 0;
                        }
                    } else {
                        $item['rate_per_unit_with_gst'] = round($item['rpu'], $fixDigit);
                        $item['classification_igst_tax'] = 0;
                        $item['classification_cgst_tax'] = 0;
                        $item['classification_sgst_tax'] = 0;
                    }
                }
            }

            $taxableValues = array_column($transaction['items'], 'total');
            $totalSubTotal = array_sum($taxableValues);
            $transaction['gross_value'] = round($totalSubTotal, 2);
            $transaction['taxable_value'] = $totalSubTotal;

            foreach ($transaction['additional_charges'] as $key => &$additionalCharge) {
                if (empty($additionalCharge['ledger'])) {
                    unset($transaction['additional_charges'][$key]);
                }
                $amountWithoutGST = $additionalCharge['ac_value'];
                $chargeType = Ledger::DISCOUNT_TYPE[$additionalCharge['ac_type']];
                $gst = null;
                if (isset($additionalCharge['ac_gst'])) {
                    if (! in_array(strtolower((string) $additionalCharge['ac_gst']), ['na', 'n/a', '0', 'exempt'])) {
                        $gst = GstTax::where('tax_rate', 'LIKE', $additionalCharge['ac_gst'].'%')->first();
                    } else {
                        $gst = GstTax::whereRaw('lower(name) = ? ', [strtolower(trim($additionalCharge['ac_gst']))])->first();
                    }
                }
                $chargeTax = ! empty($gst) ? $gst->tax_rate : 0;

                if ($chargeType == '%') {
                    $charge = ($transaction['gross_value'] * $amountWithoutGST) / 100;
                } elseif ($chargeType == '₹') {
                    $charge = $amountWithoutGST;
                }
                $charge = round($charge, $fixDigit);
                $additionalCharge['ac_gst_rate_id'] = ! empty($gst) ? $gst->id : null;
                $additionalCharge['ac_total_without_tax'] = $charge;
                $additionalCharge['tax'] = round(($charge * $chargeTax) / 100, $fixDigit);
                $additionalCharge['tax_for_cgst_sgst'] = round(($charge * $chargeTax) / 200, $fixDigit);
                $additionalCharge['ac_total'] = round($charge + $additionalCharge['tax'], $fixDigit);
                $transaction['taxable_value'] += $additionalCharge['ac_total_without_tax'];
            }
            $transaction['taxable_value'] = round($transaction['taxable_value'], $fixDigit);

            if ($transaction['is_gst_enabled']) {
                $transaction['cgst'] = 0;
                $transaction['sgst'] = 0;
                $transaction['igst'] = 0;

                // if (! $isRcmApplicable) {
                if ($gstData['gstType'] == 'CGST') {
                    $transaction['cgst'] = round(array_sum(array_column($transaction['items'], 'classification_cgst_tax')) + array_sum(array_column($transaction['additional_charges'], 'tax_for_cgst_sgst')), $fixDigit);
                    $transaction['sgst'] = round(array_sum(array_column($transaction['items'], 'classification_sgst_tax')) + array_sum(array_column($transaction['additional_charges'], 'tax_for_cgst_sgst')), $fixDigit);
                    $transaction['igst'] = 0;
                } else {
                    $transaction['cgst'] = 0;
                    $transaction['sgst'] = 0;
                    $transaction['igst'] = round(array_sum(array_column($transaction['items'], 'classification_igst_tax')) + array_sum(array_column($transaction['additional_charges'], 'tax')), $fixDigit);
                }
                // }
            }

            $tcsRate = $transaction['tcs_details']['tcs_rate'] ?? 0;
            $transaction['tcs_details']['tcs_amount'] = round(($totalSubTotal * $tcsRate) / 100, $fixDigit);

            $tdsRate = $transaction['tds_details']['tds_rate'] ?? 0;
            $transaction['tds_details']['tds_amount'] = round(($totalSubTotal * $tdsRate) / 100, $fixDigit);

            if ($transaction['is_gst_enabled']) {
                $transaction['total'] = round($transaction['taxable_value'] + ($isRcmApplicable ? 0 : ($transaction['cgst'] + $transaction['sgst'] + $transaction['igst'])) + $transaction['tcs_details']['tcs_amount'] + $transaction['cess'], $fixDigit);
            } else {
                $transaction['total'] = round($transaction['taxable_value'] + $transaction['tcs_details']['tcs_amount'], $fixDigit);
            }

            $totalForAddLess = round($transaction['taxable_value'] + ($transaction['cgst'] ?? 0) + ($transaction['sgst'] ?? 0) + ($transaction['igst'] ?? 0) + ($transaction['cess'] ?? 0) + (! empty($transaction['tcs_details']) ? $transaction['tcs_details']['tcs_amount'] : 0), 2);

            foreach ($transaction['add_less'] as &$addLess) {
                $addLess['al_total'] = $addLess['al_value'];
                if (Ledger::DISCOUNT_TYPE[$addLess['al_type']] == '%') {
                    $addLess['al_total'] = round(($totalForAddLess * $addLess['al_value']) / 100, $fixDigit);
                }
                $transaction['total'] += $addLess['al_total'];
            }

            $transaction['total'] = round($transaction['total'], $fixDigit);

            if (empty($transaction['rounding_amount']) && $transaction['rounding_amount'] !== 0) {
                if ($roundMethod == Ledger::NO_ROUNDING) {
                    $transaction['grand_total'] = $transaction['total'];
                    $transaction['rounding_amount'] = 0;
                } elseif ($roundMethod == Ledger::DOWNWARD_ROUNDING) {
                    $transaction['grand_total'] = floor($transaction['total']);
                    $transaction['rounding_amount'] = round($transaction['grand_total'] - $transaction['total'], $fixDigit);
                } elseif ($roundMethod == Ledger::NORMAL_ROUNDING) {
                    $transaction['grand_total'] = round($transaction['total']);
                    $transaction['rounding_amount'] = round($transaction['grand_total'] - $transaction['total'], $fixDigit);
                } elseif ($roundMethod == Ledger::UPWARD_ROUNDING) {
                    $transaction['grand_total'] = ceil($transaction['total']);
                    $transaction['rounding_amount'] = round($transaction['grand_total'] - $transaction['total'], $fixDigit);
                }
            } else {
                $transaction['grand_total'] = $transaction['total'] + $transaction['rounding_amount'];
            }
        }

        return [
            'transactions' => $transactions,
            'importErrors' => $importErrors,
        ];
    }

    public function getGSTCalculationDetails($classificationNatureType, $isRcmApplicable, $invoiceNo, &$transaction): array
    {
        $isGSTCalculated = false;
        $gstType = false;
        if (\in_array($classificationNatureType, purchaseIntraStateTaxArray())) {
            // if ($isRcmApplicable) {
            //     $isGSTCalculated = false;
            // } else {
            $isGSTCalculated = true;
            $gstType = 'CGST';
            // }
        } elseif (\in_array($classificationNatureType, purchaseInterstateTaxArray())) {
            // if ($isRcmApplicable) {
            //     $isGSTCalculated = false;
            // } else {
            $isGSTCalculated = true;
            $gstType = 'IGST';
            // }
        } elseif (\in_array($classificationNatureType, exportArray()) || \in_array($classificationNatureType, sezArray())) {
            $isGSTCalculated = true;
            $gstType = 'IGST';
        } elseif ($classificationNatureType == 'Deemed Export - Intrastate') {
            $isGSTCalculated = true;
            $gstType = 'CGST';
        } elseif ($classificationNatureType == 'Deemed Export - Interstate') {
            $isGSTCalculated = true;
            $gstType = 'IGST';
        } else {
            $importErrors[$invoiceNo][] = 'You have entered the classification nature type wrong.';
            $transaction['is_transaction_create'] = false;
        }

        return [
            'isGSTCalculated' => $isGSTCalculated,
            'gstType' => $gstType,
        ];
    }
}
