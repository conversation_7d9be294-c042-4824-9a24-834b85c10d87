<?php

namespace App\Actions\Imports;

use App\Models\GstTax;
use App\Models\Ledger;
use Lorisleiva\Actions\Concerns\AsAction;

class CalculationForExpenseAccountingInvoiceAction
{
    use AsAction;

    public function handle($transactions, $importErrors, $fixDigit, $roundMethod)
    {
        foreach ($transactions as $invoiceNo => &$transaction) {
            $isRcmApplicable = $transaction['is_rcm_applicable'];
            $gstData = [];
            $transaction['is_gst_na'] = false;
            $transaction['is_cgst_sgst_igst_calculated'] = true;
            $gstArray = array_filter(array_column($transaction['ledgers'], 'gst'), function ($value) {
                return $value !== null && $value !== '' && strtolower($value) !== 'na' && strtolower($value) !== 'n/a';
            });

            if ($gstArray != null) {
                foreach ($gstArray as $gstRate) {
                    if (strtolower($gstRate) == 'na') {
                        $transaction['is_gst_na'] = true;
                        $transaction['is_cgst_sgst_igst_calculated'] = false;
                    }
                }

                if ($transaction['is_gst_na'] && ! (count(array_unique($gstArray, SORT_REGULAR)) === 1)) {
                    $importErrors[$invoiceNo][] = 'You have not entered same GST rate for NA rate.';
                    unset($transaction[$invoiceNo]);

                    continue;
                }
            } else {
                $transaction['is_gst_na'] = true;
                $transaction['is_cgst_sgst_igst_calculated'] = false;
            }

            if ($transaction['is_gst_enabled']) {
                $gstData = $this->getGSTCalculationDetails($transaction['main_classification_nature_type'], $isRcmApplicable, $invoiceNo, $transaction);
            }

            $transaction['taxable_value'] = 0;
            foreach ($transaction['ledgers'] as &$ledger) {
                $ledger['total'] = $ledger['rpu'];
                $ledger['total_discount_amount'] = 0;
                if (Ledger::DISCOUNT_TYPE[$ledger['discount_type']] == Ledger::DISCOUNT_TYPE_PERCENTAGE) {
                    $ledger['total_discount_amount'] = ($ledger['rpu'] * $ledger['discount_value']) / 100;
                } elseif (Ledger::DISCOUNT_TYPE[$ledger['discount_type']] == Ledger::DISCOUNT_TYPE_AMOUNT) {
                    $ledger['total_discount_amount'] = $ledger['discount_value'];
                }
                $rateWithoutGSTAfterDiscount = round($ledger['rpu'] - $ledger['total_discount_amount'], 2);
                $totalDiscount2Amount = 0;
                if (Ledger::DISCOUNT_TYPE[$ledger['discount_type_2']] == Ledger::DISCOUNT_TYPE_PERCENTAGE) {
                    $totalDiscount2Amount = ($rateWithoutGSTAfterDiscount * $ledger['discount_value_2']) / 100;
                } elseif (Ledger::DISCOUNT_TYPE[$ledger['discount_type_2']] == Ledger::DISCOUNT_TYPE_AMOUNT) {
                    $totalDiscount2Amount = $ledger['discount_value_2'];
                }
                $ledger['total_discount_amount'] += $totalDiscount2Amount;
                $ledger['total_discount_amount'] = round($ledger['total_discount_amount'], 2);
                $ledger['total'] = round($ledger['total'] - $ledger['total_discount_amount'], 2);
                $transaction['taxable_value'] += $ledger['total'];

                if ($transaction['is_gst_enabled']) {
                    if ($gstData['isGSTCalculated'] && ! $transaction['is_gst_na'] && ! in_array(strtolower($ledger['gst']), ['na', 'n/a', '0', 'exempt'])) {
                        $ledger['rate_per_unit_with_gst'] = round($ledger['rpu'] * (1 + ($ledger['gst'] / 100)), $fixDigit);
                        if ($gstData['gstType'] == 'CGST') {
                            $ledger['classification_igst_tax'] = 0;
                            $ledger['classification_cgst_tax'] = round(($ledger['total'] * $ledger['gst']) / 200, $fixDigit);
                            $ledger['classification_sgst_tax'] = round(($ledger['total'] * $ledger['gst']) / 200, $fixDigit);
                        } else {
                            $ledger['classification_igst_tax'] = round(($ledger['total'] * $ledger['gst']) / 100, $fixDigit);
                            $ledger['classification_cgst_tax'] = 0;
                            $ledger['classification_sgst_tax'] = 0;
                        }
                    } else {
                        $ledger['rate_per_unit_with_gst'] = round($ledger['rpu'], $fixDigit);
                        $ledger['classification_igst_tax'] = 0;
                        $ledger['classification_cgst_tax'] = 0;
                        $ledger['classification_sgst_tax'] = 0;
                    }
                }
            }
            $taxableValues = array_column($transaction['ledgers'], 'total');
            $totalSubTotal = array_sum($taxableValues);
            $transaction['gross_value'] = round($totalSubTotal, $fixDigit);
            $transaction['taxable_value'] = $totalSubTotal;

            foreach ($transaction['additional_charges'] as $key => &$additionalCharge) {
                if (empty($additionalCharge['ledger'])) {
                    unset($transaction['additional_charges'][$key]);
                }
                $amountWithoutGST = $additionalCharge['ac_value'];
                $chargeType = Ledger::DISCOUNT_TYPE[$additionalCharge['ac_type']];
                $gst = null;
                if (isset($additionalCharge['ac_gst'])) {
                    if (! in_array(strtolower((string) $additionalCharge['ac_gst']), ['na', 'n/a', '0', 'exempt'])) {
                        $gst = GstTax::where('tax_rate', 'LIKE', $additionalCharge['ac_gst'].'%')->first();
                    } else {
                        $gst = GstTax::whereRaw('lower(name) = ? ', [strtolower(trim($additionalCharge['ac_gst']))])->first();
                    }
                }
                $chargeTax = ! empty($gst) ? $gst->tax_rate : 0;

                if ($chargeType == '%') {
                    $charge = ($transaction['gross_value'] * $amountWithoutGST) / 100;
                } elseif ($chargeType == '₹') {
                    $charge = $amountWithoutGST;
                }
                $charge = round($charge, $fixDigit);
                $additionalCharge['ac_gst_rate_id'] = ! empty($gst) ? $gst->id : null;
                $additionalCharge['ac_total_without_tax'] = $charge;
                $additionalCharge['tax'] = round(($charge * $chargeTax) / 100, $fixDigit);
                $additionalCharge['tax_for_cgst_sgst'] = round(($charge * $chargeTax) / 200, $fixDigit);
                $additionalCharge['ac_total'] = round($charge + $additionalCharge['tax'], $fixDigit);
                $transaction['taxable_value'] += $additionalCharge['ac_total_without_tax'];
            }
            $transaction['taxable_value'] = round($transaction['taxable_value'], $fixDigit);

            if ($transaction['is_gst_enabled']) {
                $transaction['cgst'] = 0;
                $transaction['sgst'] = 0;
                $transaction['igst'] = 0;

                // if (! $isRcmApplicable) {
                if ($gstData['gstType'] == 'CGST') {
                    $transaction['cgst'] = round(array_sum(array_column($transaction['ledgers'], 'classification_cgst_tax')) + array_sum(array_column($transaction['additional_charges'], 'tax_for_cgst_sgst')), $fixDigit);
                    $transaction['sgst'] = round(array_sum(array_column($transaction['ledgers'], 'classification_sgst_tax')) + array_sum(array_column($transaction['additional_charges'], 'tax_for_cgst_sgst')), $fixDigit);
                    $transaction['igst'] = 0;
                } else {
                    $transaction['cgst'] = 0;
                    $transaction['sgst'] = 0;
                    $transaction['igst'] = round(array_sum(array_column($transaction['ledgers'], 'classification_igst_tax')) + array_sum(array_column($transaction['additional_charges'], 'tax')), $fixDigit);
                }
                // }
            }

            $tcsRate = $transaction['tcs_details']['tcs_rate'] ?? 0;
            $transaction['tcs_details']['tcs_amount'] = round(($totalSubTotal * $tcsRate) / 100, $fixDigit);

            $tdsRate = $transaction['tds_details']['tds_rate'] ?? 0;
            $transaction['tds_details']['tds_amount'] = round(($totalSubTotal * $tdsRate) / 100, $fixDigit);

            if ($transaction['is_gst_enabled']) {
                $transaction['total'] = round($transaction['taxable_value'] + ($isRcmApplicable ? 0 : ($transaction['cgst'] + $transaction['sgst'] + $transaction['igst'])) + $transaction['tcs_details']['tcs_amount'] + $transaction['cess'], $fixDigit);
            } else {
                $transaction['total'] = round($transaction['taxable_value'] + $transaction['tcs_details']['tcs_amount'], $fixDigit);
            }

            $totalForAddLess = round($transaction['taxable_value'] + ($transaction['cgst'] ?? 0) + ($transaction['sgst'] ?? 0) + ($transaction['igst'] ?? 0) + ($transaction['cess'] ?? 0) + (! empty($transaction['tcs_details']) ? $transaction['tcs_details']['tcs_amount'] : 0), 2);

            foreach ($transaction['add_less'] as &$addLess) {
                $addLess['al_total'] = $addLess['al_value'];
                if (Ledger::DISCOUNT_TYPE[$addLess['al_type']] == '%') {
                    $addLess['al_total'] = round(($totalForAddLess * $addLess['al_value']) / 100, $fixDigit);
                }
                $transaction['total'] += $addLess['al_total'];
            }

            $transaction['total'] = round($transaction['total'], $fixDigit);

            if (empty($transaction['rounding_amount']) && $transaction['rounding_amount'] !== 0) {
                if ($roundMethod == Ledger::NO_ROUNDING) {
                    $transaction['grand_total'] = $transaction['total'];
                    $transaction['rounding_amount'] = 0;
                } elseif ($roundMethod == Ledger::DOWNWARD_ROUNDING) {
                    $transaction['grand_total'] = floor($transaction['total']);
                    $transaction['rounding_amount'] = round($transaction['grand_total'] - $transaction['total'], $fixDigit);
                } elseif ($roundMethod == Ledger::NORMAL_ROUNDING) {
                    $transaction['grand_total'] = round($transaction['total']);
                    $transaction['rounding_amount'] = round($transaction['grand_total'] - $transaction['total'], $fixDigit);
                } elseif ($roundMethod == Ledger::UPWARD_ROUNDING) {
                    $transaction['grand_total'] = ceil($transaction['total']);
                    $transaction['rounding_amount'] = round($transaction['grand_total'] - $transaction['total'], $fixDigit);
                }
            } else {
                $transaction['grand_total'] = $transaction['total'] + $transaction['rounding_amount'];
            }
        }

        return [
            'transactions' => $transactions,
            'importErrors' => $importErrors,
        ];
    }

    public function getGSTCalculationDetails($classificationNatureType, $isRcmApplicable, $invoiceNo, &$transaction): array
    {
        $isGSTCalculated = false;
        $gstType = false;
        if (\in_array($classificationNatureType, purchaseIntraStateTaxArray())) {
            // if ($isRcmApplicable) {
            //     $isGSTCalculated = false;
            // } else {
            $isGSTCalculated = true;
            $gstType = 'CGST';
            // }
        } elseif (\in_array($classificationNatureType, purchaseInterstateTaxArray())) {
            // if ($isRcmApplicable) {
            //     $isGSTCalculated = false;
            // } else {
            $isGSTCalculated = true;
            $gstType = 'IGST';
            // }
        } elseif (\in_array($classificationNatureType, exportArray()) || \in_array($classificationNatureType, sezArray())) {
            $isGSTCalculated = true;
            $gstType = 'IGST';
        } elseif ($classificationNatureType == 'Deemed Export - Intrastate') {
            $isGSTCalculated = true;
            $gstType = 'CGST';
        } elseif ($classificationNatureType == 'Deemed Export - Interstate') {
            $isGSTCalculated = true;
            $gstType = 'IGST';
        } else {
            $importErrors[$invoiceNo][] = 'You have entered the classification nature type wrong.';
            $transaction['is_transaction_create'] = false;
        }

        return [
            'isGSTCalculated' => $isGSTCalculated,
            'gstType' => $gstType,
        ];
    }
}
