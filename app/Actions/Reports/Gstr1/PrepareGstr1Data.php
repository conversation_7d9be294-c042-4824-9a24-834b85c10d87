<?php

namespace App\Actions\Reports\Gstr1;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Lorisleiva\Actions\Concerns\AsAction;

class PrepareGstr1Data
{
    use AsAction;

    public $invalidData = [];

    public function handle($input, $gstrData)
    {
        // Retrieve cache data
        $gstrCacheKey = generateCacheKey('gstr1_report');
        $cacheData = Cache::get($gstrCacheKey);
        $filingPeriod = optional($cacheData['data'] ?? null)['end_date']
            ? Carbon::parse($cacheData['data']['end_date'])->format('mY')
            : null;

        // Get company details
        $currentCompany = getCurrentCompany();
        $gstin = optional($currentCompany->companyTax)->gstin;

        // Process GSTR-1 sections
        $sections = [
            'b2b' => $this->b2bInvoiceData($gstrData['b2bSezde']),
            'b2cl' => $this->b2clInvoiceData($gstrData['b2cl']),
            'cdnr' => $this->getCdnrTransactionData($gstrData['cdnr']),
            'b2cs' => $this->b2csInvoiceData($gstrData['b2cs']),
            'exp' => $this->expInvoiceData($gstrData['exp']),
            'nil' => $this->nilInvoiceDate($gstrData['exemp']),
            'doc_issue' => $this->getDocIssuesData($gstrData['docs']),
            'cdnur' => $this->cdnurInvoiceData($gstrData['cdnur']),
        ];
        $hsnDataB2b = $this->getHsnTransactionData($gstrData['hsnB2b']);
        $hsnDataB2c = $this->getHsnTransactionData($gstrData['hsnB2c']);
        if (! empty($hsnDataB2b) || ! empty($hsnDataB2c)) {
            if (! empty($hsnDataB2b) && ! empty($hsnDataB2c)) {
                $sections['hsn'] = ['hsn_b2b' => $hsnDataB2b, 'hsn_b2c' => $hsnDataB2c];
            } elseif (! empty($hsnDataB2b)) {
                $sections['hsn'] = ['hsn_b2b' => $hsnDataB2b];
            } elseif (! empty($hsnDataB2c)) {
                $sections['hsn'] = ['hsn_b2c' => $hsnDataB2c];
            }
        }
        if (! empty($this->invalidData)) {
            return ['status' => false, 'data' => $this->invalidData];
        }

        return ['status' => true, 'data' => array_filter([
            'gstin' => $gstin,
            'fp' => $filingPeriod,
        ] + $sections)];

    }

    public function b2bInvoiceData($data)
    {
        $b2bGstData = collect($data)->groupBy('gst');
        $b2bCustomer = [];

        foreach ($b2bGstData as $gstNo => $b2bData) {
            $b2bInvoice = [];

            $b2bInvoiceData = collect($b2bData)->groupBy('invoice_number');

            foreach ($b2bInvoiceData as $b2b) {
                $b2bInv = $b2b[0];
                $pos = explode('-', $b2bInv['place_of_supplier'])[0];
                if ($b2bInv['invoice_type'] == 'Regular B2B') {
                    $invoiceType = 'R';
                } elseif ($b2bInv['invoice_type'] == 'Deemed Exp') {
                    $invoiceType = 'DE';
                } elseif ($b2bInv['classification_nature_type']['name'] == 'Sales to SEZ Taxable') {
                    $invoiceType = 'SEWP';
                } elseif ($b2bInv['classification_nature_type']['name'] == 'Sales to SEZ Exempt' || $b2bInv['classification_nature_type']['name'] == 'Sales to SEZ Nill Rated' || $b2bInv['classification_nature_type']['name'] == 'Sales to SEZ under LUT/Bond') {
                    $invoiceType = 'SEWOP';
                }

                $b2bInvoiceItem = ! empty($b2bInv['items']) ? collect($b2bInv['items'])->groupBy('gst_tax_percentage') : collect($b2bInv['ledgers'])->groupBy('gst_tax_percentage');
                $additionalCharges = ! empty($b2bInv['additionalCharges']) ? collect($b2bInv['additionalCharges'])->groupBy('gst_percentage') : [];
                $b2bItem = [];

                foreach ($b2bInvoiceItem as $gstGercentage => $items) {
                    $itemNum = $this->getMappedValue($gstGercentage);
                    $itemDetail = [];

                    foreach ($items as $item) {
                        if (! empty($itemDetail)) {
                            $itemDetail['txval'] += round($item['total'], 2) ?? 0.0;
                            $itemDetail['csamt'] += round($item['cess_amount'], 2) ?? 0.0;
                            if (! empty($b2b[0]['igst'])) {
                                $itemDetail['iamt'] += round($item['classification_igst_tax'], 2) ?? null;
                            } elseif (! empty($b2b[0]['sgst']) && ! empty($b2b[0]['cgst'])) {
                                $itemDetail['camt'] += ($item['classification_cgst_tax']) ?? null;
                                $itemDetail['samt'] += round($item['classification_sgst_tax'], 2) ?? null;
                            }
                        } else {
                            $itemDetail = [
                                'txval' => round($item['total'], 2) ?? null,
                                'rt' => $item['gst_tax_percentage'] ?? null,
                                'csamt' => round($item['cess_amount'], 2) ?? null,
                            ];

                            if (! empty($b2b[0]['igst'])) {
                                $itemDetail['iamt'] = round($item['classification_igst_tax'], 2) ?? null;
                            } elseif (! empty($b2b[0]['sgst']) && ! empty($b2b[0]['cgst'])) {
                                $itemDetail['camt'] = ($item['classification_cgst_tax']) ?? null;
                                $itemDetail['samt'] = round($item['classification_sgst_tax'], 2) ?? null;
                            }
                        }
                    }

                    $b2bItem[] = [
                        'num' => (int) $itemNum,
                        'itm_det' => $itemDetail,
                    ];
                }

                foreach ($additionalCharges as $gstGercentage => $items) {
                    $itemNum = $this->getMappedValue($gstGercentage);
                    $itemNumArray = $this->arraySearch($b2bItem, $itemNum);
                    $itemDetail = [];

                    foreach ($items as $item) {
                        $value = $item['total_without_tax'] ?? 0;
                        $gst = $value * $item['gst_percentage'] / 100;

                        $cgst = (! empty($b2b[0]['cgst']) || $b2b[0]['cgst'] != 0) ? ($gst / 2) : '0';
                        $sgst = (! empty($b2b[0]['sgst']) || $b2b[0]['sgst'] != 0) ? ($gst / 2) : '0';
                        $igst = (! empty($b2b[0]['igst']) || $b2b[0]['igst'] != 0) ? $gst : '0';

                        if (empty($itemNumArray)) {
                            $itemDetail = [
                                'txval' => round($item['total_without_tax'], 2) ?? 0.0,
                                'rt' => (float) $item['gst_percentage'] ?? 0.0,
                                'csamt' => 0,
                            ];
                            if (! empty($igst)) {
                                $itemDetail['iamt'] = round($igst, 2) ?? 0.0;
                            } elseif (! empty($cgst) && ! empty($sgst)) {
                                $itemDetail['camt'] = round($cgst, 2) ?? 0.0;
                                $itemDetail['samt'] = round($sgst, 2) ?? 0.0;
                            }
                            $b2bItem[] = [
                                'num' => (int) $itemNum,
                                'itm_det' => $itemDetail,
                            ];
                        } else {
                            $b2bItem[array_key_first($itemNumArray)]['itm_det']['txval'] += round($item['total_without_tax'], 2) ?? 0.0;
                            $b2bItem[array_key_first($itemNumArray)]['itm_det']['csamt'] += 0.0;
                            if (! empty($igst)) {
                                $b2bItem[array_key_first($itemNumArray)]['itm_det']['iamt'] += round($igst, 2) ?? 0.0;
                            } elseif (! empty($sgst) && ! empty($cgst)) {
                                $b2bItem[array_key_first($itemNumArray)]['itm_det']['camt'] += round($cgst, 2) ?? 0.0;
                                $b2bItem[array_key_first($itemNumArray)]['itm_det']['samt'] += round($sgst, 2) ?? 0.0;
                            }
                        }
                    }
                }

                if (! preg_match('/^[A-Za-z0-9\/-]+$/', $b2bInv['invoice_number'])) {
                    $this->invalidData['invalid_invoice']['B2B'][] = $b2bInv['invoice_number'];

                    continue;
                }
                $b2bInvoice[] = [
                    'inum' => $b2bInv['invoice_number'],
                    'idt' => $b2bInv['invoice_date'],
                    'val' => round($b2bInv['invoice_amount'], 2),
                    'pos' => $pos,
                    'rchrg' => $b2bInv['rcm'] == 1 ? 'Y' : 'N',
                    'inv_typ' => $invoiceType,
                    'itms' => $b2bItem,
                ];
            }
            $b2bCustomer[] = [
                'ctin' => $gstNo,
                'inv' => $b2bInvoice,
            ];
        }

        return $b2bCustomer;
    }

    public function b2clInvoiceData($data)
    {
        $b2clGstData = collect($data)->groupBy('place_of_supplier');

        $b2clPlace = [];
        foreach ($b2clGstData as $placeOfSupplire => $b2clData) {
            $pos = explode('-', $placeOfSupplire)[0];
            $b2clInvoice = [];
            foreach ($b2clData as $b2clInv) {
                $b2bclInvoiceItem = ! empty($b2clInv['items']) ? collect($b2clInv['items'])->groupBy('gst_tax_percentage') : collect($b2clInv['ledgers'])->groupBy('gst_tax_percentage');
                $additionalCharges = ! empty($b2clInv['additionalCharges']) ? collect($b2clInv['additionalCharges'])->groupBy('gst_percentage') : [];
                $b2clItem = [];
                foreach ($b2bclInvoiceItem as $gstGercentage => $items) {
                    $itemNum = $gstGercentage.'01';
                    $itemDetail = [];
                    foreach ($items as $item) {
                        if (! empty($itemDetail)) {
                            $itemDetail['txval'] += $item['total'] ?? 0.0;
                            $itemDetail['csamt'] += $item['cess_amount'] ?? 0.0;
                            if (! empty($item['classification_igst_tax'])) {
                                $itemDetail['iamt'] += $item['classification_igst_tax'] ?? null;
                            } elseif (! empty($item['classification_cgst_tax']) && ! empty($item['classification_sgst_tax'])) {
                                $itemDetail['camt'] += $item['classification_cgst_tax'] ?? null;
                                $itemDetail['samt'] += $item['classification_sgst_tax'] ?? null;
                            }
                        } else {
                            $itemDetail = [
                                'txval' => $item['total'] ?? null,
                                'rt' => $item['gst_tax_percentage'] ?? null,
                                'csamt' => $item['cess_amount'] ?? null,
                            ];
                            if (! empty($item['classification_igst_tax'])) {
                                $itemDetail['iamt'] = $item['classification_igst_tax'] ?? null;
                            } elseif (! empty($item['classification_cgst_tax']) && ! empty($item['classification_sgst_tax'])) {
                                $itemDetail['camt'] = $item['classification_cgst_tax'] ?? null;
                                $itemDetail['samt'] = $item['classification_sgst_tax'] ?? null;
                            }
                        }
                    }
                    $b2clItem[] = [
                        'num' => (int) $itemNum,
                        'itm_det' => $itemDetail,
                    ];
                }
                foreach ($additionalCharges as $gstGercentage => $items) {
                    $itemNum = $this->getMappedValue($gstGercentage);
                    $itemNumArray = $this->arraySearch($b2clItem, $itemNum);
                    $itemDetail = [];
                    foreach ($items as $item) {
                        $value = $item['total_without_tax'] ?? 0;
                        $gst = $value * $item['gst_percentage'] / 100;

                        $cgst = (! empty($b2clInv['cgst']) || $b2clInv['cgst'] != 0) ? ($gst / 2) : '0';
                        $sgst = (! empty($b2clInv['sgst']) || $b2clInv['sgst'] != 0) ? ($gst / 2) : '0';
                        $igst = (! empty($b2clInv['igst']) || $b2clInv['igst'] != 0) ? $gst : '0';

                        if (empty($itemNumArray)) {
                            $itemDetail = [
                                'txval' => round($item['total_without_tax'], 2) ?? 0.0,
                                'rt' => (float) $item['gst_percentage'] ?? 0.0,
                                'csamt' => 0,
                            ];
                            if (! empty($igst)) {
                                $itemDetail['iamt'] = round($igst, 2) ?? 0.0;
                            } elseif (! empty($cgst) && ! empty($sgst)) {
                                $itemDetail['camt'] = round($cgst, 2) ?? 0.0;
                                $itemDetail['samt'] = round($sgst, 2) ?? 0.0;
                            }
                            $b2clItem[] = [
                                'num' => (int) $itemNum,
                                'itm_det' => $itemDetail,
                            ];
                        } else {
                            $b2clItem[array_key_first($itemNumArray)]['itm_det']['txval'] += round($item['total_without_tax'], 2) ?? 0.0;
                            $b2clItem[array_key_first($itemNumArray)]['itm_det']['csamt'] += 0.0;
                            if (! empty($igst)) {
                                $b2clItem[array_key_first($itemNumArray)]['itm_det']['iamt'] += round($igst, 2) ?? 0.0;
                            } elseif (! empty($sgst) && ! empty($cgst)) {
                                $b2clItem[array_key_first($itemNumArray)]['itm_det']['camt'] += round($cgst, 2) ?? 0.0;
                                $b2clItem[array_key_first($itemNumArray)]['itm_det']['samt'] += round($sgst, 2) ?? 0.0;
                            }
                        }
                    }
                }
                if (! preg_match('/^[A-Za-z0-9\/-]+$/', $b2clInv['invoice_number'])) {
                    $this->invalidData['invalid_invoice']['B2CL'][] = $b2clInv['invoice_number'];

                    continue;
                }
                $b2clInvoice[] = [
                    'inum' => $b2clInv['invoice_number'],
                    'idt' => $b2clInv['invoice_date'],
                    'val' => $b2clInv['invoice_amount'],
                    'itms' => $b2clItem,
                ];
            }
            $b2clPlace[] = [
                'pos' => $pos,
                'inv' => $b2clInvoice,
            ];
        }

        return $b2clPlace;
    }

    public function b2csInvoiceData($data)
    {

        $b2csAllData = [];
        $company = getCurrentCompany();
        $b2csGstData = collect($data)->groupBy('place_of_supplier');
        $stateId = ! empty($company->addresses) ? $company->addresses[0]?->state_id : null;
        $currentCompanySateCode = getStateCode($stateId);
        foreach ($b2csGstData as $placeOfSupplire => $b2csData) {
            $pos = explode('-', $placeOfSupplire)[0];
            $b2bcsGstRate = collect($b2csData)
                ->map(function ($item) {
                    $item['gst_rate'] = (string) $item['gst_rate']; // Convert to string

                    return $item;
                })
                ->groupBy('gst_rate');

            foreach ($b2bcsGstRate as $gst => $gstRate) {
                $b2csInvoice = [];
                if ($pos != $currentCompanySateCode) {
                    $b2csInvoice['sply_ty'] = 'INTER';
                } else {
                    $b2csInvoice['sply_ty'] = 'INTRA';
                }
                $totalTaxableValue = collect($gstRate)->sum('taxable_value');
                $totalCgstValue = collect($gstRate)->sum('cgst');
                $totalSgstValue = collect($gstRate)->sum('sgst');
                $totalIgstValue = collect($gstRate)->sum('igst');
                $totalCessValue = collect($gstRate)->sum('cess');
                $gstValue = $totalTaxableValue * (float) $gst / 100;
                $b2csInvoice['rt'] = (float) $gst;
                $b2csInvoice['typ'] = 'OE';
                $b2csInvoice['pos'] = $pos;
                $b2csInvoice['txval'] = round($totalTaxableValue, 2);
                if ($totalCgstValue != 0 && $totalSgstValue != 0) {
                    $gstCgstSgstValue = $gstValue / 2;
                    $b2csInvoice['camt'] = round($gstCgstSgstValue, 2);
                    $b2csInvoice['samt'] = round($gstCgstSgstValue, 2);
                } elseif ($totalIgstValue != 0) {
                    $b2csInvoice['iamt'] = round($gstValue, 2);
                }
                $b2csInvoice['csamt'] = round($totalCessValue, 2);
                $b2csAllData[] = $b2csInvoice;
            }
        }

        return $b2csAllData;
    }

    public function getHsnTransactionData($data)
    {
        $hsnTransactionData = collect($data);
        $hsndata = [];
        $num = 1;
        foreach ($hsnTransactionData as $key => $hsn) {
            if (($hsn['taxable_value'] != 0) && ($hsn['hsn_code'] != '')) {

                $hsndata[$key] = [
                    'num' => $num,
                    'hsn_sc' => $hsn['hsn_code'],
                    'uqc' => isset($hsn['unit_code']) && $hsn['unit_code'] != '' ? $hsn['unit_code'] : 'NA',
                    'qty' => round($hsn['quantity'], 2) ?? 0,
                    'rt' => (float) $hsn['gst_rate'],
                    'txval' => round($hsn['taxable_value'], 2),
                    'iamt' => round($hsn['igst'] ?? 0, 2),
                    'samt' => round($hsn['sgst'] ?? 0, 2),
                    'camt' => round($hsn['cgst'] ?? 0, 2),
                    'csamt' => round($hsn['cess'] ?? 0, 2),
                ];
                if (isset($hsn['description']) && $hsn['description'] != '') {
                    $hsndata[$key]['desc'] = $hsn['description'];
                }
                $num++;
            }

        }

        return array_values($hsndata);
    }

    public function getDocIssuesData($data)
    {

        $outWardInvoiceData = [];
        if (! empty($data['invoice_for_outward_supply'])) {
            $outWardInvoiceData['doc_num'] = 1;
            $outWardInvoiceData['doc_typ'] = 'Invoices for outward supply';
        }
        $num = 1;

        foreach ($data['invoice_for_outward_supply'] as $outWardInvoice) {
            foreach ($outWardInvoice as $invNumber) {
                if (! preg_match('/^[A-Za-z0-9\/-]+$/', $invNumber)) {
                    $this->invalidData['invalid_invoice']['invoice_number'][] = $invNumber;
                }
            }

            $invoiceSeries = $this->getMissingInvoiceSeries($outWardInvoice);
            $totalNumber = count(range($invoiceSeries['minInvoice'], $invoiceSeries['maxInvoice']));

            $outWardInvoiceData['docs'][] = [
                'num' => $num,
                'from' => $outWardInvoice[0],
                'to' => end($outWardInvoice),
                'totnum' => $totalNumber,
                'cancel' => getMissingInvoiceSeries($outWardInvoice),
                'net_issue' => $totalNumber - getMissingInvoiceSeries($outWardInvoice),
            ];
            $num++;
        }

        $creditNoteInvoiceData = [];
        if (! empty($data['sale_credit_note'])) {
            $creditNoteInvoiceData['doc_num'] = 5;
            $creditNoteInvoiceData['doc_typ'] = 'Credit Note';
        }
        $CreditNoteNum = 1;
        foreach ($data['sale_credit_note'] as $outWardInvoice) {
            foreach ($outWardInvoice as $invNumber) {
                if (! preg_match('/^[A-Za-z0-9\/-]+$/', $invNumber)) {
                    $this->invalidData['invalid_invoice']['invoice_number'][] = $invNumber;
                }
            }
            $invoiceSeries = $this->getMissingInvoiceSeries($outWardInvoice);
            $totalNumber = count(range($invoiceSeries['minInvoice'], $invoiceSeries['maxInvoice']));
            $creditNoteInvoiceData['docs'][] = [
                'num' => $CreditNoteNum,
                'from' => $outWardInvoice[0],
                'to' => end($outWardInvoice),
                'totnum' => $totalNumber,
                'cancel' => getMissingInvoiceSeries($outWardInvoice),
                'net_issue' => $totalNumber - getMissingInvoiceSeries($outWardInvoice),
            ];
            $CreditNoteNum++;
        }

        $IncomeNoteInvoiceData = [];
        if (! empty($data['income_credit_note'])) {
            $creditNoteInvoiceData['doc_num'] = 5;
            $creditNoteInvoiceData['doc_typ'] = 'Credit Note';
        }
        foreach ($data['income_credit_note'] as $outWardInvoice) {
            foreach ($outWardInvoice as $invNumber) {
                if (! preg_match('/^[A-Za-z0-9\/-]+$/', $invNumber)) {
                    $this->invalidData['invalid_invoice']['invoice_number'][] = $invNumber;
                }
            }
            $invoiceSeries = $this->getMissingInvoiceSeries($outWardInvoice);
            $totalNumber = count(range($invoiceSeries['minInvoice'], $invoiceSeries['maxInvoice']));
            $creditNoteInvoiceData['docs'][] = [
                'num' => $CreditNoteNum,
                'from' => $outWardInvoice[0],
                'to' => end($outWardInvoice),
                'totnum' => $totalNumber,
                'cancel' => getMissingInvoiceSeries($outWardInvoice),
                'net_issue' => $totalNumber - getMissingInvoiceSeries($outWardInvoice),
            ];
            $CreditNoteNum++;
        }

        $IncomeDebitNoteInvoiceData = [];
        if (! empty($data['income_debit_note'])) {
            $IncomeDebitNoteInvoiceData['doc_num'] = 4;
            $IncomeDebitNoteInvoiceData['doc_typ'] = 'Debit Note';
        }
        $num = 1;
        foreach ($data['income_debit_note'] as $outWardInvoice) {
            foreach ($outWardInvoice as $invNumber) {
                if (! preg_match('/^[A-Za-z0-9\/-]+$/', $invNumber)) {
                    $this->invalidData['invalid_invoice']['invoice_number'][] = $invNumber;
                }
            }
            $invoiceSeries = $this->getMissingInvoiceSeries($outWardInvoice);
            $totalNumber = count(range($invoiceSeries['minInvoice'], $invoiceSeries['maxInvoice']));
            $IncomeDebitNoteInvoiceData['docs'][] = [
                'num' => $num,
                'from' => $outWardInvoice[0],
                'to' => end($outWardInvoice),
                'totnum' => $totalNumber,
                'cancel' => getMissingInvoiceSeries($outWardInvoice),
                'net_issue' => $totalNumber - getMissingInvoiceSeries($outWardInvoice),
            ];
            $num++;
        }

        $docs['doc_det'] = [$outWardInvoiceData, $creditNoteInvoiceData, $IncomeDebitNoteInvoiceData, $IncomeNoteInvoiceData];
        foreach ($docs['doc_det'] as $key => $doc) {
            if (empty($doc)) {
                unset($docs['doc_det'][$key]);
            }
        }

        return $docs;
    }

    public function getCdnrTransactionData($data)
    {
        $cdnrGstData = collect($data)->groupBy('gst');
        $cdnrCustomer = [];
        foreach ($cdnrGstData as $gstNo => $cdnrData) {
            $cdnrInvoice = [];

            $cdnrInvoiceData = collect($cdnrData)->groupBy('invoice_number');
            foreach ($cdnrInvoiceData as $cdnr) {
                $cdnrInv = $cdnr[0];
                $pos = explode('-', $cdnrInv['place_of_supplier'])[0];
                if ($cdnrInv['invoice_type'] == 'Regular B2B') {
                    $invoiceType = 'R';
                } elseif ($cdnrInv['invoice_type'] == 'Deemed Exp') {
                    $invoiceType = 'DE';
                } elseif ($cdnrInv['classification_nature_type']['name'] == 'Sales to SEZ Taxable') {
                    $invoiceType = 'SEWP';
                } elseif ($cdnrInv['classification_nature_type']['name'] == 'Sales to SEZ Exempt' || $cdnrInv['classification_nature_type']['name'] == 'Sales to SEZ Nill Rated' || $cdnrInv['classification_nature_type']['name'] == 'Sales to SEZ under LUT/Bond') {
                    $invoiceType = 'SEWOP';
                }
                $cdnrInvoiceItem = ! empty($cdnrInv['items']) ? collect($cdnrInv['items'])->groupBy('gst_tax_percentage') : collect($cdnrInv['ledgers'])->groupBy('gst_tax_percentage');
                $additionalCharges = ! empty($cdnrInv['additionalCharges']) ? collect($cdnrInv['additionalCharges'])->groupBy('gst_percentage') : [];

                $cdnrItem = [];
                foreach ($cdnrInvoiceItem as $gstGercentage => $items) {
                    $itemNum = $gstGercentage.'01';
                    $itemDetail = [];

                    foreach ($items as $item) {
                        if (! empty($itemDetail)) {
                            $itemDetail['txval'] += $item['total'] ?? 0.0;
                            $itemDetail['csamt'] += $item['cess_amount'] ?? 0.0;
                            if (! empty($item['classification_igst_tax'])) {
                                $itemDetail['iamt'] += $item['classification_igst_tax'] ?? null;
                            } elseif (! empty($item['classification_cgst_tax']) && ! empty($item['classification_sgst_tax'])) {
                                $itemDetail['camt'] += $item['classification_cgst_tax'] ?? null;
                                $itemDetail['samt'] += $item['classification_sgst_tax'] ?? null;
                            }
                        } else {
                            $itemDetail = [
                                'txval' => $item['total'] ?? null,
                                'rt' => $item['gst_tax_percentage'] ?? null,
                                'csamt' => $item['cess_amount'] ?? null,
                            ];
                            if (! empty($item['classification_igst_tax'])) {
                                $itemDetail['iamt'] = $item['classification_igst_tax'] ?? null;
                            } elseif (! empty($item['classification_cgst_tax']) && ! empty($item['classification_sgst_tax'])) {
                                $itemDetail['camt'] = $item['classification_cgst_tax'] ?? null;
                                $itemDetail['samt'] = $item['classification_sgst_tax'] ?? null;
                            }
                        }
                    }
                    $cdnrItem[] = [
                        'num' => (int) $itemNum,
                        'itm_det' => $itemDetail,
                    ];

                }

                foreach ($additionalCharges as $gstGercentage => $items) {
                    $itemNum = $this->getMappedValue($gstGercentage);
                    $itemNumArray = $this->arraySearch($cdnrItem, $itemNum);
                    $itemDetail = [];
                    foreach ($items as $item) {
                        $value = $item['total_without_tax'] ?? 0;
                        $gst = $value * $item['gst_percentage'] / 100;

                        $cgst = (! empty($cdnrInv['cgst']) || $cdnrInv['cgst'] != 0) ? ($gst / 2) : '0';
                        $sgst = (! empty($cdnrInv['sgst']) || $cdnrInv['sgst'] != 0) ? ($gst / 2) : '0';
                        $igst = (! empty($cdnrInv['igst']) || $cdnrInv['igst'] != 0) ? $gst : '0';

                        if (empty($itemNumArray)) {
                            $itemDetail = [
                                'txval' => round($item['total_without_tax'], 2) ?? 0.0,
                                'rt' => (float) $item['gst_percentage'] ?? 0.0,
                                'csamt' => 0,
                            ];
                            if (! empty($igst)) {
                                $itemDetail['iamt'] = round($igst, 2) ?? 0.0;
                            } elseif (! empty($cgst) && ! empty($sgst)) {
                                $itemDetail['camt'] = round($cgst, 2) ?? 0.0;
                                $itemDetail['samt'] = round($sgst, 2) ?? 0.0;
                            }
                            $cdnrItem[] = [
                                'num' => (int) $itemNum,
                                'itm_det' => $itemDetail,
                            ];
                        } else {
                            $cdnrItem[array_key_first($itemNumArray)]['itm_det']['txval'] += round($item['total'], 2) ?? 0.0;
                            $cdnrItem[array_key_first($itemNumArray)]['itm_det']['csamt'] += 0.0;
                            if (! empty($igst)) {
                                $cdnrItem[array_key_first($itemNumArray)]['itm_det']['iamt'] += round($igst, 2) ?? 0.0;
                            } elseif (! empty($sgst) && ! empty($cgst)) {
                                $cdnrItem[array_key_first($itemNumArray)]['itm_det']['camt'] += round($cgst, 2) ?? 0.0;
                                $cdnrItem[array_key_first($itemNumArray)]['itm_det']['samt'] += round($sgst, 2) ?? 0.0;
                            }
                        }
                    }
                }
                if (! preg_match('/^[A-Za-z0-9\/-]+$/', $cdnrInv['invoice_number'])) {
                    $this->invalidData['invalid_invoice']['cdnr'][] = $cdnrInv['invoice_number'];

                    continue;
                }
                $cdnrInvoice[] = [
                    'nt_num' => $cdnrInv['invoice_number'],
                    'nt_dt' => $cdnrInv['invoice_date'],
                    'ntty' => $cdnrInv['transaction_type'],
                    'val' => abs($cdnrInv['invoice_amount']),
                    'pos' => $pos,
                    'rchrg' => $cdnrInv['rcm'] == 1 ? 'Y' : 'N',
                    'inv_typ' => $invoiceType,
                    'itms' => $cdnrItem,
                ];
            }

            $cdnrCustomer[] = [
                'ctin' => $gstNo,
                'nt' => $cdnrInvoice,
            ];
        }

        return $cdnrCustomer;
    }

    public function cdnurInvoiceData($data)
    {
        $cdnurData = [];
        foreach ($data as $key => $cdnurItems) {
            $pos = explode('-', $cdnurItems['place_of_supplier'])[0];
            $items = [];

            $cdnurInvoiceItem = ! empty($value['items']) ? collect($cdnurItems['items'])->groupBy('gst_tax_percentage') : collect($cdnurItems['ledgers'])->groupBy('gst_tax_percentage');
            $additionalCharges = ! empty($value['additionalCharges']) ? collect($cdnurItems['additionalCharges'])->groupBy('gst_percentage') : [];
            $cdnurItem = [];
            foreach ($cdnurInvoiceItem as $gstGercentage => $item) {
                $itemNum = $gstGercentage.'01';
                $itemDetail = [];
                foreach ($items as $item) {
                    if (! empty($itemDetail)) {
                        $itemDetail['txval'] += round($item['total'], 2) ?? 0.0;
                        $itemDetail['csamt'] += round($item['cess_amount'], 2) ?? 0.0;
                        if (! empty($item['classification_igst_tax'])) {
                            $itemDetail['iamt'] += round($item['classification_igst_tax'], 2) ?? null;
                        } elseif (! empty($item['classification_cgst_tax']) && ! empty($item['classification_sgst_tax'])) {
                            $itemDetail['camt'] += round($item['classification_cgst_tax'], 2) ?? null;
                            $itemDetail['samt'] += round($item['classification_sgst_tax'], 2) ?? null;
                        }
                    } else {
                        $itemDetail = [
                            'txval' => round($item['total'], 2) ?? null,
                            'rt' => $item['gst_tax_percentage'] ?? null,
                            'csamt' => round($item['cess_amount'], 2) ?? null,
                        ];
                        if (! empty($item['classification_igst_tax'])) {
                            $itemDetail['iamt'] = round($item['classification_igst_tax'], 2) ?? null;
                        } elseif (! empty($item['classification_cgst_tax']) && ! empty($item['classification_sgst_tax'])) {
                            $itemDetail['camt'] = round($item['classification_cgst_tax'], 2) ?? null;
                            $itemDetail['samt'] = round($item['classification_sgst_tax'], 2) ?? null;
                        }
                    }
                }
                $cdnurItem[] = [
                    'num' => (int) $itemNum,
                    'itm_det' => $itemDetail,
                ];
            }
            foreach ($additionalCharges as $gstGercentage => $items) {
                $itemNum = $this->getMappedValue($gstGercentage);
                $itemNumArray = $this->arraySearch($cdnurItem, $itemNum);
                $itemDetail = [];
                foreach ($items as $item) {
                    $value = $item['total_without_tax'] ?? 0;
                    $gst = $value * $item['gst_percentage'] / 100;

                    $cgst = (! empty($cdnurItems['cgst']) || $cdnurItems['cgst'] != 0) ? ($gst / 2) : '0';
                    $sgst = (! empty($cdnurItems['sgst']) || $cdnurItems['sgst'] != 0) ? ($gst / 2) : '0';
                    $igst = (! empty($cdnurItems['igst']) || $cdnurItems['igst'] != 0) ? $gst : '0';

                    if (empty($itemNumArray)) {
                        $itemDetail = [
                            'txval' => round($item['total_without_tax'], 2) ?? 0.0,
                            'rt' => (float) $item['gst_percentage'] ?? 0.0,
                            'csamt' => 0,
                        ];
                        if (! empty($igst)) {
                            $itemDetail['iamt'] = round($igst, 2) ?? 0.0;
                        } elseif (! empty($cgst) && ! empty($sgst)) {
                            $itemDetail['camt'] = round($cgst, 2) ?? 0.0;
                            $itemDetail['samt'] = round($sgst, 2) ?? 0.0;
                        }
                        $cdnurItem[] = [
                            'num' => (int) $itemNum,
                            'itm_det' => $itemDetail,
                        ];
                    } else {
                        $cdnurItem[array_key_first($itemNumArray)]['itm_det']['txval'] += round($item['total'], 2) ?? 0.0;
                        $cdnurItem[array_key_first($itemNumArray)]['itm_det']['csamt'] += 0.0;
                        if (! empty($igst)) {
                            $cdnurItem[array_key_first($itemNumArray)]['itm_det']['iamt'] += round($igst, 2) ?? 0.0;
                        } elseif (! empty($sgst) && ! empty($cgst)) {
                            $cdnurItem[array_key_first($itemNumArray)]['itm_det']['camt'] += round($cgst, 2) ?? 0.0;
                            $cdnurItem[array_key_first($itemNumArray)]['itm_det']['samt'] += round($sgst, 2) ?? 0.0;
                        }
                    }
                }
            }
            if (! preg_match('/^[A-Za-z0-9\/-]+$/', $cdnurItems['invoice_number'])) {
                $this->invalidData['invalid_invoice']['cdnur'][] = $cdnurItems['invoice_number'];

                continue;
            }
            $cdnurData[] = [
                'nt_num' => $cdnurItems['invoice_number'],
                'nt_dt' => $cdnurItems['invoice_date'],
                'ntty' => $cdnurItems['transaction_type'],
                'val' => round(abs($cdnurItems['invoice_amount']), 2),
                'typ' => 'B2CL',
                'pos' => $pos,
                'itms' => $cdnurItem,
            ];
        }

        return $cdnurData;
    }

    public function expInvoiceData($data)
    {
        $expGstData = collect($data)->groupBy('export_type');
        $expData = [];
        $invouce = [];

        foreach ($expGstData as $key => $value) {
            $invoiceData = collect($value)->groupBy('invoice_number');

            foreach ($invoiceData as $invoiceNumber => $inv) {
                $invoice = $invoiceData[$invoiceNumber][0];
                $item = [];
                foreach ($invoice['items'] as $items) {
                    $item[] = [
                        'txval' => $items['taxable_value'],
                        'rt' => $items['gst_tax_percentage'],
                        'iamt' => $items['classification_igst_tax'],
                        'csamt' => $items['classification_cess_tax'] ?? 0,
                    ];
                }
                foreach ($invoice['ledgers'] as $items) {
                    $item[] = [
                        'txval' => $items['taxable_value'],
                        'rt' => $items['gst_tax_percentage'],
                        'iamt' => $items['classification_igst_tax'],
                        'csamt' => $items['classification_cess_tax'] ?? 0,
                    ];
                }
                if (! preg_match('/^[A-Za-z0-9\/-]+$/', $invoice['invoice_number'])) {
                    $this->invalidData['invalid_invoice']['exp'][] = $invoice['invoice_number'];

                    continue;
                }
                $invouce[] = [
                    'inum' => $invoice['invoice_number'],
                    'idt' => $invoice['invoice_date'],
                    'val' => $invoice['invoice_amount'],
                    'itms' => $item,
                ];
            }
            $expData[] = [
                'exp_typ' => $key,
                'inv' => $invouce,
            ];
        }

        return $expData;
    }

    public function nilInvoiceDate($data)
    {
        $invoice = [
            [
                'sply_ty' => 'INTRB2C',
                'expt_amt' => $data['interStateExemptedC_3'],
                'nil_amt' => $data['interStateNilRatedB_3'],
                'ngsup_amt' => $data['noneGstInterStateB2CD_3'],
            ],
            [
                'sply_ty' => 'INTRAB2C',
                'expt_amt' => $data['intraStateExemptedC_4'],
                'nil_amt' => $data['intraStateNilRatedB_4'],
                'ngsup_amt' => $data['noneGstIntraStateB2CD_4'],
            ],
        ];

        $filteredInvoice = array_values(array_filter($invoice, function ($row) {
            return ($row['expt_amt'] ?? 0) > 0 ||
                   ($row['nil_amt'] ?? 0) > 0 ||
                   ($row['ngsup_amt'] ?? 0) > 0;
        }));

        return ! empty($filteredInvoice) ? ['inv' => $filteredInvoice] : [];
    }

    public function getMappedValue($input)
    {
        $mapping = [
            1 => 11,
            '1.5' => 1501,
            5 => 501,
            '7.5' => 7501,
            12 => 1201,
            18 => 1801,
            28 => 2801,
            0 => 1,
            7 => 7501,
            // Add more mappings as needed
        ];

        return $mapping[$input] ?? null;
    }

    public function arraySearch($array, $searchvalue, $searchkey = 'num')
    {
        return array_filter($array, function ($item) use ($searchvalue, $searchkey) {
            return $item[$searchkey] == $searchvalue;
        });

    }

    public function getMissingInvoiceSeries($data)
    {
        $numbers = [];
        foreach ($data as $invoice) {
            preg_match('/\d+/', $invoice, $matches);
            if (! empty($matches)) {
                $numbers[] = (int) $matches[0];
            }
        }

        if (empty($numbers)) {
            return 0;
        }

        $minInvoice = min($numbers);
        $maxInvoice = max($numbers);

        $presence = array_fill($minInvoice, $maxInvoice - $minInvoice + 1, false);

        foreach ($numbers as $number) {
            $presence[$number] = true;
        }

        $data = [
            'minInvoice' => $minInvoice,
            'maxInvoice' => $maxInvoice,
            'presence' => $presence,
        ];

        return $data;
    }
}
