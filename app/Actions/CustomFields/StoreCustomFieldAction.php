<?php

namespace App\Actions\CustomFields;

use App\Models\TransactionCustomFieldOption;
use App\Models\TransactionCustomField;
use App\Models\TransactionCustomFieldSetting;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class StoreCustomFieldAction
{
    use AsAction;

    public function handle($input)
    {
        $check = TransactionCustomField::where('label_name', trim($input['label_name']))->first();

        if ($check) {
            throw new UnprocessableEntityHttpException('Custom Field already exists.');
        }

        try {
            DB::beginTransaction();

            $customFields = TransactionCustomField::create([
                'label_name' => $input['label_name'],
                'input_type' => $input['input_type'],
                'company_id' => getCurrentCompany()->id,
            ]);

            /* When Input Type is Select then Create Options */
            if (isset($input['input_type']) && $input['input_type'] == TransactionCustomField::INPUT_TYPE_SELECT) {
                foreach ($input['options'] as $option) {
                    TransactionCustomFieldOption::create([
                        'custom_field_id' => $customFields->id,
                        'company_id' => getCurrentCompany()->id,
                        'option_label' => $option['value'],
                        'option_value' => Str::slug($option['value']),
                    ]);
                }
            }

            /* Create Settings for Multiple Transaction Types */
            foreach ($input['types'] as $type) {
                TransactionCustomFieldSetting::firstOrCreate([
                    'custom_field_id' => $customFields->id,
                    'company_id' => getCurrentCompany()->id,
                    'transaction_type' => $type,
                    'module' => TransactionCustomFieldSetting::TRANSACTION,
                    'is_show_in_print' => isset($input['is_show_in_print']) ? $input['is_show_in_print'] : false,
                ]);
            }

            /* Create Settings for Sale and Purchase Report */
            CreateCustomSettingForSaleAndPurchaseReport::run($customFields, $input['types']);

            DB::commit();

            return $customFields;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("Line: " . $e->getLine() . " Error:  " . $e->getMessage());
            throw $e;
        }
    }
}
