<?php

namespace App\Actions;

use App\Models\CompanyFilter;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class UpdateCompanyFilter
{
    use AsAction;

    public function handle($input)
    {
        $currentCompanyId = getCurrentCompany()->id;
        $companyFilterData = CompanyFilter::whereCompanyId($currentCompanyId)->first();

        if ($input['key'] == 'current_financial_year') {
            if (! in_array($input['value'], getFinancialYearsLists())) {
                throw new UnprocessableEntityHttpException('Invalid Financial Year');
            }
        }

        if (empty($companyFilterData)) {
            CompanyFilter::create([
                'company_id' => $currentCompanyId,
                'filter_meta' => json_encode([], JSON_THROW_ON_ERROR),
            ]);
        } else {
            $filterMeta = $companyFilterData->filter_meta;
            $filterMeta = is_string($filterMeta) ? json_decode($filterMeta, true) : $filterMeta;

            if ($input['key'] == 'current_financial_year') {
                $financialYear = explode(' - ', $input['value']);
                $startYear = $financialYear[0];
                $endYear = $financialYear[1];

                foreach ($filterMeta as $key => $value) {
                    if ($key == 'current_financial_year') {
                        continue;
                    }
                    $dateArray = explode(' - ', $value);

                    if (count($dateArray) == 2) {
                        $currentStartDate = $dateArray[0];
                        $currentEndDate = $dateArray[1];
                        $currentStartYear = explode('-', $currentStartDate)[0];
                        $currentEndYear = explode('-', $currentEndDate)[0];

                        // if (str_contains($currentStartDate, $currentStartYear)) {
                        //     $startDate = str_replace($currentStartYear, $startYear, $currentStartDate);
                        // } else {
                        //     $startDate = str_replace($currentEndYear, $endYear, $currentStartDate);
                        // }

                        // if (str_contains($currentEndDate, $currentEndYear)) {
                        //     $endDate = str_replace($currentEndYear, $endYear, $currentEndDate);
                        // } else {
                        //     $endDate = str_replace($currentStartYear, $startYear, $currentEndDate);
                        // }

                        $startDate = $startYear.'-04-01';
                        $endDate = $endYear.'-03-31';

                        $filterMeta[$key] = $startDate.' - '.$endDate;
                    } else {
                        $currentStartDate = $dateArray[0];
                        $currentStartYear = explode('-', $currentStartDate)[0];

                        if (strlen($currentStartYear) == 2) {
                            $currentStartYear = explode('-', $currentStartDate)[2];
                        }

                        // if (str_contains($currentStartDate, $currentStartYear)) {
                        //     $startDate = '31-03-'.$endYear ;
                        // } else {
                        //     $startDate = str_replace($currentStartYear, $endYear, $currentStartDate);
                        // }

                        if ($key == 'balance_sheet_date') {
                            $startDate = '31-03-'.$endYear;
                            $filterMeta[$key] = $startDate;
                        }
                        if ($key == 'outstanding_report_date') {
                            $startDate = $endYear.'-03-31';
                            $filterMeta[$key] = $startDate;
                        }
                    }
                }
            }

            $companyFilterData->update([
                'filter_meta' => array_merge(
                    $filterMeta,
                    [$input['key'] => $input['value']]
                ),
            ]);
        }
    }
}
