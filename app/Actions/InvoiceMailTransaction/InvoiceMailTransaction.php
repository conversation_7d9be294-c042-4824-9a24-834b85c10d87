<?php

namespace App\Actions\InvoiceMailTransaction;

use App\Mail\DeliveryChallanMail;
use App\Mail\EstimateQuoteTransactionMail;
use App\Mail\ExpenseDebitNoteInvoiceMail;
use App\Mail\IncomeCreditNoteInvoiceMail;
use App\Mail\IncomeDebitNoteInvoiceMail;
use App\Mail\PaymentInvoiceMail;
use App\Mail\PurchaseInvoiceMail;
use App\Mail\PurchaseOrderTransactionMail;
use App\Mail\PurchaseReturnInvoiceMail;
use App\Mail\ReceiptInvoiceMail;
use App\Mail\SaleInvoiceMail;
use App\Mail\SaleReturnInvoiceMail;
use App\Mail\SendIncomeDebitNoteReminderMail;
use App\Mail\SendSaleTransactionReminderMail;
use App\Models\CompanySetting;
use App\Models\DeliveryChallanTransaction;
use App\Models\ExpenseDebitNoteTransaction;
use App\Models\IncomeCreditNoteTransaction;
use App\Models\IncomeDebitNoteTransaction;
use App\Models\IncomeEstimateQuoteTransaction;
use App\Models\PaymentTransaction;
use App\Models\PurchaseOrderTransaction;
use App\Models\PurchaseReturnTransaction;
use App\Models\PurchaseTransaction;
use App\Models\ReceiptTransaction;
use App\Models\SaleReturnTransaction;
use App\Models\SaleTransaction;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\Browsershot\Browsershot;

class InvoiceMailTransaction
{
    use AsAction;

    public $company = null;

    public function handle($data, $transactionType, $invoiceMailType = 1)
    {
        $config = getSadminMailSetting();
        if (count($config)) {
            Config::set('mail', $config);
        }

        $this->company = $data['currentCompany'];

        // for bouce mail
        $data['invoiceMailType'] = $invoiceMailType;
        $landscape = $this->checkLandscape($transactionType);
        if (\in_array($transactionType, [
            SaleTransaction::class, SaleReturnTransaction::class, IncomeDebitNoteTransaction::class, IncomeCreditNoteTransaction::class,
            SendSaleTransactionReminderMail::class, SendIncomeDebitNoteReminderMail::class,
        ])) {
            $data['isA5Pdf'] = false;
            session(['current_company' => $data['currentCompany']]);
            if (dockerEnabled()) {
                try {
                    if (getCompanyPdfFormat() == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE]) {
                        $pdfView = view('company.pdf.sale_pdf.download_landscape_'.landscapePreviewerUi(), $data)->render();
                    } elseif (getCompanyPdfFormat() == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE_A5]) {
                        $pdfView = view('company.pdf.sale_pdf.download_landscape_a5_'.landscapeA5PreviewerUi(), $data)->render();
                    } else {
                        $pdfView = view('company.pdf.sale_pdf.download_'.pdfPreviewerUi(), $data)->render();
                    }
                    $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                    $blResponse = Http::timeout(5)->post('http://localhost:3000/pdf?token=6R0W53R135510', [
                        'html' => $html,
                        'options' => [
                            'displayHeaderFooter' => true,
                            'printBackground' => true,
                            'format' => 'A4',
                            'landscape' => $landscape,
                        ],
                    ]);
                    $data['salePdf'] = $blResponse->body();
                } catch (\Illuminate\Http\Client\ConnectionException $e) {
                    $data['preview_enabled'] = true;
                    if (getCompanyPdfFormat() == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE]) {
                        $pdfView = view('company.pdf.sale_pdf.download_landscape_'.landscapePreviewerUi(), $data)->render();
                    } elseif (getCompanyPdfFormat() == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE_A5]) {
                        $pdfView = view('company.pdf.sale_pdf.download_landscape_a5_'.landscapeA5PreviewerUi(), $data)->render();
                    } else {
                        $pdfView = view('company.pdf.sale_pdf.download_'.pdfPreviewerUi(), $data)->render();
                    }
                    $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                    Log::error('PDF Timeout Error: '.$e->getMessage());
                    $data['salePdf'] = $this->getPdfContent($html, $landscape);
                }
            } else {
                $data['preview_enabled'] = true;
                if (getCompanyPdfFormat() == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE]) {
                    $pdfView = view('company.pdf.sale_pdf.download_landscape_'.landscapePreviewerUi(), $data)->render();
                } elseif (getCompanyPdfFormat() == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE_A5]) {
                    $pdfView = view('company.pdf.sale_pdf.download_landscape_a5_'.landscapeA5PreviewerUi(), $data)->render();
                } else {
                    $pdfView = view('company.pdf.sale_pdf.download_'.pdfPreviewerUi(), $data)->render();
                }
                $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                $data['salePdf'] = $this->getPdfContent($html, $landscape);
            }
        }

        if (\in_array($transactionType, [IncomeEstimateQuoteTransaction::class])) {
            session(['current_company' => $data['currentCompany']]);
            $estimatePdfPreviewerUi = estimatePdfPreviewerUi();
            $companyEstimatePDFFormat = getCompanyEstimatePDFFormat();
            if (dockerEnabled()) {
                try {
                    if ($companyEstimatePDFFormat == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE]) {
                        $pdfView = view('company.pdf.sale_pdf.estimate_download_landscape_'.landscapePreviewerUi(null, CompanySetting::ESTIMATE_PRINT), $data)->render();
                    } elseif ($companyEstimatePDFFormat == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE_A5]) {
                        $pdfView = view('company.pdf.sale_pdf.estimate_download_landscape_a5_'.landscapeA5PreviewerUi(null, CompanySetting::ESTIMATE_PRINT), $data)->render();
                    } else {
                        if ($estimatePdfPreviewerUi == CompanySetting::INVOICE_PDF_UI_6) {
                            $pdfView = view('company.pdf.sale_pdf.estimate_download_2', $data)->render();
                        } elseif ($estimatePdfPreviewerUi == CompanySetting::INVOICE_PDF_UI_7) {
                            $pdfView = view('company.pdf.sale_pdf.estimate_download_3', $data)->render();
                        } else {
                            $pdfView = view('company.pdf.sale_pdf.estimate_download', $data)->render();
                        }
                    }
                    $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                    $blResponse = Http::timeout(5)->post('http://localhost:3000/pdf?token=6R0W53R135510', [
                        'html' => $html,
                        'options' => [
                            'displayHeaderFooter' => true,
                            'printBackground' => true,
                            'format' => 'A4',
                            'landscape' => $landscape,
                        ],
                    ]);
                    $data['incomeEstimateQuotePdf'] = $blResponse->body();
                } catch (\Illuminate\Http\Client\ConnectionException $e) {
                    if ($companyEstimatePDFFormat == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE]) {
                        $pdfView = view('company.pdf.sale_pdf.estimate_download_landscape_'.landscapePreviewerUi(null, CompanySetting::ESTIMATE_PRINT), $data)->render();
                    } elseif ($companyEstimatePDFFormat == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE_A5]) {
                        $pdfView = view('company.pdf.sale_pdf.estimate_download_landscape_a5_'.landscapeA5PreviewerUi(null, CompanySetting::ESTIMATE_PRINT), $data)->render();
                    } else {
                        if ($estimatePdfPreviewerUi == CompanySetting::INVOICE_PDF_UI_6) {
                            $pdfView = view('company.pdf.sale_pdf.estimate_download_2', $data)->render();
                        } elseif ($estimatePdfPreviewerUi == CompanySetting::INVOICE_PDF_UI_7) {
                            $pdfView = view('company.pdf.sale_pdf.estimate_download_3', $data)->render();
                        } else {
                            $pdfView = view('company.pdf.sale_pdf.estimate_download', $data)->render();
                        }
                    }
                    $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                    $data['incomeEstimateQuotePdf'] = Browsershot::html($html)->format(getCompanyPdfFormat())->timeout(120)
                        ->setOption('args', ['--disable-web-security'])
                        ->waitUntilNetworkIdle()
                        ->pdf();
                }
            } else {
                $data['preview_enabled'] = true;
                if ($companyEstimatePDFFormat == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE]) {
                    $pdfView = view('company.pdf.sale_pdf.estimate_download_landscape_'.landscapePreviewerUi(null, CompanySetting::ESTIMATE_PRINT), $data)->render();
                } elseif ($companyEstimatePDFFormat == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE_A5]) {
                    $pdfView = view('company.pdf.sale_pdf.estimate_download_landscape_a5_'.landscapeA5PreviewerUi(null, CompanySetting::ESTIMATE_PRINT), $data)->render();
                } else {
                    if ($estimatePdfPreviewerUi == CompanySetting::INVOICE_PDF_UI_6) {
                        $pdfView = view('company.pdf.sale_pdf.estimate_download_2', $data)->render();
                    } elseif ($estimatePdfPreviewerUi == CompanySetting::INVOICE_PDF_UI_7) {
                        $pdfView = view('company.pdf.sale_pdf.estimate_download_3', $data)->render();
                    } else {
                        $pdfView = view('company.pdf.sale_pdf.estimate_download', $data)->render();
                    }
                }
                $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                $data['incomeEstimateQuotePdf'] = $this->getPdfContent($html, $landscape);
            }
        } elseif (\in_array($transactionType, [DeliveryChallanTransaction::class])) {
            session(['current_company' => $data['currentCompany']]);
            $deliveryChallanPdfPreviewerUi = deliveryChallanPdfPreviewerUi();
            $companyDeliveryChallanPDFFormat = getCompanyDeliveryChallanPDFFormat();
            $data['isA5Pdf'] = false;
            if ($companyDeliveryChallanPDFFormat == CompanySetting::PDF_FORMAT[CompanySetting::A5]) {
                $data['isA5Pdf'] = true;
            }
            if (dockerEnabled()) {
                try {
                    if ($companyDeliveryChallanPDFFormat == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE]) {
                        $pdfView = view('company.pdf.delivery_challan.delivery_challan_landscape_'.landscapePreviewerUi(null, CompanySetting::DELIVERY_CHALLAN_PRINT), $data)->render();
                    } elseif ($companyDeliveryChallanPDFFormat == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE_A5]) {
                        $pdfView = view('company.pdf.delivery_challan.delivery_challan_landscape_a5_'.landscapeA5PreviewerUi(null, CompanySetting::DELIVERY_CHALLAN_PRINT), $data)->render();
                    } elseif ($companyDeliveryChallanPDFFormat == CompanySetting::PDF_FORMAT[CompanySetting::LETTER_HEAD]) {
                        $pdfView = view('company.pdf.delivery_challan.delivery_challan_letter_head', $data)->render();
                    } else {
                        if ($deliveryChallanPdfPreviewerUi == CompanySetting::INVOICE_PDF_UI_6) {
                            $pdfView = view('company.pdf.delivery_challan.delivery_challan_pdf_2', $data)->render();
                        } elseif ($deliveryChallanPdfPreviewerUi == CompanySetting::INVOICE_PDF_UI_7) {
                            $pdfView = view('company.pdf.delivery_challan.delivery_challan_pdf_3', $data)->render();
                        } else {
                            $pdfView = view('company.pdf.delivery_challan.delivery_challan_pdf', $data)->render();
                        }
                    }
                    $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                    $blResponse = Http::timeout(5)->post('http://localhost:3000/pdf?token=6R0W53R135510', [
                        'html' => $html,
                        'options' => [
                            'displayHeaderFooter' => true,
                            'printBackground' => true,
                            'format' => 'A4',
                            'landscape' => $landscape,
                        ],
                    ]);
                    $data['deliveryChallanPDF'] = $blResponse->body();
                } catch (\Illuminate\Http\Client\ConnectionException $e) {
                    if ($companyDeliveryChallanPDFFormat == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE]) {
                        $pdfView = view('company.pdf.delivery_challan.delivery_challan_landscape_'.landscapePreviewerUi(null, CompanySetting::DELIVERY_CHALLAN_PRINT), $data)->render();
                    } elseif ($companyDeliveryChallanPDFFormat == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE_A5]) {
                        $pdfView = view('company.pdf.delivery_challan.delivery_challan_landscape_a5_'.landscapeA5PreviewerUi(null, CompanySetting::DELIVERY_CHALLAN_PRINT), $data)->render();
                    } elseif ($companyDeliveryChallanPDFFormat == CompanySetting::PDF_FORMAT[CompanySetting::LETTER_HEAD]) {
                        $pdfView = view('company.pdf.delivery_challan.delivery_challan_letter_head', $data)->render();
                    } else {
                        if ($deliveryChallanPdfPreviewerUi == CompanySetting::INVOICE_PDF_UI_6) {
                            $pdfView = view('company.pdf.delivery_challan.delivery_challan_pdf_2', $data)->render();
                        } elseif ($deliveryChallanPdfPreviewerUi == CompanySetting::INVOICE_PDF_UI_7) {
                            $pdfView = view('company.pdf.delivery_challan.delivery_challan_pdf_3', $data)->render();
                        } else {
                            $pdfView = view('company.pdf.delivery_challan.delivery_challan_pdf', $data)->render();
                        }
                    }
                    $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                    $data['deliveryChallanPDF'] = $this->getPdfContent($html, $landscape);
                }
            } else {
                $data['preview_enabled'] = true;
                if ($companyDeliveryChallanPDFFormat == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE]) {
                    $pdfView = view('company.pdf.delivery_challan.delivery_challan_landscape_'.landscapePreviewerUi(null, CompanySetting::DELIVERY_CHALLAN_PRINT), $data)->render();
                } elseif ($companyDeliveryChallanPDFFormat == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE_A5]) {
                    $pdfView = view('company.pdf.delivery_challan.delivery_challan_landscape_a5_'.landscapeA5PreviewerUi(null, CompanySetting::DELIVERY_CHALLAN_PRINT), $data)->render();
                } elseif ($companyDeliveryChallanPDFFormat == CompanySetting::PDF_FORMAT[CompanySetting::LETTER_HEAD]) {
                    $pdfView = view('company.pdf.delivery_challan.delivery_challan_letter_head', $data)->render();
                } else {
                    if ($deliveryChallanPdfPreviewerUi == CompanySetting::INVOICE_PDF_UI_6) {
                        $pdfView = view('company.pdf.delivery_challan.delivery_challan_pdf_2', $data)->render();
                    } elseif ($deliveryChallanPdfPreviewerUi == CompanySetting::INVOICE_PDF_UI_7) {
                        $pdfView = view('company.pdf.delivery_challan.delivery_challan_pdf_3', $data)->render();
                    } else {
                        $pdfView = view('company.pdf.delivery_challan.delivery_challan_pdf', $data)->render();
                    }
                }
                $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                $data['deliveryChallanPDF'] = $this->getPdfContent($html, $landscape);
            }
        } elseif (\in_array($transactionType, [PurchaseOrderTransaction::class])) {
            session(['current_company' => $data['currentCompany']]);
            $data['isA5Pdf'] = false;
            if (getCompanyExpensePDFFormat() == CompanySetting::PDF_FORMAT[CompanySetting::A5]) {
                $data['isA5Pdf'] = true;
            }

            if (dockerEnabled()) {
                try {
                    $pdfView = view('company.pdf.purchase_order.purchase_order', $data)->render();
                    $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                    $blResponse = Http::timeout(5)->post('http://localhost:3000/pdf?token=6R0W53R135510', [
                        'html' => $html,
                        'options' => [
                            'displayHeaderFooter' => true,
                            'printBackground' => false,
                            'format' => 'A4',
                        ],
                    ]);
                    $data['purchaseOrderPDF'] = $blResponse->body();
                } catch (\Illuminate\Http\Client\ConnectionException $e) {
                    $pdfView = view('company.pdf.purchase_order.purchase_order', $data)->render();
                    $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                    $data['purchaseOrderPDF'] = $this->getPdfContent($html);
                }
            } else {
                $data['preview_enabled'] = true;
                $pdfView = view('company.pdf.purchase_order.purchase_order', $data)->render();
                $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                $data['purchaseOrderPDF'] = $this->getPdfContent($html);
            }
        } elseif (\in_array($transactionType, [PurchaseTransaction::class])) {
            session(['current_company' => $data['currentCompany']]);
            $data['isA5Pdf'] = false;
            if (getCompanyExpensePDFFormat() == CompanySetting::PDF_FORMAT[CompanySetting::A5]) {
                $data['isA5Pdf'] = true;
            }

            if (dockerEnabled()) {
                try {
                    $pdfView = view('company.pdf.purchase_pdf.download_purchase', $data)->render();
                    $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                    $blResponse = Http::timeout(5)->post('http://localhost:3000/pdf?token=6R0W53R135510', [
                        'html' => $html,
                        'options' => [
                            'displayHeaderFooter' => true,
                            'printBackground' => false,
                            'format' => 'A4',
                        ],
                    ]);
                    $data['purchasePdf'] = $blResponse->body();
                } catch (\Illuminate\Http\Client\ConnectionException $e) {
                    $pdfView = view('company.pdf.purchase_pdf.download_purchase', $data)->render();
                    $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                    $data['purchasePdf'] = $this->getPdfContent($html);
                }
            } else {
                $data['preview_enabled'] = true;
                $pdfView = view('company.pdf.purchase_pdf.download_purchase', $data)->render();
                $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                $data['purchasePdf'] = $this->getPdfContent($html);
            }
        } elseif (\in_array($transactionType, [PurchaseReturnTransaction::class])) {
            session(['current_company' => $data['currentCompany']]);
            $data['isA5Pdf'] = false;
            if (getCompanyExpensePDFFormat() == CompanySetting::PDF_FORMAT[CompanySetting::A5]) {
                $data['isA5Pdf'] = true;
            }

            if (dockerEnabled()) {
                try {
                    $pdfView = view('company.pdf.purchase_pdf.download_purchase_return', $data)->render();
                    $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                    $blResponse = Http::timeout(5)->post('http://localhost:3000/pdf?token=6R0W53R135510', [
                        'html' => $html,
                        'options' => [
                            'displayHeaderFooter' => true,
                            'printBackground' => false,
                            'format' => 'A4',
                        ],
                    ]);
                    $data['purchasePdf'] = $blResponse->body();
                } catch (\Illuminate\Http\Client\ConnectionException $e) {
                    $pdfView = view('company.pdf.purchase_pdf.download_purchase_return', $data)->render();
                    $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                    $data['purchasePdf'] = $this->getPdfContent($html);
                }
            } else {
                $data['preview_enabled'] = true;
                $pdfView = view('company.pdf.purchase_pdf.download_purchase_return', $data)->render();
                $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                $data['purchasePdf'] = $this->getPdfContent($html);
            }
        } elseif (\in_array($transactionType, [ExpenseDebitNoteTransaction::class])) {
            session(['current_company' => $data['currentCompany']]);
            $data['isA5Pdf'] = false;
            if (getCompanyExpensePDFFormat() == CompanySetting::PDF_FORMAT[CompanySetting::A5]) {
                $data['isA5Pdf'] = true;
            }

            if (dockerEnabled()) {
                try {
                    $pdfView = view('company.pdf.purchase_pdf.debit_note', $data)->render();
                    $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                    $blResponse = Http::timeout(5)->post('http://localhost:3000/pdf?token=6R0W53R135510', [
                        'html' => $html,
                        'options' => [
                            'displayHeaderFooter' => true,
                            'printBackground' => false,
                            'format' => 'A4',
                        ],
                    ]);
                    $data['purchasePdf'] = $blResponse->body();
                } catch (\Illuminate\Http\Client\ConnectionException $e) {
                    $pdfView = view('company.pdf.purchase_pdf.debit_note', $data)->render();
                    $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                    $data['purchasePdf'] = $this->getPdfContent($html);
                }
            } else {
                $data['preview_enabled'] = true;
                $pdfView = view('company.pdf.purchase_pdf.debit_note', $data)->render();
                $html = str_replace('({#INVOICE_TYPE#})', '', $pdfView);
                $data['purchasePdf'] = $this->getPdfContent($html);
            }
        }

        if ($transactionType === DeliveryChallanTransaction::class) {
            if (! empty($data['cc'])) {
                $ccEmailArr = explode(',', $data['cc']);
                $ccEmail = $ccEmailArr;

                Mail::to($data['to'])
                    ->cc($ccEmail)
                    ->send((new DeliveryChallanMail($data))
                        ->from('<EMAIL>', $data['fromName'])
                        ->replyTo($data['replayToEmail'], $data['fromName']));
            } else {

                Mail::to($data['to'])
                    ->send((new DeliveryChallanMail($data))
                        ->from('<EMAIL>', $data['fromName'])
                        ->replyTo($data['replayToEmail'], $data['fromName']));
            }
            $data['transaction']->clearMediaCollection(DeliveryChallanTransaction::INVOICE_ATTACHMENT);
        }

        if ($transactionType === IncomeEstimateQuoteTransaction::class) {
            if (! empty($data['cc'])) {
                $ccEmailArr = explode(',', $data['cc']);
                $ccEmail = $ccEmailArr;

                Mail::to($data['to'])
                    ->cc($ccEmail)
                    ->send((new EstimateQuoteTransactionMail($data))
                        ->from('<EMAIL>', $data['fromName'])
                        ->replyTo($data['replayToEmail'], $data['fromName']));
            } else {
                Mail::to($data['to'])
                    ->send((new EstimateQuoteTransactionMail($data))
                        ->from('<EMAIL>', $data['fromName'])
                        ->replyTo($data['replayToEmail'], $data['fromName']));
            }
            $data['transaction']->clearMediaCollection(IncomeEstimateQuoteTransaction::INVOICE_ATTACHMENT);
        }

        if ($transactionType === PurchaseOrderTransaction::class) {
            if (! empty($data['cc'])) {
                $ccEmailArr = explode(',', $data['cc']);
                $ccEmail = $ccEmailArr;
                Mail::to($data['to'])
                    ->cc($ccEmail)
                    ->send((new PurchaseOrderTransactionMail($data))
                        ->from('<EMAIL>', $data['fromName'])
                        ->replyTo($data['replayToEmail'], $data['fromName']));
            } else {
                Mail::to($data['to'])
                    ->send((new PurchaseOrderTransactionMail($data))
                        ->from('<EMAIL>', $data['fromName'])
                        ->replyTo($data['replayToEmail'], $data['fromName']));
            }
            $data['transaction']->clearMediaCollection(PurchaseOrderTransaction::INVOICE_ATTACHMENT);
        }

        if ($transactionType === SaleTransaction::class) {
            if (! empty($data['cc'])) {
                $ccEmailArr = explode(',', $data['cc']);
                $ccEmail = $ccEmailArr;

                Mail::to($data['to'])
                    ->cc($ccEmail)
                    ->send((new SaleInvoiceMail($data))
                        ->from('<EMAIL>', $data['fromName'])
                        ->replyTo($data['replayToEmail'], $data['fromName']));
            } else {
                Mail::to($data['to'])
                    ->send((new SaleInvoiceMail($data))
                        ->from('<EMAIL>', $data['fromName'])
                        ->replyTo($data['replayToEmail'], $data['fromName']));
            }
            $data['transaction']->clearMediaCollection(SaleTransaction::INVOICE_ATTACHMENT);
        }

        if ($transactionType === SaleReturnTransaction::class) {
            if (! empty($data['cc'])) {
                $ccEmailArr = explode(',', $data['cc']);
                $ccEmail = $ccEmailArr;

                Mail::to($data['to'])
                    ->cc($ccEmail)
                    ->send((new SaleReturnInvoiceMail($data))
                        ->from('<EMAIL>', $data['fromName'])
                        ->replyTo($data['replayToEmail'], $data['fromName']));
            } else {
                Mail::to($data['to'])
                    ->send((new SaleReturnInvoiceMail($data))
                        ->from('<EMAIL>', $data['fromName'])
                        ->replyTo($data['replayToEmail'], $data['fromName']));
            }
            $data['transaction']->clearMediaCollection(SaleReturnTransaction::INVOICE_ATTACHMENT);
        }

        if ($transactionType === IncomeDebitNoteTransaction::class) {
            if (! empty($data['cc'])) {
                $ccEmailArr = explode(',', $data['cc']);
                $ccEmail = $ccEmailArr;

                Mail::to($data['to'])
                    ->cc($ccEmail)
                    ->send((new IncomeDebitNoteInvoiceMail($data))
                        ->from('<EMAIL>', $data['fromName'])
                        ->replyTo($data['replayToEmail'], $data['fromName']));
            } else {
                Mail::to($data['to'])
                    ->send((new IncomeDebitNoteInvoiceMail($data))
                        ->from('<EMAIL>', $data['fromName'])
                        ->replyTo($data['replayToEmail'], $data['fromName']));
            }
            $data['transaction']->clearMediaCollection(IncomeDebitNoteTransaction::INVOICE_ATTACHMENT);
        }

        if ($transactionType === IncomeCreditNoteTransaction::class) {
            if (! empty($data['cc'])) {
                $ccEmailArr = explode(',', $data['cc']);
                $ccEmail = $ccEmailArr;

                Mail::to($data['to'])
                    ->cc($ccEmail)
                    ->send((new IncomeCreditNoteInvoiceMail($data))
                        ->from('<EMAIL>', $data['fromName'])
                        ->replyTo($data['replayToEmail'], $data['fromName']));
            } else {
                Mail::to($data['to'])
                    ->send((new IncomeCreditNoteInvoiceMail($data))
                        ->from('<EMAIL>', $data['fromName'])
                        ->replyTo($data['replayToEmail'], $data['fromName']));
            }
            $data['transaction']->clearMediaCollection(IncomeCreditNoteTransaction::INVOICE_ATTACHMENT);
        }

        if ($transactionType === PurchaseTransaction::class) {
            if (! empty($this->data['cc'])) {
                $ccEmailArr = explode(',', $data['cc']);
                $ccEmail = $ccEmailArr;

                Mail::to($data['to'])
                    ->cc($ccEmail)
                    ->send(new PurchaseInvoiceMail($data));
            } else {
                Mail::to($data['to'])
                    ->send(new PurchaseInvoiceMail($data));
            }
            $data['transaction']->clearMediaCollection(PurchaseTransaction::INVOICE_ATTACHMENT);
        }

        if ($transactionType === PurchaseReturnTransaction::class) {
            if (! empty($this->data['cc'])) {
                $ccEmailArr = explode(',', $data['cc']);
                $ccEmail = $ccEmailArr;

                Mail::to($data['to'])
                    ->cc($ccEmail)
                    ->send(new PurchaseReturnInvoiceMail($data));
            } else {
                Mail::to($data['to'])
                    ->send(new PurchaseReturnInvoiceMail($data));
            }
            $data['transaction']->clearMediaCollection(PurchaseReturnTransaction::INVOICE_ATTACHMENT);
        }

        if ($transactionType === ExpenseDebitNoteTransaction::class) {
            if (! empty($this->data['cc'])) {
                $ccEmailArr = explode(',', $data['cc']);
                $ccEmail = $ccEmailArr;

                Mail::to($data['to'])
                    ->cc($ccEmail)
                    ->send(new ExpenseDebitNoteInvoiceMail($data));
            } else {
                Mail::to($data['to'])
                    ->send(new ExpenseDebitNoteInvoiceMail($data));
            }
            $data['transaction']->clearMediaCollection(ExpenseDebitNoteTransaction::INVOICE_ATTACHMENT);
        }

        /* if ($this->transactionType === ExpenseCreditNoteTransaction::class) {
            if (! empty($this->data['cc'])) {
                $ccEmailArr = explode(',', $this->data['cc']);
                $ccEmail = $ccEmailArr;

                Mail::to($this->data['to'])
                    ->cc($ccEmail)
                    ->send(new ExpenseCreditNoteInvoiceMail($this->data));
            } else {
                Mail::to($this->data['to'])
                    ->send(new ExpenseCreditNoteInvoiceMail($this->data));
            }
            $this->data['transaction']->clearMediaCollection(ExpenseCreditNoteTransaction::INVOICE_ATTACHMENT);
        } */

        if ($transactionType === SendSaleTransactionReminderMail::class) {
            if (! empty($data['cc'])) {
                $ccEmailArr = explode(',', $data['cc']);
                $ccEmail = $ccEmailArr;

                Mail::to($data['to'])
                    ->cc($ccEmail)
                    ->send(new SendSaleTransactionReminderMail($data));
            } else {
                Mail::to($data['to'])
                    ->send(new SendSaleTransactionReminderMail($data));
            }
            $data['transaction']->clearMediaCollection(SaleTransaction::INVOICE_ATTACHMENT);
        }

        if ($transactionType === SendIncomeDebitNoteReminderMail::class) {
            if (! empty($data['cc'])) {
                $ccEmailArr = explode(',', $data['cc']);
                $ccEmail = $ccEmailArr;

                Mail::to($data['to'])
                    ->cc($ccEmail)
                    ->send(new SendIncomeDebitNoteReminderMail($data));
            } else {
                Mail::to($data['to'])
                    ->send(new SendIncomeDebitNoteReminderMail($data));
            }
            $data['transaction']->clearMediaCollection(IncomeDebitNoteTransaction::INVOICE_ATTACHMENT);
        }

        if ($transactionType === ReceiptTransaction::class) {
            session(['current_company' => $data['currentCompany']]);
            if (dockerEnabled()) {
                try {
                    $html = view('pdf.receipt-transaction', $data)->render();
                    $blResponse = Http::timeout(5)->post('http://localhost:3000/pdf?token=6R0W53R135510', [
                        'html' => $html,
                        'options' => [
                            'displayHeaderFooter' => true,
                            'printBackground' => true,
                            'format' => 'A4',
                        ],
                    ]);
                    $data['receiptPdf'] = $blResponse->body();
                } catch (\Illuminate\Http\Client\ConnectionException $e) {
                    $data['preview_enabled'] = true;
                    $html = view('pdf.receipt-transaction', $data)->render();
                    $data['receiptPdf'] = $this->getPdfContent($html, $landscape);
                }
            } else {
                $data['preview_enabled'] = true;
                $html = view('pdf.receipt-transaction', $data)->render();
                $data['receiptPdf'] = $this->getPdfContent($html, $landscape);
            }
            if (! empty($data['cc'])) {
                $ccEmailArr = explode(',', $data['cc']);
                $ccEmail = $ccEmailArr;

                Mail::to($data['to'])
                    ->cc($ccEmail)
                    ->send((new ReceiptInvoiceMail($data))
                        ->from('<EMAIL>', $data['fromName'])
                        ->replyTo($data['replayToEmail'], $data['fromName']));
            } else {
                Log::info('here');
                Mail::to($data['to'])
                    ->send((new ReceiptInvoiceMail($data))
                        ->from('<EMAIL>', $data['fromName'])
                        ->replyTo($data['replayToEmail'], $data['fromName']));
            }
            $data['receiptTransactions']->clearMediaCollection(SaleTransaction::INVOICE_ATTACHMENT);
        }

        if ($transactionType === PaymentTransaction::class) {
            session(['current_company' => $data['currentCompany']]);

            if (dockerEnabled()) {
                try {
                    $html = view('pdf.payment-transaction', $data)->render();
                    $blResponse = Http::timeout(5)->post('http://localhost:3000/pdf?token=6R0W53R135510', [
                        'html' => $html,
                        'options' => [
                            'displayHeaderFooter' => true,
                            'printBackground' => true,
                            'format' => 'A4',
                        ],
                    ]);

                    $data['paymentPdf'] = $blResponse->body();
                } catch (\Illuminate\Http\Client\ConnectionException $e) {
                    $data['preview_enabled'] = true;
                    $html = view('pdf.payment-transaction', $data)->render();
                    $data['paymentPdf'] = $this->getPdfContent($html, $landscape);
                }
            } else {
                $data['preview_enabled'] = true;
                $html = view('pdf.payment-transaction', $data)->render();
                $data['paymentPdf'] = $this->getPdfContent($html, $landscape);
            }

            if (! empty($data['cc'])) {
                $ccEmailArr = explode(',', $data['cc']);
                $ccEmail = $ccEmailArr;

                Mail::to($data['to'])
                    ->cc($ccEmail)
                    ->send((new PaymentInvoiceMail($data))
                        ->from('<EMAIL>', $data['fromName'])
                        ->replyTo($data['replayToEmail'], $data['fromName']));
            } else {
                Mail::to($data['to'])
                    ->send((new PaymentInvoiceMail($data))
                        ->from('<EMAIL>', $data['fromName'])
                        ->replyTo($data['replayToEmail'], $data['fromName']));
            }
            $data['paymentTransactions']->clearMediaCollection(SaleTransaction::INVOICE_ATTACHMENT);
        }
    }

    public function getPdfContent($html, $landscape = false)
    {
        return Browsershot::html($html)->format('A4')
            ->timeout(120)
            ->setOption('args', ['--disable-web-security'])
            ->waitUntilNetworkIdle()
            ->showBackground()
            ->landscape($landscape)
            ->pdf();

    }

    private function checkLandscape($transactionType)
    {
        if ($transactionType == DeliveryChallanTransaction::class) {
            $companySetting = CompanySetting::where('company_id', $this->company->id)->where('key', 'delivery_challan_pdf_format')->first();
            $pdfFormatValue = ! empty($companySetting) ? CompanySetting::DELIVERY_CHALLAN_PDF_FORMAT[$companySetting->value] : CompanySetting::DELIVERY_CHALLAN_PDF_FORMAT[CompanySetting::A4];

            return $pdfFormatValue == CompanySetting::DELIVERY_CHALLAN_PDF_FORMAT[CompanySetting::LANDSCAPE] || $pdfFormatValue == CompanySetting::DELIVERY_CHALLAN_PDF_FORMAT[CompanySetting::LANDSCAPE_A5] ? true : false;
        }
        if ($transactionType == IncomeEstimateQuoteTransaction::class) {
            $companySetting = CompanySetting::where('company_id', $this->company->id)->where('key', 'estimate_pdf_format')->first();
            $pdfFormatValue = ! empty($companySetting) ? CompanySetting::ESTIMATE_PDF_FORMAT[$companySetting->value] : CompanySetting::ESTIMATE_PDF_FORMAT[CompanySetting::A4];

            return $pdfFormatValue == CompanySetting::ESTIMATE_PDF_FORMAT[CompanySetting::LANDSCAPE] || $pdfFormatValue == CompanySetting::ESTIMATE_PDF_FORMAT[CompanySetting::LANDSCAPE_A5] ? true : false;
        }
        if (in_array($transactionType, [SaleTransaction::class,
            SaleReturnTransaction::class,
            IncomeCreditNoteTransaction::class,
            IncomeDebitNoteTransaction::class,
        ], true)) {
            $companySetting = CompanySetting::where('company_id', $this->company->id)->where('key', 'pdf_format')->first();

            $pdfFormatValue = ! empty($companySetting) ? CompanySetting::PDF_FORMAT[$companySetting->value] : CompanySetting::PDF_FORMAT[CompanySetting::A4];

            return $pdfFormatValue == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE] || $pdfFormatValue == CompanySetting::PDF_FORMAT[CompanySetting::LANDSCAPE_A5] ? true : false;
        }

        return false;
    }
}
