<?php

namespace App\Actions\CustomFieldsItemMaster\Transaction;

use App\Actions\CustomFieldsItemMaster\GetInputTypeWiseValue;
use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldOption;
use App\Models\ItemCustomFieldTransactionSetting;
use Lorisleiva\Actions\Concerns\AsAction;

class GetCustomFieldItemTable
{
    use AsAction;

    public function handle($type)
    {
        $fields = ItemCustomField::with(['customFieldDefaultValue', 'customFieldDefaultFormula'])->where('open_in_popup', false)->get();

        $settings = ItemCustomFieldTransactionSetting::where('transaction_type', $type)->get(['id', 'custom_field_id', 'is_show_in_print'])->keyBy('custom_field_id');

        $data = [];

        foreach ($fields as $field) {
            $isShowInPrint = ItemCustomFieldTransactionSetting::where('transaction_type', $type)->where('custom_field_id', $field->id)->first();

            $options = [];
            if ($field->custom_field_type == ItemCustomField::CF_TYPE_DROPDOWN) {
                $options = ItemCustomFieldOption::where('custom_field_id', $field->id)->get(['id', 'option_label'])->toArray();
            }
            $defaultValue = null;

            if ($field->customFieldDefaultValue) {
                $defaultValue = GetInputTypeWiseValue::run($field->customFieldDefaultValue);
            }

            $data[] = [
                'custom_field_id' => $field->id,
                'name' => $field->label_name,
                'custom_field_type' => $field->custom_field_type,
                'is_enabled' => isset($settings[$field->id]),
                'transaction_type' => $type,
                'is_show_in_print' => $isShowInPrint && $isShowInPrint->is_show_in_print ? true : false,
                'open_in_popup' => $field->open_in_popup,
                'options' => $options,
                'default_value' => $defaultValue,
                'option_id' => $field->customFieldDefaultValue ? $field->customFieldDefaultValue->value_select_option_id : null,
                'default_formula' => $field->customFieldDefaultFormula,
                'input_type' => ItemCustomField::CUSTOM_FIELD_ARRAY_WITH_INPUT_TYPE[$field->custom_field_type]['input_type'] ?? null,
            ];
        }

        return $data;
    }
}
