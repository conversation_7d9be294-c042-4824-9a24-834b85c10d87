<?php

namespace App\Actions\CustomFieldsItemMaster\Transaction;

use App\Actions\CustomFieldsItemMaster\GetInputTypeWiseValue;
use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldTransactionSetting;
use App\Models\ItemCustomFieldValue;
use Lorisleiva\Actions\Concerns\AsAction;

class PrepareCustomFieldDataForPdfPreview
{
    use AsAction;

    public function handle($type, $transactionId, $modelType)
    {
        $itemCustomFields = ItemCustomField::whereHas('customFieldTransactionSettings', function ($q) use ($type) {
            $q->where('transaction_type', $type);
        })->where('open_in_popup', false)->get();

        $transactionCFValue = ItemCustomFieldValue::where('model_id', $transactionId)->where('model_type', $modelType)->orderBy('custom_field_id')->get()->keyBy('custom_field_id');

        $response = [];

        foreach ($itemCustomFields as $field) {
            $isShowInPrint = ItemCustomFieldTransactionSetting::where('transaction_type', $type)->where('custom_field_id', $field->id)->first();

            if (isset($transactionCFValue[$field->id])) {
                $value = GetInputTypeWiseValue::run($transactionCFValue[$field->id]);
                $fieldType = $transactionCFValue[$field->id]->field_type == 1 ? 'M' : 'Y';
            } else {
                $value = null;
                $fieldType = null;
            }

            $response[] = [
                'id' => $field->id,
                'custom_field_id' => $field->id,
                'label_name' => $field->label_name,
                'custom_field_type' => $field->custom_field_type,
                'value' => $field->custom_field_type == ItemCustomField::CF_TYPE_WARRANTY ? $value.' '.$fieldType : $value,
                'option_id' => $field->custom_field_type == ItemCustomField::CF_TYPE_DROPDOWN ? $field->value_select_option_id : null, // for select type only
                'is_show_in_print' => $isShowInPrint && $isShowInPrint->is_show_in_print ? true : false,
                'field_type' => $fieldType,
            ];
        }

        return $response;
    }
}
