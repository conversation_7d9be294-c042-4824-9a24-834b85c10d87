<?php

namespace App\Actions\CustomFieldsItemMaster;

use App\Models\ItemCustomFieldDefaultValue;
use App\Models\ItemCustomFieldFormula;
use App\Models\ItemCustomFieldSetting;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class AddUpdateCustomFieldValueOfItemMaster
{
    use AsAction;

    public function handle($input, $itemId)
    {
        try {
            DB::beginTransaction();

            $cfIds = $input['custom_fields'] ?? [];

            $existingIds = ItemCustomFieldSetting::where('item_id', $itemId)->pluck('custom_field_id')->toArray();

            $insert = array_diff($cfIds, $existingIds);

            foreach ($insert as $customFieldId) {
                ItemCustomFieldSetting::firstOrCreate([
                    'custom_field_id' => $customFieldId,
                    'item_id' => $itemId,
                ]);
            }

            ItemCustomFieldSetting::where('item_id', $itemId)->whereNotIn('custom_field_id', $cfIds)->delete();

            if (! empty($cfIds) && isset($input['custom_fields_values']) && count($input['custom_fields_values']) > 0) {
                $cfItemValues = $input['custom_fields_values'];
                $this->addUpdateCustomFieldItemLevelValue($cfItemValues, $itemId);
            } else {
                ItemCustomFieldDefaultValue::where('item_id', $itemId)->delete();
            }

            if (! empty($cfIds) && isset($input['custom_fields_formula']) && count($input['custom_fields_formula']) > 0) {
                $cfItemFormula = $input['custom_fields_formula'];
                $this->addUpdateCustomFieldItemLevelFormula($cfItemFormula, $itemId);
            } else {
                ItemCustomFieldFormula::where('item_id', $itemId)->delete();
            }

            DB::commit();

            return true;
        } catch (Exception $th) {
            DB::rollBack();
            Log::error("Line: {$th->getLine()} Error: {$th->getMessage()}");
            throw $th;
        }

    }

    private function addUpdateCustomFieldItemLevelValue($cfItemValues, $itemId)
    {
        $cfValues = ItemCustomFieldDefaultValue::where('item_id', $itemId)->pluck('id')->toArray();
        $editedValuesIds = Arr::pluck($cfItemValues, 'id');
        $removeValuesIds = array_diff($cfValues, $editedValuesIds);

        if (count($removeValuesIds) > 0) {
            ItemCustomFieldDefaultValue::whereIn('id', array_values($removeValuesIds))?->delete();
        }

        foreach ($cfItemValues as $field) {
            $dbKey = GetInputTypeWiseKey::run($field['custom_field_id']);
            $dbValue = $field['value'];

            if (! isset($field['id']) || $field['id'] == null) {
                ItemCustomFieldDefaultValue::create([
                    'custom_field_id' => $field['custom_field_id'],
                    'item_id' => $itemId,
                    $dbKey => $dbValue,
                    'field_type' => isset($field['field_type']) ? $field['field_type'] : null,
                ]);
            } else {
                ItemCustomFieldDefaultValue::where('id', $field['id'])
                    ->where('custom_field_id', $field['custom_field_id'])
                    ->where('item_id', $itemId)
                    ->update([
                        $dbKey => $dbValue,
                        'field_type' => isset($field['field_type']) ? $field['field_type'] : null,
                    ]);
            }
        }

        return true;
    }

    private function addUpdateCustomFieldItemLevelFormula($cfItemFormula, $itemId)
    {
        $cfValues = ItemCustomFieldFormula::where('item_id', $itemId)->pluck('id')->toArray();
        $editedIds = Arr::pluck($cfItemFormula, 'id');
        $removeIds = array_diff($cfValues, $editedIds);

        if (count($removeIds) > 0) {
            ItemCustomFieldFormula::whereIn('id', array_values($removeIds))?->delete();
        }

        foreach ($cfItemFormula as $field) {
            // if used_cf_ids_for_formula is not sent from client, then get the numbers from formula
            preg_match_all('/\{\s*(\d+)\s*\}/', $field['formula'], $matches);
            $numbers = $matches[1];

            $idsArray = ! empty($field['used_cf_ids_for_formula']) ? explode(',', $field['used_cf_ids_for_formula']) : array_unique($numbers) ?? [];
            $idsArray = array_map('intval', $idsArray);

            $payload = [
                'company_id' => getCurrentCompany()->id,
                'formula' => $field['formula'],
                'used_cf_ids_for_formula' => count($idsArray) > 0 ? array_unique($idsArray) : null,
                'is_system_field' => (bool) $field['is_system_field'],
                'system_field_name' => $field['is_system_field'] ? $field['system_field_name'] : null,
            ];

            if (! $field['is_system_field']) {
                $payload['custom_field_id'] = $field['custom_field_id'] ?? null;
            }

            if (empty($field['id']) || $field['id'] == null) {
                $payload['item_id'] = $itemId;
                ItemCustomFieldFormula::create($payload);
            } else {
                $query = ItemCustomFieldFormula::where('id', $field['id'])->where('item_id', $itemId);
                if (! $field['is_system_field']) {
                    $query->where('custom_field_id', $field['custom_field_id']);
                }
                $query->update($payload);
            }
        }

        return true;
    }
}
