<?php

namespace App\Actions\CustomFieldsItemMaster;

use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldDefaultValue;
use App\Models\ItemCustomFieldFormula;
use App\Models\ItemCustomFieldSetting;
use App\Models\ItemCustomFieldValue;
use Lorisleiva\Actions\Concerns\AsAction;

class GetAllItemMasterCustomFields
{
    use AsAction;

    public function handle($itemId = null)
    {
        $customFields = $this->loadCustomFieldsData();

        if (! $itemId) {
            $this->appendQuantityFormulaIfEligible($customFields);

            return $customFields;
        }

        // $this->applyItemSpecificData($customFields, $itemId);
        // $this->appendQuantityFormulaIfEligible($customFields, $itemId);

        return $customFields;
    }

    private function loadCustomFieldsData()
    {
        return ItemCustomField::with(['options', 'customFieldDefaultValue', 'customFieldDefaultFormula'])
            ->select('id', 'company_id', 'label_name', 'custom_field_type', 'enable_for_all', 'open_in_popup', 'status')
            ->get()
            ->map(function ($field) {
                $field->default_value = $field->customFieldDefaultValue
                    ? GetInputTypeWiseValue::run($field->customFieldDefaultValue)
                    : null;

                $field->default_formula = $field->customFieldDefaultFormula ?? null;

                $field->select_default_value_or_formula = $field->default_value ? 1 : ($field->default_formula ? 2 : 1);
                $field->status = $field->status ?? true;
                $field->local_status = $field->status ?? true;
                $field->input_type = ItemCustomField::CUSTOM_FIELD_ARRAY_WITH_INPUT_TYPE[$field->custom_field_type]['input_type'] ?? null;
                $field->eligible_for_formula = $field->custom_field_type == ItemCustomField::CF_TYPE_NUMBER;
                $field->field_type = $field->customFieldDefaultValue ? $field->customFieldDefaultValue->field_type : null;

                unset($field->customFieldDefaultValue);
                unset($field->customFieldDefaultFormula);

                return $field;
            });
    }

    private function applyItemSpecificData(&$customFields, $itemId)
    {
        $customFieldIds = $customFields->pluck('id')->toArray();

        $itemSettings = ItemCustomFieldSetting::where('item_id', $itemId)->get()->keyBy('custom_field_id');
        $itemCFDefaults = ItemCustomFieldDefaultValue::where('item_id', $itemId)->whereIn('custom_field_id', $customFieldIds)->get()->keyBy('custom_field_id');
        $usedFieldIds = ItemCustomFieldValue::whereIn('custom_field_id', $customFieldIds)->pluck('custom_field_id')->unique();
        $itemCFFormulas = ItemCustomFieldFormula::whereIn('custom_field_id', $customFieldIds)->where('item_id', $itemId)->get()->keyBy('custom_field_id');

        $customFields->transform(function ($field) use ($itemSettings, $itemCFDefaults, $usedFieldIds, $itemCFFormulas) {
            $field->item_cf_value = isset($itemCFDefaults[$field->id]) ? GetInputTypeWiseValue::run($itemCFDefaults[$field->id]) : null;

            $field->status = $field->enable_for_all || isset($itemSettings[$field->id]);
            $field->local_status = $field->enable_for_all || isset($itemSettings[$field->id]);
            $field->can_delete = ! $usedFieldIds->contains($field->id);
            $field->input_type = ItemCustomField::CUSTOM_FIELD_ARRAY_WITH_INPUT_TYPE[$field->custom_field_type]['input_type'] ?? null;
            $field->eligible_for_formula = $field->custom_field_type == ItemCustomField::CF_TYPE_NUMBER;

            $field->item_custom_field_default_value = $itemCFDefaults[$field->id] ?? null;
            $field->item_custom_field_default_formula = $itemCFFormulas[$field->id] ?? null;

            return $field;
        });
    }

    private function appendQuantityFormulaIfEligible(&$customFields, $itemId = null)
    {
        $hasEligible = collect($customFields)->contains(function ($item) {
            return $item['eligible_for_formula'] == true;
        });

        if ($hasEligible) {
            $defaultFormula = ItemCustomFieldFormula::where('is_system_field', true)
                ->where('system_field_name', 'Quantity')
                ->whereNull('item_id')
                ->select('id', 'formula', 'custom_field_id', 'item_id', 'used_cf_ids_for_formula')
                ->first();

            $itemDefaultFormula = null;

            if ($itemId) {
                $itemDefaultFormula = ItemCustomFieldFormula::where('is_system_field', true)
                    ->where('system_field_name', 'Quantity')
                    ->where('item_id', $itemId)
                    ->select('id', 'formula', 'custom_field_id', 'item_id', 'used_cf_ids_for_formula')
                    ->first();
            }

            $customFields->push([
                'label_name' => 'Quantity',
                // 'status' => $defaultFormula || $itemDefaultFormula ? true : false,
                // 'local_status' => $defaultFormula || $itemDefaultFormula ? true : false,
                'status' => true,
                'local_status' => true,
                'is_system_field' => true,
                'system_field_name' => 'Quantity',
                'default_formula' => $defaultFormula ? $defaultFormula : null,
                'item_custom_field_default_formula' => $itemDefaultFormula ? $itemDefaultFormula : null,
            ]);
        }
    }
}
