<?php

namespace App\Actions\CustomFieldsItemMaster;

use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldDefaultValue;
use App\Models\ItemCustomFieldFormula;
use App\Models\ItemCustomFieldOption;
use App\Models\ItemCustomFieldSetting;
use App\Models\ItemCustomFieldTransactionSetting;
use App\Models\Master\ItemMaster;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class UpdateCustomFieldItemMaster
{
    use AsAction;

    public function handle($input, $id)
    {
        if (CheckReservedKeyword::run($input['label_name'])) {
            throw new UnprocessableEntityHttpException('This label name is reserved.');
        }

        $exists = ItemCustomField::where('label_name', $input['label_name'])->whereNot('id', $id)->exists();

        if ($exists) {
            throw new UnprocessableEntityHttpException('Custom Field already exists.');
        }

        try {
            DB::beginTransaction();

            $customField = ItemCustomField::findOrFail($id);

            $customField->update([
                'label_name' => $input['label_name'],
                'custom_field_type' => $input['custom_field_type'],
                'enable_for_all' => isset($input['enable_for_all']) ? $input['enable_for_all'] : false,
                'open_in_popup' => isset($input['open_in_popup']) ? $input['open_in_popup'] : false,
                'status' => true,
            ]);

            if (isset($input['enable_for_all']) && $input['enable_for_all'] == true) {
                $items = ItemMaster::pluck('id')->toArray();
                foreach ($items as $itemId) {
                    ItemCustomFieldSetting::firstOrCreate([
                        'custom_field_id' => $customField->id,
                        'item_id' => $itemId,
                    ]);
                }
            }

            /* When Input Type is Select then Create New Options or If existing then Update */
            if (isset($input['custom_field_type']) && $input['custom_field_type'] == ItemCustomField::CF_TYPE_DROPDOWN) {
                if (isset($input['options']) && count($input['options']) > 0) {
                    $this->updateCustomFieldOptions($customField->id, $input['options']);
                }
            } else {
                ItemCustomFieldOption::where('custom_field_id', $id)->delete();
            }

            $transactionTypes = collect($input['types'])->pluck('type')->toArray();

            /* Create Settings for Multiple Transaction Types */
            ItemCustomFieldTransactionSetting::where('custom_field_id', $customField->id)->whereNotIn('transaction_type', $transactionTypes)->delete();

            foreach ($input['types'] as $type) {
                ItemCustomFieldTransactionSetting::updateOrCreate(
                    [
                        'custom_field_id' => $customField->id,
                        'transaction_type' => $type['type'],
                    ],
                    [
                        'is_show_in_print' => $type['is_show_in_print'] ?? false,
                    ]
                );
            }

            if (isset($input['default_value']) && ! empty($input['default_value'])) {
                $dbKey = GetInputTypeWiseKey::run($customField->id);

                ItemCustomFieldDefaultValue::updateOrCreate(
                    [
                        'custom_field_id' => $customField->id,
                        'item_id' => null,
                    ],
                    [
                        $dbKey => $input['default_value'],
                        'field_type' => isset($input['field_type']) ? $input['field_type'] : null,
                    ]
                );
            } else {
                ItemCustomFieldDefaultValue::where('custom_field_id', $customField->id)->whereNull('item_id')->delete();
            }

            if (isset($input['default_formula']) && ! empty($input['default_formula'])) {
                // if used_cf_ids_for_formula is not sent from client, then get the numbers from formula
                preg_match_all('/\{\s*(\d+)\s*\}/', $input['default_formula'], $matches);
                $numbers = $matches[1];

                $idsArray = ! empty($input['used_cf_ids_for_formula']) ? explode(',', $input['used_cf_ids_for_formula']) : array_unique($numbers) ?? [];
                $idsArray = array_map('intval', $idsArray);

                ItemCustomFieldFormula::updateOrCreate(
                    [
                        'company_id' => getCurrentCompany()->id,
                        'custom_field_id' => $customField->id,
                        'item_id' => null,
                    ],
                    [
                        'is_system_field' => 0,
                        'system_field_name' => null,
                        'formula' => $input['default_formula'],
                        'used_cf_ids_for_formula' => count($idsArray) > 0 ? array_unique($idsArray) : null,
                    ]
                );
            } else {
                ItemCustomFieldFormula::where('custom_field_id', $customField->id)->whereNull('item_id')->delete();
            }

            DB::commit();

            $customField->input_type = ItemCustomField::CUSTOM_FIELD_ARRAY_WITH_INPUT_TYPE[$customField->custom_field_type]['input_type'];

            return $customField;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    private function updateCustomFieldOptions($customFieldId, $options)
    {
        $existingOptions = ItemCustomFieldOption::where('custom_field_id', $customFieldId)->pluck('id')->toArray();
        $editedOptions = Arr::pluck($options, 'id');
        $removeOptionIds = array_diff($existingOptions, $editedOptions);

        if (count($removeOptionIds) > 0) {
            ItemCustomFieldOption::whereIn('id', array_values($removeOptionIds))->delete();
        }

        foreach ($options as $option) {
            if (! isset($option['id']) || $option['id'] == null) {
                ItemCustomFieldOption::create([
                    'custom_field_id' => $customFieldId,
                    'company_id' => getCurrentCompany()->id,
                    'option_label' => $option['value'],
                ]);
            } else {
                ItemCustomFieldOption::where('id', $option['id'])->update([
                    'option_label' => $option['value'],
                ]);
            }
        }
    }
}
