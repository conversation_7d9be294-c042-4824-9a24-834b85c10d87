<?php

namespace App\Actions\CustomFieldsItemMaster;

use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldFormula;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class StoreUpdateCustomFieldDefaultFormula
{
    use AsAction;

    public function handle($input)
    {
        try {
            DB::beginTransaction();
            if (isset($input['custom_field_id']) && isset($input['is_system_field']) && $input['is_system_field'] == false) {
                $checkCustomFieldInputIsNumber = ItemCustomField::where('id', $input['custom_field_id'])->whereIn('custom_field_type', [ItemCustomField::CF_TYPE_NUMBER])->first();

                if (! $checkCustomFieldInputIsNumber) {
                    throw new UnprocessableEntityHttpException('Select Custom Field is not Number.');
                }
                $input['is_system_field'] = false;
                $input['system_field_name'] = null;

                $data = ItemCustomFieldFormula::where('custom_field_id', $input['custom_field_id'])->whereNull('item_id')->first();

                if ($data) {
                    $data->update($this->prepareData($input));
                } else {
                    $data = ItemCustomFieldFormula::create($this->prepareData($input));
                }
            } else {
                if (! in_array($input['system_field_name'], ['Quantity', 'Unit Price', 'MRP', 'Amount'])) {
                    throw new UnprocessableEntityHttpException('Please select valid existing key. Available keys are "Quantity", "Unit Price", "MRP" and "Amount"');
                }
                $input['custom_field_id'] = null;
                $input['is_system_field'] = true;

                $data = ItemCustomFieldFormula::where('system_field_name', $input['system_field_name'])->whereNull('item_id')->whereNull('custom_field_id')->first();

                if ($data) {
                    $data->update($this->prepareData($input));
                } else {
                    $data = ItemCustomFieldFormula::create($this->prepareData($input));
                }
            }

            DB::commit();

            return $data;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Line: '.$e->getLine().' Error:  '.$e->getMessage());
            throw $e;
        }
    }

    private function prepareData($input)
    {
        // if used_cf_ids_for_formula is not sent from client, then get the numbers from formula
        preg_match_all('/\{\s*(\d+)\s*\}/', $input['formula'], $matches);
        $numbers = $matches[1];

        $idsArray = ! empty($input['used_cf_ids_for_formula']) ? explode(',', $input['used_cf_ids_for_formula']) : array_unique($numbers) ?? [];
        $idsArray = array_map('intval', $idsArray);

        return [
            'company_id' => getCurrentCompany()->id,
            'custom_field_id' => $input['custom_field_id'] ?? null,
            'item_id' => null,
            'formula' => $input['formula'],
            'is_system_field' => $input['is_system_field'],
            'system_field_name' => $input['system_field_name'] ?? null,
            'used_cf_ids_for_formula' => count($idsArray) > 0 ? array_unique($idsArray) : null,
        ];
    }
}
