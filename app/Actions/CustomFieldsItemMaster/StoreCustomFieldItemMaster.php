<?php

namespace App\Actions\CustomFieldsItemMaster;

use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldDefaultValue;
use App\Models\ItemCustomFieldFormula;
use App\Models\ItemCustomFieldOption;
use App\Models\ItemCustomFieldSetting;
use App\Models\ItemCustomFieldTransactionSetting;
use App\Models\Master\ItemMaster;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class StoreCustomFieldItemMaster
{
    use AsAction;

    public function handle($input)
    {
        if (CheckReservedKeyword::run($input['label_name'])) {
            throw new UnprocessableEntityHttpException('This label name is reserved.');
        }

        $exists = ItemCustomField::where('label_name', trim($input['label_name']))->exists();
        $companyId = getCurrentCompany()->id;

        if ($exists) {
            throw new UnprocessableEntityHttpException('Custom Field already exists.');
        }

        try {
            DB::beginTransaction();

            $customField = ItemCustomField::create([
                'company_id' => $companyId,
                'label_name' => trim($input['label_name']),
                'custom_field_type' => $input['custom_field_type'],
                'enable_for_all' => isset($input['enable_for_all']) ? $input['enable_for_all'] : false,
                'open_in_popup' => isset($input['open_in_popup']) ? $input['open_in_popup'] : false,
                'status' => true,
            ]);

            if (isset($input['enable_for_all']) && $input['enable_for_all'] == true) {
                $items = ItemMaster::pluck('id')->toArray();
                foreach ($items as $itemId) {
                    ItemCustomFieldSetting::firstOrCreate([
                        'custom_field_id' => $customField->id,
                        'item_id' => $itemId,
                    ]);
                }
            }

            if (isset($input['custom_field_type']) && $input['custom_field_type'] == ItemCustomField::CF_TYPE_DROPDOWN) {
                if (isset($input['options']) && count($input['options']) > 0) {
                    foreach ($input['options'] as $option) {
                        if (! isset($option['value']) || empty(trim($option['value']))) {
                            throw new UnprocessableEntityHttpException('Each option must have a valid value.');
                        }
                        ItemCustomFieldOption::create([
                            'custom_field_id' => $customField->id,
                            'option_label' => $option['value'],
                        ]);
                    }
                }
            }

            foreach ($input['types'] as $type) {
                ItemCustomFieldTransactionSetting::updateOrCreate(
                    [
                        'custom_field_id' => $customField->id,
                        'transaction_type' => $type['type'],
                    ],
                    [
                        'is_show_in_print' => $type['is_show_in_print'] ?? false,
                    ]
                );
            }

            if (isset($input['default_value']) && ! empty($input['default_value'])) {
                $dbKey = GetInputTypeWiseKey::run($customField->id);
                ItemCustomFieldDefaultValue::create([
                    'custom_field_id' => $customField->id,
                    'item_id' => null,
                    $dbKey => $input['default_value'],
                    'field_type' => isset($input['field_type']) ? $input['field_type'] : null,
                ]);
            }

            if (isset($input['default_formula']) && ! empty($input['default_formula'])) {
                // if used_cf_ids_for_formula is not sent from client, then get the numbers from formula
                preg_match_all('/\{\s*(\d+)\s*\}/', $input['default_formula'], $matches);
                $numbers = $matches[1];

                $idsArray = ! empty($input['used_cf_ids_for_formula']) ? explode(',', $input['used_cf_ids_for_formula']) : array_unique($numbers) ?? [];
                $idsArray = array_map('intval', $idsArray);

                ItemCustomFieldFormula::create([
                    'company_id' => $companyId,
                    'custom_field_id' => $customField->id,
                    'formula' => $input['default_formula'],
                    'item_id' => null,
                    'used_cf_ids_for_formula' => count($idsArray) > 0 ? array_unique($idsArray) : null,
                ]);
            }

            DB::commit();

            $customField->input_type = ItemCustomField::CUSTOM_FIELD_ARRAY_WITH_INPUT_TYPE[$customField->custom_field_type]['input_type'] ?? null;

            return $customField;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Line: '.$e->getLine().' Error:  '.$e->getMessage());
            throw $e;
        }
    }
}
