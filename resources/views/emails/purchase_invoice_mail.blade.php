<!doctype html>
<html xmlns="[http://www.w3.org/1999/xhtml" ](http://www.w3.org/1999/xhtml") xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head><title> Mail </title>
    <!--[if !mso]><!-->
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!--<![endif]-->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type="text/css">
        #outlook a {
            padding: 0;
        }
        body {
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
        }
        p {
            display: block;
            margin: 0px !important;
        }
        .main-card {
            border-radius: 10px;
            border: 1px solid lightgrey;
            border-top: 4px solid #441B7E;
        }
    </style>
    <!--[if mso]>
        <noscript>
            <xml>
                <o:OfficeDocumentSettings>
                    <o:AllowPNG/>
                    <o:PixelsPerInch>96</o:PixelsPerInch>
                </o:OfficeDocumentSettings>
            </xml>
        </noscript>
    <![endif]-->
    <!--[if lte mso 11]>
        <style type="text/css">
            .mj-outlook-group-fix {
                width: 100% !important;
            }
        </style>
    <![endif]-->
    <!--[if !mso]><!-->
    <link href="[https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700" ](https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700")
    rel="stylesheet" type="text/css">
    <style type="text/css">
        @import url([https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700);](https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700);)
    </style>
    <!--<![endif]-->
    <style type="text/css">
        @media only screen and (min-width: 480px) {
            .mj-column-per-100 {
                width: 100% !important;
                max-width: 100%;
            }
        }
    </style>
    <style media="screen and (min-width:480px)">
        .moz-text-html .mj-column-per-100 {
            width: 100% !important;
            max-width: 100%;
        }
    </style>
</head>
@php
    $pdfSymbol = getPrintPdfCurrencySymbol($companyDetails->id);
@endphp
<body style="word-spacing:normal;">
<div style>
    <!--[if mso | IE]>
        <table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:600px;" width="600" bgcolor="#EA8E5A">
            <tr>
                <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;">
    <![endif]-->
    <div style="background:#EA8E5A;background-color:#EA8E5A;margin:0px auto;max-width:600px;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="background:#EA8E5A;background-color:#EA8E5A;width:100%;">
            <tbody>
                <tr>
                    <td style="direction:ltr;font-size:0px;padding:20px 0;text-align:center;">
                        <!--[if mso | IE]>
                            <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                                <tr>
                                    <td class="" style="vertical-align:top;width:600px;">
                        <![endif]-->
                        <div class="mj-column-per-100 mj-outlook-group-fix" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                            <table border="0" cellpadding="0" cellspacing="0" role="presentation"
                                style="vertical-align:top;" width="100%">
                                <tbody>
                                    <tr>
                                        <td align="center" style="font-size:0px;padding:10px 25px;word-break:break-word;">
                                            <div style="font-family:Ubuntu, Helvetica, Arial, sans-serif;font-size:20px;font-style:Poppins;line-height:1;text-align:center;color:#ffffff;">
                                                {{ $taxInvoice }} # {{ $invoiceNumber }}
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--[if mso | IE]>
                                    </td>
                                </tr>
                            </table>
                        <![endif]-->
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <!--[if mso | IE]>
                </td>
            </tr>
        </table>
        <table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:600px;" width="600">
            <tr>
                <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;">
    <![endif]-->
    <div style="margin:0px auto;max-width:600px;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width:100%;">
            <tbody>
                <tr>
                    <td style="direction:ltr;font-size:0px;padding:20px 0;text-align:center;">
                        <!--[if mso | IE]>
                            <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                                <tr>
                                    <td class="" style="vertical-align:top;width:600px;">
                        <![endif]-->
                        <div class="mj-column-per-100 mj-outlook-group-fix" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                            <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="vertical-align:top;" width="100%">
                                <tbody>
                                    <tr>
                                        <td align="left" style="font-size:0px;padding:10px 25px;word-break:break-word;">
                                            <div style="font-family:Ubuntu, Helvetica, Arial, sans-serif;font-size:15px;font-style:Poppins;line-height:1;text-align:left;color:#000000;">
                                                {!! $body !!}
                                            </div>
                                        </td>
                                    </tr>
                                    {{-- <tr>
                                        <td align="left" style="font-size:0px;padding:10px 25px;word-break:break-word;">
                                            <div style="font-family:Ubuntu, Helvetica, Arial, sans-serif;font-size:14px;font-style:Poppins;line-height:18px;text-align:left;color:#6C7074;">
                                                Thank you for your business with <b>{{ $companyDetails->trade_name }}</b>. Your
                                                invoice is attached here with this mail. You can download it.
                                            </div>
                                        </td>
                                    </tr> --}}
                                </tbody>
                            </table>
                        </div>
                        <!--[if mso | IE]>
                                    </td>
                                </tr>
                            </table>
                        <![endif]-->
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <!--[if mso | IE]>
                </td>
            </tr>
        </table>
        <table align="center" border="0" cellpadding="0" cellspacing="0" class="main-card-outlook" role="presentation" style="width:600px;" width="600">
            <tr>
                <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;">
    <![endif]-->
    <div class="main-card" style="overflow: hidden; width: 86%; margin: 0px auto; border-radius: 10px; max-width: 600px;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width:100%;border-radius:10px;">
            <tbody>
                <tr>
                    <td style="border:1px solid lightgrey;border-top:4px solid #441B7E;;direction:ltr;font-size:0px;padding:20px 0;text-align:center;">
                        <!--[if mso | IE]>
                            <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                                <tr>
                                    <td class="" style="vertical-align:top;width:598px;">
                        <![endif]-->
                        <div class="mj-column-per-100 mj-outlook-group-fix" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                        <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="vertical-align:top;" width="100%">
                            <tbody>
                            <tr>
                                <td align="center" style="font-size:0px;padding:10px 25px;padding-top:15px;word-break:break-word;">
                                    <div style="font-family:Ubuntu, Helvetica, Arial, sans-serif;font-size:16px;font-style:Poppins;line-height:1;text-align:center;color:#6C7074;">
                                        Invoice Amount
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td align="center" style="font-size:0px;padding:10px 25px;word-break:break-word;">
                                    <div style="font-family:Ubuntu, Helvetica, Arial, sans-serif;font-size:20px;font-style:Poppins;font-weight:600;line-height:30px;text-align:center;color:#EA8E5A;">
                                        {{$pdfSymbol.''.getCurrencyFormat(! empty($grandAmount) ? $grandAmount : '0')}}
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td align="center" style="background:#F5F6F7;font-size:0px;padding:10px 25px;padding-top:20px;padding-bottom:50px;word-break:break-word;">
                                    <table cellpadding="0" cellspacing="0" width="250" border="0" style="color:#000000;font-family:Ubuntu, Helvetica, Arial, sans-serif;font-size:13px;line-height:22px;table-layout:auto;width:250px;border:none;">
                                        {{-- <tr>
                                            <td style="color:#6C7074;">Paid Amount</td>
                                            <td style="text-align:right"> {{$pdfSymbol.''.getCurrencyFormat(! empty($paidAmount) ? $paidAmount : '0')}}</td>
                                        </tr>
                                        <tr>
                                            <td style="color:#6C7074"> Pending Amount</td>
                                            <td style="text-align:right">{{$pdfSymbol.''.getCurrencyFormat(! empty($pendingAmount) ? $pendingAmount : '0')}}</td>
                                        </tr> --}}
                                        <tr>
                                            <td style="color:#6C7074"> Sale No</td>
                                            <td style="text-align:right"> {{$voucherNumber}}</td>
                                        </tr>
                                        <tr>
                                            <td style="color:#6C7074"> Invoice Date</td>
                                            <td style="text-align:right"> {{\Carbon\Carbon::parse($invoiceDate)->format('d-m-Y')}}</td>
                                        </tr>

                                    </table>
                                </td>
                            </tr>
                            @if(!empty($razorpayCredential) && $onlinePaymentLedger != null)
                                @if(!empty($pendingAmount) && $pendingAmount > 0 && $transactionType === \App\Models\SaleTransaction::class)
                                    <tr>
                                        <td align="center" vertical-align="middle" style="font-size:0px;word-break:break-word;">
                                            <table border="0" cellpadding="0" cellspacing="0" role="presentation"
                                                   style="border-collapse:separate;width:150px;   position: relative;  bottom: 20px; line-height:100%;">
                                                <tbody>
                                                <tr>
                                                    <td align="center" bgcolor="#441B7E" role="presentation"
                                                        style="border:none;border-radius:10px;cursor:auto;mso-padding-alt:10px 25px;background:#441B7E;"
                                                        valign="middle">
                                                        <a href="{{ route('sale-payment-show',['transactionId' => $transactionId,'transactionType' => $transactionType]) }}"
                                                           style="display:inline-block;width:100px;background:#441B7E;color:#ffffff;font-family:Ubuntu, Helvetica, Arial, sans-serif;font-size:13px;font-weight:normal;line-height:120%;margin:0;text-decoration:none;text-transform:none;padding:10px 15px;mso-padding-alt:0px;border-radius:10px;"
                                                           target="_blank">
                                                            PAY NOW
                                                        </a>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                @endif
                                @if(!empty($pendingAmount) && $pendingAmount > 0 && $transactionType === \App\Models\IncomeDebitNoteTransaction::class)
                                    <tr>
                                        <td align="center" vertical-align="middle" style="font-size:0px;word-break:break-word;">
                                            <table border="0" cellpadding="0" cellspacing="0" role="presentation"
                                                   style="border-collapse:separate;width:150px;   position: relative;  bottom: 20px; line-height:100%;">
                                                <tbody>
                                                <tr>
                                                    <td align="center" bgcolor="#441B7E" role="presentation"
                                                        style="border:none;border-radius:10px;cursor:auto;mso-padding-alt:10px 25px;background:#441B7E;"
                                                        valign="middle">
                                                        <a href="{{ route('income-debit-note-payment-show',['transactionId' => $transactionId]) }}"
                                                           style="display:inline-block;width:100px;background:#441B7E;color:#ffffff;font-family:Ubuntu, Helvetica, Arial, sans-serif;font-size:13px;font-weight:normal;line-height:120%;margin:0;text-decoration:none;text-transform:none;padding:10px 15px;mso-padding-alt:0px;border-radius:10px;"
                                                           target="_blank">
                                                            PAY NOW
                                                        </a>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                @endif
                            @endif
                            </tbody>
                        </table>
                        </div>
                        <!--[if mso | IE]>
                                    </td>
                                </tr>
                            </table>
                        <![endif]-->
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <!--[if mso | IE]>
                </td>
            </tr>
        </table>
        <table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:600px;" width="600">
            <tr>
                <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;">
    <![endif]-->
    <div style="margin:0px auto;max-width:600px;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width:100%;">
            <tbody>
                <tr>
                    <td style="direction:ltr;font-size:0px; padding: 20px 0 50px 0;text-align:center;">
                    <!--[if mso | IE]>
                        <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                            <tr>
                                <td class="" style="vertical-align:top;width:600px;">
                    <![endif]-->
                    <div class="mj-column-per-100 mj-outlook-group-fix" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                        <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="vertical-align:top;" width="100%">
                            <tbody>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td align="left" style="font-size:0px;padding:10px 25px;padding-bottom:0;word-break:break-word;">
                                        <div style="font-family:Ubuntu, Helvetica, Arial, sans-serif;font-size:16px;font-style:Poppins;line-height:1.3;text-align:left;color:#6C7074;">
                                            {!! nl2br($regards) !!}
                                        </div>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    <!--[if mso | IE]>
                                </td>
                            </tr>
                        </table>
                    <![endif]-->
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <!--[if mso | IE]>
                </td>
            </tr>
        </table>
    <![endif]-->
</div>
</body>
</html>
