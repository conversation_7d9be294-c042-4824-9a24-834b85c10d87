<div class="card card-border-1 mt-2">
    <div class="card-body position-relative pt-10 pb-6 px-lg-10 px-sm-8 px-6">
        <div class="position-absolute configuration-button d-flex align-items-center">
            <a href="{{ !empty($previousBillId) ? route('company.transaction-payment.edit', ['transaction_payment' => $previousBillId]) : '#' }}"
                class="{{ empty($previousBillId) ? ' disabled ' : '' }} btn previous-next-btn d-flex justify-content-center align-items-center"
                data-bs-toggle="tooltip" data-bs-placement="bottom" title="Previous">
                <i class="fa-solid fa-angle-left"></i>
            </a>
            <a href="{{ !empty($nextBillId) ? route('company.transaction-payment.edit', ['transaction_payment' => $nextBillId]) : route('company.transaction-payment.create') }}"
                class="btn previous-next-btn d-flex justify-content-center align-items-center mx-2"
                data-bs-toggle="tooltip" data-bs-placement="bottom"
                @if (!empty($nextBillId)) title="Next" @else title="Add" @endif>
                <i class="fa-solid fa-angle-right"></i>
            </a>
            <a href="javascript:void (0)"
                class="configuration-setting-btn payment-transaction-configuration-modal d-flex justify-content-center align-items-center ms-8"
                data-bs-toggle="tooltip" data-bs-placement="bottom" title="Configuration, Shortcut Key : Alt + F12">
                <i class="fa-solid fa-gear"></i>
            </a>
        </div>
        <div class="row">
            @php
                use App\Models\Master\Customer;
                use App\Models\Master\Supplier;
                $receiptNumberIsDisabled = false;
                if ($methodVoucherType == \App\Models\Master\PaymentTransaction::AUTOMATIC) {
                    $receiptNumberIsDisabled = true;
                }
            @endphp
            <div class="col-lg-3 col-md-5 col-sm-6 col-12 mb-3">
                {{ Form::label('receiptNumber', 'Payment Voucher Number:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 required mb-0']) }}
                {{ Form::text('payment_voucher_number', $payment->payment_voucher_number ?? null, [
                    'class' => 'form-control',
                    'id' => 'receiptNumber',
                    $receiptNumberIsDisabled ? 'readonly' : '',
                    'maxLength' => 50,
                ]) }}
            </div>
            <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
                {{ Form::label('date', 'Date:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 required mb-0']) }}
                <input type="text" class="form-control date-of-receipt-transaction"
                    value="{{ !empty($payment->date) ? \Carbon\Carbon::parse($payment->date)->format('d-m-Y') : null }}"
                    name="date">
            </div>
            <div class="col-lg-3 col-md-5 col-sm-6 col-12 mb-3">
                {{ Form::label('bankCashLedger', 'Bank/Cash:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 required mb-0']) }}
                <div class="input-group flex-nowrap custom-input-group">
                    {{ Form::select('bank_cash_ledger_id', getBandAndCashLedgers() ?? [], $payment->bank_cash_ledger_id ?? null, ['id' => 'bankCashLedger', 'class' => 'form-select payment-change-bank-and-cash-ledger  bank-cash-ledger-name custom-select2-width', 'data-control' => 'select2', 'required', 'placeholder' => 'Select Bank/Cash Ledger']) }}
                    <a href="javascript:void(0)"
                        class="input-group-text add-ledger-bank-cash-model custom-group-text right-0" data-link-id='1'
                        data-bs-toggle="tooltip" data-bs-placement="bottom">
                        <i class="fas fa-plus text-gray-900"></i>
                    </a>
                </div>
                {{-- {{ Form::select('bank_cash_ledger_id',getBandAndCashLedgers() ?? [], $payment->bank_cash_ledger_id ?? null, ['id'=>'bankCashLedger','class' => 'form-select ledger-name payment-change-bank-and-cash-ledger',  'data-control' => 'select2', 'required','placeholder'=>'Select Ledger']) }} --}}
                <span>Current Balance: <span class="bank-and-cash-ledger-balance"></span></span>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
                {{ Form::label('ledgerId', 'Ledger name:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 required mb-0']) }}
                <div class="input-group flex-nowrap custom-input-group">
                    {{ Form::select('ledger_id', getCompanyLedger() ?? [], $payment->ledger_id ?? null, [
                        'id' => 'ledgerId',
                        'class' => 'form-select change-ledger-name income-ledger-name custom-select2-width',
                        'data-control' => 'select2',
                        'required',
                        'placeholder' => 'Select Ledger',
                    ]) }}
                    <a href="javascript:void(0)"
                        class="input-group-text add-ledger-transaction-model custom-group-text right-0" data-link-id='1'
                        data-bs-toggle="tooltip" data-bs-placement="bottom" title="Shortcut Key : Alt + L">
                        <i class="fas fa-plus text-gray-900"></i>
                    </a>
                </div>
                <span>Current Balance: <span class="ledger-opening-balance"></span></span>
            </div>
            <div class="col-lg-3 col-md-5 col-sm-6 col-12 mb-3">
                {{ Form::label('amount', 'Amount:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 required mb-0']) }}
                {{ Form::number('total_paid_amount', $payment->total_paid_amount ?? null, [
                    'class' => 'form-control
                                get-total-received-amount received-amount-total',
                    'placeholder' => 'Amount',
                    'required',
                    'step' => '.01',
                ]) }}
            </div>
            <div
                class="col-lg-3 col-md-5 col-sm-6 col-12 d-flex payment-manage-transaction-toggle-button {{ $payment->ledgers->model_type == Customer::class || $payment->ledgers->model_type == Supplier::class ? '' : 'd-none' }}">
                <div class="form-check form-switch form-check-custom form-switch-sm mb-3">
                    {{ Form::label('is_manage_transaction', 'Settle invoices? :', [
                        'class' => 'form-check-label fs-6 fw-bolder
                                        text-gray-900 ms-0 me-2',
                    ]) }}
                    {{ Form::checkbox('is_manage_transaction', 1, $payment->is_manage_transaction == 1 ? 'checked' : '', [
                        'class' => 'form-check-input is-payment-manage-transaction',
                        'id' => 'isPaymentManageTransaction',
                        'data-ledger-id' => $payment->ledger_id ?? null,
                    ]) }}
                </div>
            </div>
        </div>
        <div class="row">

            <div
                class="col-lg-3 col-md-5 col-sm-6 col-12 mb-3 enable-payment-mode {{ $paymentTransactionConfiguration?->enable_payment_mode ? '' : 'd-none' }}">
                {{ Form::label('payment_mode', 'Payment Mode:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 mb-0']) }}
                <div class="input-group flex-nowrap custom-input-group">
                    {{ Form::select('payment_mode', getPaymentModeForPayment() ?? [], $payment->payment_mode ?? null, ['id' => 'paymentModeList', 'class' => 'form-select payment-mode-name custom-select2-width', 'data-control' => 'select2', 'placeholder' => 'Select Ledger']) }}
                    <a href="javascript:void(0)"
                        class="input-group-text payment-mode-add-for-receipt custom-group-text right-0" data-link-id='1'
                        data-bs-toggle="tooltip" data-bs-placement="bottom">
                        <i class="fas fa-plus text-gray-900"></i>
                    </a>
                </div>
            </div>
            <div
                class="col-lg-3 col-md-5 col-sm-6 col-12 mb-3 enable-reference-number {{ $paymentTransactionConfiguration?->enable_reference_number ? '' : 'd-none' }}">
                {{ Form::label('reference_number', 'Reference Number:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 mb-0']) }}
                {{ Form::text('reference_number', $payment->reference_number ?? null, ['class' => 'form-control', 'placeholder' => 'Reference Number']) }}
            </div>
            <div class="d-flex col-6 align-items-end d-none print-cheque-div">
                <div class="col-sm-6 col-12 mb-3 ">
                    {{ Form::label('cheque_date', 'Cheque Date:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 mb-0']) }}
                    <input type="text" style="" class="form-control cheque-date date-of-cheque-transaction" value="{{ \Carbon\Carbon::parse(getDefaultDate())->format('d-m-Y') }}" name="cheque-date">
                </div>
                <div class="col-sm-6 col-12 mb-3 ms-2">
                    {{ Form::button('Print Cheque',['class' => 'btn btn-primary open-cheque-modal','style' => 'padding: 6px 10px;']) }}
                </div>
            </div>
        </div>
    </div>

</div>
<div class="card my-7 append-receipt-transaction-screen">
    @if ($payment->is_manage_transaction == 1)
        {{-- @if (count($payment->paymentTransactionItem) > 0) --}}
            <div class="card-body items-details-card py-6 px-lg-10 px-sm-8 px-6">
                <div class="row justify-content-between mb-2">
                    <div class="col-sm-12 col-12">
                        <h5 class="title-name">Unpaid Invoice</h5>
                    </div>
                </div>
                <div class="row">
                    <table class="ms-2 table table-bordered">
                        <tr>
                            <th class="fw-bold">Invoice Number</th>
                            <th class="fw-bold" style="width: 90px !important;">Invoice Date</th>
                            <th class="fw-bold">Transaction Type</th>
                            <th class="fw-bold">Total Amount</th>
                            <th class="fw-bold">Paid Amount</th>
                            <th class="fw-bold">Pending Amount</th>
                            <th class="fw-bold">Paid Amount</th>
                            <th class="fw-bold">Discount Amount</th>
                            <th class="fw-bold">Rounding Amount</th>
                        </tr>
                        @each('company.transaction.payment-transaction.rc_transaction_item', $payment->paymentTransactionItem, 'paymentTransactionItem')
                        <tr class="border-top mt-5">
                            <td colspan="3" class="fw-bolder">Total Amount</td>

                            @if (
                                !empty($payment->paymentTransactionItem[0]->purchase_id) ||
                                    !empty($payment->paymentTransactionItem[0]->expense_credit_id) ||
                                    !empty($payment->paymentTransactionItem[0]->receipt_transaction_id) ||
                                    !empty($payment->paymentTransactionItem[0]->opening_balance_invoice_id))
                                @php
                                    /** @var  \App\Models\PaymentTransaction $payment */
                                    $grandTotal =
                                        $payment->paymentTransactionItem->sum('purchaseBill.grand_total') +
                                        $payment->paymentTransactionItem->sum('expenseCreditBill.grand_total') +
                                        $payment->paymentTransactionItem->sum(
                                            'paymentTransactionBill.total_received_amount',
                                        ) +
                                        $payment->paymentTransactionItem->sum('openingBalanceInvoice.total_amount');

                                    $discountAmount = $payment->paymentTransactionItem->sum('discount');

                                    $otherTransactions = \App\Models\PaymentTransactionItem::where(
                                        'pc_transaction_id',
                                        '!=',
                                        $payment->id,
                                    );

                                    $saleTransactionDiscount = $otherTransactions
                                        ->whereIn('purchase_id', $payment->paymentTransactionItem->pluck('purchase_id'))
                                        ->sum('discount');

                                    $incomeDebitTransactionDiscount = $otherTransactions
                                        ->whereIn(
                                            'expense_credit_id',
                                            $payment->paymentTransactionItem->pluck('expense_credit_id'),
                                        )
                                        ->sum('discount');

                                    $paymentTransactionDiscount = $otherTransactions
                                        ->whereIn(
                                            'receipt_transaction_id',
                                            $payment->paymentTransactionItem->pluck('receipt_transaction_id'),
                                        )
                                        ->sum('discount');

                                    $openingBalanceInvoiceDiscount = $otherTransactions
                                        ->whereIn(
                                            'opening_balance_invoice_id',
                                            $payment->paymentTransactionItem->pluck('opening_balance_invoice_id')
                                        )
                                        ->sum('discount');
                                    $otherDiscountAmount =
                                        $saleTransactionDiscount +
                                        $incomeDebitTransactionDiscount +
                                        $paymentTransactionDiscount +
                                        $openingBalanceInvoiceDiscount;

                                    $transactionTotal = 0;

                                    foreach ($purchaseTransactions as $purchaseTransaction) {
                                        /** @var \App\Models\PurchaseTransaction $purchaseTransaction */
                                        $transactionTotal += $purchaseTransaction->paymentTransactionItem->sum(
                                            'paid_amount',
                                        );
                                    }

                                    foreach ($expenseCreditTransactions as $expenseCreditTransaction) {
                                        /** @var \App\Models\ExpenseCreditNoteTransaction $expenseCreditTransaction */
                                        $transactionTotal += $expenseCreditTransaction->paymentTransactionItem->sum(
                                            'paid_amount',
                                        );
                                    }

                                    foreach ($receiptTransactions as $receiptTransaction) {
                                        /** @var \App\Models\ReceiptTransaction $receiptTransaction  */
                                        $transactionTotal += $receiptTransaction->receiptTransactionItem?->sum(
                                            'received_amount',
                                        );
                                    }

                                    foreach ($openingBalance as $openingInvoice) {
                                        /** @var \App\Models\BillWiseOpeningBalance $openingInvoice */
                                        $transactionTotal += ($openingInvoice->total_paid_amount - $openingInvoice->paid_amount);
                                    }

                                    $paidAmount =
                                        $transactionTotal -
                                        $payment->paymentTransactionItem->sum('paid_amount') +
                                        $otherDiscountAmount;
                                    $roundOffAmount = $payment->paymentTransactionItem->sum('round_off');
                                    $receivedAmount = $payment->paymentTransactionItem->sum('paid_amount');
                                    $pendingAmount = $grandTotal - $paidAmount;

                                @endphp
                            @elseif(
                                !empty($payment->paymentTransactionItem[0]->sale_return_id) ||
                                    !empty($payment->paymentTransactionItem[0]->income_credit_note_id) ||
                                    !empty($payment->paymentTransactionItem[0]->receipt_transaction_id))
                                @php

                                    $grandTotal =
                                        $payment->paymentTransactionItem->sum('saleReturnBill.grand_total') +
                                        $payment->paymentTransactionItem->sum('incomeCreditBill.grand_total') +
                                        $payment->paymentTransactionItem->sum(
                                            'receiptTransactionBill.total_received_amount',
                                        );

                                    $discountAmount = $payment->paymentTransactionItem->sum('discount');

                                    $otherTransactions = \App\Models\paymentTransactionItem::where(
                                        'pc_transaction_id',
                                        '!=',
                                        $payment->id,
                                    );

                                    $purchaseTransactionDiscount = $otherTransactions
                                        ->whereIn(
                                            'sale_return_id',
                                            $payment->paymentTransactionItem->pluck('sale_return_id'),
                                        )
                                        ->sum('discount');

                                    $expenseCreditTransactionDiscount = $otherTransactions
                                        ->whereIn(
                                            'income_credit_note_id',
                                            $payment->paymentTransactionItem->pluck('income_credit_note_id'),
                                        )
                                        ->sum('discount');

                                    $paymentTransactionDiscount = $otherTransactions
                                        ->whereIn(
                                            'receipt_transaction_id',
                                            $payment->paymentTransactionItem->pluck('receipt_transaction_id'),
                                        )
                                        ->sum('discount');

                                    $otherDiscountAmount =
                                        $purchaseTransactionDiscount +
                                        $expenseCreditTransactionDiscount +
                                        $paymentTransactionDiscount;
                                    $transactionTotal = 0;
                                    foreach ($incomeCreditTransactions as $incomeCreditTransaction) {
                                        /** @var \App\Models\IncomeCreditNoteTransaction $incomeCreditTransaction */
                                        $transactionTotal += $incomeCreditTransaction->paymentTransactionItem->sum(
                                            'paid_amount',
                                        );
                                    }

                                    foreach ($saleReturnTransactions as $saleReturnTransaction) {
                                        /** @var \App\Models\SaleReturnTransaction $saleReturnTransaction */
                                        $transactionTotal += $saleReturnTransaction->paymentTransactionItem->sum(
                                            'paid_amount',
                                        );
                                    }

                                    foreach ($receiptTransactions as $receiptTransaction) {
                                        /** @var \App\Models\ReceiptTransaction $receiptTransaction */
                                        $transactionTotal += $receiptTransaction->paymentTransactionItem->sum(
                                            'paid_amount',
                                        );
                                    }

                                    $paidAmount =
                                        $transactionTotal -
                                        $payment->paymentTransactionItem->sum('paid_amount') +
                                        $otherDiscountAmount;
                                    $roundOffAmount = $payment->paymentTransactionItem->sum('round_off');
                                    $receivedAmount = $payment->paymentTransactionItem->sum('paid_amount');
                                    $pendingAmount = $grandTotal - $paidAmount;
                                @endphp
                            @endif
                            <td class="fw-bolder">{{ getCurrencySymbol() . getCurrencyFormat($grandTotal ?? 0.00) }}</td>
                            <td class="fw-bolder">{{ getCurrencySymbol() . getCurrencyFormat($paidAmount ?? 0.00) }}</td>
                            <td class="fw-bolder">{{ getCurrencySymbol() . getCurrencyFormat($pendingAmount ?? 0.00) }}</td>
                            <td class="fw-bolder total-received-amount">{{ getCurrencySymbol() . getCurrencyFormat($receivedAmount ?? 0.00) }}</td>
                            <td class="fw-bolder total-discount-amount">{{ getCurrencySymbol() . getCurrencyFormat($discountAmount ?? 0.00) }}</td>
                            <td class="fw-bolder total-rounding-amount">{{ getCurrencySymbol() . getCurrencyFormat($roundOffAmount ?? 0.00) }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        {{-- @endif --}}
    @endif
</div>
{{-- Narratio & Term-Conditions --}}
<div class="card  card-border-1">
    <div class="card-body py-6 px-lg-10 px-sm-8 px-6">
        <div class="row">
            <div
                class="col-sm-6 mb-3 enable-narration  {{ $paymentTransactionConfiguration?->is_enable_narration ? '' : 'd-none' }}">
                {{ Form::label('narration', 'Narration:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 mb-0']) }}
                {{ Form::textarea('narration', $payment->narration ?? null, ['class' => 'form-control check-limit-textarea', 'maxLength' => 250, 'rows' => 3, 'cols' => 2]) }}
            </div>
            <div class="col-sm-6 mb-3">
                {{ Form::label('upload_payment_invoice', 'Upload Document:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 mb-0']) }}
                {{ Form::file('upload_payment_invoice[]', ['class' => 'form-control file-upload-validate', 'multiple' => true, 'data-bs-toggle' => 'tooltip', 'data-bs-placement' => 'bottom', 'title' => 'Maximum file size is 2 MB.']) }}
                @if (isset($payment->media))
                    @foreach ($payment->media as $key => $attachment)
                        <div
                            class="symbol symbol-50px symbol-2by3 my-5 mx-2 remove-document-image-div-{{ $attachment->id }} image-show__attachment-container">
                            <a href="javascript:void(0)" data-id="{{ $attachment->id }}"
                                class="symbol-badge badge badge-circle  start-100 delete-document-images"><i
                                    class="bi bi-x fs-2 text-gray-900"></i></a>
                            <a class="{{ isImage($attachment) ? 'attachment-lightbox' : '' }}"
                                href="{{ isImage($attachment) ? returnAttachmentLink($attachment) : $attachment->getFullUrl() }}"
                                target="{{ isImage($attachment) ? '_self' : '_blank' }}" data-effect="mfp-3d-unfold">
                                <img src="{{ returnAttachmentLink($attachment) }}" alt="Profile Image"
                                    class="object-fit-cover" width="40" height="56">
                            </a>
                        </div>
                    @endforeach
                @endif
            </div>

        </div>
        <div class="d-flex flex-wrap gap-2 mt-5">
            {{ Form::hidden('submit_button_value', null, ['class' => 'transaction-payment-btn-value']) }}
            <button type="submit" name="submit_button" class="btn btn-primary" value=""
                data-bs-toggle="tooltip" data-bs-placement="bottom" title="Shortcut Key : Alt + S">Save
            </button>
            <button type="submit" name="submit_button" class="btn btn-primary transaction-payment-btn"
                value="{{ \App\Models\PaymentTransaction::SAVE_AND_PRINT_BUTTON }}">Save & Print</button>
            <a href="{{ route('company.transaction-payment.index') }}"
                class="btn btn-light btn-active-light-primary payment-transaction-back-btn">Back</a>
            <a href="javascript:void(0)" data-id="{{ $payment->id }}" data-redirectIndex="true"
                class="btn btn-danger payment-delete-btn">Delete</a>
        </div>
    </div>
</div>
