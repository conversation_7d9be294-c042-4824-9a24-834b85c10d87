<div class="card card-border-1 mt-2">
    <div class="card-body position-relative pt-10 pb-6 px-lg-10 px-sm-8 px-6">
        <div class="position-absolute configuration-button d-flex align-items-center">
            <a href="{{ !empty($previousBillId) ? route('company.transaction-journal.edit', ['transaction_journal' => $previousBillId ]) : '#' }}" class="{{ empty($previousBillId) ? ' disabled ' : '' }} btn previous-next-btn d-flex justify-content-center align-items-center" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Previous">
                <i class="fa-solid fa-angle-left"></i>
            </a>
            <a href="{{ !empty($nextBillId) ? route('company.transaction-journal.edit', ['transaction_journal' => $nextBillId ]) : route('company.transaction-journal.create') }}" class="btn previous-next-btn d-flex justify-content-center align-items-center mx-2" data-bs-toggle="tooltip" data-bs-placement="bottom" @if(!empty($nextBillId)) title="Next" @else title="Add" @endif>
                <i class="fa-solid fa-angle-right"></i>
            </a>
            <a href="javascript:void (0)"
                class="configuration-setting-btn journal-transaction-configuration-modal d-flex justify-content-center align-items-center ms-8"
                data-bs-toggle="tooltip" data-bs-placement="bottom" title="Configuration, Shortcut Key : Alt + F12">
                <i class="fa-solid fa-gear"></i>
            </a>
        </div>
        <div class="row">
            <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
                {{ Form::label('date', 'Date:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 required mb-0']) }}
                <input type="text" class="form-control date-of-journal-transaction" name="date"
                    value="{{ !empty($journal->date) ? \Carbon\Carbon::parse($journal->date)->format('d-m-YY') : null }}">
            </div>
            <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
                {{ Form::label('voucherNumber', 'Voucher Number:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 required mb-0']) }}
                {{ Form::text('voucher_number', $journal->voucher_number ?? null, ['class' => 'form-control', 'required']) }}
            </div>
        </div>
        <div class="row mt-5">
            <div class="col-6 col-md-6 col-sm-12 col-12">
                <div class="row justify-content-between mb-2">
                    <div class="col-sm-6 col-12">
                        <h5 class="title-name">Debit Details</h5>
                    </div>
                </div>
                @php
                    $journalCreditTransactions = $journal->journalDebitCredits->where('debit_credit_type', \App\Models\JournalTransaction::CREDIT);

                    $journalDebitTransactions = $journal->journalDebitCredits->where('debit_credit_type', \App\Models\JournalTransaction::DEBIT);
                @endphp
                <div class="debit-transaction-container">
                    <div class="row">
                        <div class="col-lg-7 col-md-7 col-12">
                            {{ Form::label('journalDebitLedgerId', 'Ledger:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 required mb-0']) }}
                        </div>
                        <div class="col-lg-4 col-md-4 col-12">
                            {{ Form::label('debitAmount', 'Amount:', ['class' => 'form-label fs-6 fw-bolder required text-gray-900 mb-0']) }}
                        </div>
                    </div>
                    @foreach ($journalDebitTransactions as $key => $debit)
                        @php
                            $uniqueId = ++$key;
                        @endphp
                        <div class="row debit-details-{{ $debit->ledger_id }} mt-2">
                            {{ form::hidden('debit_credit_type[]', $debit->debit_credit_type ?? \App\Models\JournalTransaction::DEBIT) }}
                            {{ Form::hidden('debit_credit_type_id[]', $debit->id) }}
                            <div class="col-lg-7 col-md-7 col-12">
                                <div class="input-group flex-nowrap">
                                    {{ Form::select('ledger_id[]', getCompanyJVLedger(), $debit->ledger_id ?? null, ['class' => 'form-select change-debit-ledger-name custom-select2-width', 'debit-ledger-id' => $debit->ledger_id, 'data-control' => 'select2', 'required', 'placeholder' => 'Select Ledger']) }}
                                    <a href="javascript:void(0)"
                                        class="input-group-text add-ledger-transaction-model custom-group-text"
                                        journal-debit-link-id='{{ $uniqueId }}' data-bs-toggle="tooltip"
                                        data-bs-placement="bottom" title="Shortcut Key : Alt + L">
                                        <i class="fas fa-plus text-gray-900"></i>
                                    </a>
                                </div>
                                <span>
                                    Current Balance: <span class="ledger-opening-balance"
                                        debit-ledger-current-balance="{{ $debit->ledger_id }}">0</span>
                                </span>
                            </div>
                            <div class="col-lg-4 col-md-4 col-12">
                                <div class="input-group flex-nowrap">
                                    {{ Form::number('amount[]', $debit->amount ?? null, ['class' => 'form-control journal-debit-amount', 'min' => 0, 'step' => $companyFixedDigitNumber, 'placeholder' => 'Amount', 'required', 'data-journal-debit-amount' => $debit->ledger_id]) }}
                                </div>
                            </div>
                            @if (!$loop->first)
                                <div class="col-1 mt-2 justify-content-end d-flex">
                                    <a href="javascript:void(0)" class="remove-journal-debit"
                                        data-id="{{ $debit->ledger_id }}">
                                        <i class="fas fs-2 fa-trash-alt text-danger ms-3"></i>
                                    </a>
                                </div>
                            @endif
                        </div>
                    @endforeach
                    {{ Form::hidden('append_debit_id', $uniqueId, ['class' => 'update-append-debit-unique-id']) }}
                </div>
                <button type="button" class="btn-sm btn-icon btn-icon-primary mt-2 add-item-btn" id="addDebitRow">
                    Debit
                </button>
            </div>
            <div class="col-6 col-md-6 col-sm-12 col-12">
                <div class="row justify-content-between mb-2">
                    <div class="col-sm-6 col-12">
                        <h5 class="title-name">Credit Details</h5>
                    </div>
                </div>
                <div class="credit-transaction-container">
                    <div class="row ">
                        {{ form::hidden('debit_credit_type[]', \App\Models\JournalTransaction::CREDIT) }}
                        <div class="col-lg-7 col-md-7  col-12">
                            {{ Form::label('journalCreditLedgerId', 'Ledger:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 required mb-0']) }}
                        </div>
                        <div class="col-lg-4 col-md-4 col-12">
                            {{ Form::label('credit_amount', 'Amount:', ['class' => 'form-label fs-6 fw-bolder required  text-gray-900 mb-0']) }}
                        </div>
                    </div>
                    @php
                        $checkFirstRecord = false;
                    @endphp
                    @foreach ($journalCreditTransactions as $key => $credit)
                        @php
                            $uniqueId = ++$key;
                        @endphp
                        <div class="row credit-details-{{ $credit->ledger_id }} mt-2">
                            {{ form::hidden('debit_credit_type[]', $credit->debit_credit_type ?? \App\Models\JournalTransaction::DEBIT) }}
                            {{ Form::hidden('debit_credit_type_id[]', $credit->id) }}
                            <div class="col-lg-7 col-md-7 col-12">
                                <div class="input-group flex-nowrap">
                                    {{ Form::select('ledger_id[]', getCompanyJVLedger(), $credit->ledger_id ?? null, ['class' => 'form-select change-credit-ledger-name custom-select2-width', 'credit-ledger-id' => $credit->ledger_id, 'data-control' => 'select2', 'required', 'placeholder' => 'Select Ledger']) }}
                                    <a href="javascript:void(0)"
                                        class="input-group-text add-ledger-transaction-model custom-group-text"
                                        journal-credit-link-id='{{ $credit->ledger_id }}' data-bs-toggle="tooltip"
                                        data-bs-placement="bottom" title="Shortcut Key : Alt + L">
                                        <i class="fas fa-plus text-gray-900"></i>
                                    </a>
                                </div>
                                <span>
                                    Current Balance: <span class="ledger-opening-balance"
                                        credit-ledger-current-balance="{{ $credit->ledger_id }}">0</span>
                                </span>
                            </div>
                            <div class="col-lg-4 col-md-4 col-12">
                                <div class="input-group flex-nowrap">
                                    {{ Form::number('amount[]', $credit->amount ?? null, ['class' => 'form-control journal-credit-amount', 'min' => 0, 'step' => $companyFixedDigitNumber, 'placeholder' => 'Amount', 'required', 'data-journal-credit-amount' => $credit->ledger_id]) }}
                                </div>
                            </div>
                            @if ($checkFirstRecord)
                                <div class="col-1 mt-2 justify-content-end d-flex">
                                    <a href="javascript:void(0)" class="remove-journal-credit"
                                        data-id="{{ $credit->ledger_id }}">
                                        <i class="fas fs-2 fa-trash-alt text-danger ms-3"></i>
                                    </a>
                                </div>
                            @endif
                            @php
                                $checkFirstRecord = true;
                            @endphp
                        </div>
                    @endforeach
                    {{ Form::hidden('append_credit_id', $uniqueId, ['class' => 'update-append-credit-unique-id']) }}
                </div>
                <button type="button" class="btn-sm btn-icon btn-icon-primary mt-2 add-item-btn" id="addCreditRow">
                    Credit
                </button>
            </div>
            <div class="col-6 col-md-6 mt-3">
                <div class="row">
                    <div class="col-lg-7 col-md-7 col-12 mt-2">
                        {{ Form::label('total', 'Total:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 mb-0']) }}
                    </div>
                    <div class="col-lg-4 col-md-4 col-12 mt-2">
                        <b>
                            <span class="debit-currency-symbol">{{ getCurrencySymbol() }}</span>
                            <span
                                class="journal-debit-total-amount">{{ getCurrencyFormat($journal->debit_total ?? 0.0) }}</span>
                        </b>
                        {{ Form::hidden('debit_total', $journal->debit_total ?? 0, ['class' => 'form-control hidden-debit-total-amount']) }}
                    </div>
                </div>
            </div>
            <div class="col-6 col-md-6 mt-3">
                <div class="row">
                    <div class="col-lg-7 col-md-7 col-12 mt-2">
                        {{ Form::label('total', 'Total:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 mb-0']) }}
                    </div>
                    <div class="col-lg-4 col-md-4 col-12 mt-2">
                        <b>
                            <span class="credit-currency-symbol">{{ getCurrencySymbol() }}</span>
                            <span
                                class="journal-credit-total-amount">{{ getCurrencyFormat($journal->credit_total ?? 0.0) }}</span>
                        </b>
                        {{ Form::hidden('credit_total', $journal->credit_total ?? 0, ['class' => 'form-control hidden-credit-total-amount']) }}
                    </div>
                </div>
            </div>
            @php
                $creditLedgerModelType = $journalCreditTransactions->pluck('ledger.model_type')->toArray();
                $debitLedgerModelType = $journalDebitTransactions->pluck('ledger.model_type')->toArray();
                $ledgersIds = array_merge($journalCreditTransactions->pluck('ledger_id')->toArray(),
                $journalDebitTransactions->pluck('ledger_id')->toArray());
                $allLedgerData = \App\Models\Ledger::whereIn('id', $ledgersIds)->get()->keyBy('id');

            @endphp
            @if (in_array(\App\Models\Master\Customer::class, $creditLedgerModelType) ||
                    in_array(\App\Models\Master\Supplier::class, $creditLedgerModelType))
                <div class="col-12 mt-4 credit-append-title">
                    <h4 class="title-name mb-0 text-decoration-underline">Credit Details Unpaid Invoice</h4>
                </div>
            @endif
            <div class="col-12 append-credit-transaction">
                @php
                    $creditLedgerIds = [];
                @endphp
                @foreach ($journalCreditTransactions as $key => $transactions)

                    @php
                        $creditSectionShow = false;
                        if (!in_array($transactions->ledger_id, $creditLedgerIds)) {
                            $creditLedgerIds[] = $transactions->ledger_id;
                            $creditSectionShow = true;
                        }
                    @endphp
                    @if (
                        $creditSectionShow &&
                            in_array($transactions->ledger->model_type, [
                                \App\Models\Master\Customer::class,
                                \App\Models\Master\Supplier::class,
                            ]))
                        <div class="col-12 customer-credit-details-{{ $transactions->ledger_id }}">
                            @php
                                $ledger = $allLedgerData[$transactions->ledger_id];
                                $transactions = $customerCreditTransactions[$transactions->ledger_id] ?? [];
                                $transactionsAppend = count($transactions) ?? 0;
                            @endphp
                            @include('company.transaction.journal-transaction.edit.credit-transactions', [
                                'transactions' => $transactions,
                                'ledger' => $ledger,
                                'isManageTransaction' => $transactionsAppend,
                            ])
                        </div>
                    @endif
                @endforeach
            </div>
            @if (in_array(\App\Models\Master\Customer::class, $debitLedgerModelType) ||
                    in_array(\App\Models\Master\Supplier::class, $debitLedgerModelType))
                <div class="col-12 mt-4 debit-append-title">
                    <h4 class="title-name mb-0 text-decoration-underline">Debit Details Unpaid Invoice</h4>
                </div>
            @endif
            <div class="col-12 append-debit-transaction">
                @php
                    $debitLedgersIds = [];
                @endphp
                @foreach ($journalDebitTransactions as $key => $transactions)
                    @php
                        $debitSectionShow = false;
                        if (!in_array($transactions->ledger_id, $debitLedgersIds)) {
                            $debitLedgersIds[] = $transactions->ledger_id;
                            $debitSectionShow = true;
                        }
                    @endphp
                    @if (
                        $debitSectionShow &&
                            in_array($transactions->ledger->model_type, [
                                \App\Models\Master\Customer::class,
                                \App\Models\Master\Supplier::class,
                            ]))
                        <div class="col-12 supplier-debit-details-{{ $transactions->ledger_id }}">
                            @php
                                $ledger = $allLedgerData[$transactions->ledger_id];
                                $transaction = $supplierDebitTransactions[$transactions->ledger_id] ?? [];
                                $transactionsAppend = count($transaction) ?? 0;
                            @endphp
                            @include('company.transaction.journal-transaction.edit.debit-transactions', [
                                'transactions' => $transaction,
                                'ledger' => $ledger,
                                'isManageTransaction' => $transactionsAppend,
                            ])
                        </div>
                    @endif
                @endforeach
            </div>
        </div>
        <div class="row mt-5">
            <div
                class="col-md-6 mb-3 enable-narration {{ $journalTransactionConfiguration->is_enable_narration ? '' : 'd-none' }}">
                {{ Form::label('narration', 'Narration:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 mb-0']) }}
                {{ Form::textarea('narration', $journal->narration ?? null, ['class' => 'form-control check-limit-textarea', 'maxLength' => 250, 'rows' => 3, 'cols' => 2]) }}
            </div>
            <div class="col-sm-6 mb-3">
                {{ Form::label('upload_journal_invoice', 'Upload Document:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 mb-0']) }}
                {{ Form::file('upload_journal_invoice[]',['class'=>'form-control file-upload-validate', 'multiple' => true, 'data-bs-toggle' => 'tooltip','data-bs-placement' => 'bottom','title' => 'Maximum file size is 2 MB.']) }}
                @if(isset($journal->media))
                    @foreach($journal->media as $key => $attachment)
                        <div class="symbol symbol-50px symbol-2by3 my-5 mx-2 remove-document-image-div-{{$attachment->id}} image-show__attachment-container">
                            <a href="javascript:void(0)" data-id="{{$attachment->id}}"
                               class="symbol-badge badge badge-circle  start-100 delete-document-images"><i
                                        class="bi bi-x fs-2 text-gray-900"></i></a>
                            <a class="{{ isImage($attachment) ? 'attachment-lightbox' : '' }}"
                               href="{{ isImage($attachment) ? returnAttachmentLink($attachment) : $attachment->getFullUrl() }}"
                               target="{{ isImage($attachment) ? '_self' : '_blank' }}"
                               data-effect="mfp-3d-unfold">
                                <img src="{{ returnAttachmentLink($attachment) }}"
                                     alt="Profile Image"
                                     class="object-fit-cover"
                                     width="40" height="56">
                            </a>
                        </div>
                    @endforeach
                @endif
            </div>
        </div>
        <div class="d-flex flex-wrap gap-2 mt-5">
            {{ Form::hidden('submit_button_value', null, ['class' => 'journal-btn-value']) }}
            <button type="submit" name="submit_button" class="btn btn-primary"
                value="{{ \App\Models\JournalTransaction::SAVE_BUTTON }}" data-bs-toggle="tooltip"
                    data-bs-placement="bottom" title="Shortcut Key : Alt + S">Save
            </button>
            <button type="submit" name="submit_button" class="btn btn-primary journal-btn me-2" value="{{ \App\Models\JournalTransaction::SAVE_AND_PRINT_BUTTON }}" >Save & Print</button>
            <a href="{{ route('company.transaction-journal.index') }}"
                class="btn btn-light btn-active-light-primary">Back</a>
            <a href="javascript:void(0)" data-id="{{ $journal->id }}" data-redirectIndex="true" class="btn btn-danger journal-delete-btn">Delete</a>
        </div>
    </div>
</div>
