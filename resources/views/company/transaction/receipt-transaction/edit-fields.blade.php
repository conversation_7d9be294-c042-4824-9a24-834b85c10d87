<div class="card card-border-1">
    <div class="card-body position-relative">
        <div class="position-absolute configuration-button d-flex align-items-center">
            <a href="{{ !empty($previousBillId) ? route('company.transaction-receipt.edit', ['transaction_receipt' => $previousBillId ]) : '#' }}" class="{{ empty($previousBillId) ? ' disabled ' : '' }} btn previous-next-btn d-flex justify-content-center align-items-center" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Previous">
                <i class="fa-solid fa-angle-left"></i>
            </a>
            <a href="{{ !empty($nextBillId) ? route('company.transaction-receipt.edit', ['transaction_receipt' => $nextBillId ]) : route('company.transaction-receipt.create') }}" class="btn previous-next-btn d-flex justify-content-center align-items-center mx-2" data-bs-toggle="tooltip" data-bs-placement="bottom" @if(!empty($nextBillId)) title="Next" @else title="Add" @endif>
                <i class="fa-solid fa-angle-right"></i>
            </a>
            <a href="javascript:void (0)"
                class="configuration-setting-btn receipt-transaction-configuration-modal d-flex justify-content-center align-items-center ms-8"
                data-bs-toggle="tooltip" data-bs-placement="bottom" title="Configuration, Shortcut Key : Alt + F12">
                <i class="fa-solid fa-gear"></i>
            </a>
        </div>
        <div class="row">
            @php
                use \App\Models\Master\Customer;
                use \App\Models\Master\Supplier;
                $receiptNumberIsDisabled = false;
                if ($methodVoucherType == \App\Models\Master\ReceiptTransaction::AUTOMATIC) {
                    $receiptNumberIsDisabled = true;
                }
            @endphp
            <div class="col-lg-3 col-md-5 col-sm-6 col-12 mb-3">
                {{ Form::label('receiptNumber', 'Receipt Number:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 required mb-0']) }}
                {{ Form::text('receipt_number', $receipt->receipt_number ?? null, ['class' => 'form-control', 'id' => 'receiptNumber',
                    $receiptNumberIsDisabled ? 'readonly' : '','maxLength'=>50]) }}
            </div>
            <div class="col-lg-3 col-md-5 col-sm-6 col-12 mb-3">
                {{ Form::label('date', 'Date:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 required mb-0']) }}
                <input type="text" class="form-control date-of-receipt-transaction"
                    value="{{ (!empty($receipt->date)) ? \Carbon\Carbon::parse($receipt->date)->format('d-m-Y') :  null  }}" name="date">
            </div>
            <div class="col-lg-3 col-md-5 col-sm-6 col-12 mb-3 custom-input-group">
                {{ Form::label('bankCashLedger', 'Bank/Cash:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 required mb-0']) }}
                <div class="input-group flex-nowrap">
                    {{ Form::select('bank_cash_ledger_id',getBandAndCashLedgers() ?? [], $receipt->bank_cash_ledger_id ?? null, ['id'=>'bankCashLedger','class' => 'form-select change-bank-and-cash-ledger bank-cash-ledger-name custom-select2-width',  'data-control' => 'select2', 'required','placeholder'=>'Select Bank/Cash Ledger']) }}
                        <a href="javascript:void(0)" class="input-group-text add-ledger-bank-cash-model custom-group-text right-0"
                           data-link-id='1' data-bs-toggle="tooltip" data-bs-placement="bottom">
                            <i class="fas fa-plus text-gray-900"></i>
                        </a>
                    </div>
                <span>Current Balance: <span
                            class="bank-and-cash-ledger-balance"></span></span>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
                {{ Form::label('ledgerId', 'Ledger name:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 required mb-0']) }}
                <div class="input-group flex-nowrap custom-input-group">
                    {{ Form::select('ledger_id',getCompanyLedger() ?? [], $receipt->ledger_id ?? null, ['id'=>'ledgerId','class' =>
                    'form-select change-ledger-name income-ledger-name custom-select2-width', 'data-control' => 'select2',
                    'required','placeholder'=>'Select Ledger']) }}
                    <a href="javascript:void(0)" class="input-group-text add-ledger-transaction-model custom-group-text right-0"
                        data-link-id='1' data-bs-toggle="tooltip" data-bs-placement="bottom" title="Shortcut Key : Alt + L">
                        <i class="fas fa-plus text-gray-900"></i>
                    </a>
                </div>
                <span>Current Balance: <span class="ledger-opening-balance"></span></span>
            </div>
            <div class="col-lg-3 col-md-5 col-sm-6 col-12 mb-3">
                {{ Form::label('amount', 'Amount:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 required mb-0']) }}
                {{ Form::number('total_received_amount', $receipt->total_received_amount ?? null, ['class' => 'form-control
                get-total-received-amount received-amount-total','placeholder' => 'Amount','required','step'=>'.01']) }}
            </div>
            <div class="col-lg-3 col-md-5 col-sm-6 col-12 d-flex manage-transaction-toggle-button {{ $receipt->ledgers->model_type == Customer::class || $receipt->ledgers->model_type == Supplier::class ? '' : 'd-none' }}">
                <div class="form-check form-switch form-check-custom form-switch-sm mb-3">
                    {{ Form::label('is_manage_transaction', 'Settle invoices? :', ['class' => 'form-check-label fs-6 fw-bolder
                    text-gray-900 ms-0 me-2']) }}
                    {{ Form::checkbox('is_manage_transaction', 1, $receipt->is_manage_transaction == 1 ? 'checked' : '', ['class' =>
                    'form-check-input is-manage-transaction', 'id' => 'isManageTransaction','data-ledger-id' => $receipt->ledger_id
                    ?? null]) }}
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-3 col-md-5 col-sm-6 col-12 mb-3 enable-payment-mode {{ $receiptTransactionConfiguration?->enable_payment_mode  ? '' : 'd-none'}}">
                {{ Form::label('payment_mode', 'Payment Mode:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 mb-0']) }}
                <div class="input-group flex-nowrap custom-input-group">
                    {{ Form::select('payment_mode', getPaymentReceiptModeList() ?? [], $receipt->payment_mode ?? null, ['id' => 'paymentModeList', 'class' => 'form-select payment-mode-name custom-select2-width', 'data-control' => 'select2', 'placeholder' => 'Select Ledger']) }}
                    <a href="javascript:void(0)" class="input-group-text payment-mode-add-for-receipt custom-group-text right-0"
                        data-link-id='1' data-bs-toggle="tooltip" data-bs-placement="bottom">
                        <i class="fas fa-plus text-gray-900"></i>
                    </a>
                </div>
            </div>
            <div class="col-lg-3 col-md-5 col-sm-6 col-12 mb-3 enable-reference-number {{ $receiptTransactionConfiguration?->enable_reference_number  ? '' : 'd-none'}}">
                {{ Form::label('reference_number', 'Reference Number:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 mb-0']) }}
                {{ Form::text('reference_number', $receipt->reference_number ?? null, ['class' => 'form-control','placeholder' => 'Reference Number']) }}
            </div>
        </div>
    </div>
</div>
<div class="card my-7 append-receipt-transaction-screen">
    @if($receipt->is_manage_transaction == 1)
        {{-- @if(count($receipt->receiptTransactionItem) > 0) --}}
            <div class="card-body items-details-card">
                <div class="row justify-content-between mb-2">
                    <div class="col-sm-12 col-12">
                        <h5 class="title-name">Unpaid Invoice</h5>
                    </div>
                </div>
                <div class="row">
                    <table class="ms-2 table table-bordered">
                        <tr>
                            <th class="fw-bold">Invoice Number</th>
                            <th class="fw-bold" style="width: 90px !important;">Invoice Date</th>
                            <th class="fw-bold">Transaction Type</th>
                            <th class="fw-bold">Total Amount</th>
                            <th class="fw-bold">Paid Amount</th>
                            <th class="fw-bold">Pending Amount</th>
                            <th class="fw-bold">Received Amount</th>
                            <th class="fw-bold">Discount Amount</th>
                            <th class="fw-bold">Rounding Amount</th>
                        </tr>
                        @each('company.transaction.receipt-transaction.rc_transaction_item', $receipt->receiptTransactionItem, 'receiptTransactionItem')
                        <tr class="border-top mt-5">
                            <td colspan="3" class="fw-bolder">Total Amount</td>
                            @if (!empty($receipt->receiptTransactionItem[0]->sale_id)
                                    || !empty($receipt->receiptTransactionItem[0]->income_debit_id)
                                    || !empty($receipt->receiptTransactionItem[0]->payment_transaction_id)
                                    || !empty($receipt->receiptTransactionItem[0]->opening_balance_invoice_id)
                                    )

                                @php
                                    /** @var  \App\Models\ReceiptTransaction $receipt */
                                    $grandTotal = $receipt->receiptTransactionItem->sum('saleBill.grand_total') +
                                        $receipt->receiptTransactionItem->sum('incomeDebitBill.grand_total') +
                                        $receipt->receiptTransactionItem->sum('paymentTransactionBill.total_paid_amount') +
                                        $receipt->receiptTransactionItem->sum('openingBalanceInvoice.total_amount');

                                    $discountAmount = $receipt->receiptTransactionItem->sum('discount');

                                    $otherTransactions = \App\Models\ReceiptTransactionItem::where('rc_transaction_id', '!=', $receipt->id);

                                    $saleTransactionDiscount = $otherTransactions->whereIn('sale_id', $receipt->receiptTransactionItem->pluck('sale_id'))
                                        ->sum('discount');

                                    $incomeDebitTransactionDiscount = $otherTransactions->whereIn('income_debit_id',
                                        $receipt->receiptTransactionItem->pluck('income_debit_id'))->sum('discount');

                                    $paymentTransactionDiscount = $otherTransactions->whereIn('payment_transaction_id',
                                        $receipt->receiptTransactionItem->pluck('payment_transaction_id'))->sum('discount');

                                    $openingBalanceInvoiceDiscount = $otherTransactions
                                        ->whereIn(
                                            'opening_balance_invoice_id',
                                            $receipt->receiptTransactionItem->pluck('opening_balance_invoice_id')
                                        )
                                        ->sum('discount');

                                    $otherDiscountAmount = $saleTransactionDiscount + $incomeDebitTransactionDiscount +
                                        $paymentTransactionDiscount + $openingBalanceInvoiceDiscount;

                                    $transactionTotal = 0;

                                    foreach ($saleTransactions as $saleTransaction) {
                                        /** @var \App\Models\SaleTransaction $saleTransaction */
                                        $transactionTotal += $saleTransaction->receiptTransactionItem->sum('received_amount');
                                        $transactionTotal += $saleTransaction->sale_return_sum_grand_total ?? 0;
                                        $transactionTotal += $saleTransaction->credit_note_sum_grand_total ?? 0;
                                    }

                                    foreach ($incomeDebitTransactions as $incomeDebitTransaction) {
                                        /** @var \App\Models\IncomeDebitNoteTransaction $incomeDebitTransaction */
                                        $transactionTotal += $incomeDebitTransaction->receiptTransactionItem->sum('received_amount');
                                    }

                                    foreach ($paymentTransactions as $paymentTransaction) {
                                        /** @var \App\Models\PaymentTransaction $paymentTransaction */
                                        $transactionTotal += $paymentTransaction->receiptTransactionItem->sum('received_amount');
                                    }

                                    foreach ($openingBalance as $openingInvoice) {
                                        // dd($openingInvoice);
                                        /** @var \App\Models\BillWiseOpeningBalance $openingInvoice */
                                        $transactionTotal += ($openingInvoice->total_paid_amount - $openingInvoice->paid_amount);
                                    }

                                    $paidAmount = ($transactionTotal - $receipt->receiptTransactionItem->sum('received_amount')) + $otherDiscountAmount;
                                    $roundOffAmount = $receipt->receiptTransactionItem->sum('round_off');
                                    $receivedAmount = $receipt->receiptTransactionItem->sum('received_amount');
                                    $pendingAmount = $grandTotal - $paidAmount;


                                @endphp

                            @elseif(!empty($receipt->receiptTransactionItem[0]->purchase_return_id)
                                    || !empty($receipt->receiptTransactionItem[0]->expense_debit_id)
                                    || !empty($receipt->receiptTransactionItem[0]->payment_transaction_id))
                                @php

                                    $grandTotal = $receipt->receiptTransactionItem->sum('purchaseReturnBill.grand_total') +
                                                  $receipt->receiptTransactionItem->sum('expenseDebitBill.grand_total') +
                                                  $receipt->receiptTransactionItem->sum('paymentTransactionBill.total_paid_amount');

                                    $discountAmount = $receipt->receiptTransactionItem->sum('discount');

                                    $otherTransactions = \App\Models\ReceiptTransactionItem::where('rc_transaction_id', '!=', $receipt->id);

                                    $purchaseTransactionDiscount = $otherTransactions->whereIn('purchase_return_id',
                                        $receipt->receiptTransactionItem->pluck('purchase_return_id'))->sum('discount');

                                    $expenseCreditTransactionDiscount = $otherTransactions->whereIn('expense_debit_id',
                                        $receipt->receiptTransactionItem->pluck('expense_debit_id'))->sum('discount');

                                    $paymentTransactionDiscount = $otherTransactions->whereIn('payment_transaction_id',
                                        $receipt->receiptTransactionItem->pluck('payment_transaction_id'))->sum('discount');

                                    $otherDiscountAmount = $purchaseTransactionDiscount + $expenseCreditTransactionDiscount + $paymentTransactionDiscount;

                                    $transactionTotal = 0;

                                    foreach ($expenseDebitTransactions as $expenseDebitTransaction) {
                                        /** @var \App\Models\ExpenseDebitNoteTransaction $expenseDebitTransaction */
                                        $transactionTotal += $expenseDebitTransaction->receiptTransactionItem->sum('received_amount');
                                    }

                                    foreach ($purchaseReturnTransactions as $purchaseReturnTransaction) {
                                        /** @var \App\Models\PurchaseReturnTransaction $purchaseReturnTransaction */
                                        $transactionTotal += $purchaseReturnTransaction->receiptTransactionItem->sum('received_amount');
                                    }

                                    foreach ($paymentTransactions as $paymentTransaction) {
                                        /** @var \App\Models\PaymentTransaction $paymentTransactions */
                                        $transactionTotal += $paymentTransactions->receiptTransactionItem->sum('received_amount');
                                    }

                                    $paidAmount = ($transactionTotal - $receipt->receiptTransactionItem->sum('received_amount')) + $otherDiscountAmount;
                                    $roundOffAmount = $receipt->receiptTransactionItem->sum('round_off');
                                    $receivedAmount = $receipt->receiptTransactionItem->sum('received_amount');
                                    $pendingAmount = $grandTotal - $paidAmount;

                                @endphp
                            @endif
                                <td class="fw-bolder">{{ getCurrencySymbol().getCurrencyFormat($grandTotal ?? 0.00) }}</td>
                                <td class="fw-bolder">{{ getCurrencySymbol().getCurrencyFormat($paidAmount ?? 0.00) }}</td>
                                <td class="fw-bolder">{{ getCurrencySymbol().getCurrencyFormat($pendingAmount ?? 0.00) }}</td>
                                <td class="fw-bolder total-received-amount">{{ getCurrencySymbol().getCurrencyFormat($receivedAmount ?? 0.00) }}</td>
                                <td class="fw-bolder total-discount-amount">{{ getCurrencySymbol().getCurrencyFormat($discountAmount ?? 0.00) }}</td>
                                <td class="fw-bolder total-rounding-amount">{{ getCurrencySymbol().getCurrencyFormat($roundOffAmount ?? 0.00) }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        {{-- @endif  --}}
    @endif
</div>

{{--Narratio & Term-Conditions--}}
<div class="card  card-border-1">
    <div class="card-body">
        <div class="row">
            <div class="col-6 col-lg-6 col-md-6 col-sm-12 mb-3 enable-narration {{$receiptTransactionConfiguration->is_enable_narration ? '' : 'd-none' }}">
                {{ Form::label('narration', 'Narration:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 mb-0']) }}
                {{ Form::textarea('narration',$receipt->narration ?? null, ['class' => 'form-control check-limit-textarea','maxLength' => 250,'rows'=>3,'cols'=>2]) }}
            </div>
            <div class="col-6 col-lg-6 col-md-6 col-sm-12 mb-3">
                {{ Form::label('upload_receipt_invoice', 'Upload Document:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 mb-0']) }}
                {{ Form::file('upload_receipt_invoice[]',['class'=>'form-control file-upload-validate', 'multiple' => true, 'data-bs-toggle' => 'tooltip','data-bs-placement' => 'bottom','title' => 'Maximum file size is 2 MB.']) }}
                @if(isset($receipt->media))
                    @foreach($receipt->media as $key => $attachment)
                        <div class="symbol symbol-50px symbol-2by3 my-5 mx-2 remove-document-image-div-{{$attachment->id}} image-show__attachment-container">
                            <a href="javascript:void(0)" data-id="{{$attachment->id}}"
                                class="symbol-badge badge badge-circle  start-100 delete-document-images"><i
                                    class="bi bi-x fs-2 text-gray-900"></i></a>
                            <a class="{{ isImage($attachment) ? 'attachment-lightbox' : '' }}"
                                href="{{ isImage($attachment) ? returnAttachmentLink($attachment) : $attachment->getFullUrl() }}"
                                target="{{ isImage($attachment) ? '_self' : '_blank' }}"
                               data-effect="mfp-3d-unfold">
                                <img src="{{ returnAttachmentLink($attachment) }}"
                                     alt="Profile Image"
                                    class="object-fit-cover"
                                     width="40" height="56">
                            </a>
                        </div>
                    @endforeach
                @endif
            </div>

        </div>
        <div class="d-flex mt-5">
            {{ Form::hidden('submit_button_value',null,['class'=>'receipt-transaction-btn-value']) }}
            <button type="submit" name="submit_button" class="btn btn-primary me-2" value=""
                data-bs-toggle="tooltip" data-bs-placement="bottom" title="Shortcut Key : Alt + S">Save
            </button>
            <button type="submit" name="submit_button" class="btn btn-primary receipt-transaction-btn me-2" value="{{ \App\Models\ReceiptTransaction::SAVE_AND_PRINT_BUTTON }}" >Save & Print</button>
            <a href="{{ route('company.transaction-receipt.index') }}"
                class="btn btn-light btn-active-light-primary me-2 receipt-transaction-back-btn">Back</a>
            <a href="javascript:void(0)" data-id="{{ $receipt->id }}" data-redirectIndex="true" class="btn btn-danger receipt-delete-btn">Delete</a>
        </div>
    </div>
</div>
