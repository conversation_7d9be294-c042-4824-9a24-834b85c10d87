@extends('company.layouts.app')
@section('title')
    GSTR - 1
@endsection
@section('content')
    @php
        $currentCompany = getCurrentCompany();
        const MONTHS = 1;
        const QUARTERLY = 2;
        const CUSTOM = 3;
        if (session('impersonated_by') != null) {
            $user = \App\Models\User::whereId(session('impersonated_by'))->first();
        } else {
            $user = auth()->user();
        }

        // $invoices = isset($gstStatus->meta_data) ? json_decode($gstStatus->meta_data) : [];
        // $collection = !empty($invoices->B2B->b2b) ? collect($invoices->B2B->b2b) : collect();
        // $totalInvCount = $collection->sum(function ($item) {
        //     return is_array($item->inv) ? count($item->inv) : 0;
        // });

    @endphp
    <div class="d-flex container-fluid ">
        <div id="kt_content_container" class=" container-fluid">
            {{-- @if ($user->roles[0]->name == 'consultant') --}}
                <div class=" px-5 w-100 mb-5">
                    <div class="d-flex align-items-center rounded py-4 px-5  bg-white">
                        <div class="text-black-700 fw-bold fs-6">
                            <svg width="16" height="19" viewBox="0 0 14 17" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M8.8749 17H5.12486V15.75H8.8749V17ZM10.1249 15.125H3.87486V13.875H10.1249V15.125ZM6.99986 0.125C7.8205 0.125 8.6333 0.28659 9.3915 0.60059C10.1497 0.91468 10.8394 1.37569 11.4198 1.95605C12 2.53634 12.4603 3.22529 12.7743 3.9834C13.0883 4.74164 13.2499 5.55429 13.2499 6.375C13.2916 7.2823 13.118 8.1868 12.742 9.0137C12.3662 9.8404 11.7988 10.5659 11.0877 11.1309C10.4565 11.7184 10.1249 12.05 10.1249 12.625H8.8749C8.8749 11.4627 9.5685 10.8378 10.2372 10.2129C10.8223 9.7663 11.2892 9.1826 11.5965 8.5137C11.9038 7.8446 12.0424 7.11 11.9999 6.375C11.9999 5.04911 11.4734 3.77749 10.536 2.83984C9.5983 1.90216 8.3259 1.375 6.99986 1.375C5.67383 1.37504 4.40234 1.9022 3.4647 2.83984C2.52707 3.77752 1.99986 5.04896 1.99986 6.375C1.95657 7.1095 2.09407 7.8437 2.40025 8.5127C2.70643 9.1816 3.1726 9.7656 3.75669 10.2129C4.43156 10.8315 5.12486 11.4752 5.12486 12.625H3.87486C3.87486 12.0501 3.53774 11.7188 2.91294 11.1377C2.20124 10.5717 1.63362 9.8446 1.25767 9.0166C0.881748 8.1887 0.707588 7.2833 0.749858 6.375C0.749858 4.71744 1.40886 3.12815 2.58091 1.95605C3.75297 0.78399 5.34231 0.12504 6.99986 0.125Z"
                                    fill="#FFC107" />
                            </svg>
                            <span class="fw-bold ms-2">
                                <b>
                                    Beta Feature: Please verify your data on the GST platform before filing.
                                </b>
                            </span>
                        </div>
                    </div>
                </div>
            {{-- @endif --}}
                    <div class="d-flex justify-content-center align-items-center flex-column mb-4">
                        <h1 class="mb-0" style="font-size: 17px">GSTR-1</h1>
                        <h4 class="mb-0" style="font-size: 17px">{{ \Carbon\Carbon::parse($data['start_date'])->format('d/m/Y') }} To
                            {{ \Carbon\Carbon::parse($data['end_date'])->format('d/m/Y') }}</h4>
                        <h6 class="mb-0 mt-2"> {{ getCurrentCompany()->trade_name ?? '' }} ({{ $currentCompany->companyTax->gstin ?? '' }})</h6>
                        <div class="col-12  p-0 m-0">
                            {{-- <h6 class="mb-0 pt-2">{{ $currentCompany->companyTax->gstin ?? '' }}</h6> --}}
                        </div>
                        <div>
                            <h6 class="mb-0 pt-2 text-primary">Filing Frequency : <span class="fw-bold ">{{ isset($gstLogin->filing_type) ? ($gstLogin->filing_type == 'M' ? 'Monthly' : 'Quarterly') : ''}}</span></h6>
                        </div>

                    </div>
            <div class="section-heading text-center flex-wrap border-dark d-flex row-gap-16px  justify-content-between align-items-end mb-4"
                id="gstr1ReportIndex">
                <div class="d-flex align-items-end gap-3 flex-wrap">
                    {{-- @if ($user->roles[0]->name == 'consultant') --}}
                        @if((empty($gstFiling) || !$gstFiling->gstr_1_filed))
                            <div class="p-2 rounded custom-align-block-table d-flex align-items-center mt-0">
                                <h6 class="mb-0">Filing Status: <span class="p-2 rounded custom-align-block-table" style="background-color: #ffffff;border: 1px solid #8E92A2;font-weight: 500; max-width: 330px;">Not yet uploaded</span></h6>
                            </div>
                        @elseif(!empty($gstStatus) && (empty($gstFiling) || !$gstFiling->gstr_1_filed) )
                            <div class="p-2 rounded custom-align-block-table d-flex align-items-center mt-0">
                                <h6>Filing Status: <span class="p-2 rounded custom-align-block-table" style="background-color: #ffffff;border: 1px solid #8E92A2;font-weight: 500; max-width: 330px;">Uploaded</span></h6>
                            </div>
                        @elseif(!empty($gstFiling) || isset($gstFiling->gstr_1_filed))
                            <div class="p-2 rounded custom-align-block-table d-flex align-items-center mt-0">
                                <h6>Filing Status: <span class="p-2 rounded custom-align-block-table " style="background-color: #ffffff;border: 1px solid #8E92A2;font-weight: 500; max-width: 330px;">Filed</span></h6>
                            </div>
                        @endif
                        <div class=" rounded custom-align-block-table mt-0"
                            style="background-color: #ffffff;border: 1px solid #8E92A2;font-weight: 500; max-width: 330px;  padding: 5px 8px !important;">
                            Last Status Updated :
                            {{ Illuminate\Support\Carbon::parse(getLastStatusUpdateGstDate('updated_at'))->format('jS M Y h:i A') }}
                        </div>
                    {{-- @endif --}}
                </div>
                <div class="ms-auto">
                    <div
                        class="d-flex flex-wrap flex-nowrap-main-screen gap-3 gap-sm-0 w-auto justify-content-end align-items-center">
                        <div class="mb-xl-0 mb-0 mx-3 mt-xl-0">
                            <div>
                                {{ Form::select('gstr1_date_range', [1 => 'Months', 2 => 'Quarters', 3 => 'Custom'], $filterType ?? 1, ['class' => 'form-control form-select', 'data-control' => 'select2', 'id' => 'gstR1DateRange', 'placeholder' => 'Select Date']) }}
                            </div>
                        </div>
                        <div class="d-flex flex-wrap flex-xxl-nowrap  w-auto justify-content-end gap-3 gap-sm-0">
                            <div
                                class="d-flex date-input-div mx-1 gstr1-report-daterange {{ $filterType == 3 ? '' : 'd-none' }}">
                                <input name="date_range" class="form-control" placeholder="dd-mm-yyyy - dd-mm-yyyy"
                                    id="gstr1ReportDateRange" />
                            </div>
                            <div id="gstR1StartMonth"
                                class="input-group form-control justify-content-between align-items-center date custom-monthpicker-width {{ $filterType == 1 ? '' : 'd-none' }}"
                                data-date-format="mm-dd-yyyy">
                                <input class="border-0 outline-0 date-input gstr1-start-month" type="text"
                                    placeholder="Month" readonly />
                                <span class="input-group-addon" style="margin-top: 5px; margin-left:15px;"><i
                                        class="fas fa-calendar text-primary"></i></span>
                            </div>
                            <div id="gstR1QuarterMonth"
                                class="input-group form-control date custom-monthpicker-width  {{ $filterType == 2 ? '' : 'd-none' }}"
                                data-date-format="mm-dd-yyyy">
                                <input class="border-0 outline-0 date-input gstr1-quarter-month" type="text"
                                    placeholder="Quarter" readonly />
                                <span class="input-group-addon" style="margin-top: 5px; margin-left:15px;"><i
                                        class="fas fa-calendar text-primary"></i></span>
                            </div>
                            {{-- @if ($user->roles[0]->name == 'consultant') --}}
                                @if ($gstLogin?->status)
                                    <div class="d-flex justify-content-end text-nowrap mx-1">
                                        <a class="btn-sm fw-5 bg-primary text-white fs-13"
                                            href="javascript:void(0)">GSTIN Connected</a>
                                    </div>
                                @else
                                    <div class="d-flex justify-content-end text-nowrap mx-1">
                                        <a class="btn-sm fw-5 btn-primary gst-portal-login"
                                            href="javascript:void(0)">Connect to GST Portal</a>
                                    </div>
                                @endif
                            {{-- @endif --}}
                            <div class="d-flex custom-align-block-table mx-1 mt-2">
                                @if (getLoginUser()->can('company_import_export_gstr_1_report'))
                                    <a href="{{ route('company.reports.gstr-1.pdf', [
                                        'start_date' => $data['start_date'],
                                        'end_date' => $data['end_date'],
                                    ]) }}"
                                        class="gstr-1-summary-pdf mx-2" target="_blank" data-bs-toggle="tooltip"
                                        data-bs-placement="bottom"
                                        data-bs-original-title="PDF Export, Shortcut Key : Alt + D">
                                        <i class="far fa-file-pdf text-gray text-orange"
                                            style="font-size: 24px !important;"></i>
                                    </a>
                                    <input type="hidden" id="gstr1StartDate" value="{{ $data['start_date'] }}">
                                    <input type="hidden" id="gstr1EndDate" value="{{ $data['end_date'] }}">
                                    <a href="{{ route('company.reports.gstr-1-excel', [
                                        'start_date' => $data['start_date'],
                                        'end_date' => $data['end_date'],
                                    ]) }}"
                                        data-bs-toggle="tooltip" data-bs-placement="bottom"
                                        data-bs-original-title="Excel Export, Shortcut Key : Alt + E"
                                        class="gstr-1-summary-excel mx-2" target="_blank">
                                        <i class="far fa-file-excel text-gray text-success"
                                            style="font-size: 24px !important;"></i>
                                    </a>
                                    <a href="{{ route('company.reports.gstr-1-json-export', ['start_date' => $data['start_date'], 'end_date' => $data['end_date']]) }}"
                                        data-bs-toggle="tooltip" data-bs-placement="bottom"
                                        data-bs-original-title="Json Export" class="gstr-1-summary-excel mx-2"
                                        target="_blank">
                                        <svg width="24" height="24" viewBox="0 0 872 872" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M633.835 100.484H621.096V195.927C621.096 200.444 622.891 204.777 626.084 207.97C629.278 211.164 633.61 212.959 638.127 212.959H729.21V198.993L709.727 178.896H655.158V122.489L633.835 100.484ZM624.08 630.156V405.276C624.08 400.759 622.286 396.426 619.091 393.233C615.898 390.039 611.565 388.244 607.049 388.244H108.748C104.231 388.244 99.899 390.039 96.7051 393.233C93.511 396.426 91.7168 400.759 91.7168 405.276V630.156C91.7168 634.673 93.511 639.006 96.7051 642.199C99.899 645.392 104.231 647.188 108.748 647.188H607.049C611.565 647.188 615.898 645.392 619.091 642.199C622.286 639.006 624.08 634.673 624.08 630.156ZM590.017 613.125H125.779V422.307H590.017V613.125Z"
                                                fill="black" />
                                            <path
                                                d="M775.514 173.378L660.519 54.5683C658.93 52.922 657.025 51.6147 654.918 50.7248C652.809 49.8349 650.544 49.3812 648.257 49.3908H207.011C202.495 49.3908 198.162 51.1852 194.968 54.3791C191.773 57.5731 189.98 61.905 189.98 66.422V371.213H224.042V83.4533H641.056L746.241 192.112V788.547H224.042V664.219H189.98V805.578C189.98 810.095 191.773 814.428 194.968 817.621C198.162 820.816 202.495 822.609 207.011 822.609H763.252C767.769 822.609 772.101 820.816 775.295 817.621C778.488 814.428 780.283 810.095 780.283 805.578V185.232C780.285 180.812 778.575 176.565 775.514 173.378Z"
                                                fill="black" />
                                            <path
                                                d="M169.76 578.749C175.713 580.924 182.001 582.038 188.338 582.04C204.191 582.04 230.787 575.908 230.787 534.85V463.598C230.787 460.888 229.71 458.289 227.794 456.373C225.877 454.455 223.278 453.379 220.568 453.379H206.82C204.111 453.379 201.512 454.455 199.594 456.373C197.678 458.289 196.602 460.888 196.602 463.598V533.766C196.602 549.939 192.671 549.939 187.221 549.939C184.554 549.953 181.905 549.491 179.4 548.577C177.997 548.03 176.489 547.8 174.987 547.904C173.485 548.008 172.024 548.442 170.709 549.178C169.394 549.912 168.257 550.927 167.381 552.152C166.505 553.376 165.909 554.779 165.639 556.261L163.595 567.475C163.177 569.776 163.562 572.151 164.684 574.202C165.806 576.254 167.598 577.86 169.76 578.749ZM462.214 581.174H475.158C477.867 581.174 480.466 580.098 482.384 578.182C484.3 576.266 485.376 573.667 485.376 570.956V521.688L485.785 522.335L519.596 576.379C520.517 577.842 521.796 579.046 523.31 579.88C524.824 580.715 526.526 581.151 528.254 581.147H542.159C544.868 581.147 547.467 580.071 549.385 578.155C551.301 576.239 552.377 573.64 552.377 570.929V464.463C552.377 461.753 551.301 459.154 549.385 457.238C547.467 455.32 544.868 454.244 542.159 454.244H529.215C526.505 454.244 523.906 455.32 521.988 457.238C520.072 459.154 518.996 461.753 518.996 464.463V511.299L485.867 458.992C484.947 457.533 483.672 456.33 482.161 455.498C480.65 454.663 478.954 454.225 477.229 454.224H462.241C459.531 454.224 456.933 455.3 455.015 457.218C453.099 459.134 452.022 461.733 452.022 464.442V570.956C452.022 573.662 453.095 576.258 455.006 578.172C456.916 580.088 459.508 581.168 462.214 581.174ZM386.268 582.919C415.971 582.919 445.946 562.413 445.946 516.612C445.946 478.871 422.054 452.514 387.856 452.514C352.724 452.514 328.178 479.716 328.178 518.67C328.205 556.493 352.097 582.919 386.268 582.919ZM387.222 484.137C406.011 484.137 410.97 505.774 410.97 517.212C410.97 534.148 403.579 551.234 387.059 551.234C370.709 551.234 363.31 534.202 363.31 518.316C363.31 502.429 369.605 484.165 387.249 484.165L387.222 484.137ZM243.049 574.294C253.043 580.023 264.388 582.973 275.906 582.837C308.728 582.837 323.423 562.311 323.423 541.962C323.423 517.328 305.397 507.218 289.708 501.141C275.075 495.48 275.075 492.769 275.075 490.139C275.075 486.549 278.754 484.396 284.919 484.396C290.265 484.381 295.531 485.689 300.247 488.204C301.557 488.92 303.006 489.34 304.494 489.434C305.983 489.529 307.475 489.298 308.863 488.754C310.253 488.213 311.506 487.371 312.537 486.293C313.567 485.214 314.347 483.923 314.826 482.509L318.62 471.296C319.38 469.046 319.334 466.602 318.487 464.383C317.641 462.162 316.047 460.307 313.981 459.135C305.145 454.581 295.304 452.33 285.368 452.589C259.481 452.589 240.699 469.048 240.699 491.72C240.699 509.916 252.45 523.316 275.381 531.443C288.89 536.641 288.89 539.904 288.89 543.072C288.89 549.646 282.33 551.022 276.826 551.022C270.025 550.949 263.366 549.069 257.533 545.572C256.199 544.751 254.695 544.247 253.137 544.101C251.578 543.953 250.006 544.166 248.543 544.723C247.079 545.278 245.764 546.163 244.696 547.31C243.628 548.454 242.84 549.83 242.388 551.329L238.914 562.856C238.287 564.954 238.35 567.199 239.095 569.258C239.839 571.317 241.225 573.083 243.049 574.294Z"
                                                fill="black" />
                                        </svg>
                                    </a>
                                @endif
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            @include('flash::message')
            <div class="card card-border-1  mt-3 overflow-auto" style="padding: 30px;">
                <div class="card-body p-0 gstr-3b-3-1-summary">
                    <table class="table mb-0 rounded-3 fs-13" style="font-size: 13px; ">
                        <thead>
                            <tr>
                                <td class="border-right-primary vertical-align-middle"
                                    style="border-top-left-radius: 0.625rem;width: 5%;"> Sl No.
                                </td>
                                <td class="border-right-primary vertical-align-middle" style="width:27%">
                                    Particulars
                                </td>
                                <td class="border-right-primary vertical-align-middle" style="width:8.5%"> Voucher
                                    Count
                                </td>
                                <td class="border-right-primary vertical-align-middle" style="width:8.5%"> Taxable
                                    Amount
                                </td>
                                <td class="border-right-primary vertical-align-middle" style="width:8.5%"> IGST</td>
                                <td class="border-right-primary vertical-align-middle" style="width:8.5%"> CGST</td>
                                <td class="border-right-primary vertical-align-middle" style="width:8.5%">
                                    SGST/UTSGT
                                </td>
                                <td class="border-right-primary vertical-align-middle" style="width:8.5%"> CESS</td>
                                <td class="border-right-primary vertical-align-middle" style="width:8.5%"> Total
                                    tax
                                </td>
                                <td class="vertical-align-middle " style="border-top-right-radius: 0.625rem;width: 8.5% ">
                                    Invoice Amount
                                </td>
                            </tr>
                        </thead>
                        <tbody>
                            {{-- B2B Invoices - 4A, 4B, 4C, 6B, 6C --}}
                            <tr style="background: none !important">
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">1
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    B2B
                                    Invoices - 4A, 4B, 4C, 6B, 6C
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    <a
                                        href="{{ route('company.sale-report', ['sale' => $b2bSezdeTotal['sale'] ?? '', 'sale_return' => $b2bSezdeTotal['sale_return'] ?? '', 'income_dr_note' => $b2bSezdeTotal['income_dr_note'] ?? '', 'income_cr_note' => $b2bSezdeTotal['income_cr_note'] ?? '', 'startDate' => $data['start_date'], 'endDate' => $data['end_date']]) }}">
                                        {{ $b2bSezdeTotal['totalRecord'] }}
                                    </a>
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2bSezdeTotal['invoiceTaxableAmount']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2bSezdeTotal['igst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2bSezdeTotal['cgst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2bSezdeTotal['sgst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2bSezdeTotal['cess']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2bSezdeTotal['totalTaxAmount']) }}
                                </td>
                                <td class=" border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2bSezdeTotal['invoiceAmount']) }}
                                </td>
                            </tr>
                            <tr>
                                <td class="border-right-primary border-bottom-primary">2</td>
                                <td class="border-right-primary border-bottom-primary">B2C(Large) Invoices - 5A, 5B</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    <a
                                        href="{{ route('company.sale-report', ['sale' => $b2cl['sale'] ?? '', 'sale_return' => $b2cl['sale_return'] ?? '', 'income_dr_note' => $b2cl['income_dr_note'] ?? '', 'income_cr_note' => $b2cl['income_cr_note'] ?? '', 'startDate' => $data['start_date'], 'endDate' => $data['end_date']]) }}">
                                        {{ $b2cl['totalRecord'] }}
                                    </a>
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2cl['invoiceTaxableAmount']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2cl['igst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2cl['cgst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2cl['sgst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2cl['cess']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2cl['totalTaxAmount']) }}
                                </td>
                                <td class=" border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2cl['invoiceAmount']) }}
                                </td>
                            </tr>
                            <tr>
                                <td class="border-right-primary border-bottom-primary">3</td>
                                <td class="border-right-primary border-bottom-primary">B2C(Small) Invoices - 7</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    <a
                                        href="{{ route('company.sale-report', ['sale' => $b2cs['sale'] ?? '', 'sale_return' => $b2cs['sale_return'] ?? '', 'income_dr_note' => $b2cs['income_dr_note'] ?? '', 'income_cr_note' => $b2cs['income_cr_note'] ?? '', 'startDate' => $data['start_date'], 'endDate' => $data['end_date']]) }}">
                                        {{ $b2cs['totalRecord'] }}
                                    </a>
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2cs['invoiceTaxableAmount']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2cs['igst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2cs['cgst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2cs['sgst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2cs['cess']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2cs['totalTaxAmount']) }}
                                </td>
                                <td class=" border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($b2cs['invoiceAmount']) }}
                                </td>
                            </tr>
                            <tr>
                                <td class="border-right-primary border-bottom-primary">4</td>
                                <td class="border-right-primary border-bottom-primary">Credit/Debit Notes(Registered) - 9B
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    <a
                                        href="{{ route('company.sale-report', ['sale' => $cdnr['sale'] ?? '', 'sale_return' => $cdnr['sale_return'] ?? '', 'income_dr_note' => $cdnr['income_dr_note'] ?? '', 'income_cr_note' => $cdnr['income_cr_note'] ?? '', 'startDate' => $data['start_date'], 'endDate' => $data['end_date']]) }}">
                                        {{ $cdnr['totalRecord'] }}
                                    </a>
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($cdnr['invoiceTaxableAmount']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($cdnr['igst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($cdnr['cgst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($cdnr['sgst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($cdnr['cess']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($cdnr['totalTaxAmount']) }}
                                </td>
                                <td class=" border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($cdnr['invoiceAmount']) }}
                                </td>
                            </tr>
                            <tr>
                                <td class="border-right-primary border-bottom-primary">5</td>
                                <td class="border-right-primary border-bottom-primary">Credit/Debit Notes(Unregistered) -
                                    9B
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    <a
                                        href="{{ route('company.sale-report', ['sale' => $cdnur['sale'] ?? '', 'sale_return' => $cdnur['sale_return'] ?? '', 'income_dr_note' => $cdnur['income_dr_note'] ?? '', 'income_cr_note' => $cdnur['income_cr_note'] ?? '', 'startDate' => $data['start_date'], 'endDate' => $data['end_date']]) }}">
                                        {{ $cdnur['totalRecord'] }}
                                    </a>
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($cdnur['invoiceTaxableAmount']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($cdnur['igst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($cdnur['cgst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($cdnur['sgst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($cdnur['cess']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($cdnur['totalTaxAmount']) }}
                                </td>
                                <td class=" border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($cdnur['invoiceAmount']) }}
                                </td>
                            </tr>
                            <tr>
                                <td class="border-right-primary border-bottom-primary">6</td>
                                <td class="border-right-primary border-bottom-primary">Exports Invoices - 6A</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    <a href="{{ route('company.sale-report', ['sale' => $exp['sale'] ?? '', 'sale_return' => $exp['sale_return'] ?? '', 'income_dr_note' => $exp['income_dr_note'] ?? '', 'income_cr_note' => $exp['income_cr_note'] ?? '', 'startDate' => $data['start_date'], 'endDate' => $data['end_date']]) }}"
                                        target="_blank">
                                        {{ $exp['totalRecord'] }}
                                    </a>
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($exp['invoiceTaxableAmount']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($exp['igst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($exp['cgst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($exp['sgst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($exp['cess']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($exp['totalTaxAmount']) }}
                                </td>
                                <td class=" border-bottom-primary" style="">
                                    {{ getCurrencySymbol() . getCurrencyFormat($exp['invoiceAmount']) }}
                                </td>
                            </tr>
                            <tr>
                                <td class="border-right-primary border-bottom-primary">7</td>
                                <td class="border-right-primary border-bottom-primary">Tax Liability(Advances received) -
                                    11A(1),
                                    11A(2)
                                </td>
                                <td class="border-right-primary border-bottom-primary">0</td>
                                <td class="border-right-primary border-bottom-primary">0.00</td>
                                <td class="border-right-primary border-bottom-primary">0.00</td>
                                <td class="border-right-primary border-bottom-primary">0.00</td>
                                <td class="border-right-primary border-bottom-primary">0.00</td>
                                <td class="border-right-primary border-bottom-primary">0.00</td>
                                <td class="border-right-primary border-bottom-primary">0.00</td>
                                <td class="border-bottom-primary">0.00</td>
                            </tr>
                            <tr>
                                <td class="border-right-primary border-bottom-primary">8</td>
                                <td class="border-right-primary border-bottom-primary">Adjustment of Advances - 11B(1),
                                    11B(2)
                                </td>
                                <td class="border-right-primary border-bottom-primary">0</td>
                                <td class="border-right-primary border-bottom-primary">0.00</td>
                                <td class="border-right-primary border-bottom-primary">0.00</td>
                                <td class="border-right-primary border-bottom-primary">0.00</td>
                                <td class="border-right-primary border-bottom-primary">0.00</td>
                                <td class="border-right-primary border-bottom-primary">0.00</td>
                                <td class="border-right-primary border-bottom-primary">0.00</td>
                                <td class="border-bottom-primary">0.00</td>
                            </tr>
                            <tr>
                                <td class="border-right-primary border-bottom-primary text-right">9</td>
                                <td class="border-right-primary border-bottom-primary text-right">Nil Rated Invoices - 8A,
                                    8B,
                                    8C, 8D
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    <a
                                        href="{{ route('company.sale-report', ['sale' => $nillRated['sale'] ?? '', 'sale_return' => $nillRated['sale_return'] ?? '', 'income_dr_note' => $nillRated['income_dr_note'] ?? '', 'income_cr_note' => $nillRated['income_cr_note'] ?? '', 'startDate' => $data['start_date'], 'endDate' => $data['end_date']]) }}">
                                        {{ $nillRated['totalRecord'] }}
                                    </a>
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($nillRated['invoiceTaxableAmount']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($nillRated['igst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($nillRated['cgst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($nillRated['sgst']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($nillRated['cess']) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($nillRated['totalTaxAmount']) }}
                                </td>
                                <td class=" border-bottom-primary" style="">
                                    {{ getCurrencySymbol() . getCurrencyFormat($nillRated['invoiceAmount']) }}
                                </td>
                            </tr>
                            @php
                                $totalVoucher =
                                    $nillRated['totalRecord'] +
                                    $exp['totalRecord'] +
                                    $cdnur['totalRecord'] +
                                    $cdnr['totalRecord'] +
                                    $b2cs['totalRecord'] +
                                    $b2cl['totalRecord'] +
                                    $b2bSezdeTotal['totalRecord'];

                                $totalTaxableAmount =
                                    $nillRated['invoiceTaxableAmount'] +
                                    $exp['invoiceTaxableAmount'] +
                                    $cdnur['invoiceTaxableAmount'] +
                                    $cdnr['invoiceTaxableAmount'] +
                                    $b2cs['invoiceTaxableAmount'] +
                                    $b2cl['invoiceTaxableAmount'] +
                                    $b2bSezdeTotal['invoiceTaxableAmount'];

                                $totalIgst =
                                    $nillRated['igst'] +
                                    $exp['igst'] +
                                    $cdnur['igst'] +
                                    $cdnr['igst'] +
                                    $b2cs['igst'] +
                                    $b2cl['igst'] +
                                    $b2bSezdeTotal['igst'];

                                $totalSgst =
                                    $nillRated['sgst'] +
                                    $exp['sgst'] +
                                    $cdnur['sgst'] +
                                    $cdnr['sgst'] +
                                    $b2cs['sgst'] +
                                    $b2cl['sgst'] +
                                    $b2bSezdeTotal['sgst'];

                                $totalCgst =
                                    $nillRated['cgst'] +
                                    $exp['cgst'] +
                                    $cdnur['cgst'] +
                                    $cdnr['cgst'] +
                                    $b2cs['cgst'] +
                                    $b2cl['cgst'] +
                                    $b2bSezdeTotal['cgst'];

                                $totalCess =
                                    $nillRated['cess'] +
                                    $exp['cess'] +
                                    $cdnur['cess'] +
                                    $cdnr['cess'] +
                                    $b2cs['cess'] +
                                    $b2cl['cess'] +
                                    $b2bSezdeTotal['cess'];

                                $totalTaxAmount =
                                    $nillRated['totalTaxAmount'] +
                                    $exp['totalTaxAmount'] +
                                    $cdnur['totalTaxAmount'] +
                                    $cdnr['totalTaxAmount'] +
                                    $b2cs['totalTaxAmount'] +
                                    $b2cl['totalTaxAmount'] +
                                    $b2bSezdeTotal['totalTaxAmount'];

                                $totalGrandAmount =
                                    $nillRated['invoiceAmount'] +
                                    $exp['invoiceAmount'] +
                                    $cdnur['invoiceAmount'] +
                                    $cdnr['invoiceAmount'] +
                                    $b2cs['invoiceAmount'] +
                                    $b2cl['invoiceAmount'] +
                                    $b2bSezdeTotal['invoiceAmount'];
                            @endphp
                            <tr style="background: #e6e6e6;">
                                <td class="border-right-primary border-bottom-primary"></td>
                                <td class="border-right-primary border-bottom-primary">Total</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ $totalVoucher }}</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($totalTaxableAmount) }}</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($totalIgst) }}</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($totalSgst) }}</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($totalCgst) }}</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($totalCess) }}</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($totalTaxAmount) }}</td>
                                <td class=" border-bottom-primary" style="">
                                    {{ getCurrencySymbol() . getCurrencyFormat($totalGrandAmount) }}</td>
                            </tr>
                            <tr>
                                <td class="border-right-primary border-bottom-primary"></td>
                                <td class="border-right-primary border-bottom-primary">HSN/SAC Summary</td>
                                <td class="border-right-primary border-bottom-primary text-right">
                                    <a href="{{ route('company.hsn-summary-outward-report', ['startDate' => $data['start_date'], 'endDate' => $data['end_date']]) }}"
                                        class="text-decoration-underline">HSN</a>
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat(round($hsnB2b['invoiceTaxableAmount'] + $hsnB2c['invoiceTaxableAmount'], 2)) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat(round($hsnB2b['igst'] + $hsnB2c['igst'], 2)) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat(round($hsnB2b['cgst'] + $hsnB2c['cgst'], 2)) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat(round($hsnB2b['sgst'] + $hsnB2c['sgst'], 2)) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat(round($hsnB2b['cess'] + $hsnB2c['cess'], 2)) }}
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat(round($hsnB2b['totalTaxAmount'] + $hsnB2c['totalTaxAmount'], 2)) }}
                                </td>
                                <td class="border-bottom-primary" style="color: black !important; ">
                                    {{ getCurrencySymbol() . getCurrencyFormat(round($hsnB2b['invoiceAmount'] + $hsnB2c['invoiceAmount'], 2)) }}
                                </td>
                            </tr>
                            {{-- <tr> --}}
                            {{-- <td class="border-right-primary border-bottom-primary"></td> --}}
                            {{-- <td class="border-right-primary border-bottom-primary">HSN/SAC Summary</td> --}}
                            {{-- <td class="border-right-primary border-bottom-primary text-right">
                                    <a href="{{ route('company.hsn-summary-outward-report', ['startDate' => $data['start_date'], 'endDate' => $data['end_date']]) }}"
                                        class="text-decoration-underline">HSNB2C</a>
                                </td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat(round($hsnB2c['invoiceTaxableAmount'],2)) }}</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat(round($hsnB2c['igst'],2)) }}</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat(round($hsnB2c['cgst'],2)) }}</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat(round($hsnB2c['sgst'],2)) }}</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat(round($hsnB2c['cess'],2)) }}</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat(round($hsnB2c['totalTaxAmount'],2)) }}</td>
                                <td class="border-bottom-primary" style="color: black !important; ">
                                    {{ getCurrencySymbol() . getCurrencyFormat(round($hsnB2c['invoiceAmount'],2)) }}</td> --}}
                            {{-- </tr> --}}
                            @php
                                $fixedDigitNumber = getCompanyFixedDigitNumber() ?? 2;
                                $differenceTotalGrandAmount = bcsub(
                                    $totalGrandAmount,
                                    $hsnB2b['invoiceAmount'] + $hsnB2c['invoiceAmount'],
                                    $fixedDigitNumber,
                                );
                                $differenceTotalTaxAmount = bcsub(
                                    $totalTaxAmount,
                                    $hsnB2b['totalTaxAmount'] + $hsnB2c['totalTaxAmount'],
                                    $fixedDigitNumber,
                                );
                                $differenceTotalCess = bcsub(
                                    $totalCess,
                                    round($hsnB2b['cess'], 2) + round($hsnB2c['cess'], 2),
                                    $fixedDigitNumber,
                                );
                                $differenceTotalCgst = bcsub(
                                    $totalCgst,
                                    round($hsnB2b['cgst'], 2) + round($hsnB2c['cgst'], 2),
                                    $fixedDigitNumber,
                                );
                                $differenceTotalSgst = bcsub(
                                    $totalSgst,
                                    round($hsnB2b['sgst'], 2) + round($hsnB2c['sgst'], 2),
                                    $fixedDigitNumber,
                                );
                                $differenceTotalIgst = bcsub(
                                    $totalIgst,
                                    round($hsnB2b['igst'], 2) + round($hsnB2c['igst'], 2),
                                    $fixedDigitNumber,
                                );
                                $differenceTotalTaxableAmount = bcsub(
                                    $totalTaxableAmount,
                                    $hsnB2b['invoiceTaxableAmount'] + $hsnB2c['invoiceTaxableAmount'],
                                    $fixedDigitNumber,
                                );
                            @endphp
                            <tr style="background: #e6e6e6;">
                                <td class="border-right-primary border-bottom-primary"
                                    style="border-bottom-left-radius: 0.625rem;"></td>
                                <td class="border-right-primary border-bottom-primary">Difference</td>
                                <td class="border-right-primary border-bottom-primary"></td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($differenceTotalTaxableAmount) }}</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($differenceTotalIgst) }}</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($differenceTotalSgst) }}</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($differenceTotalCgst) }}</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($differenceTotalCess) }}</td>
                                <td class="border-right-primary border-bottom-primary" style="color: black !important;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($differenceTotalTaxAmount) }}</td>
                                <td class=" border-bottom-primary" style=";border-bottom-right-radius: 0.625rem;">
                                    {{ getCurrencySymbol() . getCurrencyFormat($differenceTotalGrandAmount) }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                @if ($gstLogin?->status)
                    {{-- <div class="card mt-5" style="min-width:380px; max-height:320px">
                        <div class=" card-body">
                            <div class="d-flex">
                                <div class=" card-border-1 border-primary p-3 w-100 upload-status-content">
                                    <div class="d-flex justify-content-between align-items-center">
                                    <h4 class="mt-3">GSTR1 Upload Status</h4>
                                        <button class="btn btn-primary btn-sm float-end " id="gstr1Reload" data-start-date={{ $data['start_date'] }} data-end-date="{{ $data['end_date'] }}" style="padding: 4px 15px;border-radius:50px">
                                        Reload
                                        </button>
                                    </div>
                                    <hr>
                                    <div>
                                        <div class="form-check m-0 p-3 @if ($gstLogin) checked @endif">
                                            <input class="form-check-input ms-0" type="checkbox" value=""
                                                id="flexCheckDefault" @if ($gstLogin) checked @endif disabled>
                                            <label class="form-check-label ms-3 fw-bold" for="flexCheckDefault">
                                                Connect to GSTN
                                            </label>
                                        </div>
                                        <div class="form-check m-0 p-3 @if ($gstStatus?->status) checked @endif">
                                            <input class="form-check-input ms-0" type="checkbox" value=""
                                                id="flexCheckDefault" @if ($gstStatus?->status) checked  @endif disabled>
                                            <label class="form-check-label ms-3 fw-bold" for="flexCheckDefault">
                                                Save Data on GSTN
                                            </label>
                                        </div>
                                        <div class="form-check m-0 p-3">
                                            <input class="form-check-input ms-0 @if ($gstStatus?->status == App\Models\GstFiling::STATUS['P']) checked @endif" type="checkbox" value=""
                                                id="flexCheckDefault" @if ($gstStatus?->status == App\Models\GstFiling::STATUS['P']) checked  @endif disabled>
                                            <label class="form-check-label ms-3 fw-bold" for="flexCheckDefault">
                                                Upload status on GSTN
                                            </label>
                                        </div>
                                        <div class="form-check m-0 p-3">
                                            <input class="form-check-input ms-0" type="checkbox" value=""
                                                id="flexCheckDefault" disabled>
                                            <label class="form-check-label ms-3 fw-bold" for="flexCheckDefault">
                                                Get summary by GSTN
                                            </label>
                                        </div>
                                    </div>
                                        <button class="btn btn-primary btn-sm float-end" id="resetGstData" data-start-date={{ $data['start_date'] }} data-end-date="{{ $data['end_date'] }}" style="padding: 4px 15px;border-radius:50px">
                                        Reset
                                        </button>
                                </div>
                            </div>
                        </div>
                    </div> --}}
                @endif
                {{-- @if ($user->roles[0]->name == 'consultant') --}}
                    @if ($gstLogin?->status)
                        <div class="filing-satuts mt-10">
                            <div class="d-flex gap-3 align-items-center mb-3">
                                <h4 class="mb-0 fw-5 fs-16">Filing Status</h4>
                                {{-- @dd((empty($gstFiling) || !$gstFiling->gstr_1_filed),$gstStatus->status); --}}
                                @if ((empty($gstFiling) || !$gstFiling->gstr_1_filed))
                                    {{-- <div class="text-orange fs-14">
                                (Upload initiated : 21 min ago)
                            </div> --}}
                                @if(isset($gstStatus) && $gstStatus->status != App\Models\GstFiling::STATUS['FILED'])
                                    <button id="gstr1Reload" data-start-date={{ $data['start_date'] }}
                                        data-end-date="{{ $data['end_date'] }}"
                                        style="background: transparent; border: none; padding: 0;">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="23"
                                            viewBox="0 0 20 23" fill="none">
                                            <path
                                                d="M12 1.5C12 1.5 12.8492 1.62132 16.364 5.13604C19.8787 8.65076 19.8787 14.3492 16.364 17.864C15.1187 19.1092 13.5993 19.9133 12 20.2762M12 1.5L18 1.5M12 1.5L12 7.5M8 21.4998C8 21.4998 7.15076 21.3785 3.63604 17.8638C0.12132 14.349 0.12132 8.65056 3.63604 5.13584C4.88131 3.89057 6.40072 3.0865 8 2.72363M8 21.4998L2 21.5M8 21.4998L8 15.5"
                                                stroke="#181C32" stroke-width="2" stroke-linecap="round"
                                                stroke-linejoin="round" />
                                        </svg>
                                    </button>
                                @endif
                                @else
                                    <button class="btn btn-green-gstr d-flex gap-2 align-items-center">
                                        <img src="{{ asset('assets/images/gstr-step-3.png') }}" alt="images" />
                                        Filed
                                    </button>
                                @endif
                            </div>
                            <div class="filing-status-box mb-5">
                                <div class="d-flex gap-2 align-items-start filing-content mb-2">
                                    <div class="filing-icons position-relative">
                                        @if (empty($gstStatus) && (empty($gstFiling) || !$gstFiling->gstr_1_filed))
                                            <div class="step-1">1</div>
                                        @else
                                            <div class="step-3">
                                                <img src="{{ asset('assets/images/gstr-step-3.png') }}"
                                                    alt="images" />
                                            </div>
                                        @endif
                                    </div>
                                    <p class="fs-16 fw-5 mb-0">Sending Request to GSTN</p>
                                </div>
                                <div class="d-flex gap-2 align-items-start filing-content mb-2">
                                    <div class="filing-icons position-relative">
                                        @if (empty($gstStatus) && (empty($gstFiling) || !$gstFiling->gstr_1_filed))
                                            <div class="step-1">2</div>
                                        @else
                                            <div class="step-3">
                                               <img src="{{ asset('assets/images/gstr-step-3.png') }}"
                                                    alt="images" />
                                            </div>
                                        @endif

                                    </div>
                                    <p class="fs-16 fw-5 mb-0">Request Received by GSTN</p>
                                </div>
                                <div class="d-flex gap-2 align-items-start">
                                    <div>
                                        @if (empty($gstStatus) && (empty($gstFiling) || !$gstFiling->gstr_1_filed))
                                            <div class="step-1">3</div>
                                        @elseif($gstStatus->status == App\Models\GstFiling::STATUS['PE'])
                                            {{-- <div class="step-3"> --}}
                                                <img src="{{ asset('assets/images/error_symbols.png') }}"
                                                    alt="images" />

                                            {{-- </div> --}}
                                        @elseif((!empty($gstFiling) && $gstFiling->gstr_1_filed) || $gstStatus->status == App\Models\GstFiling::STATUS['P'] || $gstStatus->status == App\Models\GstFiling::STATUS['FILED'])
                                            <div class="step-3">
                                                <img src="{{ asset('assets/images/gstr-step-3.png') }}"
                                                    alt="images" />
                                            </div>
                                        @elseif($gstStatus->status == App\Models\GstFiling::STATUS['IP'])
                                            <div class="step-2">
                                                <img src="{{ asset('assets/images/gstr-step-2.gif') }}" alt="images"
                                                    class="h-100 w-100" />
                                            </div>
                                            @else
                                            <img src="{{ asset('assets/images/error_symbols.png') }}"
                                                    alt="images" />
                                        @endif
                                    </div>
                                    @if(!empty($gstStatus) && $gstStatus->status == App\Models\GstFiling::STATUS['PE'])
                                        <span class="fs-16 fw-5 mb-0 text-danger">Error in Processing</span>
                                        <a href="{{ route('company.reports.gstr1-error-report-excel', ['start_date' => $data['start_date'], 'end_date' => $data['end_date']]) }}" class="btn gst-error-btn">
                                            <img src="{{ asset('assets/images/download.svg') }}" alt="images"> Error Sheet</a>
                                    @elseif (!empty($gstStatus) && $gstStatus->status == App\Models\GstFiling::STATUS['ER'])
                                        <p class="fs-16 fw-5 mb-0">Error</p>
                                    @else
                                        <p class="fs-16 fw-5 mb-0">Processed</p>
                                    @endif
                                </div>
                            </div>

                            @if ((empty($gstStatus) || $gstStatus->status == App\Models\GstFiling::STATUS['PE'] || $gstStatus->status == App\Models\GstFiling::STATUS['ER']) && (empty($gstFiling) || !$gstFiling->gstr_1_filed))
                                <button class="btn btn-primary" id="fileGstR1Button"
                                    data-start-date="{{ $data['start_date'] }}"
                                    data-end-date="{{ $data['end_date'] }}">
                                    Upload to GST Portal
                                </button>
                            @elseif($gstStatus->status == App\Models\GstFiling::STATUS['IP'])
                                <button class="btn btn-secondary">
                                    Uploading...............
                                </button>
                            @elseif(!empty($gstFiling) && $gstFiling->gstr_1_filed)
                                {{-- <button class="btn btn-primary">
                                    Preview Form
                                </button> --}}
                            @elseif(!empty($gstStatus) && $gstStatus->status == App\Models\GstFiling::STATUS['FILED'])
                          {{-- <button class="btn btn-primary">
                                    Preview Form
                                </button> --}}
                            @else
                                <div class="alert-box-gstr d-flex align-items-center gap-3 mb-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                        viewBox="0 0 20 20" fill="none">
                                        <path
                                            d="M10 5.40801L16.275 16.2497H3.725L10 5.40801ZM10 2.08301L0.833336 17.9163H19.1667L10 2.08301Z"
                                            fill="#D65907" />
                                        <path d="M10.8333 13.7497H9.16667V15.4163H10.8333V13.7497Z" fill="#D65907" />
                                        <path d="M10.8333 8.74967H9.16667V12.9163H10.8333V8.74967Z" fill="#D65907" />
                                    </svg>
                                    <p class="fs-16 fw-5 mb-0">By continuing to file hisabkitab will submit & file you
                                        GSTR-1 Return on the GST Portal. This action is not reversable. Please make sure
                                        everything is correct by reviewing your summary of the return.</p>
                                </div>
                                <div class="d-flex gap-3 align-items-center">
                                    {{-- <a href="{{ route('company.get-gstr1-report-preview', ['start_date' => $data['start_date'], 'end_date' => $data['end_date']]) }}" class="btn btn-primary">Preview Form</a> --}}
                                    <button class="btn btn-primary" id="fileGstR1EVCButton"
                                                data-start-date={{ $data['start_date'] }}
                                                data-end-date="{{ $data['end_date'] }}">
                                        File with EVC OTP
                                    </button>
                                    {{-- <button class="btn-sm btn-primary" id="fileGstR1EVCButton"
                                                data-start-date={{ $data['start_date'] }}
                                                data-end-date="{{ $data['end_date'] }}">File with EVC OTP</button> --}}
                                    <button class="btn btn-secondary" id="resetGstData"
                                        data-start-date="{{ $data['start_date'] }}"
                                        data-end-date="{{ $data['end_date'] }}">
                                        Reset Data
                                    </button>
                                </div>

                            @endif
                        </div>
                    @endif
                {{-- @endif --}}
            </div>
        </div>
    </div>
@endsection
@include('company.gst-reports.gstr-1.gstr1-file-otp-modal')
@include('company.gst-reports.gstr-1.gstr1-file-pan-number-modal')
@include('company.gst-reports.gstr-1.gst-portal-login-modal')
@include('company.gst-reports.gstr-1.congratulations-modal')
