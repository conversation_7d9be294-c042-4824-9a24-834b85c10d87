<div class="modal fade login-gst-portal-modal" tabindex="-1" id="loginGSTPortalModal" style="display: none">
    <div class="modal-dialog">
        <div class="modal-content h-100 overflow-hidden">
            <div class="offcanvas-header bg-white">
                <h3 class="mb-0">Login to GST Portal</h3>
                <button class="btn close-button p-0" type="button" data-bs-dismiss="modal"
                aria-label="Close">×</button></div>
            {{-- <div class="modal-header py-3 bg-primary">
                <h5 class="modal-title text-white">Login to GST Portal</h5>
                <div class="btn btn-icon btn-sm text-white btn-active-light-primary ms-2" data-bs-dismiss="modal"
                    aria-label="Close">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                        fill="none">
                        <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1"
                            transform="rotate(-45 6 17.3137)" fill="currentColor"></rect>
                        <rect x="7.41422" y="6" width="16" height="2" rx="1"
                            transform="rotate(45 7.41422 6)" fill="currentColor"></rect>
                    </svg>
                </div>
            </div> --}}
            <div class="modal-body">
                {{ Form::open(['method' => 'POST', 'id' => 'loginGSTPortalForm']) }}
                <div class="p-0">
                    @csrf
                    {{ Form::hidden('date',null,['id' => 'GstFileingDate']) }}
                  <div class="gstr-card-body mb-8">
                    <p class="mb-8">
                        <a href="{{ asset('gst_api_access_guidelines/gst_api_access_guidelines.pdf') }}"
                            class="text-decoration-none text-primary ms-3 " target="_blank">
                            Login to your GST portal and follow the PDF guide. <span class="bi bi-box-arrow-up-right fs-6"></span>
                        </a>
                    </p>
                    <div class="mb-5 edit-form-floating position-relative">
                        {{ Form::text('gst_number',  getCurrentCompany()->companyTax->gstin, ['class' => 'form-control edit-floating-input','readonly', 'required', 'id' => 'gstNumber']) }}
                        {{ Form::label('gst_number', 'GSTIN', ['class' => 'form-label px-1 mb-0 required']) }}
                    </div>
                    <div class="edit-form-floating position-relative">
                        {{ Form::text('user_name', getGstDashboardLogin('username'), ['class' => 'form-control edit-floating-input', 'required', 'id' => 'gstLoginUser']) }}
                        {{ Form::label('user_name', 'User Name', ['class' => 'form-label px-1 mb-0 required']) }}
                    </div>
                    <div class="edit-form-floating position-relative mt-7 gst-otp d-none">
                        {{ Form::text('otp', null, ['class' => 'form-control edit-floating-input', 'id' => 'otpForGstr1','maxlength' => 6]) }}
                        {{ Form::label('otp', 'Enter OTP', ['class' => 'form-label px-1 mb-0 required']) }}
                        <span class="btn btn-sm btn-icon position-absolute translate-middle top-50 end-0 pb-5 mt-2"
                              data-kt-password-meter-control="visibility">
                                 <i class="bi bi-eye-slash fs-2 text-gray-900"></i>
                                  <i class="bi bi-eye fs-2 text-gray-900 d-none"></i>
                        </span>
                    </div>
                  </div>
                  <div class="d-flex gap-2 send-gst-otp">
                    <div class="float-start">
                        {{ Form::submit('Generate OTP', ['class' => 'btn btn-primary ']) }}
                    </div>
                    <div class="float-start">
                        {{ Form::button('Back', ['class' => 'btn btn-secondary', 'data-bs-dismiss' => 'modal']) }}
                    </div>
                  </div>
                  <div class="gst-otp d-none">
                    <p class="fs-14 text-end mb-3">Didn't receive OTP? <a href="" class="text-orange text-decoration-none">Regenerate OTP</a></p>
                    <div class="d-flex gap-2">
                        <div class="float-start">
                            {{ Form::submit('Connect to GSTN', ['class' => 'btn btn-primary']) }}
                        </div>
                        <div class="float-start">
                            {{ Form::button('Back', ['class' => 'btn btn-secondary', 'data-bs-dismiss' => 'modal']) }}
                        </div>
                    </div>
                  </div>

                </div>
                {{ Form::close() }}
            </div>
        </div>
    </div>
</div>
