@extends('company.layouts.app')
@section('title')
    Compose Email
@endsection
@section('content')
    <div id="kt_content_container" class="container">
        <div class="d-flex flex-column flex-lg-row">
            <div class="flex-lg-row-fluid mb-10 mb-lg-0 me-lg-7 me-xl-10">
                <div class="row">
                    <div class="col-12">
                        @include('flash::message')
                        @include('layouts.errors')
                    </div>
                </div>
                @if($type == 'purchase')
                    {{ Form::open(['route' => ['company.purchases-send-email','purchase'=> $sale->id],'method' => 'POST','id'=>'saleSendEmailForm','enctype'=>'multipart/form-data']) }}
                @elseif($type == 'purchaseReturn')
                    {{ Form::open(['route' => ['company.purchase-returns-send-email','purchase_return'=> $sale->id],'method' => 'POST','id'=>'saleSendEmailForm','enctype'=>'multipart/form-data']) }}
                @elseif($type == 'expenseDebitNote')
                    {{ Form::open(['route' => ['company.expense-debit-notes-send-email','expense_debit_note'=> $sale->id],'method' => 'POST','id'=>'saleSendEmailForm','enctype'=>'multipart/form-data']) }}
                @endif
                <div class="card card-border-1">
                    <div class="card-body ">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-12 mb-3">
                                {{ Form::label('to', 'To:', ['class' => 'form-label fs-6 required fw-bolder text-gray-900 mb-0']) }}
                                {{ Form::text('to', $sale->supplier->model->person_email, ['class' => 'form-control','id'=>'saleEmail', 'required']) }}
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-12 mb-3">
                                {{ Form::label('cc', 'CC:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 mb-0']) }}
                                {{ Form::text('cc', null, ['class' => 'form-control','id'=>'saleEmail','placeholder' => 'CC (comma separated email addresses)']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-12 col-md-6 col-sm-6 col-12 mb-3">
                            <div class="col-md-6 col-12 mb-3">
                                {{ Form::label('subject', 'Subject:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 mb-0']) }}
                                {{ Form::text('subject', $notificationTemplate->subject ?? null,['class' => 'form-control']) }}
                            </div>
                              {{ Form::hidden('body_data',$notificationTemplate->body ?? null,['id' => 'saleNotificationTemplateData']) }}
                              <div class="col-md-12 col-12 mb-5">
                                 {{ Form::label('body', 'Body:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 mb-1']) }}
                                 <div id="editor-container">
                                    <div id="saleNotificationTemplateBody" name="body" style="height: 230px"></div>
                                    {{ Form::hidden('body', null, ['id' => 'salebBodyData']) }}
                                 </div>
                              </div>
                              <div class="col-md-6 col-12 mb-3">
                                 {{ Form::label('regards', 'Footer:', ['class' => 'form-label fs-6 fw-bolder mail-required-label text-gray-900  mb-1']) }}
                                 {{ Form::textarea('regards', $notificationTemplate->regards ?? null, ['class' => 'form-control mail-credential','id'=>'regards','rows' => 3]) }}
                              </div>
                            </div>
                        </div>
                        {{ Form::hidden('is_attachment',$notificationTemplate->is_attachment, null) }}
                        @if($notificationTemplate->is_attachment)
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-12 mb-3">
                                {{ Form::label('file', 'Attachment:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 mb-0']) }}<br>
                                <div class="file-upload-wrapper" data-text="Upload!">
                                    <input name="attachments[]" type="file" multiple class="file-upload-field form-control email-attachments" value="">
                                </div>
                                <p class="file-list"></p>
                            </div>
                        </div>
                        {{ Form::hidden('removed_attachment[]', null, ['id' => 'removedAttachment']) }}
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-12 mb-3">
                                {{ Form::label('file', 'Attach Invoice PDF:', ['class' => 'form-label fs-6 fw-bolder text-gray-900 mb-0']) }}<br>
                                @if($type == 'purchase')
                                    <a href="{{route('company.purchase-pdf-download',['purchasePdfDownloadId'=> $sale->id, 'isView' => true])}}" >{{$sale->voucher_number}}</a>
                                @elseif($type == 'purchaseReturn')
                                    <a href="{{route('company.purchase-return-pdf-download',['purchaseReturnPdfDownloadId'=> $sale->id, 'isView' => true])}}" >{{$sale->supplier_purchase_return_number}}</a>
                                @elseif($type == 'expenseDebitNote')
                                    <a href="{{route('company.expense-dn-pdf-download',['expenseDnDownloadPdfId'=> $sale->id, 'isView' => true])}}" >{{$sale->voucher_number}}</a>
                                @endif
                            </div>
                        </div>
                        @endif
                        <div class="row">
                            <div class="d-flex mt-5">
                                <button type="submit" name="submit_button" class="btn btn-primary me-2 click-sale-btn">Send
                                </button>
                                <a href="{{ route('company.sales.index') }}" type="button" name="submit_button" class="btn btn-light btn-active-light-primary">Cancel
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {{ Form::close() }}
            </div>
        </div>
    </div>
@endsection
