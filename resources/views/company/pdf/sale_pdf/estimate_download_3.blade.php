<!DOCTYPE html>
<html>
@php
    $pdfSymbol = getPrintPdfCurrencySymbol($currentCompany->id);
    $dockereEnabled = dockerEnabled() && !isset($preview_enabled);
    $fontStyleName = $dockereEnabled ? 'Arial Unicode MS' : 'Arial-unicode-ms';
    $fontCssName = $dockereEnabled ? 'font-docker.css' : 'font.css';
@endphp

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/' . $fontCssName) }}">
    <title>{{ isset($fileName) ? $fileName : $transaction->document_number }}</title>
    @include('company.pdf.sale_pdf.print_custom_font_css_variables')
    <style>
         * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "{{ $fontStyleName }}";
            font-size: 13px;
            font-weight: 400;
        }

        @page {
            margin: 20px !important;
        }

        .main-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .d-flex {
            display: flex;
        }

        .justify-content-center {
            justify-content: center;
        }

        .align-items-center {
            align-items: center;
        }

        .w-100 {
            width: 100%;
        }

        .w-60 {
            width: 60%
        }

        .h-100 {
            height: 100%;
        }

        table {
            max-width: 100%;
            width: 100%;
            border-collapse: collapse;
            display: table;
        }

        table td,
        table th {
            display: table-cell;
            vertical-align: top;
        }

        .lh-1-5 {
            line-height: 1.5;
        }

        .fs-10 {
            font-size: 10px;
        }

        .fs-11 {
            font-size: 11px;
        }

        .fs-12 {
            font-size: 12px;
        }

        .fs-13 {
            font-size: 13px;
        }

        .fs-14 {
            font-size: 14px;
        }

        .text-black {
            color: black;
        }

        .bg-black {
            background-color: var(--color-theme);
        }

        .text-white {
            color: white;
        }

        .text-nowrap {
            white-space: nowrap;
        }

        .jusstify-content-center {
            jusstify-content: center;
        }

        .break-text {
            word-break: break-all;
        }

        .justify-content-between {
            justify-content: space-between;
        }

        .fw-semibold {
            font-weight: 600;
        }

        .text-end {
            text-align: end;
        }

        .text-center {
            text-align: center;
        }

        .ms-auto {
            margin-left: auto;
        }

        .text-start {
            text-align: start;
        }

        .ls-1px {
            letter-spacing: 1px;
        }

        .w-50 {
            width: 50%
        }

        .px-4 {
            padding-left: 20px;
            padding-right: 20px;
        }

        .fs-24 {
            font-size: 24px;
        }

        .fs-22 {
            font-size: 24px;
        }

        .pt-3 {
            padding-top: 16px;
        }

        .pt-2 {
            padding-top: 8px;
        }

        .pb-1 {
            padding-bottom: 4px;
        }

        address {
            font-style: normal;
        }

        .text-decoration-none {
            text-decoration: none;
        }

        .logo-img {
            max-width: 170px;
            max-height: 90px;
            height: 90px;
        }

        .object-fit-cover {
            object-fit: cover;
        }

        .w-33 {
            width: 33.33%;
        }

        .pe-1 {
            padding-right: 4px;
        }

        .ps-4 {
            padding-left: 22px;
        }

        .pe-4 {
            padding-right: 22px;
        }

        .mb-4 {
            margin-bottom: 24px;
        }

        .py-1 {
            padding-top: 4px;
            padding-bottom: 4px;
        }

        .py-2 {
            padding-top: 8px;
            padding-bottom: 8px;
        }

        .px-3 {
            padding-left: 16px;
            padding-right: 16px;
        }

        .px-22px {
            padding-left: 22px;
            padding-right: 22px;
        }

        .px-2 {
            padding-left: 8px;
            padding-right: 8px;
        }

        .mb-3 {
            margin-bottom: 16px;
        }

        .sn-table tbody td {
            border-right: 1px solid #e2e2e2;
        }

        .sn-table tbody td:last-child {
            border-right: none !important;
        }

        .flex-grow-1 {
            flex-grow: 1;
        }

        .sn-table tbody tr:nth-last-child(2) td {
            height: 100%;
        }

        .flex-column {
            flex-direction: column;
        }

        .mb-1 {
            margin-bottom: 4px;
        }

        .qr-img {
            width: 75px;
            min-width: 75px;
            max-height: 75px;
        }

        .gap-20px {
            gap: 20px;
        }

        .sign {
            height: 120px;
            width: 180px;
            justify-content: center;
            overflow: hidden;
        }

        .mt-2 {
            margin-top: 8px;
        }

        .mb-2 {
            margin-bottom: 8px;
        }

        .mt-3 {
            margin-top: 16px;
        }

        .pe-2 {
            padding-right: 8px;
        }

        .mx-auto {
            margin: 0 auto;
        }

        .w-120px {
            min-width: 90px;
        }

        .px-12px {
            padding-left: 12px;
            padding-right: 12px;
        }

        .w-30px {
            min-width: 30px;
        }

        .fw-medium {
            font-weight: 500;
        }
        .ps-2 {
            padding-left: 8px;
        }
    </style>
    @include('company.pdf.sale_pdf.print_custom_font_css')
</head>

<body>
    <div class="main-container">
        {{-- Slogan / Invoice Type Section Start --}}
        <div class="px-2">
            {{-- Slogan Section Start --}}
            @if (($invoiceSetting['show_slogan'] ?? false) && isset($invoiceSetting['slogan']))
                <p class="text-black company-address-font-size lh-1-5 text-center mb-1">{{ $invoiceSetting['slogan'] }}</p>
            @endif
            {{-- Slogan Section End --}}

            {{-- Invoice Type Section Start --}}
            <div class="d-flex">
                <p class="fs-12 text-black lh-1-5 ls-1px">{{ $transaction->transactionTitle->name }}</p>
            </div>
            {{-- Invoice Type Section End --}}
        </div>
        {{-- Slogan / Invoice Type Section End --}}

        {{-- Company Details Section Start --}}
        <div class="px-2 mb-3 pt-3">
            <table>
                <tbody>
                    <tr>
                        <td class="w-50">
                            <div>
                                <p class="company-name-font-size company-font-customization fw-medium text-black lh-1-5 pb-0">
                                    {{ strtoupper($currentCompany->trade_name) }}</p>
                                    @if(($invoiceSetting['prop_details'] ?? true) && isset($customProp))
                                    <p class="mt-1 fw-6 company-address-font-size text-black">{{ $customProp->label_name.' : '.$customProp->label_value }}</p>
                                @endif
                                @if ($currentCompany->is_gst_applicable)
                                    <p class="company-address-font-size text-black lh-1-5">
                                        {{ $changeLabel['gstin'] ?? 'GSTIN' }}:
                                        {{ ' ' . $currentCompany->companyTax->gstin ?? null }}
                                    </p>
                                @endif
                                <address class="company-address-font-size text-black lh-1-5">
                                    {{ isset($companyBillingAddress->address_1) ? strtoupper($companyBillingAddress->address_1 . ',') : null }}
                                    {{ isset($companyBillingAddress->address_2) ? strtoupper($companyBillingAddress->address_2 . ',') : null }}
                                    {{ isset($companyBillingAddress->city_id) ? strtoupper(getCityName($companyBillingAddress->city_id) . ',') : null }}
                                    {{ isset($companyBillingAddress->state_id) ? strtoupper(getStateName($companyBillingAddress->state_id) . ',') : null }}
                                    {{ isset($companyBillingAddress->country_id) ? strtoupper(getCountryName($companyBillingAddress->country_id) . ',') : null }}
                                    {{ $companyBillingAddress->pin_code ?? null }}
                                </address>
                                @if ($invoiceSetting['estimate_mobile_number'] ?? true)
                                    <p class="d-flex text-black company-address-font-size lh-1-5">
                                        {{ $changeLabel['estimate_tel'] ?? 'Tel' }} :
                                        {{ (isset($invoiceSetting['region_code']) ? '+' . $invoiceSetting['region_code'] . ' ' : '') .
                                            (isset($invoiceSetting['alternate_phone'])
                                                ? $invoiceSetting['alternate_phone']
                                                : '+' . $currentCompany->user->region_code . ' ' . $currentCompany->user->phone) .
                                            (isset($invoiceSetting['region_code_2']) && isset($invoiceSetting['alternate_phone_2'])
                                                ? ', +' . $invoiceSetting['region_code_2'] . ' '
                                                : '') .
                                            (isset($invoiceSetting['alternate_phone_2'])
                                                ? (isset($invoiceSetting['region_code_2']) ? '' : ', ') . $invoiceSetting['alternate_phone_2']
                                                : '') }}
                                    </p>
                                @endif
                                @if ($invoiceSetting['estimate_email'] ?? true)
                                    <p class="d-flex text-black company-address-font-size lh-1-5">
                                        {{ $changeLabel['email'] ?? 'Email' }} :
                                        {{ $invoiceSetting['alternate_email'] ?? ($currentCompany->user->email ?? null) }}
                                    </p>
                                @endif
                            </div>
                        </td>
                        @if (isset($currentCompany->company_logo) &&
                                ($invoiceSetting['estimate_logo'] ?? true) &&
                                $currentCompany->company_logo != asset('images/preview-img.png'))
                            <td class="w-50">
                                <div class="logo-img ms-auto">
                                    <img src="{{ $currentCompany->company_logo ?? '' }}" class="h-100 ms-auto d-flex w-100" style="object-fit: contain" />
                                </div>
                            </td>
                        @endif
                    </tr>
                </tbody>
            </table>
        </div>
        {{-- Company Details Section End --}}

        {{-- PDF Invoice Details And Customer Section Start --}}
        <div class="px-2 mb-2" style="display:inline-flex; flex-wrap: wrap;">
            {{-- Invoice Details Section Start --}}
            <div class="w-33 mb-4">
                <table>
                    <tbody>
                        <tr>
                            <td class="text-black header-contents-font-size lh-1-5">
                                {{ $changeLabel['estimate_invoice_number'] ?? 'Estimate No' }}
                            </td>
                            <td class="text-black header-contents-font-size lh-1-5 w-60">
                                : {{ $transaction->document_number }}
                            </td>
                        </tr>
                        <tr>
                            <td class="text-black header-contents-font-size lh-1-5">
                                {{ $changeLabel['estimate_invoice_date'] ?? 'Estimate Date' }}
                            </td>
                            <td class="text-black header-contents-font-size lh-1-5 w-60">
                                : {{ Carbon\Carbon::parse($transaction->document_date)->format('d-m-Y') }}
                            </td>
                        </tr>
                        @if($invoiceSetting['show_estimate_due_date'] ?? true)
                        <tr>
                            <td class="text-black header-contents-font-size lh-1-5">
                                {{ $changeLabel['estimate_due_date'] ?? 'Due Date' }}
                             </td>
                            <td class="text-black header-contents-font-size lh-1-5 w-60">
                                : {{ $dueDate }}
                            </td>
                        </tr>
                        @endif
                        @if($invoiceSetting['show_estimate_credit_period'] ?? true)
                        <tr>
                            <td class="text-black header-contents-font-size lh-1-5">
                                {{ $changeLabel['estimate_credit_period'] ?? 'Credit Period' }}
                            </td>
                            <td class="text-black header-contents-font-size lh-1-5 w-60">
                                : {{ $creditPeriod }}
                            </td>
                        </tr>
                        @endif
                    </tbody>
                </table>
            </div>
            {{-- Invoice Details Section End --}}

            {{-- Bill To Section End --}}
            <div class="w-33 mb-4">
                <div>
                    <p class="text-black header-labels-font-size lh-1-5 fw-semibold">
                        {{ $changeLabel['estimate_bill_to'] ?? 'Estimate To:' }} :
                    </p>
                    <p class="text-black header-contents-font-size lh-1-5">{{ strtoupper($customerDetail->name) }}</p>
                    @if ($showGst)
                        <p class="text-black header-contents-font-size lh-1-5">
                            GSTIN: {{ $transaction->gstin ?? ($customerDetail->model->gstin ?? null) }}
                        </p>
                    @endif
                    @if (!empty($panNumber) && $showPanNumber)
                        <p class="text-black header-contents-font-size lh-1-5">
                            PAN: {{ $panNumber ?? null }}
                        </p>
                    @endif
                    @if (isset($billingAddress))
                        <address class="text-black header-contents-font-size lh-1-5 pe-4">
                            @if ($billingAddress->address_1 != null)
                                {{ strtoupper($billingAddress->address_1) }}
                            @endif
                            @if ($billingAddress->address_2 != null)
                                {{ strtoupper($billingAddress->address_2) }},
                            @endif
                            {{ $billingAddress->city_id != null ? strtoupper(getCityName($billingAddress->city_id)) . ',' : null }}
                            {{ $billingAddress->state_id != null ? strtoupper(getStateName($billingAddress->state_id)) . ',' : null }}
                            {{ strtoupper(getCountryName($billingAddress->country_id ?? null)) }},
                            {{ $billingAddress->pin_code ?? null }}
                        </address>
                    @endif
                    <p class="text-black header-contents-font-size lh-1-5 pe-4">
                        @if (!empty($transaction->party_phone_number))
                            Contact No:
                                +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                        @elseif (!empty($customerDetail->model->phone_1))
                            Contact No:
                                +{{ $customerDetail->model->region_code_1 ?? '' }} {{ $customerDetail->model->phone_1 ?? null }}
                        @endif
                        @if (!empty($customerDetail->model->phone_2) && ($customerDetail->model->phone_1 !== $customerDetail->model->phone_2))
                            {{ empty($customerDetail->model->phone_1) && empty($transaction->party_phone_number) ? 'Contact No:' : "|" }}
                            <span class="text-nowrap header-contents-font-size">
                                +{{ $customerDetail->model->region_code_2 ?? '' }} {{ $customerDetail->model->phone_2 ?? null }}
                            </span>
                        @endif
                    </p>
                    @if (!empty($customerDetail->model->person_email))
                        <p class="text-black header-contents-font-size lh-1-5">
                            Email: {{ $customerDetail->model->person_email ?? null }}
                        </p>
                    @endif
                </div>
            </div>
            {{-- Bill To Section End --}}

            {{-- Ship to Section Start --}}
            @if ($invoiceSetting['estimate_ship_to_details'] ?? true)
                <div class="w-33 mb-4">
                    <div>
                        <p class="text-black header-labels-font-size lh-1-5 fw-semibold">
                            {{ $changeLabel['estimate_ship_to'] ?? 'Ship to' }}:
                        </p>
                        <p class="text-black header-contents-font-size lh-1-5">
                            {{ strtoupper($transaction->shipping_name ?? $customerDetail->name) }}
                        </p>
                        @if ($isCompanyGstApplicable && !empty($transaction->shipping_gstin))
                            <p class="text-black header-contents-font-size lh-1-5">
                                GSTIN:
                                {{ $transaction->shipping_gstin ?? null }}
                            </p>
                        @endif
                        @if (!empty($panNumber) && $showPanNumber)
                            <p class="text-black header-contents-font-size1 lh-1-5">
                                PAN: {{ $panNumber ?? null }}
                            </p>
                        @endif
                        <address class="text-black header-contents-font-size1 lh-1-5">
                            @if (isset($shippingAddress->address_1))
                                {{ strtoupper($shippingAddress->address_1) }},
                            @endif
                            @if (isset($shippingAddress->address_2))
                                {{ strtoupper($shippingAddress->address_2) }},
                            @endif
                            {{ isset($shippingAddress->city_id) ? strtoupper(getCityName($shippingAddress->city_id)) . ',' : '' }}
                            {{ isset($shippingAddress->state_id) ? strtoupper(getStateName($shippingAddress->state_id)) . ',' : '' }}
                            {{ isset($shippingAddress->country_id) ? strtoupper(getCountryName($shippingAddress->country_id)) : '' }},
                            {{ $shippingAddress->pin_code ?? null }}
                        </address>
                        <p class="text-black header-contents-font-size lh-1-5">
                            @if (!empty($transaction->party_phone_number))
                                Contact No:
                                    +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                            @endif
                        </p>
                    </div>
                </div>
            @endif
            {{-- Ship to Section End --}}

            {{-- Po Details and Broker Details Section Start --}}
            @if(empty($transaction->valid_till_date) || ($invoiceSetting['estimate_po_number'] ?? true) || ($invoiceSetting['estimate_broker_details'] ?? true))
                <div class="w-33 mb-4">
                    <table>
                        <tbody>
                            @if ($invoiceSetting['estimate_po_number'] ?? true)
                                <tr>
                                    <td class="text-balck lh-1-5 header-contents-font-size">
                                        {{ $changeLabel['estimate_po_number_label'] ?? 'PO No' }}
                                    </td>
                                    <td class="text-balck lh-1-5 header-contents-font-size w-60">
                                        <span>: </span>
                                        {{ $transaction->po_no }}
                                    </td>
                                </tr>
                            @endif
                            @if($invoiceSetting['show_estimate_po_date'] ?? true)
                                <tr>
                                    <td class="text-balck lh-1-5 header-contents-font-size">
                                        {{ $changeLabel['estimate_po_date'] ?? 'PO Date' }}
                                    </td>
                                    <td class="text-balck lh-1-5 header-contents-font-size w-60">
                                        <span>: </span>
                                        {{ isset($transaction->po_date) ? \Carbon\Carbon::parse($transaction->po_date)->format('d-m-Y') : null }}
                                    </td>
                                </tr>
                            @endif
                            <tr>
                                <td class="text-balck lh-1-5 header-contents-font-size">
                                    {{ $changeLabel['valid_for'] ?? 'Valid For' }}
                                </td>
                                <td class="text-balck lh-1-5 header-contents-font-size w-60">
                                    <span>: </span>
                                    {{ $validFor }}
                                    {{ !empty($transaction->valid_till_date) ? ' (' . Carbon\Carbon::parse($transaction->valid_till_date)->format('d-m-Y') . ')' : '' }}
                                </td>
                            </tr>
                            @if ($invoiceSetting['estimate_broker_details'] ?? true)
                                <tr>
                                    <td class="text-balck lh-1-5 header-contents-font-size">Broker </td>
                                    <td class="text-balck lh-1-5 header-contents-font-size w-60">
                                        <span>: </span>
                                        {{ $transaction->brokerDetails->broker_name ?? '' }}
                                    </td>
                                </tr>
                                @if($isCompanyGstApplicable)
                                <tr>
                                    <td class="text-balck lh-1-5 header-contents-font-size">GSTIN </td>
                                    <td class="text-balck lh-1-5 header-contents-font-size w-60">
                                        <span>: </span>
                                        {{ $transaction->brokerDetails->gstin ?? '' }}
                                    </td>
                                </tr>
                                @endif
                            @endif
                        </tbody>
                    </table>
                </div>
            @endif
            {{-- Po Details and Broker Details Section End --}}

            {{-- transport Details Section Start --}}
            @if (($invoiceSetting['estimate_transport_details'] ?? true) || !empty($transaction->transporter_vehicle_number))
                <div class="w-33 mb-4">
                    <table>
                        <tbody>
                            @if ($invoiceSetting['estimate_transport_details'] ?? true)
                                <tr>
                                    <td class="text-black lh-1-5 header-contents-font-size">
                                        {{ $changeLabel['estimate_transport_name'] ?? 'Transport Name' }}
                                    </td>
                                    <td class="text-black lh-1-5 w-60 header-contents-font-size">
                                        <span>: </span>
                                        {{ $transaction->transportDetails->transporter_name ?? '' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-black lh-1-5 header-contents-font-size">
                                        {{ $changeLabel['estimate_document_no'] ?? 'Document No' }}
                                    </td>
                                    <td class="text-black lh-1-5 w-60 header-contents-font-size ">
                                        <span>: </span>
                                        {{ $transaction->transporter_document_number ?? '' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-black lh-1-5 header-contents-font-size">
                                        {{ $changeLabel['estimate_document_date'] ?? 'Document Date' }}
                                    </td>
                                    <td class="text-black lh-1-5 header-contents-font-size w-60">
                                        <span>: </span>
                                        {{ !empty($transaction->transporter_document_date) ? \Carbon\Carbon::parse($transaction->transporter_document_date)->format('d-m-Y') : '' }}
                                    </td>
                                </tr>
                            @endif
                            @if (!empty($transaction->transporter_vehicle_number))
                                <tr>
                                    <td class="text-black lh-1-5 header-contents-font-size">
                                        {{ $changeLabel['estimate_transport_vehicle_number'] ?? 'Vehicle Number' }}
                                    </td>
                                    <td class="text-black lh-1-5 header-contents-font-size w-60">
                                        <span>: </span>
                                        {{ $transaction->transporter_vehicle_number ?? '' }}
                                    </td>
                                </tr>
                            @endif
                        </tbody>
                    </table>
                </div>
            @endif
            {{-- transport Details Section End --}}

            {{-- Custom Details Section Start --}}
            @if (!is_null(printCustomPDFLabelsForEstimate()))
                <div class="w-33 mb-4">
                    <table>
                        <tbody>
                            @foreach (printCustomPDFLabelsForEstimate() as $key => $customLabel)
                                <tr>
                                    <td class="text-black header-contents-font-size lh-1-5">{{ $key ?? null }}</td>
                                    <td class="text-black header-contents-font-size lh-1-5 w-60">
                                        <span>: </span>
                                        {{ $customLabel ?? null }}
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @endif
            {{-- Custom Details Section End --}}
        </div>
        {{-- PDF Invoice Details And Customer Section End --}}

        {{-- Custom Fields Section Start --}}
        @if (count($customFieldValues) > 0)
            @php
                $customFields = collect($customFieldValues)->where('is_show_in_print', true)->values();
            @endphp
            <div class="px-2 mb-3">
                <table cellpadding="0">
                    @foreach ($customFields->chunk(3) as $chunk)
                        <tr>
                            @foreach ($chunk as $customField)
                                <td class="text-balck fs-11">
                                    <span class="lh-1-5 fw-semibold">{{ $customField['label_name'] ?? '' }}</span> : {{ $customField['value'] ?? '' }}
                                </td>
                            @endforeach
                        </tr>
                    @endforeach
                </table>
            </div>
        @endif
        {{-- Custom Fields Section End --}}

        {{-- Item Table Section Start --}}
        @php
            $customFieldItemsValues = collect($transactionItems[0]['customItemsValues'] ?? $transactionLedgers[0]['customItemsValues'])->where('is_show_in_print', true)->values();
            $customFieldItemsHeaders = collect($customFieldItemsValues)->pluck('label_name')->toArray();
        @endphp
        <div class="mb-4 d-flex flex-grow-1">
                <table class="sn-table">
                    <thead>
                        <tr>
                            @foreach ($rearrangeItems['headings'] as $headings)
                                @if($headings['is_show_in_print'])
                                    <th class="px-2 text-white bg-black lh-1-5 py-2 text-center table-headings-font-size {{ ($loop->index === $loop->count - 2) ? '' : 'border-right' }} {{ $headings['class'] }}"
                                        style="padding: 6px 8px; font-weight: bold;">
                                        {{ $headings['name'] }}
                                    </th>
                                @endif
                            @endforeach
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($rearrangeItems['detail']  as $key => $items)
                        <tr class="item-table-main-content">
                            @foreach ($items as  $key => $item)
                                @if($item['is_show_in_print'])
                                    <td class="table-contents-font-size lh-1-5 px-2 pt-2 pb-1 {{ ($key == 'item_name') ? 'min-width-150' : 'text-center'}}"
                                        style="padding: 4px 8px 0 8px;  {{ ($key == 'primary_quantity' || $key == 'secondary_quantity') ? 'white-space: nowrap;' : '' }}">
                                        {{ $item['value'] }}
                                        @if($key == 'item_name')
                                            @if($item['show_sku'])
                                                <p class="description-font-size">Item Code:
                                                    {{ $item['sku'] ?? null }}</p>
                                            @endif
                                            @if($item['show_consolidating_items'])
                                                <p style="word-break: break-word; " class="description-font-size">
                                                    {!! $item['consolidating_items'] !!}
                                                </p>
                                            @endif
                                            @if($item['show_additional_description'])
                                                <p style="word-break: break-word;" class="description-font-size">
                                                     {!! $item['additional_description'] !!}
                                                </p>
                                            @endif
                                            @if($item['show_item_image'])
                                                <div><img src="{{ $item['item_image'] }}" width="60"  height="60" style="margin-top: 4px"></div>
                                            @endif
                                        @endif
                                    </td>
                                @endif
                            @endforeach
                        </tr>
                        @endforeach

                        {{-- Additional/ Taxable Amount / GST Tax Section Start --}}
                        <tr>
                            <td></td>
                            <td class="table-contents-font-size lh-1-5 text-start pt-2 pb-1 text-nowrap">
                                @foreach ($additionalCharges as $additionalCharge)
                                    <p class="table-contents-font-size lh-1-5 px-2 text-start text-nowrap">
                                        {{ $additionalCharge['ledger_name'] }}
                                    </p>
                                @endforeach
                                <p class="table-contents-font-size lh-1-5 px-2 text-start text-nowrap fw-bold">
                                    {{ $changeLabel['sub_total'] ?? 'Sub Total' }}:
                                </p>
                                @if ($isCompanyGstApplicable && $transaction->cgst != 0)
                                    <p class="table-contents-font-size lh-1-5 px-2 text-start text-nowrap">
                                        {{ $changeLabel['cgst'] ?? 'CGST' }}
                                    </p>
                                @endif
                                @if ($isCompanyGstApplicable && $transaction->sgst != 0)
                                    <p class="table-contents-font-size lh-1-5 px-2 text-start text-nowrap">
                                        {{ $changeLabel['sgst'] ?? 'SGST' }}
                                    </p>
                                @endif
                                @if ($isCompanyGstApplicable && $transaction->igst != 0)
                                    <p class="table-contents-font-size lh-1-5 px-2 text-start text-nowrap">
                                        {{ $changeLabel['igst'] ?? 'IGST' }}
                                    </p>
                                @endif
                                @if ($transaction->tcs_amount != 0)
                                    <p class="table-contents-font-size lh-1-5 px-2 text-start text-nowrap">
                                        {{ $changeLabel['tcs'] ?? 'TCS' }}
                                    </p>
                                @endif
                                @if ($transaction->cess != 0)
                                    <p class="table-contents-font-size lh-1-5 px-2 text-start text-nowrap">
                                        {{ $changeLabel['cess'] ?? 'Cess' }}
                                    </p>
                                @endif
                                <p class="table-contents-font-size lh-1-5 px-2 text-start text-nowrap fw-bold">
                                    {{ $changeLabel['round_off'] ?? 'Round off' }}:
                                </p>
                                @foreach (collect($addLess)->where('is_show_in_print',1) as $addLessItem)
                                    <p class="table-contents-font-size lh-1-5 px-2 text-start text-nowrap">
                                        {{ $addLessItem['ledger_name'] }}
                                    </p>
                                @endforeach
                            </td>
                                @php
                                    $lastIndex = array_key_last($rearrangeItems['headings']);
                                    $filteredKeys = array_keys($rearrangeItems['headings']);

                                    // Get last 2 keys (preserve original keys)
                                    $lastTwoKeys = array_slice($filteredKeys, -2, 2, true);
                                    $firstTwoKeys = [0, 1];

                                    // Merge keys to unset
                                    $unset = array_merge($firstTwoKeys, array_keys($lastTwoKeys));

                                    // Filter out the unwanted keys
                                    $filtered = collect($rearrangeItems['headings'])
                                        ->filter(function ($value, $key) use ($unset) {
                                            return !in_array($key, $unset);
                                        })
                                        ->values()
                                        ->all();
                                @endphp
                            @foreach ($filtered as $headings)
                                @if($headings['is_show_in_print'])
                                    <td class="table-contents-font-size lh-1-5 text-end px-3 pt-2 pb-1 text-nowrap"></td>
                                @endif
                            @endforeach
                            <td class="table-contents-font-size lh-1-5 text-end px-3 pt-2 pb-1 text-nowrap">
                                @foreach ($additionalCharges as $additionalCharge)
                                    <p class="table-contents-font-size lh-1-5 text-end text-nowrap">
                                        {{ $pdfSymbol . getCurrencyFormat($additionalCharge['amount'] ?? '0.0') }}
                                    </p>
                                @endforeach
                                <p class="table-contents-font-size lh-1-5 text-end text-nowrap">
                                    {{ $pdfSymbol . getCurrencyFormat(round($transaction->taxable_value, getCompanyFixedDigitNumber())) }}
                                </p>
                                @if ($isCompanyGstApplicable && $transaction->cgst != 0)
                                    <p class="table-contents-font-size lh-1-5 text-end text-nowrap">
                                        {{ $pdfSymbol . getCurrencyFormat($transaction->cgst) }}
                                    </p>
                                @endif
                                @if ($isCompanyGstApplicable && $transaction->sgst != 0)
                                    <p class="table-contents-font-size lh-1-5 text-end text-nowrap">
                                        {{ $pdfSymbol . getCurrencyFormat($transaction->sgst) }}
                                    </p>
                                @endif
                                @if ($isCompanyGstApplicable && $transaction->igst != 0)
                                    <p class="table-contents-font-size lh-1-5 text-end text-nowrap">
                                        {{ $pdfSymbol . getCurrencyFormat($transaction->igst) }}
                                    </p>
                                @endif
                                @if ($transaction->tcs_amount != 0)
                                    <p class="table-contents-font-size lh-1-5 text-end text-nowrap">
                                        {{ $pdfSymbol . getCurrencyFormat($transaction->tcs_amount ?? '0.0') }}
                                    </p>
                                @endif
                                @if ($transaction->cess != 0)
                                    <p class="table-contents-font-size lh-1-5 text-end text-nowrap">
                                        {{ $pdfSymbol . getCurrencyFormat($transaction->cess ?? '0.0') }}
                                    </p>
                                @endif
                                <p class="table-contents-font-size lh-1-5 text-end text-nowrap">
                                    {{ $pdfSymbol . getCurrencyFormat($transaction->rounding_amount ?? '0.0') }}
                                </p>
                                @foreach ($addLess as $addLessItem)
                                    <p class="table-contents-font-size lh-1-5 text-end text-nowrap">
                                        {{ $pdfSymbol . getCurrencyFormat($addLessItem['amount'] ?? '0.0') }}
                                    </p>
                                @endforeach
                            </td>
                        </tr>
                        {{-- Additional/ Taxable Amount / GST Tax Section End --}}
                    </tbody>
                    @php
                        if (empty($addLess)) {
                            $total = $transaction->grand_total;
                        } else {
                            $addLessSum = array_sum(array_column($addLess, 'amount'));
                            $total =  $transaction->grand_total - $addLessSum;
                            $addLessSumTotal = collect($addLess)->where('is_show_in_print',1)->sum('amount');
                            $total = $total + $addLessSumTotal;

                        }
                    @endphp
                    <tfoot>
                        <tr>
                            @foreach ($rearrangeItems['footer'] as $key => $footer)
                                @if($footer['is_show_in_print'])
                                    <td class="bg-black table-headings-font-size text-white text-start py-2 px-3 lh-1-5 fw-semibold {{ ($loop->index === $loop->count - 2) ? '' : 'border-right' }}  fw-6 {{ ($key == 'item_name') ? 'min-width-150 ps-2' : 'text-center'}}"
                                    style="">{{ $loop->index === $loop->count - 2 ? $pdfSymbol.getCurrencyFormatFor3digit($total) : $footer['value'] }}</td>
                                @endif
                            @endforeach
                        </tr>
                    </tfoot>
                </table>
        </div>
        {{-- Item Table Section End --}}

        {{-- Bank Details / Signature Section Start --}}

        <div class="px-2 d-flex justify-content-between align-items-start">
            {{-- Bank Details Section Start --}}
            <div>
                @if ($showPrintSettings['show_estimate_in_words'] ?? true)

                    <p class="table-contents-font-size lh-1-5 text-black mb-1">{{ $changeLabel['estimate_in_words'] ?? 'In Words' }} :
                    </p>
                    <p class="table-contents-font-size text-black lh-1-5 mb-3">{{ getAmountToWord($total ?? '0.0') }}
                        Only</p>
                @endif
                @if ((($invoiceSetting['estimate_qr_code'] ?? true) && isset($bankDetail->upi_id)) || ($invoiceSetting['estimate_bank_details'] ?? true))
                    <div class="{{ ($invoiceSetting['estimate_qr_code'] ?? true) && isset($bankDetail->upi_id) ? 'd-flex gap-20px' : '' }}">
                        <div class="d-flex flex-column jusstify-content-center">
                            @if ((($invoiceSetting['estimate_qr_code'] ?? true)) && isset($bankDetail->upi_id))
                                <div class="qr-img h-100 w-100 mx-auto">
                                    <img src="data:image/svg+xml;base64,{{ base64_encode(generateUPIQRCode($bankDetail->upi_id, $transaction->due_amount)) }}"
                                        width="75" height="75" />
                                </div>
                                <p class="mt-2  footer-contents-font-size">
                                    {{ $bankDetail->upi_id }}
                                </p>
                            @endif
                        </div>
                        @if ($invoiceSetting['estimate_bank_details'] ?? true)
                            <div>
                                <p class="footer-headings-font-size lh-1-5 text-black mb-1">Bank Details</p>
                                <p class="footer-contents-font-size lh-1-5 text-black mb-1">
                                    <span class="fw-semibold footer-headings-font-size"> Bank :</span>
                                    <span class="fw-semibold footer-contents-font-size">{{ !empty($bankDetail) ? $bankDetail->bank_name : null }}</span>
                                </p>
                                <p class="footer-contents-font-size lh-1-5 text-black mb-1">
                                    <span class="fw-semibold footer-headings-font-size">Account no. :</span>
                                    <span class="fw-semibold footer-contents-font-size">{{ $accountNumber }}</span>
                                </p>
                                @if (!empty($bankDetail) && $bankDetail->swift_code != null)
                                    <p class="footer-contents-font-size lh-1-5 text-black mb-1">
                                        <span class="fw-semibold footer-headings-font-size">Swift Code:</span>
                                        <span class="fw-semibold footer-contents-font-size">{{ $bankDetail->swift_code }}</span>
                                    </p>
                                @endif
                                <p class="footer-contents-font-size lh-1-5 text-black mb-1">
                                    <span class="fw-semibold footer-headings-font-size">IFSC :</span>
                                    <span class="fw-semibold footer-contents-font-size">{{ !empty($bankDetail) ? $bankDetail->ifsc_code : null }}</span>
                                </p>
                                @if (isset($bankDetail->account_holder_name))
                                    <p class="footer-contents-font-size lh-1-5 text-black mb-1">
                                        <span class="fw-semibold footer-headings-font-size">A/C Name:</span>
                                        <span class="fw-semibold footer-contents-font-size">{{ $bankDetail->account_holder_name }}</span>
                                    </p>
                                @endif
                                <p class="footer-contents-font-size lh-1-5 text-black mb-1">
                                    <span class="fw-semibold footer-headings-font-size">Branch :</span>
                                    <span class="fw-semibold footer-contents-font-size">{{ $branchName }}</span>
                                </p>
                            </div>
                        @endif
                    </div>
                @endif
                {{-- Terms & Notes Section Start --}}
                <div class="pe-2">
                    @if (($showPrintSettings['show_estimate_narration'] ?? true) && $transaction->narration)
                        <p class="text-black note-font-size fw-semibold lh-1-5 mb-1">
                            {{ $changeLabel['estimate_narration'] ?? 'Notes' }}:
                        </p>
                        <p class="text-black note-font-size lh-1-5 mb-3">
                            {!! nl2br($transaction->narration) !!}
                        </p>
                    @endif
                    @if (($showPrintSettings['show_estimate_terms_and_conditions'] ?? true) && $transaction->term_and_condition)
                        <p class="text-black terms-and-conditions-font-size fw-semibold lh-1-5 mb-1 mt-2">
                            {{ $changeLabel['estimate_terms_and_conditions'] ?? 'Terms and Conditions' }}:</p>
                        <p class="text-black terms-and-conditions-font-size lh-1-5">
                            {!! nl2br($transaction->term_and_condition) !!}
                        </p>
                    @endif
                </div>
                {{-- Terms & Notes Section End --}}
            </div>
            {{-- Bank Details Section End --}}

            {{-- Signature Section Start --}}
            @if (($invoiceSetting['estimate_signature'] ?? false) || ($showPrintSettings['show_estimate_authorized_signatory'] ?? true))
                <div class="sign-section ms-auto pe-2 mt-2">
                    @if ($showPrintSettings['show_estimate_authorized_signatory'] ?? true)
                        <p class="text-black footer-contents-font-size fw-semibold text-center lh-1-5 mb-1">
                            For, {{ strtoupper($currentCompany->trade_name) }}
                        </p>
                    @endif
                    @if ($invoiceSetting['estimate_signature'] ?? false)
                        <div class="sign d-flex align-items-center p-2 ms-auto mb-2">
                            <img src="{{ $currentCompany->company_signature ?? null }}" alt="company_signature"
                                class="w-100" />
                        </div>
                    @endif
                    @if ($showPrintSettings['show_estimate_authorized_signatory'] ?? true)
                        <p class="text-black footer-contents-font-size ms-auto text-center mb-0">
                            {{ $changeLabel['authorized_signatory'] ?? 'Authorized Signatory' }}
                        </p>
                    @endif
                </div>
            @endif
            {{-- Signature Section End --}}
        </div>
        {{-- Bank Details / Signature Section End --}}
    </div>
</body>

</html>
