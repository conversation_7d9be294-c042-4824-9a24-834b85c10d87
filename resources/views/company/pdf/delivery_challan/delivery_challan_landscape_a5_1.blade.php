<!DOCTYPE html>
<html lang="en">
    {{-- landscape a5 format pdf ui 1 --}}
@php
    $pdfSymbol = getPrintPdfCurrencySymbol($currentCompany->id);
    $dockereEnabled = dockerEnabled() && !isset($preview_enabled);
    $fontStyleName = $dockereEnabled ? 'Arial Unicode MS' : 'Arial-unicode-ms';
    $fontCssName = $dockereEnabled ? 'font-docker.css' : 'font.css';
@endphp

<head>
    <meta charset="UTF-8" />
    <title>{{ isset($fileName) ? $fileName : $transaction->full_invoice_number }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/' . $fontCssName) }}">
    @include('company.pdf.sale_pdf.print_custom_font_css_variables')
    <style>
        * {
            margin: 0;
            padding: 0;
            text-indent: 0;
            font-family: "{{ $fontStyleName }}";
            font-size: 13px;
            font-weight: 400;
            box-sizing: border-box;
        }
        @page {
            margin: 20px !important;
        }
        h1 {
            font-size: 24px;
        }
        .main-table {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            width: 100%;
            box-sizing: border-box;
            border: 2px solid #A9A9A9;
            color: #181C32;
        }
        table {
            display: table;
            width: 100%;
            border-collapse: collapse;
        }
        td {
            vertical-align: top;
        }
        .text-primary {
            color: var(--color-theme);
        }
        .fs-14 {
            font-size: 14px;
        }
        .fs-11 {
            font-size: 11px;
        }
        .fw-6 {
            font-weight: 600;
        }
        .fw-7 {
            font-weight: 700;
        }
        .whitespace-nowrap {
            white-space: nowrap;
        }
        .border-bottom {
            border-bottom: 1px solid #A9A9A9 !important;
        }
        .border-right {
            border-right: 1px solid #A9A9A9 !important;
        }
        .border-top {
            border-top: 1px solid #A9A9A9 !important;
        }
        .border-left {
            border-left: 1px solid #A9A9A9;
        }
        .vertical-top {
            vertical-align: top;
        }
        .vertical-middle {
            vertical-align: middle;
        }
        .vertical-bottom {
            vertical-align: bottom;
        }
        .text-center {
            text-align: center !important;
        }
        .text-start {
            text-align: left !important;
        }
        .text-end {
            text-align: right !important;
        }
        .text-black {
            color: #181C32 !important;
        }
        .signature {
            max-width: 200px;
            height: 50px;
        }
        .qr-code {
            width: 75px;
            height: 75px;
            min-width: 75px;
        }
        .px-8 {
            padding-left: 8px !important;
            padding-right: 8px !important;
        }
        .pt-2 {
            padding-top: 2px !important;
        }
        .pb-2 {
            padding-bottom: 2px !important;
        }
        .pt-4 {
            padding-top: 4px !important;
        }
        .pb-4 {
            padding-bottom: 4px !important;
        }
        .w-100 {
            width: 100%;
        }
        .mb-0 {
            margin-bottom: 0 !important;
        }
        .px-3 {
            padding-left: 16px !important;
            padding-right: 16px !important;
        }
        .text-uppercase {
            text-transform: uppercase;
        }
        .row {
            display: flex;
            flex-wrap: wrap;
        }
        .col {
            flex: 1 0 0%;
        }
        .w-25 {
            width: 25%;
        }
        .p-0 {
            padding: 0 !important;
        }
        .font-italic {
            font-style: italic;
        }
        .item-table tbody tr:last-child td{
            height: 100%;
        }
        .h-100 {
            height: 100%;
        }
    </style>
      @include('company.pdf.sale_pdf.print_custom_font_css')
</head>

<body>
    <div>
        <div class="main-table w-100">
            <h2 class="pt-4 pb-2 mb-0 text-center company-name-font-size company-font-customization">
                {{ strtoupper($currentCompany->trade_name) }}
            </h2>
            @if(($invoiceSetting['prop_details'] ?? true) && isset($customProp))
                <p class="mb-0 text-center company-address-font-size">{{ $customProp->label_name.' : '.$customProp->label_value }}</p>
            @endif
            <p class="mb-0 text-center company-address-font-size">
                {{ isset($companyBillingAddress->address_1) ? strtoupper($companyBillingAddress->address_1 .',') : null }}
                {{ isset($companyBillingAddress->address_2) ? strtoupper($companyBillingAddress->address_2 .',') : null }}
                {{ isset($companyBillingAddress->city_id) ?  strtoupper(getCityName($companyBillingAddress->city_id).',') : null }}
                {{ isset($companyBillingAddress->state_id) ? strtoupper(getStateName($companyBillingAddress->state_id).',') : null }}
                {{ isset($companyBillingAddress->country_id) ? strtoupper(getCountryName($companyBillingAddress->country_id).',') : null }}
                {{ $companyBillingAddress->pin_code ?? null }}
            </p>
            @if ($invoiceSetting['delivery_challan_email'] ?? true)
                <a class="text-center text-black company-address-font-size">
                    {{ $invoiceSetting['alternate_email'] ?? ($currentCompany->user->email ?? null) }}
                </a>
            @endif
            <div class="text-center">
                @foreach (printCustomPDFLabelsForSale() as $key => $customLabel)
                    <span class="company-address-font-size" style="{{ $loop->first ? 'margin-right:25px;' : '' }}">
                        {{ $key ?? null }}:
                        {{ $customLabel ?? null }}
                    </span>
                @endforeach
            </div>
            <div class="text-center">
                @if ($currentCompany->is_gst_applicable)
                    <span class="company-address-font-size" style="margin-right:25px;">
                        {{ $changeLabel['delivery_challan_gstin_label'] ?? 'GSTIN' }}:
                        {{ '  ' . $currentCompany->companyTax->gstin ?? null }}
                    </span>
                @endif
                @if ($invoiceSetting['delivery_challan_mobile_number'] ?? true)
                    <span class="company-address-font-size">
                        {{ $changeLabel['delivery_challan_tel_label'] ?? 'Phone no.' }} :
                        {{
                            (isset($invoiceSetting['region_code']) ? '+' . $invoiceSetting['region_code'] . ' ' : '') .
                            (isset($invoiceSetting['alternate_phone']) ? $invoiceSetting['alternate_phone'] : ('+' . $currentCompany->user->region_code . ' ' . $currentCompany->user->phone)) .
                            (isset($invoiceSetting['region_code_2']) && isset($invoiceSetting['alternate_phone_2']) ? ', +' . $invoiceSetting['region_code_2'] . ' ' : '') .
                            (isset($invoiceSetting['alternate_phone_2']) ? (isset($invoiceSetting['region_code_2']) ? '' : ', ') . $invoiceSetting['alternate_phone_2'] : '')
                        }}
                    </span>
                @endif
            </div>
            <div class="px-3 border-bottom">
                <table class="table mb-0">
                    <tr>
                        <td class="px-8 pt-4 pb-4 fs-11 text-uppercase fw-7"> ({#INVOICE_TYPE#}) </td>
                        <td class="px-8 pt-4 pb-4 fs-11 text-uppercase fw-7 text-end"> {{ $deliveryChallanTransactionMaster->title_of_print }} </td>
                    </tr>
                </table>
            </div>
            <div class="row border-bottom">
                <div class="px-8 pt-4 pb-4 col vertical-top">
                    <h4 class="mb-0 fw-6 header-contents-font-size">
                        {{ strtoupper($customerDetail->name) }}
                    </h4>
                    @if ($showGst)
                        <p class="mb-0 header-contents-font-size">
                            GSTIN : {{ $transaction->gstin ?? $customerDetail->model->gstin ?? null }}
                        </p>
                    @endif
                    @if (!empty($panNumber) && $showPanNumber)
                        <p class="mb-0 header-contents-font-size">
                            PAN : {{ $panNumber ?? null }}
                        </p>
                    @endif
                    @if (isset($billingAddress))
                        <p class="m-0 header-contents-font-size">
                            @if ($billingAddress->address_1 != null)
                                {{ strtoupper($billingAddress->address_1) }}
                            @endif
                            @if ($billingAddress->address_2 != null)
                                {{ strtoupper($billingAddress->address_2) }},
                            @endif
                            {{ $billingAddress->city_id != null ? strtoupper(getCityName($billingAddress->city_id)) . ',' : null }}
                            {{ $billingAddress->state_id != null ? strtoupper(getStateName($billingAddress->state_id)) . ',' : null }}
                            {{ strtoupper(getCountryName($billingAddress->country_id ?? null)) }},
                            {{ $billingAddress->pin_code ?? null }}
                        </p>
                    @endif
                    <p class="mb-0 header-contents-font-size">
                        @if (!empty($transaction->party_phone_number))
                            ph.: +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                        @endif
                    </p>
                </div>
                @if ($invoiceSetting['delivery_challan_ship_to_details'] ?? true)
                    <div class="px-8 pt-4 pb-4 col vertical-top">
                        <h4 class="mb-0 fw-6 header-contents-font-size">
                            {{ strtoupper($transaction->shipping_name ?? $customerDetail->name) }}
                        </h4>
                        @if ($isCompanyGstApplicable && !empty($transaction->shipping_gstin))
                            <p class="mb-0 header-contents-font-size">
                                GSTIN : {{ $transaction->shipping_gstin ?? null }}
                            </p>
                        @endif
                        <p class="m-0 header-contents-font-size">
                            @if (isset($shippingAddress->address_1))
                                {{ strtoupper($shippingAddress->address_1) }},
                            @endif
                            @if (isset($shippingAddress->address_2))
                                {{ strtoupper($shippingAddress->address_2) }},
                            @endif
                            {{ isset($shippingAddress->city_id) ? strtoupper(getCityName($shippingAddress->city_id)) . ',' : ''  }}
                            {{ isset($shippingAddress->state_id) ? strtoupper(getStateName($shippingAddress->state_id)) . ',' : '' }}
                            {{ isset($shippingAddress->country_id) ? strtoupper(getCountryName($shippingAddress->country_id)) . ',' : '' }},
                            {{ $shippingAddress->pin_code ?? null }}
                        </p>
                        <p class="mb-0 header-contents-font-size">
                            @if (!empty($transaction->party_phone_number))
                                ph.: +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                            @endif
                        </p>
                    </div>
                @endif
                @if (isset($transaction['dispatch_address_id']) && isset($dispatchAddress) && (($invoiceSetting['delivery_challan_dispatch_from_details'] ?? false)))
                    <div class="px-8 pt-4 pb-4 col vertical-top">
                        <p class="m-0 header-contents-font-size">
                            {{ isset($companyShippingAddress->address_1) ? strtoupper($companyShippingAddress->address_1 .',') : null }}
                            {{ isset($companyShippingAddress->address_2) ? strtoupper($companyShippingAddress->address_2 .',') : null }}
                            {{ isset($companyShippingAddress->city_id) ?  strtoupper(getCityName($companyShippingAddress->city_id).',') : null }}
                            {{ isset($companyShippingAddress->state_id) ? strtoupper(getStateName($companyShippingAddress->state_id).',') : null }}
                            {{ isset($companyShippingAddress->country_id) ? strtoupper(getCountryName($companyShippingAddress->country_id).',') : null }}
                            {{ $companyShippingAddress->pin_code ?? null }}
                        </p>
                    </div>
                @endif
                <div class="px-8 pt-4 pb-4 col vertical-top">
                    <table class="table">
                        <tr>
                            <td class="p-0 fw-7 header-contents-font-size">
                                {{ $changeLabel['delivery_challan_number_label'] ?? 'Challan No' }}:
                            </td>
                            <td class="p-0 header-contents-font-size">
                                {{ $transaction->challan_number }}
                            </td>
                        </tr>
                        <tr>
                            <td class="p-0 fw-7 header-contents-font-size">
                                {{ $changeLabel['delivery_challan_date_label'] ?? 'Challan Date' }}:
                            </td>
                            <td class="p-0 header-contents-font-size">
                                {{ Carbon\Carbon::parse($transaction->challan_date)->format('d-m-Y') }}
                            </td>
                        </tr>
                        @if ($invoiceSetting['delivery_challan_po_number'] ?? true)
                            <tr>
                                <td class="p-0 fw-7 header-contents-font-size">
                                    {{ $changeLabel['delivery_challan_po_number_label'] ?? 'PO No' }}:
                                </td>
                                <td class="p-0 header-contents-font-size">
                                    {{ $transaction->po_no }}
                                </td>
                            </tr>
                        @endif
                        @if ($invoiceSetting['show_delivery_challan_po_date'] ?? true)
                            <tr>
                                <td class="p-0 fw-7 header-contents-font-size">
                                    {{ $changeLabel['delivery_challan_po_date_label'] ?? 'PO Date' }}:
                                </td>
                                <td class="p-0 header-contents-font-size">
                                    {{ isset($transaction->po_date) ? \Carbon\Carbon::parse($transaction->po_date)->format('d-m-Y') : null }}
                                </td>
                            </tr>
                        @endif
                    </table>
                </div>
            </div>
            @if (($invoiceSetting['delivery_challan_transport_details'] ?? true) || count($customFieldValues) > 0 || (isset($eWayBill) || (isset($transaction->eway_bill_number) || isset($transaction->eway_bill_date)) || (($invoiceSetting['delivery_challan_transport_details'] ?? true) && isset($transaction->transporter_vehicle_number))))
                <div class="row border-bottom">
                    @if ($invoiceSetting['delivery_challan_transport_details'] ?? true)
                        <div class="px-8 pt-4 pb-4 col vertical-top w-25">
                            <table class="table">
                                <tr>
                                    <td class="p-0 fw-7 header-contents-font-size">
                                        {{ $changeLabel['delivery_challan_transport_name_label'] ?? 'Transport Name' }}:
                                    </td>
                                    <td class="p-0 header-contents-font-size">
                                        {{ $transaction->transportDetails->transporter_name ?? '' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="p-0 fw-7 header-contents-font-size">
                                        GSTIN:
                                    </td>
                                    <td class="p-0 header-contents-font-size">
                                        {{ $transaction->transportDetails->gstin ?? '' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="p-0 fw-7 header-contents-font-size">
                                        Document No:
                                    </td>
                                    <td class="p-0 header-contents-font-size">
                                        {{ $transaction->transporter_document_number ?? '' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="p-0 fw-7 header-contents-font-size">
                                        Document Date:
                                    </td>
                                    <td class="p-0 header-contents-font-size">
                                        {{ isset($transaction->transporter_document_date) ? \Carbon\Carbon::parse($transaction->transporter_document_date)->format('d-m-Y') : ' ' }}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    @endif
                    @if ((($invoiceSetting['delivery_challan_transport_details'] ?? true) && isset($transaction->transporter_vehicle_number)))
                        <div class="px-8 pt-4 pb-4 col vertical-top">
                            <table class="table">
                                @if (($invoiceSetting['delivery_challan_transport_details'] ?? true) && isset($transaction->transporter_vehicle_number))
                                    <tr>
                                        <td class="p-0 fw-7 header-contents-font-size">
                                            {{ $changeLabel['delivery_challan_transport_vehicle_number_label'] ?? 'Vehicle No.' }}:
                                        </td>
                                        <td class="p-0 header-contents-font-size">
                                            {{ $transaction->transporter_vehicle_number ?? '' }}
                                        </td>
                                    </tr>
                                @endif
                            </table>
                        </div>
                    @endif
                    {{-- Custom Fields Section Start --}}
                    @if (count($customFieldValues) > 0)
                        @php
                            $customFields = collect($customFieldValues)->where('is_show_in_print', true)->values();
                        @endphp
                        <div class="px-8 pt-4 pb-4 col vertical-top">
                            <table class="table">
                                @foreach ($customFields as $customField)
                                    <tr>
                                        <td class="p-0 fw-7 header-contents-font-size">
                                            {{ $customField['label_name'] ?? '' }}:
                                        </td>
                                        <td class="p-0 header-contents-font-size">
                                            {{ $customField['value'] ?? '' }}
                                        </td>
                                    </tr>
                                @endforeach
                            </table>
                        </div>
                    @endif
                    {{-- Custom Fields Section End --}}
                </div>
            @endif
            {{-- Item Table Section Start --}}

            @php
                $customFieldItemsValues = collect($transactionItems[0]['customItemsValues'])->where('is_show_in_print', true)->values();
                $customFieldItemsHeaders = collect($customFieldItemsValues)->pluck('label_name')->toArray();
            @endphp
            @if ($itemType == \App\Models\DeliveryChallanTransaction::WITH_AMOUNT)
                {{-- Item Type WITH AMOUNT --}}
                <table class="item-table" cellpadding="0" style="flex-grow: 1;">
                    <thead>
                        <tr class="border-bottom">
                            <td class="px-8 pt-4 pb-4 text-center table-headings-font-size border-right fw-7">
                                {{ $changeLabel['delivery_challan_sn_label'] ?? 'SN' }}
                            </td>
                            <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap fw-7 w-100">
                                {{ $changeLabel['delivery_challan_item_name_label'] ?? 'Item Name' }}
                            </td>
                            @if (count($customFieldItemsHeaders) > 0)
                                @foreach ($customFieldItemsHeaders as $customFieldItemHeader)
                                    <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap fw-7">
                                        {{ $customFieldItemHeader ?? '' }}
                                    </td>
                                @endforeach
                            @endif
                            @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_hsn_sac'] ?? true))
                                <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-start fw-7">
                                    {{ $changeLabel['delivery_challan_hsn_sac_label'] ?? 'HSN/SAC' }} </p>
                                </td>
                            @endif
                            @if(($showPrintSettings['show_delivery_challan_mrp'] ?? true) && $transactionItems->sum('mrp') != 0.0)
                                <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-end fw-7">
                                    {{ $changeLabel['delivery_challan_mrp_label'] ?? 'MRP' }}
                                </td>
                            @endif
                            @if($showPrintSettings['show_delivery_challan_qty'] ?? true)
                                <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-end fw-7">
                                    {{ $changeLabel['delivery_challan_qty_label'] ?? 'Qty' }}
                                </td>
                            @endif
                            @if (($showPrintSettings['delivery_challan_uom_enable'] ?? false) && $transactionItems->sum('secondary_quantity') != 0.0)
                                <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-end fw-7">
                                    {{ $changeLabel['delivery_challan_uom_label'] ?? 'UOM' }}
                                </td>
                            @endif
                            @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_rate_with_gst'] ?? false))
                                <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-end fw-7">
                                    {{ $changeLabel['delivery_challan_rate_with_gst_label'] ?? 'Rate With GST' }}
                                </td>
                            @endif
                            @if ($showPrintSettings['show_delivery_challan_rate'] ?? true)
                                <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-end fw-7">
                                    {{ $changeLabel['delivery_challan_rate_label'] ?? 'Rate' }}
                                </td>
                            @endif
                            @if (($showPrintSettings['show_delivery_challan_discount'] ?? true) && $transactionItems->sum('discount_value') != 0.0)
                                <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-end fw-7">
                                    {{ $changeLabel['delivery_challan_discount_label'] ?? 'Dis.' }}
                                </td>
                            @endif
                            @if (($showPrintSettings['delivery_challan_dis_2_enable'] ?? true) && $transactionItems->sum('discount_value_2') != 0.0)
                                <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-end fw-7">
                                    {{ $changeLabel['delivery_challan_dis_2_label'] ?? 'Dis. 2' }}
                                </td>
                            @endif
                            @if (($showPrintSettings['show_delivery_challan_total_discount'] ?? true) && $transactionItems->sum('total_discount_amount') != 0.0)
                                <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-end fw-7">
                                    {{ $changeLabel['delivery_challan_total_discount_label'] ?? 'Total Dis.' }}
                                </td>
                            @endif
                            @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_gst'] ?? true))
                                <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-end fw-7">
                                    {{ $changeLabel['delivery_challan_gst_label'] ?? 'GST (%)' }}
                                </td>
                                <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-end fw-7">
                                    {{ $changeLabel['delivery_challan_gst_amount_label'] ?? 'GST Amt' }}
                                </td>
                            @endif
                            @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_total_amount'] ?? true))
                                <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-end fw-7">
                                    {{ $changeLabel['delivery_challan_taxable_value_label'] ?? ($isCompanyGstApplicable ? 'Taxable Value' : 'Amount') }}
                                </td>
                                <td class="px-8 pt-4 pb-4 table-headings-font-size whitespace-nowrap text-end fw-7">
                                    {{ $changeLabel['delivery_challan_total_amount_label'] ?? 'Total Amount' }}
                                </td>
                            @else
                                <td class="px-8 pt-4 pb-4 table-headings-font-size whitespace-nowrap text-end fw-7">
                                    {{ $changeLabel['delivery_challan_taxable_value_label'] ?? ($isCompanyGstApplicable ? 'Taxable Value' : 'Amount') }}
                                </td>
                            @endif
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($transactionItems as $key => $item)
                            @php
                                $uniqueId = ++$key;
                                $ids = collect($customFieldItemsValues)->pluck('custom_field_item_id')->values();
                                $printCustomFields = collect($item['customItemsValues'])->where('is_show_in_print', true)->whereIn('custom_field_item_id', $ids)->values();
                            @endphp
                            <tr>
                                <td class="px-8 pt-4 text-center table-contents-font-size border-right">
                                    {{ $uniqueId }}
                                </td>
                                <td class="px-8 pt-4 table-contents-font-size border-right fw-6">
                                    {{ $item->items->item_name ?? null }}
                                    @if ($item->items->sku != null && ($invoiceSetting['show_delivery_challan_item_sku'] ?? true))
                                        <p style="" class="description-font-size">Item Code:
                                            {{ $item->items->sku ?? null }}</p>
                                    @endif
                                    <p style="word-break: break-word; " class="description-font-size">
                                        {!! !empty($item->consolidating_items_to_invoice)
                                            ? '(' . consolidatingItemsToInvoice($item->consolidating_items_to_invoice) . ')'
                                            : null !!}
                                    </p>
                                    <p style="word-break: break-word;" class="description-font-size font-italic">
                                        {!! !empty($item->additional_description) ? nl2br('(' . $item->additional_description . ')') : null !!}
                                    </p>
                                    @if(!empty($item->items->item_image) && ($invoiceSetting['show_item_image'] ?? true))
                                        <img src="{{ $item->items->item_image }}" width="60"  height="60" style="margin-top: 4px">
                                    @endif
                                </td>
                                @if (count($printCustomFields) > 0)
                                    @foreach ($printCustomFields as $customFieldItemsValue)
                                        <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                                            {{ $customFieldItemsValue['value'] ?? '' }}
                                        </td>
                                    @endforeach
                                @endif
                                @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_hsn_sac'] ?? true))
                                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                                        {{ $item->hsn_code ?? $item->items->model->hsn_sac_code ?? null }}
                                    </td>
                                @endif
                                @if(($showPrintSettings['show_delivery_challan_mrp'] ?? true) && $transactionItems->sum('mrp') != 0.0)
                                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                                        {{ !empty($item->mrp) ? $pdfSymbol.getCurrencyFormatFor3digit($item->mrp) : '-' }}
                                    </td>
                                @endif
                                @if($showPrintSettings['show_delivery_challan_qty'] ?? true)
                                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                                        {{ $item->primary_quantity }}
                                        @if($showPrintSettings['show_delivery_challan_unit'] ?? true)
                                            {{ $item->primary_unit_name }}
                                        @endif
                                    </td>
                                @endif
                                @if (($showPrintSettings['delivery_challan_uom_enable'] ?? false) && $transactionItems->sum('secondary_quantity') != 0.0)
                                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                                        {{ round($item->secondary_quantity,2) }}
                                        @if($showPrintSettings['show_delivery_challan_unit'] ?? true)
                                            {{ $item->secondary_unit_name }}
                                        @endif
                                    </td>
                                @endif
                                @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_rate_with_gst'] ?? false))
                                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                                        {{ $pdfSymbol.getCurrencyFormatFor3digit($item->rpu_with_gst) }}
                                    </td>
                                @endif
                                @if ($showPrintSettings['show_delivery_challan_rate'] ?? true)
                                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                                        {{ $pdfSymbol.getCurrencyFormatFor3digit($item->rpu_without_gst) }}
                                    </td>
                                @endif
                                @if (($showPrintSettings['show_delivery_challan_discount'] ?? true) && $transactionItems->sum('discount_value') != 0.0)
                                    <td class="px-8 pt-4 table-contents-font-size border-right whitespace-nowrap text-end">
                                        @if ($item->discount_type == \App\Models\SaleTransaction::DISCOUNT_TYPE_AMOUNT)
                                            {{ $pdfSymbol . getCurrencyFormatFor3digit($item->discount_value) ?? '0.0' }}
                                        @else
                                            {{ $item->discount_value . '(%)' ?? '0.0(%)' }}
                                        @endif
                                    </td>
                                @endif
                                @if (($showPrintSettings['delivery_challan_dis_2_enable'] ?? true) && $transactionItems->sum('discount_value_2') != 0.0)
                                    <td class="px-8 pt-4 table-contents-font-size border-right whitespace-nowrap text-end">
                                        @if ($item->discount_type_2 == \App\Models\SaleTransaction::DISCOUNT_TYPE_AMOUNT)
                                            {{ $pdfSymbol . getCurrencyFormatFor3digit($item->discount_value_2) ?? '0.0' }}
                                        @else
                                            {{ $item->discount_value_2 . '(%)' ?? '0.0(%)' }}
                                        @endif
                                    </td>
                                @endif
                                @if (($showPrintSettings['show_delivery_challan_total_discount'] ?? true) && $transactionItems->sum('total_discount_amount') != 0.0)
                                    <td class="px-8 pt-4 table-contents-font-size border-right whitespace-nowrap text-end">
                                        {{ $pdfSymbol . getCurrencyFormatFor3digit($item->total_discount_amount ?? '0.0') }}
                                    </td>
                                @endif
                                @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_gst'] ?? true))
                                    <td class="px-8 pt-4 table-contents-font-size border-right whitespace-nowrap text-end">
                                        {{ $item->gst_tax_percentage ?? '0.0' }}
                                    </td>
                                @endif
                                @if($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_gst_amount'] ?? true))
                                    <td class="px-8 pt-4 table-contents-font-size border-right whitespace-nowrap text-end">
                                        {{ $pdfSymbol . getCurrencyFormat(round($item->classification_igst_tax + $item->classification_cgst_tax + $item->classification_sgst_tax, getCompanyFixedDigitNumber())) }}
                                    </td>
                                @endif
                                @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_total_amount'] ?? true))
                                    <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-end fw-7">
                                        {{ $pdfSymbol . getCurrencyFormat(round($item->taxable_value ?? '0.0', getCompanyFixedDigitNumber())) }}
                                    </td>
                                    @php
                                        $totalAmount = $item->taxable_value + $item->classification_igst_tax + $item->classification_cgst_tax + $item->classification_sgst_tax
                                    @endphp
                                    <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end">
                                        {{ $pdfSymbol . getCurrencyFormat(round($totalAmount ?? '0.0', getCompanyFixedDigitNumber())) }}
                                    </td>
                                @else
                                    <td class="px-8 pt-4 table-contents-font-size whitespace-nowrap text-end">
                                        {{ $pdfSymbol . getCurrencyFormat(round($item->taxable_value ?? '0.0', getCompanyFixedDigitNumber())) }}
                                    </td>
                                @endif
                            </tr>
                        @endforeach
                    </tbody>
                    <tfoot>
                        <tr class="border-top border-bottom">
                            <td class="px-8 pt-4 text-center table-headings-font-size border-right"></td>
                            <td class="px-8 pt-4 table-headings-font-size border-right fw-6">
                                Total
                            </td>
                            @if (count($customFieldItemsValues) > 0)
                                @foreach ($customFieldItemsValues as $customFieldItemsValue)
                                    <td></td>
                                @endforeach
                            @endif
                            @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_hsn_sac'] ?? true))
                                <td class="px-8 pt-4 text-center table-headings-font-size whitespace-nowrap border-right"></td>
                            @endif
                            @if(($showPrintSettings['show_delivery_challan_mrp'] ?? true) && $transactionItems->sum('mrp') != 0.0)
                                <td class="px-8 pt-4 text-center table-headings-font-size whitespace-nowrap border-right"></td>
                            @endif
                            @if($showPrintSettings['show_delivery_challan_qty'] ?? true)
                                <td class="px-8 pt-4 text-center table-headings-font-size whitespace-nowrap border-right">
                                    {{ getCurrencyFormat($transactionItems->sum('primary_quantity')) }}
                                </td>
                            @endif
                            @if (($showPrintSettings['delivery_challan_uom_enable'] ?? false) && $transactionItems->sum('secondary_quantity') != 0.0)
                                <td class="px-8 pt-4 text-center table-headings-font-size whitespace-nowrap border-right">
                                    {{ getCurrencyFormat($transactionItems->sum('secondary_quantity')) }}
                                </td>
                            @endif
                            @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_rate_with_gst'] ?? false))
                                <td class="px-8 pt-4 text-center table-headings-font-size whitespace-nowrap border-right"></td>
                            @endif
                            @if ($showPrintSettings['show_delivery_challan_rate'] ?? true)
                                <td class="px-8 pt-4 text-center table-headings-font-size border-right whitespace-nowrap"></td>
                            @endif
                            @if (($showPrintSettings['show_delivery_challan_discount'] ?? true) && $transactionItems->sum('discount_value') != 0.0)
                                <td class="px-8 pt-4 table-headings-font-size border-right whitespace-nowrap text-end"></td>
                            @endif
                            @if (($showPrintSettings['delivery_challan_dis_2_enable'] ?? true) && $transactionItems->sum('discount_value_2') != 0.0)
                                <td class="px-8 pt-4 table-headings-font-size border-right whitespace-nowrap text-end"></td>
                            @endif
                            @if (($showPrintSettings['show_delivery_challan_total_discount'] ?? true) && $transactionItems->sum('total_discount_amount') != 0.0)
                                <td class="px-8 pt-4 table-headings-font-size border-right whitespace-nowrap text-end">
                                    {{ $pdfSymbol.getCurrencyFormat($transactionItems->sum('total_discount_amount' ?? 0.0)) }}
                                </td>
                            @endif
                            @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_gst'] ?? true))
                                <td class="px-8 pt-4 table-headings-font-size border-right whitespace-nowrap text-end"></td>
                            @endif
                            @if($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_gst_amount'] ?? true))
                                @php
                                    $igst = $transactionItems->sum('classification_igst_tax') ?? 0.0;
                                    $cgst = $transactionItems->sum('classification_cgst_tax') ?? 0.0;
                                    $sgst = $transactionItems->sum('classification_sgst_tax') ?? 0.0;
                                    $totalGSTAmount = $igst + $cgst + $sgst;
                                @endphp
                                <td class="px-8 pt-4 table-headings-font-size border-right whitespace-nowrap text-end">
                                    {{ $pdfSymbol.getCurrencyFormat(round($totalGSTAmount ?? 0.0, getCompanyFixedDigitNumber())) }}
                                </td>
                            @endif
                            @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_total_amount'] ?? true))
                                <td class="px-8 pt-4 table-headings-font-size border-right whitespace-nowrap text-end">
                                    {{ $pdfSymbol.getCurrencyFormat($transaction->gross_value) }}
                                </td>
                                @php
                                    $igst = $transactionItems->sum('classification_igst_tax') ?? 0.0;
                                    $cgst = $transactionItems->sum('classification_cgst_tax') ?? 0.0;
                                    $sgst = $transactionItems->sum('classification_sgst_tax') ?? 0.0;
                                    $totalAmount = $transaction->gross_value + $igst + $cgst + $sgst;
                                @endphp
                                <td class="px-8 pt-4 table-headings-font-size whitespace-nowrap text-end fw-7">
                                    {{ $pdfSymbol.getCurrencyFormat(round($totalAmount ?? 0.0, getCompanyFixedDigitNumber())) }}
                                </td>
                            @else
                                <td class="px-8 pt-4 table-headings-font-size whitespace-nowrap text-end fw-7">
                                    {{ $pdfSymbol.getCurrencyFormat(round($transaction->gross_value ?? 0.0, getCompanyFixedDigitNumber())) }}
                                </td>
                            @endif
                        </tr>
                    </tfoot>
                </table>
                {{-- Item Type WITH AMOUNT End --}}
            @else
                <table class="item-table" cellpadding="0" style="flex-grow: 1;">
                    <thead>
                        <tr class="border-bottom">
                            <td class="px-8 pt-4 pb-4 text-center table-headings-font-size border-right fw-7">
                                {{ $changeLabel['delivery_challan_sn_label'] ?? 'SN' }}
                            </td>
                            <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap fw-7 w-100">
                                {{ $changeLabel['delivery_challan_item_name_label'] ?? 'Item Name' }}
                            </td>
                            @if (count($customFieldItemsHeaders) > 0)
                                @foreach ($customFieldItemsHeaders as $customFieldItemHeader)
                                    <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap fw-7">
                                        {{ $customFieldItemHeader ?? '' }}
                                    </td>
                                @endforeach
                            @endif
                            @if($showPrintSettings['show_delivery_challan_qty'] ?? true)
                                <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-end fw-7">
                                    {{ $changeLabel['delivery_challan_qty_label'] ?? 'Qty' }}
                                </td>
                            @endif
                            @if (($showPrintSettings['delivery_challan_uom_enable'] ?? false) && $transactionItems->sum('secondary_quantity') != 0.0)
                                <td class="px-8 pt-4 pb-4 table-headings-font-size border-right whitespace-nowrap text-end fw-7">
                                    {{ $changeLabel['delivery_challan_uom_label'] ?? 'UOM' }}
                                </td>
                            @endif
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($transactionItems as $key => $item)
                            @php
                                $uniqueId = ++$key;
                                $ids = collect($customFieldItemsValues)->pluck('custom_field_item_id')->values();
                                $printCustomFields = collect($item['customItemsValues'])->where('is_show_in_print', true)->whereIn('custom_field_item_id', $ids)->values();
                            @endphp
                            <tr>
                                <td class="px-8 pt-4 text-center table-contents-font-size border-right">
                                    {{ $uniqueId }}
                                </td>
                                <td class="px-8 pt-4 table-contents-font-size border-right fw-6">
                                    <p class="table-contents-font-size">{{ $item->items->item_name ?? null }}</p>
                                    @if ($item->items->sku != null && ($invoiceSetting['show_delivery_challan_item_sku'] ?? true))
                                        <p style="" class="description-font-size">Item Code:
                                            {{ $item->items->sku ?? null }}</p>
                                    @endif
                                    <p style="word-break: break-word; " class="description-font-size">
                                        {!! !empty($item->consolidating_items_to_invoice)
                                            ? '(' . consolidatingItemsToInvoice($item->consolidating_items_to_invoice) . ')'
                                            : null !!}
                                    </p>
                                    <p style="word-break: break-word;" class="description-font-size font-italic">
                                        {!! !empty($item->additional_description) ? nl2br('(' . $item->additional_description . ')') : null !!}
                                    </p>
                                    @if(!empty($item->items->item_image) && ($invoiceSetting['show_item_image'] ?? true))
                                        <img src="{{ $item->items->item_image }}" width="60"  height="60" style="margin-top: 4px">
                                    @endif
                                </td>
                                @if (count($printCustomFields) > 0)
                                    @foreach ($printCustomFields as $customFieldItemsValue)
                                        <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                                            {{ $customFieldItemsValue['value'] ?? '' }}
                                        </td>
                                    @endforeach
                                @endif
                                @if($showPrintSettings['show_delivery_challan_qty'] ?? true)
                                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                                        {{ $item->primary_quantity }}
                                        @if($showPrintSettings['show_delivery_challan_unit'] ?? true)
                                            {{ $item->primary_unit_name }}
                                        @endif
                                    </td>
                                @endif
                                @if (($showPrintSettings['delivery_challan_uom_enable'] ?? false) && $transactionItems->sum('secondary_quantity') != 0.0)
                                    <td class="px-8 pt-4 text-center table-contents-font-size whitespace-nowrap border-right">
                                        {{ round($item->secondary_quantity,2) }}
                                        @if($showPrintSettings['show_delivery_challan_unit'] ?? true)
                                            {{ $item->secondary_unit_name }}
                                        @endif
                                    </td>
                                @endif
                            </tr>
                        @endforeach
                    </tbody>
                    <tfoot>
                        <tr class="border-top border-bottom">
                            <td class="px-8 pt-4 text-center table-headings-font-size border-right"></td>
                            <td class="px-8 pt-4 table-headings-font-size border-right fw-6">
                                Total
                            </td>
                            @if (count($customFieldItemsValues) > 0)
                                @foreach ($customFieldItemsValues as $customFieldItemsValue)
                                    <td></td>
                                @endforeach
                            @endif
                            @if($showPrintSettings['show_delivery_challan_qty'] ?? true)
                                <td class="px-8 pt-4 text-center table-headings-font-size whitespace-nowrap border-right">
                                    {{ getCurrencyFormat($transactionItems->sum('primary_quantity')) }}
                                </td>
                            @endif
                            @if (($showPrintSettings['delivery_challan_uom_enable'] ?? false) && $transactionItems->sum('secondary_quantity') != 0.0)
                                <td class="px-8 pt-4 text-center table-headings-font-size whitespace-nowrap border-right">
                                    {{ getCurrencyFormat($transactionItems->sum('secondary_quantity')) }}
                                </td>
                            @endif
                        </tr>
                    </tfoot>
                </table>
            @endif
            {{-- Item Table Section End --}}

            <div style="display: flex;">
                <div style="display: flex; flex-direction: column; width: 100%; justify-content: space-between;">
                    @if ((($showPrintSettings['show_delivery_challan_narration'] ?? true) && $transaction->narration) || (($showPrintSettings['show_delivery_challan_terms_and_conditions'] ?? true) && $transaction->term_and_condition))
                        <div>
                            @if (($showPrintSettings['show_delivery_challan_narration'] ?? true) && $transaction->narration)
                                <div class="pt-4 pb-4">
                                    <h4 class="px-8 pt-4 note-font-size fw-7">
                                        {{ $changeLabel['delivery_challan_narration'] ?? 'Note' }} :
                                    </h4>
                                    <p class="px-8 pt-4 note-font-size">
                                        {!! nl2br($transaction->narration) !!}
                                    </p>
                                </div>
                            @endif
                            @if (($showPrintSettings['show_delivery_challan_terms_and_conditions'] ?? true) && $transaction->term_and_condition)
                                <div class="pt-4 pb-4">
                                    <h4 class="px-8 pt-4 terms-and-conditions-font-size fw-7">
                                        {{ $changeLabel['delivery_challan_terms_and_conditions'] ?? 'Terms and Conditions' }} :
                                    </h4>
                                    <p class="px-8 pt-4 terms-and-conditions-font-size">
                                        {!! nl2br($transaction->term_and_condition) !!}
                                    </p>
                                </div>
                            @endif
                        </div>
                    @endif
                    @php
                        if (empty($addLess)) {
                            $total = $transaction->grand_total;
                        } else {
                            $addLessSum = collect($addLess)->sum('amount');
                            $total = $transaction->grand_total - $addLessSum;
                            $addLessSumTotal = collect($addLess)->where('is_show_in_print',1)->sum('amount');
                            $total = $total + $addLessSumTotal;
                        }
                    @endphp
                    @if (($showPrintSettings['show_delivery_challan_in_words'] ?? true) || ($invoiceSetting['delivery_challan_received_by'] ?? true) || ($invoiceSetting['delivery_challan_delivered_by'] ?? true) || (($showPrintSettings['show_delivery_challan_authorized_signatory'] ?? true) || ($invoiceSetting['delivery_challan_signature'] ?? false)))
                        <div class="{{ ($showPrintSettings['show_delivery_challan_in_words'] ?? true) ? '' : 'h-100' }}">
                            @if (($showPrintSettings['show_delivery_challan_in_words'] ?? true) && $itemType == \App\Models\DeliveryChallanTransaction::WITH_AMOUNT)
                                <div class="px-8 pt-4 pb-4 {{ ((($showPrintSettings['show_delivery_challan_narration'] ?? true) && $transaction->narration) || (($showPrintSettings['show_delivery_challan_terms_and_conditions'] ?? true) && $transaction->term_and_condition)) ? 'border-top' : '' }} table-contents-font-size">
                                    <span class="fw-7 table-contents-font-size" style="margin-right:4px;">
                                        {{ $changeLabel['delivery_challan_in_words'] ?? 'In Words' }} :
                                    </span>
                                    {{ getAmountToWord($total ?? '0.0') }} Only
                                </div>
                            @endif
                            <table class="{{ ($showPrintSettings['show_delivery_challan_in_words'] ?? true) ? '' : 'h-100' }}" cellpadding="0" style="page-break-inside: avoid !important">
                                <tr class="{{ ((($showPrintSettings['show_delivery_challan_narration'] ?? true) && $transaction->narration) || (($showPrintSettings['show_delivery_challan_terms_and_conditions'] ?? true) && $transaction->term_and_condition) || (($showPrintSettings['show_delivery_challan_in_words'] ?? true) && $itemType == \App\Models\DeliveryChallanTransaction::WITH_AMOUNT)) ? 'border-top' : '' }}">
                                    @if ($invoiceSetting['delivery_challan_received_by'] ?? true)
                                        <td class="border-right">
                                            <table>
                                                <tr>
                                                    <td class="fw-7 footer-headings-font-size" style="padding: 4px 8px 0 8px">{{ $changeLabel['delivery_challan_recived_by'] ?? 'Received By' }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="footer-contents-font-size" style="padding: 4px 8px 0 8px">Name:</td>
                                                </tr>
                                                <tr>
                                                    <td class="footer-contents-font-size" style="padding: 4px 8px 0 8px">Comment:</td>
                                                </tr>
                                                <tr>
                                                    <td class="footer-contents-font-size" style="padding: 4px 8px 0 8px">Date:</td>
                                                </tr>
                                                <tr>
                                                    <td class="footer-contents-font-size" style="padding: 4px 8px 4px 8px">Signature:</td>
                                                </tr>
                                            </table>
                                        </td>
                                    @endif
                                    @if ($invoiceSetting['delivery_challan_delivered_by'] ?? true)
                                        <td class="border-right">
                                            <table>
                                                <tr>
                                                    <td class="fw-7 footer-headings-font-size" style="padding: 4px 8px 0 8px">{{ $changeLabel['delivery_challan_delivered_by_label'] ?? 'Delivered By' }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="footer-contents-font-size" style="padding: 4px 8px 0 8px">Name:</td>
                                                </tr>
                                                <tr>
                                                    <td class="footer-contents-font-size" style="padding: 4px 8px 0 8px">Comment:</td>
                                                </tr>
                                                <tr>
                                                    <td class="footer-contents-font-size" style="padding: 4px 8px 0 8px">Date:</td>
                                                </tr>
                                                <tr>
                                                    <td class="footer-contents-font-size" style="padding: 4px 8px 4px 8px">Signature:</td>
                                                </tr>
                                            </table>
                                        </td>
                                    @endif
                                    @if (($showPrintSettings['show_delivery_challan_authorized_signatory'] ?? true) || ($invoiceSetting['delivery_challan_signature'] ?? false))
                                        <td class="vertical-bottom border-right" style="width: 200px; position: relative">
                                            <div class="px-8 pt-4 pb-4">

                                                <div class="text-end signature">
                                                    {{-- <img src="{{ $currentCompany->company_signature ?? null }}" alt="company-img"
                                                        style="max-width:100%; height:100%;  object-fit:contain; margin-left:auto;"> --}}
                                                </div>
                                                <p class="text-center verical-bottom footer-contents-font-size">
                                                    Receiver Signatory
                                                </p>
                                            </div>
                                        </td>
                                    @endif
                                    @if (($showPrintSettings['show_delivery_challan_authorized_signatory'] ?? true) || ($invoiceSetting['delivery_challan_signature'] ?? false))
                                        <td class="vertical-bottom" style="width: 200px; position: relative">
                                            <div class="px-8 pt-4 pb-4">
                                                @if ($showPrintSettings['show_delivery_challan_authorized_signatory'] ?? true)
                                                    <p class="text-center footer-headings-font-size fw-7">
                                                        For, {{ strtoupper($currentCompany->trade_name) }}
                                                    </p>
                                                @endif
                                                <div class="text-center signature" style="margin: 0 auto;">
                                                    @if (($invoiceSetting['delivery_challan_signature'] ?? false) && $currentCompany->company_signature != asset('images/preview-img.png'))
                                                        <img src="{{ $currentCompany->company_signature ?? null }}" alt="company-img"
                                                            style="max-width:100%; height:100%;  object-fit:contain; margin-left:auto;">
                                                    @endif
                                                </div>
                                                @if ($showPrintSettings['show_delivery_challan_authorized_signatory'] ?? true)
                                                    <p class="text-center verical-bottom footer-contents-font-size">
                                                        {{ $changeLabel['authorized_signatory'] ?? 'Authorized Signatory' }}
                                                    </p>
                                                @endif
                                            </div>
                                        </td>
                                    @endif
                                </tr>
                            </table>
                        </div>
                    @endif
                </div>
                @if ($itemType == \App\Models\DeliveryChallanTransaction::WITH_AMOUNT)
                    <div class="vertical-top border-left" style="white-space: nowrap; display: flex;
                    flex-direction: column;
                    justify-content: space-between; ">
                        <div>
                            <table cellpadding="0" style="flex-grow: 1;">
                                @foreach ($additionalCharges as $additionalCharge)
                                    <tr>
                                        <td class="px-8 pt-4 pb-2 table-contents-font-size vertical-top">
                                            {{ $additionalCharge['ledger_name'] }}
                                        </td>
                                        <td class="px-8 pt-4 pb-2 table-contents-font-size vertical-top text-end">
                                            {{ $pdfSymbol.getCurrencyFormat($additionalCharge['amount'] ?? '0.0') }}
                                        </td>
                                    </tr>
                                @endforeach
                                <tr>
                                    <td class="px-8 pb-2 table-contents-font-size vertical-top fw-7">
                                          {{ $changeLabel['delivery_challan_sub_total'] ?? ($isCompanyGstApplicable ? 'Taxable Value' : 'Sub Total') }}:
                                    </td>
                                    <td class="px-8 pb-2 table-contents-font-size vertical-top text-end fw-7">
                                        {{ $pdfSymbol.getCurrencyFormat(round($transaction->taxable_value, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                    </td>
                                </tr>
                                @if ($isCompanyGstApplicable)
                                    @if ($transaction->cgst != 0)
                                        <tr>
                                            <td class="px-8 pb-2 table-contents-font-size vertical-top">
                                                {{ $changeLabel['delivery_challan_cgst'] ?? 'CGST' }}
                                            </td>
                                            <td class="px-8 pb-2 table-contents-font-size vertical-top text-end">
                                                {{ $pdfSymbol.getCurrencyFormat(round($transaction->cgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                            </td>
                                        </tr>
                                    @endif
                                    @if ($transaction->sgst != 0)
                                        <tr>
                                            <td class="px-8 pb-2 table-contents-font-size vertical-top">
                                                {{ $changeLabel['delivery_challan_sgst'] ?? 'SGST' }}
                                            </td>
                                            <td class="px-8 pb-2 table-contents-font-size vertical-top text-end">
                                                {{ $pdfSymbol.getCurrencyFormat(round($transaction->sgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                            </td>
                                        </tr>
                                    @endif
                                    @if ($transaction->igst != 0)
                                        <tr>
                                            <td class="px-8 pb-2 table-contents-font-size vertical-top">
                                                {{ $changeLabel['delivery_challan_igst'] ?? 'IGST' }}
                                            </td>
                                            <td class="px-8 pb-2 table-contents-font-size vertical-top text-end">
                                                {{ $pdfSymbol.getCurrencyFormat(round($transaction->igst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                            </td>
                                        </tr>
                                    @endif
                                @endif
                                @if ($transaction->cess != 0)
                                <tr>
                                    <td class="px-8 pb-2 table-contents-font-size vertical-top">
                                        {{ $changeLabel['delivery_challan_cess'] ?? 'CESS' }}
                                    </td>
                                    <td class="px-8 pb-2 table-contents-font-size vertical-top text-end">
                                        {{ $pdfSymbol.getCurrencyFormat($transaction->cess ?? '0.0') }}
                                    </td>
                                </tr>
                                @endif
                                @if ($transaction->tcs_amount != 0)
                                    <tr>
                                        <td class="px-8 pb-2 table-contents-font-size vertical-top">
                                            {{ $changeLabel['delivery_challan_tcs'] ?? 'TCS' }}
                                        </td>
                                        <td class="px-8 pb-2 table-contents-font-size vertical-top text-end">
                                            {{ $pdfSymbol.getCurrencyFormat($transaction->tcs_amount ?? '0.0') }}
                                        </td>
                                    </tr>
                                @endif
                                @foreach (collect($addLess)->where('is_show_in_print',1) as $addLessItem)
                                    <tr>
                                        <td class="px-8 pb-2 table-contents-font-size vertical-top">
                                            {{ $addLessItem['ledger_name'] }}
                                        </td>
                                        <td class="px-8 pb-2 table-contents-font-size vertical-top text-end">
                                            {{ $pdfSymbol.getCurrencyFormat($addLessItem['amount'] ?? '0.0') }}
                                        </td>
                                    </tr>
                                @endforeach
                                <tr>
                                    <td class="px-8 pb-4 table-contents-font-size vertical-top">
                                        {{ $changeLabel['delivery_challan_round_off'] ?? 'Round off' }}
                                    </td>
                                    <td class="px-8 pb-4 table-contents-font-size vertical-top text-end">
                                        {{ $pdfSymbol.getCurrencyFormat($transaction->rounding_amount ?? '0.0') }}
                                    </td>
                                </tr>

                            </table>
                        </div>
                        <div>
                            <table class="vertical-bottom">
                                <tr class="border-top">
                                    <td class="px-8 pt-4 pb-4 text-primary total-font-size fw-7">
                                        {{ $changeLabel['total'] ?? 'Total' }}
                                    </td>
                                    <td class="px-8 pt-4 pb-4 text-primary text-end total-font-size fw-7">
                                        {{ $pdfSymbol.getCurrencyFormat($total ?? '0.0') }}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</body>

</html>
