<!DOCTYPE html>
<html lang="en">
@php
    if (!isset($isA5Pdf)) {
        $isA5Pdf = false;
        if (getCompanyDeliveryChallanPDFFormat() == \App\Models\CompanySetting::PDF_FORMAT[\App\Models\CompanySetting::A5]) {
            $isA5Pdf = true;
        }
    }
    $pdfSymbol = getPrintPdfCurrencySymbol($currentCompany->id);
    $dockereEnabled = dockerEnabled() && !isset($preview_enabled);
    $fontStyleName = $dockereEnabled ? 'Arial Unicode MS' : 'Arial-unicode-ms';
    $fontCssName = $dockereEnabled ? 'font-docker.css' : 'font.css';
@endphp
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/' . $fontCssName) }}">
    <title>{{ isset($fileName) ? $fileName : $transaction->challan_number }}</title>
    @include('company.pdf.sale_pdf.print_custom_font_css_variables')
    <style>
        h1 {
            font-size: 27px;
        }

        * {
            margin: 0;
            padding: 0;
            text-indent: 0;
            font-family: "{{ $fontStyleName }}";
            font-size: 13px;
            font-weight: 400;
            box-sizing: border-box;
        }

        @page {
            margin: 20px;
        }
        .main-table {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            width: 100%;
            box-sizing: border-box;
            border: 2px solid black;
        }

        table {
            display: table;
            width: 100%;
            border-collapse: collapse;
        }

        .text-primary {
            color: #4f158c;
        }

        .address {
            font-size: 11px;
            /* line-height: 14px; */
            padding-left: 8px;
            padding-right: 8px;
        }

        .phone {
            font-size: 11px;
            /* line-height: 14px; */
            padding-left: 8px;
            padding-right: 8px;
        }

        td {
            vertical-align: top;
        }

        .fs-13 {
            font-size: 13px;
        }

        .fs-12 {
            font-size: 12px;
        }

        .fw-6 {
            font-weight: 600 !important;
        }

        .whitespace-nowrap {
            white-space: nowrap;
        }

        .border-bottom {
            border-bottom: 1px solid black;
        }

        .border-right {
            border-right: 1px solid black;
        }

        .border-top {
            border-top: 1px solid black;
        }

        .border-left {
            border-left: 1px solid black;
        }

        .vertical-top {
            vertical-align: top;
        }

        .vertical-middle {
            vertical-align: middle;
        }

        .vertical-bottom {
            vertical-align: bottom;
        }

        .text-center {
            text-align: center;
        }

        .text-start {
            text-align: left;
        }

        .text-end {
            text-align: right;
        }

        .table-heading {
            padding: 3px 8px;
            text-align: left;
            position: relative;
            /* background-color: #eeeeee !important; */
        }

        .signature {
            max-width: 95%;
            height: 100px;
        }

        .desc {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            font-size: 12px;
            position: relative;
            padding-left: 5px;
            padding-right: 5px;
        }

        .desc::before {
            position: absolute;
            content: "(";
            top: 0;
            left: 0;
            font-size: 12px;
        }

        .desc::after {
            position: absolute;
            content: ")";
            bottom: 2px;
            right: 0;
            font-size: 12px;
        }

        .item-table tr:nth-last-child(-n+2):not(:last-child) {
            font-size: 12px;
            border-bottom: 1px solid black;
            padding: 4px 8px 0 4px;
            height: 100%;
            vertical-align: top;
        }

    </style>
    @include('company.pdf.sale_pdf.print_custom_font_css')
</head>

<body>
    <div style="padidng:20px;">
    <div class="main-table">
        @if(($invoiceSetting['show_slogan'] ?? false) && isset($invoiceSetting['slogan']))
        <div class="text-center border-bottom" style="padding: 4px 2px 2px 2px">
            <h6 class="mb-1 company-address-font-size">
                {{ $invoiceSetting['slogan'] }}
            </h6>
        </div>
    @endif
        <div class="text-center border-bottom" style="padding: 4px 2px 2px 2px">
            <h6 class="fw-6 text-primary" style="font-size: 15px">
                {{ $deliveryChallanTransactionMaster->title_of_print }} ({#INVOICE_TYPE#})
            </h6>
        </div>

        {{-- Logo / Company Name Section Start --}}
        <table cellpadding="0">
            <tr>
                <td class="vertical-middle" style="padding: 10px 25px; width: 100px; max-width:100px; height: 100px">
                    @if (isset($currentCompany->company_logo) && ($invoiceSetting['delivery_challan_logo'] ?? true) && $currentCompany->company_logo != asset('images/preview-img.png'))
                        <img src={{ $currentCompany->company_logo ?? '' }} alt="Logo" style="object-fit: contain;" width="100" >
                    @endif
                </td>
                <td class="vertical-middle text-center" style="margin-left:-50px; width:60%;">
                    <h1 class=" company-name-font-size company-font-customization" >{{ strtoupper($currentCompany->trade_name) }}</h1>
                    @if(($invoiceSetting['prop_details'] ?? true) && isset($customProp))
                        <p class="mt-1 fw-6 company-address-font-size">{{ $customProp->label_name.' : '.$customProp->label_value }}</p>
                    @endif

                    <p class="company-address-font-size">
                        {{ isset($companyBillingAddress->address_1) ? strtoupper($companyBillingAddress->address_1 .',') : null }}
                        {{ isset($companyBillingAddress->address_2) ? strtoupper($companyBillingAddress->address_2 .',') : null }}
                        {{ isset($companyBillingAddress->city_id) ?  strtoupper(getCityName($companyBillingAddress->city_id).',') : null }}
                        {{ isset($companyBillingAddress->state_id) ? strtoupper(getStateName($companyBillingAddress->state_id).',') : null }}
                        {{ isset($companyBillingAddress->country_id) ? strtoupper(getCountryName($companyBillingAddress->country_id).',') : null }}
                        {{ $companyBillingAddress->pin_code ?? null }}
                    </p>
                    <p class="company-address-font-size">
                        @if ($invoiceSetting['delivery_challan_mobile_number'] ?? true)
                            {{ $changeLabel['delivery_challan_tel_label'] ?? 'Tel' }}:
                            {{
                                (isset($invoiceSetting['region_code']) ? '+' . $invoiceSetting['region_code'] . ' ' : '') .
                                (isset($invoiceSetting['alternate_phone']) ? $invoiceSetting['alternate_phone'] : ('+' . $currentCompany->user->region_code . ' ' . $currentCompany->user->phone)) .
                                (isset($invoiceSetting['region_code_2']) && isset($invoiceSetting['alternate_phone_2']) ? ', +' . $invoiceSetting['region_code_2'] . ' ' : '') .
                                (isset($invoiceSetting['alternate_phone_2']) ? (isset($invoiceSetting['region_code_2']) ? '' : ', ') . $invoiceSetting['alternate_phone_2'] : '')
                            }}
                        @endif
                        @if (($invoiceSetting['delivery_challan_mobile_number'] ?? true) && ($invoiceSetting['delivery_challan_email'] ?? true) && (($invoiceSetting['alternate_email'] ?? ($currentCompany->user->email ?? null)) != null))
                            |
                        @endif
                        @if (isset($invoiceSetting['delivery_challan_email']) ? $invoiceSetting['delivery_challan_email'] : true)
                            {{ $invoiceSetting['alternate_email'] ?? ($currentCompany->user->email ?? null) }}
                        @endif
                    </p>
                    @if (isCompanyGstApplicable())
                        <p class="company-address-font-size">{{ $changeLabel['delivery_challan_gstin_label'] ?? 'GSTIN' }}: {{ '  ' . $currentCompany->companyTax->gstin ?? null }}</p>
                    @endif
                    @foreach (printCustomPDFLabelsForDeliveryChallan() as $key => $customLabel)
                        <p class="company-address-font-size">{{ $key ?? null }}: {{ $customLabel ?? null }}</p>
                    @endforeach
                </td>
                <td class="vertical-middle text-end" style="padding: 10px 25px; width: 100px; max-width:100px; height: 100px">
                </td>
            </tr>
        </table>
        {{-- Logo / Company Name Section End --}}

        {{-- Bill To / Ship to / Invoice Details Section Start --}}
        <table cellpadding="0">
            <tr class="border-bottom">
                <td class="border-right vertical-top" style="width:33.33%;">
                    <p class="text-primary border-bottom border-top fw-6 table-heading header-labels-font-size"
                        >
                        {{ $changeLabel['delivery_challan_bill_to_label'] ?? 'Billing Address' }}:
                    </p>
                    <h4 class="fw-6 header-contents-font-size" style="padding: 4px 0 1px 8px;">
                        {{ strtoupper($customerDetail->name) }}
                    </h4>
                    @if (isset($billingAddress))
                        <p class="address header-contents-font-size" >
                            @if ($billingAddress->address_1 != null)
                                {{ strtoupper($billingAddress->address_1) }}
                            @endif
                            @if ($billingAddress->address_2 != null)
                                {{ strtoupper($billingAddress->address_2) }},<br>
                            @endif
                            {{ $billingAddress->city_id != null ? strtoupper(getCityName($billingAddress->city_id)) . ',' : null }}
                            {{ $billingAddress->state_id != null ? strtoupper(getStateName($billingAddress->state_id)) . ',' : null }}
                            {{ strtoupper(getCountryName($billingAddress->country_id ?? null)) }},
                            {{ $billingAddress->pin_code ?? null }}
                        </p>
                    @endif
                    <p class="phone header-contents-font-size">
                        @if (!empty($transaction->party_phone_number))
                            Contact No:
                                +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                        @elseif (!empty($customerDetail->model->phone_1))
                            Contact No:
                                +{{ $customerDetail->model->region_code_1 ?? '' }} {{ $customerDetail->model->phone_1 ?? null }}
                        @endif
                        @if (!empty($customerDetail->model->phone_2) && ($customerDetail->model->phone_1 !== $customerDetail->model->phone_2))
                            {{ empty($customerDetail->model->phone_1) && empty($transaction->party_phone_number) ? 'Contact No:' : "|" }}
                            <span class="whitespace-nowrap header-contents-font-size">
                                +{{ $customerDetail->model->region_code_2 ?? '' }} {{ $customerDetail->model->phone_2 ?? null }}
                            </span>
                        @endif
                    </p>
                    @if (!empty($customerDetail->model->person_email))
                        <p class="header-contents-font-size" style="padding: 2px 0px 0px 8px;">
                            Email: {{ $customerDetail->model->person_email ?? null }}
                        </p>
                    @endif
                    @if ($showGst)
                        <p class="header-contents-font-size" style="padding: 2px 0px 0px 8px;">
                            GSTIN: {{ $transaction->gstin ?? $customerDetail->model->gstin ?? null }}
                        </p>
                    @endif
                    @if (! empty($panNumber) && $showPanNumber)
                        <p class="header-contents-font-size" style="padding: 2px 0px 0px 8px;">
                            PAN: {{ $panNumber ?? null }}
                        </p>
                    @endif
                </td>
                @if ($invoiceSetting['delivery_challan_ship_to_details'] ?? true)
                    <td class="border-right vertical-top" style="width:33.33%;">
                        <p class="text-primary border-bottom border-top fw-6 table-heading header-labels-font-size" >
                            {{ $changeLabel['delivery_challan_ship_to_label'] ?? 'Shipping Address' }}:
                        </p>
                        <h4 class="fw-6 header-contents-font-size" style="padding: 4px 0 1px 8px;">
                           {{ strtoupper($transaction->shipping_name ?? $customerDetail->name) }}
                        </h4>
                        <p class="address header-contents-font-size">
                            @if (isset($shippingAddress->address_1))
                                {{ strtoupper($shippingAddress->address_1) }},
                            @endif
                            @if (isset($shippingAddress->address_2))
                                {{ strtoupper($shippingAddress->address_2) }},
                            @endif
                            {{ isset($shippingAddress->city_id) ? strtoupper(getCityName($shippingAddress->city_id)) . ',' : '' }}
                            {{ isset($shippingAddress->state_id) ? strtoupper(getStateName($shippingAddress->state_id)) . ',' : '' }}
                            {{ isset($shippingAddress->country_id) ? strtoupper(getCountryName($shippingAddress->country_id)) : '' }}
                            {{ $shippingAddress->pin_code ?? null }}
                        </p>
                        <p class="phone header-contents-font-size">
                            @if (!empty($transaction->party_phone_number))
                                Contact No:
                                    +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                            @endif
                        </p>
                        @if($isCompanyGstApplicable && !empty($transaction->shipping_gstin))
                            <p class="header-contents-font-size" style="padding: 2px 0px 0px 8px;">
                                GSTIN: {{$transaction->shipping_gstin ?? null }}
                            </p>
                        @endif
                        @if (! empty($panNumber) && $showPanNumber)
                        <p class="header-contents-font-size" style="padding: 0px 0px 2px 8px; ">
                            PAN: {{ $panNumber ?? null }}
                        </p>
                    @endif
                    </td>
                @endif
                    <td class="vertical-top border-left" style="width: 33.33%">
                        <p class="text-primary border-bottom border-top fw-6 table-heading header-labels-font-size">
                            {{ $changeLabel['delivery_challan_details_label'] ?? 'Challan Details' }}:
                        </p>
                        <div style="padding: 0 8px !important">
                            <table class="table">
                                <tr>
                                    <td class="fw-6 header-contents-font-size" style="padding: 4px 1px 1px 1px;">
                                        {{ $changeLabel['delivery_challan_number_label'] ?? 'Challan No' }}:
                                    </td>
                                    <td class="text-end header-contents-font-size" style="padding: 4px 1px 1px 1px;">
                                        {{ $transaction->challan_number }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="header-contents-font-size fw-6" style="padding: 4px 1px 1px 1px;">
                                        {{ $changeLabel['delivery_challan_date_label'] ?? 'Challan Date' }}:
                                    </td>
                                    <td class="header-contents-font-size text-end" style="padding: 4px 1px 1px 1px;">
                                        {{ Carbon\Carbon::parse($transaction->challan_date)->format('d-m-Y') }}
                                    </td>
                                </tr>
                                @if ($invoiceSetting['delivery_challan_po_number'] ?? true)
                                    <tr>
                                        <td class="header-contents-font-size fw-6" style="padding: 4px 1px 1px 1px;">
                                            {{ $changeLabel['delivery_challan_po_number_label'] ?? 'PO No' }}:
                                        </td>
                                        <td class="header-contents-font-size text-end" style="padding: 4px 1px 1px 1px;">
                                            {{ $transaction->po_no }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="header-contents-font-size fw-6" style="padding: 4px 1px 4px 1px;">
                                            {{ $changeLabel['delivery_challan_po_date_label'] ?? 'PO Date' }}:
                                        </td>
                                        <td class="header-contents-font-size text-end" style="padding: 4px 1px 4px 1px;">
                                            {{ isset($transaction->po_date) ? \Carbon\Carbon::parse($transaction->po_date)->format('d-m-Y') : null }}
                                        </td>
                                    </tr>
                                @endif
                            </table>
                        </div>
                    </td>
            </tr>
        </table>
        {{-- Bill To / Ship to / Invoice Details Section End --}}

        {{-- Transport Details / EInvoice / EWay Section Start --}}
        @if (($invoiceSetting['delivery_challan_dispatch_from_details'] ?? true) || $invoiceSetting['delivery_challan_transport_details'] ?? true || !empty($eInvoice) || !empty($eWayBill) || !empty($transaction->transporter_vehicle_number))
            <table cellpadding="0">
                <tr class="border-bottom">
                    @if (isset($transaction['dispatch_address_id']) && isset($companyShippingAddress) && (($invoiceSetting['delivery_challan_dispatch_from_details'] ?? false)))
                    <td class="vertical-top border-right border-bottom header-contents-font-size" style="width:33.33%;">
                        <p class="text-primary border-bottom fw-6 table-heading header-labels-font-size">
                            {{ $changeLabel['delivery_challan_dispatch_from_label'] ?? 'Dispatch From' }}:
                        </p>
                        {{-- <h4 class="fw-6" style="padding: 4px 0 1px 8px; font-size: 15px">
                        {{ strtoupper($customerDetail->name) }}
                        </h4> --}}
                        <p class="address header-contents-font-size" >
                            {{ isset($companyShippingAddress->address_1) ? strtoupper($companyShippingAddress->address_1 .',') : null }}
                            {{ isset($companyShippingAddress->address_2) ? strtoupper($companyShippingAddress->address_2 .',') : null }}
                            {{ isset($companyShippingAddress->city_id) ?  strtoupper(getCityName($companyShippingAddress->city_id).',') : null }}
                            {{ isset($companyShippingAddress->state_id) ? strtoupper(getStateName($companyShippingAddress->state_id).',') : null }}
                            {{ isset($companyShippingAddress->country_id) ? strtoupper(getCountryName($companyShippingAddress->country_id).',') : null }}
                            {{ $companyShippingAddress->pin_code ?? null }}
                        </p>
                    </td>
                    @endif
                    @if ($invoiceSetting['delivery_challan_transport_details'] ?? true)
                        <td class="vertical-top" style="width:33.33%;">
                            <table style="display: table">
                                <tr>
                                    <td class="header-contents-font-size fw-6" style="padding: 3px 0 0 8px; width: 110px; ">
                                        {{ $changeLabel['delivery_challan_transport_name_label'] ?? 'Transport Name' }}:

                                    </td>
                                    <td style="padding: 3px 0 0 0px; " class="header-contents-font-size">
                                        {{ $transaction->transportDetails->transporter_name ?? '' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="header-contents-font-size" style="padding: 3px 0 0 8px;font-weight: bold;width: 110px; ">
                                        Document No:
                                    </td>
                                    <td style="padding: 3px 0 0 0px; " class="header-contents-font-size">
                                        {{ $transaction->transporter_document_number ?? '' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="header-contents-font-size" style="padding: 3px 0 0 8px;font-weight: bold;width: 110px; ">
                                        Document Date:
                                    </td>
                                    <td style="padding: 3px 0 0 0px; " class="header-contents-font-size">
                                        {{ !empty($transaction->transporter_document_date) ? \Carbon\Carbon::parse($transaction->transporter_document_date)->format('d-m-Y') : '' }}
                                    </td>
                                </tr>
                                @if(!empty($transaction->transporter_vehicle_number))
                                    <tr>
                                        <td class="header-contents-font-size vertical-top" style="padding: 3px 0 4px 8px;font-weight: bold;width: 110px; ">
                                            {{ $changeLabel['delivery_challan_transport_vehicle_number_label'] ?? 'Vehicle No' }}:
                                        </td>
                                        <td class="header-contents-font-size vertical-top" style="padding: 3px 0 4px 0px; ">
                                            {{ $transaction->transporter_vehicle_number ?? '' }}
                                        </td>
                                    </tr>
                                @endif
                            </table>
                        </td>
                    @endif
                    {{-- @if(!empty($transaction->transporter_vehicle_number))
                        <td>
                            <table>
                                <tr>
                                    <td class="fs-12 vertical-top" style="padding: 3px 0 4px 8px;font-weight: bold;width: 110px;">
                                        {{ $changeLabel['delivery_challan_transport_vehicle_number_label'] ?? 'Vehicle No' }}:
                                    </td>
                                    <td class="fs-12 vertical-top" style="padding: 3px 0 4px 0px;">
                                        {{ $transaction->transporter_vehicle_number ?? '' }}
                                    </td>
                                </tr>
                            </table>
                        </td>
                    @endif --}}
                </tr>
            </table>
        @endif
        {{-- Transport Details / EInvoice / eWay Section End --}}

        {{-- Custom Fields Section Start --}}
        @if (count($customFieldValues) > 0)
            @php
                $customFields = collect($customFieldValues)->where('is_show_in_print', true)->values();
            @endphp
            <table cellpadding="0" class="item-table">
                @foreach ($customFields->chunk(3) as $chunk)
                    <tr class="border-bottom">
                        @foreach ($chunk as $customField)
                            <td class="{{ $loop->last ? '' : 'border-right' }}" style="padding: 6px 8px; width:150px; {{ $isA5Pdf ? 'font-size: 9px; line-height: 8px;' : '' }}">
                                <span style="font-weight: bold;{{ $isA5Pdf ? 'font-size: 9px; line-height: 8px;' : '' }}">{{ $customField['label_name'] ?? '' }}</span> : {{ $customField['value'] ?? '' }}
                            </td>
                        @endforeach
                    </tr>
                @endforeach
            </table>
        @endif
        {{-- Custom Fields Section End --}}

        @php
            $customFieldItemsValues = collect($transactionItems[0]['customItemsValues'])->where('is_show_in_print', true)->values();
            $customFieldItemsHeaders = collect($customFieldItemsValues)->pluck('label_name')->toArray();
        @endphp
        {{-- Item Table Section Start --}}
        @if ($itemType == \App\Models\DeliveryChallanTransaction::WITH_AMOUNT)
            {{-- Item Type WITH AMOUNT --}}
            <table cellpadding="0" style="flex-grow:1;" class="item-table">
                <tr class="border-bottom">
                    <td class="table-headings-font-size border-right text-center"
                        style="padding: 6px 8px;font-weight: bold; width:25px; ">
                        {{ $changeLabel['delivery_challan_sn_label'] ?? 'SN' }}
                    </td>
                    <td class="table-headings-font-size border-right whitespace-nowrap {{ getCompanyPdfFormat(true, true) == \App\Models\CompanySetting::A5 ? 'min-width-150' : 'min-width-250' }}"
                        style="padding: 6px 8px;font-weight: bold;">
                        {{ $changeLabel['delivery_challan_item_name_label'] ?? 'Item Name' }}
                    </td>
                    @if (count($customFieldItemsHeaders) > 0)
                        @foreach ($customFieldItemsHeaders as $customFieldItemHeader)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-center"
                                style="padding: 6px 8px; font-weight: bold; width:100px;">
                                {{ $customFieldItemHeader ?? '' }}
                            </td>
                        @endforeach
                    @endif
                    @if ($isCompanyGstApplicable &&($showPrintSettings['show_delivery_challan_hsn_sac'] ?? true))
                        <td
                            class="table-headings-font-size border-right whitespace-nowrap text-start" style="padding: 6px 8px; font-weight: bold; width:80px; ">
                            {{ $changeLabel['delivery_challan_hsn_sac_label'] ?? 'HSN/SAC' }}
                        </td>
                    @endif
                    @if ($isCompanyGstApplicable &&($showPrintSettings['show_delivery_challan_gst'] ?? true))
                        <td
                            class="table-headings-font-size border-right whitespace-nowrap text-center" style="padding: 6px 8px; font-weight: bold; width:80px;">
                            {{ $changeLabel['delivery_challan_gst_label'] ?? 'GST (%)' }}
                        </td>
                    @endif
                    @if($showPrintSettings['show_delivery_challan_qty'] ?? true)
                        <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                            style="padding: 6px 8px; font-weight: bold; width:100px;">
                            {{ $changeLabel['delivery_challan_qty_label'] ?? 'Qty' }}
                        </td>
                    @endif
                    @if ($showPrintSettings['delivery_challan_uom_enable'] ?? true)
                        @if ($transactionItems->sum('secondary_quantity') != 0.0)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                style="padding: 6px 8px; font-weight: bold; width:100px;">
                                {{ $changeLabel['delivery_challan_uom_label'] ?? 'UOM' }}
                            </td>
                        @endif
                    @endif
                    @if($showPrintSettings['show_delivery_challan_mrp'] ?? true)
                    @if ($transactionItems->sum('mrp') != 0.0)
                        <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                            style="padding: 6px 8px; font-weight: bold; width:100px; ">
                            {{ $changeLabel['delivery_challan_mrp_label'] ?? 'MRP' }}
                        </td>
                    @endif
                    @endif
                    @if ($isCompanyGstApplicable)
                        @if($showPrintSettings['show_delivery_challan_rate_with_gst'] ?? false)
                        <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                            style="padding: 6px 8px; font-weight: bold; width:100px; ">
                            {{ $changeLabel['delivery_challan_rate_with_gst_label'] ?? 'Rate With GST' }}
                        </td>
                        @endif
                    @endif
                    @if($showPrintSettings['show_delivery_challan_rate'] ?? true)
                    <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                        style="padding: 6px 8px; font-weight: bold; width:100px; ">
                        {{ $changeLabel['delivery_challan_rate_label'] ?? 'Rate' }}
                    </td>
                    @endif

                    @if($showPrintSettings['show_delivery_challan_discount'] ?? true)
                        @if($transactionItems->sum('discount_value') != 0.0)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                style="padding: 6px 8px; font-weight: bold; width:100px; ">
                                {{ $changeLabel['delivery_challan_discount_label'] ?? 'Dis.' }}
                            </td>
                        @endif
                    @endif
                    @if ($showPrintSettings['delivery_challan_dis_2_enable'] ?? true)
                        @if($transactionItems->sum('discount_value_2') != 0.0)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                style="padding: 6px 8px; font-weight: bold; width:100px; ">
                                {{ $changeLabel['delivery_challan_dis_2_label'] ?? 'Dis. 2' }}
                            </td>
                        @endif
                    @endif
                    @if ($transactionItems->sum('total_discount_amount') != 0.0)
                        @if($showPrintSettings['show_delivery_challan_total_discount'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                style="padding: 6px 8px; font-weight: bold; width:100px; ">
                                {{ $changeLabel['delivery_challan_total_discount_label'] ?? 'Total Dis.' }}
                            </td>
                        @endif
                    @endif
                    <td class="table-headings-font-size whitespace-nowrap text-end"
                        style="padding: 6px 8px; font-weight: bold; width:100px; ">
                        {{ $changeLabel['delivery_challan_taxable_value_label'] ?? ($isCompanyGstApplicable ? 'Taxable Value' : 'Amount') }}
                    </td>
                </tr>

                @foreach ($transactionItems as $key => $item)

                    @php
                        $uniqueId = ++$key;
                        $ids = collect($customFieldItemsValues)->pluck('custom_field_item_id')->values();
                        $printCustomFields = collect($item['customItemsValues'])->where('is_show_in_print', true)->whereIn('custom_field_item_id', $ids)->values();
                    @endphp
                    <tr>
                        <td class="table-contents-font-size border-right text-center"
                            style="padding: 4px 8px 0 8px;  ">
                            {{ $uniqueId }}
                        </td>
                        <td class="table-contents-font-size border-right fw-6"
                            style="padding: 4px 8px 0 8px; ">
                            <p class="table-contents-font-size">{{ $item->items->item_name ?? null }}</p>
                            @if ($item->items->sku != null && ($invoiceSetting['show_delivery_challan_item_sku'] ?? true))
                                <p style="" class="description-font-size">Item Code:
                                    {{ $item->items->sku ?? null }}</p>
                            @endif
                            <p style="word-break: break-word; " class="description-font-size">
                                {!! !empty($item->consolidating_items_to_invoice)
                                    ? '(' . consolidatingItemsToInvoice($item->consolidating_items_to_invoice) . ')'
                                    : null !!}
                            </p>
                            <p style="word-break: break-word;" class="description-font-size">
                                {!! !empty($item->additional_description) ? nl2br('(' . $item->additional_description . ')') : null !!}
                            </p>
                            @if(!empty($item->items->item_image) && ($invoiceSetting['show_item_image'] ?? true))
                                <img src="{{ $item->items->item_image }}" width="60"  height="60" style="margin-top: 4px">
                            @endif
                        </td>
                        @if (count($printCustomFields) > 0)
                            @foreach ($printCustomFields as $customFieldItemsValue)
                                <td class="table-contents-font-size border-right whitespace-nowrap text-end" style="padding: 4px 8px 0 8px; ">
                                    {{ $customFieldItemsValue['value'] ?? '' }}
                                </td>
                            @endforeach
                        @endif

                        @if ($isCompanyGstApplicable)

                        @if($showPrintSettings['show_delivery_challan_hsn_sac'] ?? true)
                            <td class="table-contents-font-size whitespace-nowrap text-start border-right"
                                style="padding: 4px 8px 0 8px; ">
                                {{ $item->hsn_code ?? $item->items->model->hsn_sac_code ?? null }}
                            </td>
                        @endif
                            @if($showPrintSettings['show_delivery_challan_gst'] ?? true)
                            <td class="table-contents-font-size border-right whitespace-nowrap text-center"
                                style="padding: 4px 8px 0 8px; ">
                                {{ $item->gst_tax_percentage ?? '0.0' }}
                            </td>
                            @endif
                        @endif

                        @if($showPrintSettings['show_delivery_challan_qty'] ?? true)
                        <td class="table-contents-font-size border-right whitespace-nowrap text-end"
                            style="padding: 4px 8px 0 8px; ">
                            {{ $item->primary_quantity }}
                            @if($showPrintSettings['show_delivery_challan_unit'] ?? true)
                            {{ $item->primary_unit_name }}
                            @endif
                        </td>
                        @endif
                        @if ($showPrintSettings['delivery_challan_uom_enable'] ?? true)
                            @if ($transactionItems->sum('secondary_quantity') != 0.0)
                                <td class="table-contents-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 4px 8px 0 8px; ">
                                    {{ round( $item->secondary_quantity,2) }}
                                    @if($showPrintSettings['show_delivery_challan_unit'] ?? true)
                                    {{ $item->secondary_unit_name }}
                                    @endif
                                </td>
                            @endif
                        @endif
                        @if($showPrintSettings['show_delivery_challan_mrp'] ?? true)
                        @if ($transactionItems->sum('mrp') != 0.0)
                            <td class="table-contents-font-size border-right whitespace-nowrap text-end"
                                style="padding: 4px 8px 0 8px; ">
                                {{ !empty($item->mrp) ? $pdfSymbol.getCurrencyFormatFor3digit($item->mrp) : '-' }}
                            </td>
                        @endif
                        @endif
                        @if ($isCompanyGstApplicable)
                        @if($showPrintSettings['show_delivery_challan_rate_with_gst'] ?? false)
                        <td class="table-contents-font-size border-right whitespace-nowrap text-end"
                            style="padding: 4px 8px 0 8px; ">
                            {{ $pdfSymbol.getCurrencyFormatFor3digit($item->rpu_with_gst) }}
                        </td>
                        @endif
                        @endif
                        @if($showPrintSettings['show_delivery_challan_rate'] ?? true)
                        <td class="table-contents-font-size border-right whitespace-nowrap text-end"
                            style="padding: 4px 8px 0 8px;">
                            {{ $pdfSymbol.getCurrencyFormatFor3digit($item->rpu_without_gst) }}
                        </td>
                        @endif

                        @if ($transactionItems->sum('discount_value') != 0.0)
                            @if($showPrintSettings['show_delivery_challan_discount'] ?? true)
                                @if ($item->discount_type == \App\Models\DeliveryChallanTransaction::DISCOUNT_TYPE_AMOUNT)
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-end"
                                        style="padding: 4px 8px 0 8px; ">
                                        {{ $pdfSymbol.getCurrencyFormatFor3digit($item->discount_value) ?? '0.0' }}
                                    </td>
                                @else
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-end"
                                        style="padding: 4px 8px 0 8px; ">
                                        {{ $item->discount_value . '(%)' ?? '0.0(%)' }}
                                    </td>
                                @endif
                            @endif
                        @endif
                        @if($transactionItems->sum('discount_value_2') != 0.0)
                            @if ($showPrintSettings['delivery_challan_dis_2_enable'] ?? true)
                                @if ($item->discount_type_2 == \App\Models\DeliveryChallanTransaction::DISCOUNT_TYPE_AMOUNT)
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-end"
                                        style="padding: 4px 8px 0 8px; ">
                                        {{ $pdfSymbol.getCurrencyFormatFor3digit($item->discount_value_2) ?? '0.0' }}
                                    </td>
                                @else
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-end"
                                        style="padding: 4px 8px 0 8px; ">
                                        {{ $item->discount_value_2 . '(%)' ?? '0.0(%)' }}
                                    </td>
                                @endif
                            @endif
                        @endif
                        @if ($transactionItems->sum('total_discount_amount') != 0.0)
                            @if($showPrintSettings['show_delivery_challan_total_discount'] ?? true)
                                <td class="table-contents-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 4px 8px 0 8px; ">
                                    {{ $pdfSymbol.getCurrencyFormatFor3digit($item->total_discount_amount ?? '0.0') }}
                                </td>
                            @endif
                        @endif
                        <td class="table-contents-font-size whitespace-nowrap text-end"
                            style="padding: 4px 8px 0 8px; ">
                            {{ $pdfSymbol.getCurrencyFormat(round($item->total ?? '0.0', getCompanyFixedDigitNumber())) }}
                        </td>
                    </tr>
                @endforeach
                <tr class="">
                    <td class="table-headings-font-size border-right text-center fw-6"
                        style=""></td>
                    <td class="table-headings-font-size border-right fw-6"
                        style="padding: 4px 8px; ">
                        Total
                    </td>
                    @if (count($customFieldItemsValues) > 0)
                        @foreach ($customFieldItemsValues as $customFieldItemsValue)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6" style="padding: 4px 8px;">
                            </td>
                        @endforeach
                    @endif
                    @if ($isCompanyGstApplicable)
                        @if($showPrintSettings['show_delivery_challan_hsn_sac'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-center fw-6"
                                style="padding: 4px 8px; ">
                            </td>
                        @endif
                        @if($showPrintSettings['show_delivery_challan_gst'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-center fw-6"
                                style="padding: 4px 8px; ">
                            </td>
                        @endif
                    @endif
                    @if($showPrintSettings['show_delivery_challan_qty'] ?? true)
                        <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6"
                            style="padding: 4px 8px; ">
                            {{ getCurrencyFormat($transactionItems->sum('primary_quantity')) }}
                        </td>
                    @endif
                    @if ($showPrintSettings['delivery_challan_uom_enable'] ?? true)
                            @if ($transactionItems->sum('secondary_quantity') != 0.0)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6"
                                    style="padding: 4px 8px 0 8px; ">
                                    {{ getCurrencyFormat($transactionItems->sum('secondary_quantity')) }}
                                </td>
                            @endif
                        @endif
                    @if($showPrintSettings['show_delivery_challan_mrp'] ?? true)
                        @if ($transactionItems->sum('mrp') != 0.0)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6"
                                style="padding: 4px 8px; ">
                            </td>
                        @endif
                    @endif
                    @if ($isCompanyGstApplicable)
                    @if($showPrintSettings['show_delivery_challan_rate_with_gst'] ?? false)
                    <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6"
                        style="padding: 4px 8px; ">
                    </td>
                    @endif
                    @endif
                    @if($showPrintSettings['show_delivery_challan_rate'] ?? true)
                        <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6"
                            style="padding: 4px 8px; ">
                        </td>
                    @endif
                    @if ($transactionItems->sum('discount_value') != 0.0)
                        @if($showPrintSettings['show_delivery_challan_discount'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6"
                                style="padding: 4px 8px; ">
                            </td>
                        @endif
                    @endif
                    @if($transactionItems->sum('discount_value_2') != 0.0)
                        @if ($showPrintSettings['delivery_challan_dis_2_enable'] ?? true)
                            <td class="table-headings-font-size border-right whitespace-nowrap text-end fw-6"
                                style="padding: 4px 8px; ">
                            </td>
                        @endif
                    @endif
                    @if ($transactionItems->sum('total_discount_amount') != 0.0)
                        @if($showPrintSettings['show_delivery_challan_total_discount'] ?? true)
                            <td
                                class="table-headings-font-size border-right whitespace-nowrap text-end fw-6"style="padding: 4px 8px; ">
                                {{ $pdfSymbol.getCurrencyFormat($transactionItems->sum('total_discount_amount' ?? 0.0)) }}
                            </td>
                        @endif
                    @endif
                    <td
                        class="table-headings-font-size whitespace-nowrap text-end fw-6"style="padding: 4px 8px; ">
                        {{ $pdfSymbol.getCurrencyFormat($transaction->gross_value) }}
                    </td>
                </tr>
            </table>
            {{-- Item Type WITH AMOUNT End --}}
        @else
        <table cellpadding="0" style="flex-grow: 1" class="item-table">
            <tr class="border-bottom">
                <td class="table-headings-font-size border-right text-center" style="padding: 6px 8px;font-weight: bold; width:25px;">
                    {{ $changeLabel['delivery_challan_sn_label'] ?? 'SN' }}
                </td>
                <td class="table-headings-font-size border-right whitespace-nowrap" style="padding: 6px 8px;font-weight: bold; ">
                    {{ $changeLabel['delivery_challan_item_name_label'] ?? 'Item Name' }}
                </td>
                @if (count($customFieldItemsHeaders) > 0)
                    @foreach ($customFieldItemsHeaders as $customFieldItemHeader)
                        <td class="table-headings-font-size border-left whitespace-nowrap text-end"
                            style="padding: 6px 8px; font-weight: bold; width:100px;">
                            {{ $customFieldItemHeader ?? '' }}
                        </td>
                    @endforeach
                @endif
                @if($showPrintSettings['show_delivery_challan_qty'] ?? true)
                    <td class="table-headings-font-size border-left whitespace-nowrap text-end"
                        style="padding: 6px 8px; font-weight: bold; width:100px;">
                    {{ $changeLabel['delivery_challan_qty_label'] ?? 'Qty' }}
                </td>
                @endif
                @if (($showPrintSettings['delivery_challan_uom_enable'] ?? false) && ($transactionItems->sum('secondary_quantity') != 0.0))
                    <td class="table-headings-font-size border-left whitespace-nowrap text-end" style="padding: 6px 8px; font-weight: bold; width:100px;">
                        {{ $changeLabel['delivery_challan_uom_label'] ?? 'UOM' }}
                    </td>
                @endif
            </tr>
            @foreach ($transactionItems as $key => $item)
                @php
                    $uniqueId = ++$key;
                    $ids = collect($customFieldItemsValues)->pluck('custom_field_item_id')->values();
                    $printCustomFields = collect($item['customItemsValues'])->where('is_show_in_print', true)->whereIn('custom_field_item_id', $ids)->values();
                @endphp
                <tr>
                    <td class="table-contents-font-size border-right text-center" style="padding: 4px 8px 0 8px;">
                        {{ $uniqueId }}
                    </td>
                    <td class="table-contents-font-size border-right fw-6" style="padding: 4px 8px 0 8px;">
                        {{ $item->items->item_name ?? null }}
                        @if ($item->items->sku != null && ($invoiceSetting['show_delivery_challan_item_sku'] ?? true))
                            <p style="" class="description-font-size">Item Code:
                            {{ $item->items->sku ?? null }}</p>
                        @endif
                        <p style="word-break: break-word;" class="description-font-size">
                            {!! !empty($item->consolidating_items_to_invoice) ? '(' . consolidatingItemsToInvoice($item->consolidating_items_to_invoice) . ')' : null !!}
                        </p>
                        <p style="word-break: break-word; " class="description-font-size">
                            {!! !empty($item->additional_description) ? nl2br('(' . $item->additional_description . ')') : null !!}
                        </p>
                        @if(!empty($item->items->item_image) && ($invoiceSetting['show_delivery_challan_item_image'] ?? true))
                            <img src="{{ $item->items->item_image }}" width="60"  height="60" style="margin-top: 4px">
                        @endif
                    </td>
                    @if (count($printCustomFields) > 0)
                        @foreach ($printCustomFields as $customFieldItemsValue)
                            <td class="table-contents-font-size border-left whitespace-nowrap text-end" style="padding: 4px 8px 0 8px; ">
                                {{ $customFieldItemsValue['value'] ?? '' }}
                            </td>
                        @endforeach
                    @endif
                    @if($showPrintSettings['show_delivery_challan_qty'] ?? true)
                        <td class="table-contents-font-size border-left whitespace-nowrap text-end" style="padding: 4px 8px 0 8px; ">
                            {{ $item->primary_quantity }}
                            @if($showPrintSettings['show_delivery_challan_unit'] ?? true)
                            {{ $item->primary_unit_name }}
                            @endif
                        </td>
                    @endif
                    @if (($showPrintSettings['delivery_challan_uom_enable'] ?? false) && ($transactionItems->sum('secondary_quantity') != 0.0))
                        <td class="table-contents-font-size border-left whitespace-nowrap text-end" style="padding: 4px 8px 0 8px;">
                            {{ round( $item->secondary_quantity,2) ?? null }}
                            @if($showPrintSettings['show_delivery_challan_unit'] ?? true)
                            {{ $item->secondary_unit_name ?? null }}
                            @endif
                        </td>
                    @endif
                </tr>
            @endforeach
            <tr class="">
                <td class="table-headings-font-size border-right fw-6" style="padding: 4px 8px;">

                </td>
                <td class="table-headings-font-size border-right fw-6" style="padding: 4px 8px 0 8px;">Total</td>
                @if (count($customFieldItemsValues) > 0)
                    @foreach ($customFieldItemsValues as $customFieldItemsValue)
                        <td class="table-headings-font-size border-left whitespace-nowrap text-end fw-6" style="padding: 4px 8px;">
                        </td>
                    @endforeach
                @endif
                @if($showPrintSettings['show_delivery_challan_qty'] ?? true)
                    <td class="table-headings-font-size border-left whitespace-nowrap text-end fw-6" style="padding: 4px 8px;">
                        {{ getCurrencyFormat($transactionItems->sum('primary_quantity')) }}
                    </td>
                @endif
                @if (($showPrintSettings['delivery_challan_uom_enable'] ?? false) && ($transactionItems->sum('secondary_quantity') != 0.0))
                    <td class="table-headings-font-size border-left whitespace-nowrap text-end fw-6" style="padding: 4px 8px;">
                        {{ getCurrencyFormat($transactionItems->sum('secondary_quantity')) }}
                    </td>
                @endif
            </tr>
        </table>
        @endif
        {{-- Item Table Section End --}}

        {{-- Broker Section Start --}}
            <table cellpadding="0">
                <tr class="border-top">
                        <td>
                            @if ($invoiceSetting['delivery_challan_broker_details'] ?? true)
                            <div>
                                <table>
                                    <tr class="vertical-top">
                                        <td class="header-contents-font-size vertical-top fw-6" style="padding: 5px 8px 0px 8px;">
                                            Broker:
                                        </td>
                                        <td class="header-contents-font-size text-start vertical-bottom" style="white-space: normal;">
                                            {{ $transaction->brokerDetails->broker_name ?? '' }}
                                        </td>
                                    </tr>
                                    @if($isCompanyGstApplicable)
                                    <tr>
                                        <td class="header-contents-font-size whitespace-nowrap fw-6" style="padding: 3px 8px 1px 8px; ">
                                            GSTIN:
                                        </td>
                                        <td class="header-contents-font-size text-start" style="white-space: nowrap; ">
                                            {{ $transaction->brokerDetails->gstin ?? '' }}
                                        </td>
                                    </tr>
                                    @endif
                                </table>
                            </div>
                            @endif
                        </td>
                    @if ($itemType == \App\Models\DeliveryChallanTransaction::WITH_AMOUNT)
                        <td class="vertical-top" style="{{ $isA5Pdf ? 'width: 150px' : 'width: 250px' }}; border-left: 1px solid black">
                            <table>
                                @foreach ($additionalCharges as $additionalCharge)
                                    <tr class="{{ $loop->last ? 'border-bottom' : '' }}">
                                        <td class="vertical-top table-contents-font-size" style="padding: 4px 8px 2px 8px; ">{{ $additionalCharge['ledger_name'] }}</td>
                                        <td class="table-contents-font-size vertical-top  text-end" style="padding: 4px 8px 2px 8px; ">{{ $pdfSymbol.getCurrencyFormat($additionalCharge['amount'] ?? '0.0') }}</td>
                                    </tr>
                                @endforeach
                                <tr class="">
                                    <td class="table-contents-font-size vertical-top fw-bold fw-6"
                                        style="padding: 4px 8px 2px 8px; ">
                                        {{ $changeLabel['delivery_challan_sub_total'] ?? ($isCompanyGstApplicable ? 'Taxable Value' : 'Sub Total') }}:
                                    </td>
                                    <td class="table-contents-font-size vertical-top text-end fw-6"
                                        style="padding: 4px 8px 2px 8px; ">
                                        {{ $pdfSymbol.getCurrencyFormat(round($transaction->taxable_value, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                    </td>
                                </tr>
                                @if ($isCompanyGstApplicable)
                                    @if ($transaction->cgst != 0)
                                        <tr>
                                            <td class="table-contents-font-size vertical-top"
                                                style="padding: 3px 8px 2px 8px; ">
                                                {{ $changeLabel['delivery_challan_cgst'] ?? 'CGST' }}:
                                            </td>
                                            <td class="table-contents-font-size vertical-top text-end"
                                                style="padding: 3px 8px 2px 8px; ">
                                                {{ $pdfSymbol.getCurrencyFormat(round($transaction->cgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                            </td>
                                        </tr>
                                    @endif
                                    @if ($transaction->sgst != 0)
                                        <tr>
                                            <td class="table-contents-font-size vertical-top"
                                                style="padding: 3px 8px 2px 8px;">
                                                {{ $changeLabel['delivery_challan_sgst'] ?? 'SGST' }}:
                                            </td>
                                            <td class="table-contents-font-size vertical-top text-end"
                                                style="padding: 3px 8px 2px 8px;">
                                                {{ $pdfSymbol.getCurrencyFormat(round($transaction->sgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                            </td>
                                        </tr>
                                    @endif
                                    @if ($transaction->igst != 0)
                                        <tr>
                                            <td class="table-contents-font-size vertical-top"
                                                style="padding: 3px 8px 2px 8px; ">
                                                {{ $changeLabel['delivery_challan_igst'] ?? 'IGST' }}:
                                            </td>
                                            <td class="table-contents-font-size vertical-top text-end"
                                                style="padding: 3px 8px 2px 8px; ">
                                                {{ $pdfSymbol.getCurrencyFormat(round($transaction->igst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                            </td>
                                        </tr>
                                    @endif
                                @endif
                                @if ($transaction->tcs_amount != 0)
                                    <tr>
                                        <td class="table-contents-font-size vertical-top"
                                            style="padding: 3px 8px 2px 8px; ">
                                            {{ $changeLabel['delivery_challan_tcs'] ?? 'TCS' }}:
                                        </td>
                                        <td class="table-contents-font-size vertical-top text-end"
                                            style="padding: 3px 8px 2px 8px; ">
                                            {{ $pdfSymbol.getCurrencyFormat($transaction->tcs_amount ?? '0.0') }}
                                        </td>
                                    </tr>
                                @endif
                                @if ($transaction->cess != 0)
                                    <tr>
                                        <td class="table-contents-font-size vertical-top"
                                            style="padding: 3px 8px 2px 8px;">
                                            {{ $changeLabel['delivery_challan_cess'] ?? 'Cess' }}:
                                        </td>
                                        <td class="table-contents-font-size vertical-top text-end"
                                            style="padding: 3px 8px 2px 8px; ">
                                            {{ $pdfSymbol.getCurrencyFormat($transaction->cess ?? '0.0') }}
                                        </td>
                                    </tr>
                                @endif
                                <tr>
                                    <td class="table-contents-font-size vertical-top"
                                        style="padding: 3px 8px 4px 8px;">
                                        {{ $changeLabel['delivery_challan_round_off'] ?? 'Round off' }}:
                                    </td>
                                    <td class="table-contents-font-size vertical-top text-end"
                                        style="padding: 3px 8px 4px 8px;">
                                        {{ $pdfSymbol.getCurrencyFormat($transaction->rounding_amount ?? '0.0') }}
                                    </td>
                                </tr>
                                @php
                                    if (empty($addLess)) {
                                        $total = $transaction->grand_total;
                                    } else {
                                        $addLessSum = collect($addLess)->sum('amount');
                                        $total = $transaction->grand_total - $addLessSum;
                                        $addLessSumTotal = collect($addLess)->where('is_show_in_print',1)->sum('amount');
                                        $total = $total + $addLessSumTotal;
                                    }
                                @endphp
                                @foreach (collect($addLess)->where('is_show_in_print',1) as $addLessItem)
                                    <tr>
                                        <td class="table-contents-font-size vertical-top"
                                            style="padding: 3px 8px 4px 8px;">
                                            {{ $addLessItem['ledger_name'] }}
                                        </td>
                                        <td class="table-contents-font-size vertical-top text-end"
                                            style="padding: 3px 8px 4px 8px;">
                                            {{ $pdfSymbol.getCurrencyFormat($addLessItem['amount'] ?? '0.0') }}
                                        </td>
                                    </tr>
                                @endforeach
                            </table>
                        </td>
                    @endif
                </tr>
            </table>
        {{-- Terms Of Payment / Broker Section End --}}

          {{-- Total Section Start --}}
        @if ($itemType == \App\Models\DeliveryChallanTransaction::WITH_AMOUNT)
            <table cellpadding="0">
            <tr class="border-top">
                @if($showPrintSettings['show_delivery_challan_in_words'] ?? true)
                    <td class="border-right vertical-bottom">
                        <p class="table-contents-font-size fw-6"
                            style="display: flex; padding: 6px 3px 6px 8px;">
                            {{ $changeLabel['delivery_challan_in_words'] ?? 'In Words' }}:
                            <span class="table-contents-font-size"
                                style="margin-left: 3px; font-weight: 400; ">
                                {{ getAmountToWord($total ?? '0.0') }} Only
                            </span>
                        </p>
                    </td>
                @endif
                <td style="width: 250px">
                    <table class="vertical-bottom">
                        <tr>
                            <td class="text-primary total-font-size"
                                style="padding: 6px 8px; font-weight: bold;">
                                {{ $changeLabel['total'] ?? 'Total' }}:
                            </td>
                            <td class="text-primary text-end total-font-size"
                                style="padding: 6px 8px;  font-weight: bold;">
                                {{ $pdfSymbol.getCurrencyFormat($total ?? '0.0') }}
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            </table>
        @endif
        {{-- Total Section End --}}

        {{-- GST Details Section Start --}}
        @if ($isCompanyGstApplicable && !empty($checkHsnCodeExist))
            <table cellpadding="0">
                <tr class="border-bottom" style="width: 100%">
                    <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold;">
                        SN
                    </td>
                    <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold;">
                        HSN/SAC
                    </td>
                    <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold;">
                        Taxable Amount
                    </td>
                    <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold;">
                        GST (%)
                    </td>
                    @if ($cgst != 0.0)
                        <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold;">
                            CGST
                        </td>
                    @endif
                    @if ($sgst != 0.0)
                        <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold;">
                            SGST
                        </td>
                    @endif
                    @if ($igst != 0.0)
                        <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold;">
                            IGST
                        </td>
                    @endif
                    <td class="footer-headings-font-size text-center" style="padding: 4px 8px; font-weight: bold;">
                        Total Tax
                    </td>
                </tr>
                @php
                    $uniquekey = 1;
                @endphp
                @foreach ($checkHsnCodeExist as $key => $item)
                    @foreach ($item as $hsnCode => $data)
                        <tr>
                            <td class="footer-contents-font-size border-right text-center" style="padding: 2px 8px;">
                                {{ $uniquekey++ }}
                            </td>
                            <td class="footer-contents-font-size border-right text-center" style="padding: 2px 8px;">
                                {{ !empty($hsnCode) ? $hsnCode : '-' }}
                            </td>
                            <td class="footer-contents-font-size border-right text-center" style="padding: 2px 8px;">
                                {{ getCurrencyFormat(round($checkTAXtExist[$key]['taxableValue'][$hsnCode], getCompanyFixedDigitNumber()) ?? 0) }}
                            </td>
                            <td class="footer-contents-font-size border-right text-center" style="padding: 2px 8px;">
                                {{ !empty($key) ? $key : '-' }}
                            </td>
                            @if ($cgst != 0.0)
                                <td class="footer-contents-font-size border-right text-center" style="padding: 2px 8px;">
                                    {{ getCurrencyFormat(round($checkTAXtExist[$key]['cgst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                </td>
                            @endif
                            @if ($sgst != 0.0)
                                <td class="footer-contents-font-size border-right text-center"style="padding: 2px 8px;">
                                    {{ getCurrencyFormat(round($checkTAXtExist[$key]['sgst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                </td>
                            @endif
                            @if ($igst != 0.0)
                                <td class="footer-contents-font-size border-right text-center"style="padding: 2px 8px;">
                                    {{ getCurrencyFormat(round($checkTAXtExist[$key]['igst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                </td>
                            @endif
                            @php
                                $totalTax =
                                    round($checkTAXtExist[$key]['cgst'][$hsnCode], getCompanyFixedDigitNumber()) +
                                    round($checkTAXtExist[$key]['sgst'][$hsnCode], getCompanyFixedDigitNumber()) +
                                    round($checkTAXtExist[$key]['igst'][$hsnCode], getCompanyFixedDigitNumber());
                            @endphp
                            <td class="footer-contents-font-size text-center" style="padding: 2px 8px;">
                                {{ getCurrencyFormat(round($totalTax ?? 0, getCompanyFixedDigitNumber())) }}
                            </td>
                        </tr>
                    @endforeach
                @endforeach

            </table>
        @endif
        {{-- GST Details Section End --}}

        {{-- Term and Condition / Narration / Signature Section Start --}}
        <table cellpadding="0" style="page-break-inside: avoid !important">
            <tr class="border-top">
                @if ($transaction->term_and_condition || $transaction->narration)
                    <td class="vertical-top border-right">
                        @if($showPrintSettings['show_delivery_challan_terms_and_conditions'] ?? true)
                            @if ($transaction->term_and_condition)
                                <div class="" style="{{ (($showPrintSettings['show_delivery_challan_narration'] ?? true) && $transaction->narration) ? 'border-bottom: 1px solid black' :'' }}">
                                    <h4 class="terms-and-conditions-font-size border-bottom fw-6" style="padding: 4px 8px;border-bottom: none;">
                                        {{ $changeLabel['delivery_challan_terms_and_conditions'] ?? 'Terms and Conditions' }}:
                                    </h4>
                                    <div style="padding: 4px 8px;" class="fs-12">
                                        <p class="terms-and-conditions-font-size" >
                                            {!! nl2br($transaction->term_and_condition) !!}
                                        </p>
                                    </div>
                                </div>
                            @endif
                        @endif
                        @if($showPrintSettings['show_delivery_challan_narration'] ?? true)
                            @if ($transaction->narration)
                                <div class="">
                                    <h4 class="note-font-size border-top border-bottom fw-6" style="padding: 4px 8px;border-top: none;border-bottom: none;">
                                        {{ $changeLabel['delivery_challan_narration'] ?? 'Note' }}:
                                    </h4>
                                    <div style="padding: 4px 8px;border-top: none;">
                                        <p class="note-font-size" >
                                            {!! nl2br($transaction->narration) !!}
                                        </p>
                                    </div>
                                </div>
                            @endif
                        @endif
                    </td>
                @endif
                @if(($showPrintSettings['show_delivery_challan_authorized_signatory'] ?? true) || ($invoiceSetting['delivery_challan_signature'] ?? true))
                    <td class="vertical-bottom " style="min-width: 217px; position: relative">
                        <div style="height: 100%; min-height: 175px">
                            @if ($showPrintSettings['show_delivery_challan_authorized_signatory'] ?? true)
                                <p class="footer-contents-font-size whitespace-nowrap vertical-bottom fw-6 fw-6 text-end"
                                    style="padding: 10px 8px 1px 8px;position: absolute;bottom: 130px;right: 0;">
                                    For, {{ strtoupper($currentCompany->trade_name) }}
                                </p>
                            @endif
                            <div class="text-end signature" style="position: absolute;bottom: 30px;right: 0;">
                                @if (isset($invoiceSetting['delivery_challan_signature']) && $invoiceSetting['delivery_challan_signature'] == true  && ($currentCompany->company_signature != asset('images/preview-img.png')))
                                    {{-- <div style="background-image: url('{{ $currentCompany->signature_for_estimate ?? '' }}');background-size: contain; background-repeat: no-repeat; background-position: center; width:100%; height:100% ;">
                                    </div> --}}
                                    <img src={{ $currentCompany->company_signature ?? '' }} alt="Signature"
                                        style="padding-right: 10px; background-size: contain; background-repeat: no-repeat; background-position: center; width:100%; max-height:100px;"
                                        width="100" height="100">
                                @endif
                            </div>
                            @if ($showPrintSettings['show_delivery_challan_authorized_signatory'] ?? true)
                            <p class="verical-bottom text-end footer-contents-font-size"
                                style="padding: 4px 10px 10px 8px;position: absolute;bottom: 0;right: 0;">
                                {{ $changeLabel['delivery_challan_authorized_signatory'] ?? 'Authorized Signatory' }}
                            </p>
                            @endif
                        </div>
                    </td>
                @endif
            </tr>
        </table>
        {{-- Term and Condition / Narration / Signature Section End --}}

        {{-- Received by and delivered by Section Start --}}
        <table>

            <tr class="border-top">
                @if($invoiceSetting['delivery_challan_received_by'] ?? true)
                    <td class="border-right">
                        <table>
                            <tr>
                                <td class="footer-headings-font-size fw-6" style="padding: 4px 8px 0 8px;">{{ $changeLabel['delivery_challan_recived_by'] ?? 'Received By' }}</td>
                            </tr>
                            <tr>
                                <td class="footer-contents-font-size fw-6" style="padding: 4px 8px 0 8px;">Name:</td>
                            </tr>
                            <tr>
                                <td class="footer-contents-font-size fw-6" style="padding: 4px 8px 0 8px;">Comment:</td>
                            </tr>
                            <tr>
                                <td class="footer-contents-font-size fw-6" style="padding: 4px 8px 0 8px;">Date:</td>
                            </tr>
                            <tr>
                                <td class="footer-contents-font-size fw-6" style="padding: 4px 8px 4px 8px;">Signature:</td>
                            </tr>
                        </table>
                    </td>
                @endif
                @if($invoiceSetting['delivery_challan_delivered_by'] ?? true)
                    <td>
                        <table>
                            <tr>
                                <td class="footer-headings-font-size fw-6" style="padding: 4px 8px 0 8px;">{{ $changeLabel['delivery_challan_delivered_by_label'] ?? 'Delivered By' }}</td>
                            </tr>
                            <tr>
                                <td class="footer-contents-font-size fw-6" style="padding: 4px 8px 0 8px; ">Name:</td>
                            </tr>
                            <tr>
                                <td class="footer-contents-font-size fw-6" style="padding: 4px 8px 0 8px;">Comment:</td>
                            </tr>
                            <tr>
                                <td class="footer-contents-font-size fw-6" style="padding: 4px 8px 0 8px;">Date:</td>
                            </tr>
                            <tr>
                                <td class="footer-contents-font-size fw-6" style="padding: 4px 8px 4px 8px;">Signature:</td>
                            </tr>
                        </table>
                    </td>
                @endif
            </tr>
        </table>
        </div>
    </div>
</body>
</html>
