<div class="">
    @php
    $user = getLoginUser();
    @endphp
    @if ($user->can('company_view_trading_profit_loss_report'))
    <a class="btn custom-dashboard-btn mt-3 me-3 px-1 py-2 " style="margin-top: 6px !important" data-bs-toggle="tooltip"
        data-bs-placement="bottom" title="Shortcut Key : Shift + 2" href="{{ route('company.trading-profit-loss', [
                            'start_date' => getDateCompanyFilter('trading_profit_loss_report_date')[0],
                            'end_date' => getDateCompanyFilter('trading_profit_loss_report_date')[1],
                            // 'stock_method' => '',
                            'details'=> false,
                            'hideZeroBalance'=> false,
                        ]) }}">Trading P & L
    </a>
    @endif
    @if ($user->can('company_view_balance_sheet_report'))
    <a class="btn custom-dashboard-btn mt-2 me-3 px-1 py-2 tradingProfitLoss"
        href="{{ route('company.balance-sheet-report', ['date' => getCompanyFilters()['balance_sheet_date'] ?? \Carbon\Carbon::now()->format('d-m-Y'),]) }}"
        data-bs-toggle="tooltip" data-bs-placement="bottom" title="Shortcut Key : Shift + 3">Balance Sheet
    </a>
    @endif
    @if ($user->can('company_view_ledger_report'))
    <a class="btn custom-dashboard-btn mt-2 me-3 px-1 py-2" href="{{ route('company.ledger-report')}}"
        data-bs-toggle="tooltip" data-bs-placement="bottom" title="Shortcut Key : Shift + 1">
        Ledger
    </a>
    @endif
    @if (getCurrentCompany()->is_gst_applicable && ($user->can('company_view_gstr_1_report') || $user->can('company_view_gstr_3b_report') ||
    $user->can('company_view_input_tax_report') || $user->can('company_view_output_tax_report') ||
    $user->can('company_view_hsn_outward_report') || $user->can('company_view_hsn_inward_report')))
    <div class="gst-dashboad-btn position-relative">
        <a class="btn custom-dashboard-btn mt-2 me-3 px-1 py-2" href="#">GST Report</a>
        <div class="company-gst-report-list border-dashboard-card">
            @if ($user->can('company_view_gstr_1_report'))
            <a class="btn mt-2 text-primary w-100 px-3 px-1 py-2" href="{{ route('company.reports.gstr-1', [
                            'start_date' => getDateCompanyFilter('gstr_1_report_date')[0],
                            'end_date' => getDateCompanyFilter('gstr_1_report_date')[1]
                            ]) }}" data-bs-toggle="tooltip" data-bs-placement="left"
                title="Shortcut Key : Shift + 8">GSTR 1
            </a>
            @endif
            {{-- <a class="btn mt-2 text-primary w-100 px-3 px-1 py-2" href="#">GSTR 2A</a>--}}
            {{-- <a class="btn mt-2 text-primary w-100 px-3 px-1 py-2" href="#">GSTR 2B</a>--}}
            {{-- <a class="btn mt-2 text-primary w-100 px-3 px-1 py-2" href="#">GSTR 9</a>--}}
            {{-- <a class="btn mt-2 text-primary w-100 px-3 px-1 py-2" href="#">GSTR 9C</a>--}}
            @if ($user->can('company_view_gstr_3b_report'))
            <a class="btn mt-2 text-primary w-100 px-3 px-1 py-2" href="{{ route('company.reports.gstr-3b-summary', [
                                'start_date' => getDateCompanyFilter('gstr_3b_report_date')[0],
                                'end_date' => getDateCompanyFilter('gstr_3b_report_date')[1]
                                ]) }}" data-bs-toggle="tooltip" data-bs-placement="left"
                title="Shortcut Key : Shift + 7">GSTR 3B Summary
            </a>
            @endif
            {{-- <a class="btn mt-2 text-primary w-100 px-3 px-1 py-2" href="#">GSTR 3B Vs 1</a>--}}
            @if ($user->can('company_view_input_tax_report'))
            <a class="btn mt-2 text-primary w-100 px-3 px-1 py-2" href="{{route('company.input-tax-register-report')}}">
                Input Tax Register
            </a>
            @endif
            @if ($user->can('company_view_output_tax_report'))
            <a class="btn mt-2 text-primary w-100 px-3 px-1 py-2"
                href="{{route('company.output-tax-register-report')}}">
                Output Tax Register
            </a>
            @endif
            @if ($user->can('company_view_hsn_outward_report'))
            <a class="btn mt-2 text-primary w-100 px-3 px-1 py-2"
                href="{{route('company.hsn-summary-outward-report')}}">
                HSN Summary Outward
            </a>
            @endif
            @if ($user->can('company_view_hsn_inward_report'))
            <a class="btn mt-2 text-primary w-100 px-3 px-1 py-2" href="{{route('company.hsn-summary-inward-report')}}">
                HSN Summary Inward
            </a>
            @endif
        </div>
    </div>
    @endif

    @if (getCompanyProfileDetails('is_tds_applicable') && ($user->can('company_view_tds_liability_report') || $user->can('company_view_tcs_liability_report') ||
    $user->can('company_view_tds_return_report') || $user->can('company_view_tcs_return_report')))
    <div class="company-tds-report-dashboard-btn position-relative">
        <a class="btn custom-dashboard-btn mt-2 me-3 px-1 py-2" href="#">TDS Report </a>
        <div class="company-tds-report-list position-absolute border-dashboard-card">
            @if ($user->can('company_view_tds_liability_report'))
            <a class="btn text-primary w-100 mt-2 px-3 px-1 py-2" href="{{ route('company.tds-liability-report') }}"
                data-bs-toggle="tooltip" data-bs-placement="left" title="Shortcut Key : Shift + 9">
                TDS Liability Statement
            </a>
            @endif
            @if ($user->can('company_view_tcs_liability_report'))
            <a class="btn text-primary w-100 mt-2 px-3 px-1 py-2" href="{{ route('company.tcs-liability-report') }}">
                TCS Liability Statement
            </a>
            @endif
            @if ($user->can('company_view_tds_return_report'))
            <a class="btn text-primary w-100 mt-2 px-3 px-1 py-2" href="{{ route('company.tds-return-report') }}">
                TDS Return Statement
            </a>
            @endif
            @if ($user->can('company_view_tcs_return_report'))
            <a class="btn text-primary w-100 mt-2 px-3 px-1 py-2" href="{{ route('company.tcs-return-report') }}">
                TCS Return Statement
            </a>
            @endif
        </div>
    </div>
    @endif
</div>
@foreach(getCompanyDashboardButton() as $buttonData)
<div class="company-report-button">
    <a class="btn custom-dashboard-btn mt-2 me-3 px-1 py-2" @if(($buttonData->button_id ==
        \App\Models\CompanyDashboardButton::TRIAL_BALANCE))
        data-bs-toggle="tooltip" data-bs-placement="bottom" title="Shortcut Key : Shift + 4"
        @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::CASH_FLOW))
        data-bs-toggle="tooltip" data-bs-placement="bottom" title="Shortcut Key : Shift + 5"
        @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::STOCK_REPORT))
        data-bs-toggle="tooltip" data-bs-placement="bottom" title="Shortcut Key : Shift + 6"
        @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::GSTR_3B_SUMMARY))
        data-bs-toggle="tooltip" data-bs-placement="bottom" title="Shortcut Key : Shift + 7"
        @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::GSTR_1))
        data-bs-toggle="tooltip" data-bs-placement="bottom" title="Shortcut Key : Shift + 8"
        @endif
        href="
        @if(($buttonData->button_id == \App\Models\CompanyDashboardButton::OUTSTANDING)){{route('company.outstanding-report')}}@elseif(($buttonData->button_id
        == \App\Models\CompanyDashboardButton::AGEING_REPORT)){{route('company.ageing-report')}}@elseif(($buttonData->button_id
        == \App\Models\CompanyDashboardButton::SALE_REPORT)){{route('company.sale-report')}}@elseif(($buttonData->button_id
        == \App\Models\CompanyDashboardButton::PURCHASE_REPORT)){{route('company.purchase-report')}}@elseif(($buttonData->button_id
        == \App\Models\CompanyDashboardButton::DAY_BOOK_REPORT))
        {{ route('company.day-book-report') }}
        @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::CASH_BANK_REPORT))
        {{ route('company.cash-bank-report') }}
        @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::STOCK_REPORT))
        {{ route('company.report-stock',[
        'start_date' => getDateCompanyFilter('stock_report_date')[0],
        'end_date' => getDateCompanyFilter('stock_report_date')[1],
        ]) }}
        @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::BROKER_REPORT))
        {{route('company.broker-report')}}
        @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::TREND_ANALYSIS))#
        @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::TRIAL_BALANCE))
        {{ route('company.trial-balance-report') }}
        @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::CASH_FLOW))
        {{ route('company.cash-flow-report') }}
        @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::GSTR_1))
        {{ route('company.reports.gstr-1', [
        'start_date' => getDateCompanyFilter('gstr_1_report_date')[0],
        'end_date' => getDateCompanyFilter('gstr_1_report_date')[1],
        'filter_type' => 3
        ]) }}
        {{-- @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::GSTR_2A))--}}
        {{-- #--}}
        {{-- @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::GSTR_2B))--}}
        #
        @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::GSTR_3B_SUMMARY))
        {{ route('company.reports.gstr-3b-summary', [
        'start_date' =>getDateCompanyFilter('gstr_3b_report_date')[0],
        'end_date' => getDateCompanyFilter('gstr_3b_report_date')[1]
        ]) }}
        {{--@elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::GSTR_3B_VS_1))--}}
        {{-- #--}}
        @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::INPUT_TAX_REGISTER))
        {{route('company.input-tax-register-report')}}
        @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::OUTPUT_TAX_REGISTER))
        {{route('company.output-tax-register-report')}}
        @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::HSN_SUMMARY_INWARD))
        {{route('company.hsn-summary-outward-report')}}
        @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::HSN_SUMMARY_OUTWARD))
        {{route('company.hsn-summary-inward-report')}}
        {{-- @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::GSTR_9))--}}
        {{-- #--}}
        {{--@elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::GSTR_9C))--}}
        {{-- #--}}
        @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::TDS_RETURN_STATEMENT))
        {{ route('company.tds-return-report') }}
        @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::TCS_LIABILITY_STATEMENT))
        {{ route('company.tcs-liability-report') }}
        @elseif(($buttonData->button_id == \App\Models\CompanyDashboardButton::TCS_RETURN_STATEMENT))
        {{ route('company.tcs-return-report') }}
        @else
        #
        @endif">
        {{\App\Models\CompanyDashboardButton::REPORT_ARR[$buttonData->button_id]}}
    </a>
    <div class="circle-minus-icon company-dashboard-button" data-button-id="{{$buttonData->button_id}}">
        <svg width="20" height="20" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="11.5" cy="11.5" r="11" fill="white" stroke="#F76600" />
            <rect x="6" y="10" width="11" height="3" rx="1" fill="#F76600" />
        </svg>
    </div>
</div>
@endforeach
