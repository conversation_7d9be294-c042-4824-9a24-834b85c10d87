<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Gstr 3b Detailed Report</title>
</head>
<body>
<table>
    <thead>
    {{-- <tr>
            <th style="width: 200%"><b>{{ $company->trade_name }}</b></th>
    </tr>
    <tr>
        <th style="width: 500px">{{ $companyAddress->address_1 ?? null }}</th>
    </tr>
    <tr>
        <th style="width: 500px">{{ $companyAddress->address_2 ?? null }}</th>
    </tr>
    <tr>
        <th style="width: 200%">{{ getCityName($companyAddress->city_id ?? null) }}</th>
    </tr>
    <tr>
        <th style="width: 500px">{{ getStateName($companyAddress->state_id ?? null).' -'.($companyAddress->pin_code ?? null) }}</th>
    </tr>
    <tr>
        <th style="width: 500px">GSTIN : {{ $company->companyTax->gstin ?? null }}</th>
    </tr>
    <tr>
        <th style="width: 500px">PAN : {{ $company->companyTax->pan_number ?? null }}</th>
    </tr>
    <tr>
        <th style="width: 500px">Email: {{ $company->user->email ?? null}}</th>
    </tr>
    <tr>
        <th style="width: 500px">Contact : {{ $company?->phone ?? null }}
            , {{ $company?->companyPrimaryContact?->phone ?? null }}</th>
    </tr>
    <tr>
        <td style="width: 200%">Periods:</td>
        <td style="width: 200%">{{ $financialYears }}</td>
    </tr> --}}
    <tr></tr>
    </thead>
    <tbody>
        <tr>
            <td colspan="3" style="text-align: center; background-color: #4e158c; border: 1px solid white; color:white" class="text-center fw-bold fs-14 ">Vendor Details	</td>
            <td colspan="7" style="text-align: center; background-color: #4e158c; border: 1px solid white; color:white" class="text-center fw-bold fs-14 ">As Per Books of Accounts</td>
            <td colspan="7" style="text-align: center; background-color: #4e158c; border: 1px solid white; color:white" class="text-center fw-bold fs-14 ">As Per GSTR-2B	</td>
            <td colspan="5" style="text-align: center; background-color: #4e158c; border: 1px solid white; color:white" class="text-center fw-bold fs-14 ">Difference</td>
        </tr>
        <tr>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">GSTIN</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">Supplier Name	</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">Doc. Type	</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">Doc. No.</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">Date</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">Taxable Amount</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">IGST</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">CGST</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">SGST</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">CESS</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">Doc. No.</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">Date</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">Taxable Amount</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">IGST</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">CGST</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">SGST</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">CESS</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">Taxable Amount</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">IGST</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">CGST</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">SGST</td>
            <td class="fw-bold fs-12" style="background-color: #4e158c; border: 1px solid white; color:white">CESS</td>
        </tr>

            @foreach ($data as $item)
                <tr>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['gstin'] }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['name'] }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['books_doc'] }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['books_doc_no'] }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['books_doc_date']  }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['books_taxable_value'] }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['books_igst'] }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['books_cgst']  }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['books_sgst']  }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['books_cess']  }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['portal_doc_no']  }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['portal_doc_date']  }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['portal_taxable_value']  }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['portal_igst']  }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['portal_cgst']  }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['portal_sgst']  }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['portal_cess']  }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['difference_taxable_value']  }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['difference_igst']  }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['difference_cgst']  }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['difference_sgst']  }}</td>
                    <td style="background-color: {{ $item['color'] }};border: 1px solid black">{{ $item['difference_cess']  }}</td>
                </tr>
            @endforeach
            {{-- <tr>
                <td class="fw-bold fs-14">Total</td>
                <td></td>
                <td></td>
                <td class="fw-bold fs-12">{{ getCurrencyFormat(array_sum(array_column($data, 'sales'))) }}</td>
                <td class="fw-bold fs-12">{{ getCurrencyFormat(array_sum(array_column($data, 'gstr_1_sum'))) }}</td>
                <td class="fw-bold fs-12">{{ getCurrencyFormat(array_sum(array_column($data, 'gstr_3b_sum'))) }}</td>
                <td class="fw-bold fs-12">{{ getCurrencyFormat(array_sum(array_column($data, 'itc'))) }}</td>
                <td class="fw-bold fs-12">{{ getCurrencyFormat(array_sum(array_column($data, 'itc_gstr_2a_sum'))) }}</td>
                <td class="fw-bold fs-12">{{ getCurrencyFormat(array_sum(array_column($data, 'itc_gstr_3b_sum'))) }}</td>
            </tr> --}}
    </tbody>
</table>
</body>
</html>
