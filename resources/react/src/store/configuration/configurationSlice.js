import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axiosApi from "../../config/apiConfig";
import { apiBaseURL, TABLE_HEADER_TYPE, toastType, TRANSACTION_TYPE } from "../../constants";
import { addToast, errorToast } from "../actions/toastAction";
import { getTableHeaderDetail } from "../table/tableSlice";

const initialState = {
    configuration: [],
    customFieldList: [],
    itemMasterCustomFieldList: [],
    transactionType: "",
    getCustomFieldById: "",
    getItemCustomFieldById: "",
    getItemMasterCustomFieldList: [],
    bankList: []
};

export const fetchConfigurationList = createAsyncThunk(
    "configuration/fetchConfigurationList",
    async url => {
        const response = await axiosApi.get(url, {
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response.data.data;
    }
);
export const fetchCustomFieldList = createAsyncThunk(
    "configuration/fetchCustomFieldList",
    async () => {
        const response = await axiosApi.get(apiBaseURL.CUSTOM_FIELD_TYPE, {
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response.data.data;
    }
);

export const fetchItemMasterCustomFieldList = createAsyncThunk(
    "configuration/fetchItemMasterCustomFieldList",
    async () => {
        const response = await axiosApi.get(apiBaseURL.CUSTOM_FIELD_ITEM_MASTER_TYPE, {
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response.data.data;
    }
);

export const configurationSlice = createSlice({
    name: "Configuration",
    initialState,
    reducers: {
        transactionType: (state, action) => {
            state.transactionType = action.payload;
        },
        bankList: (state, action) => {
            state.bankList = action.payload;
        },
        getCustomFieldById: (state, action) => {
            state.getCustomFieldById = action.payload;
        },
        getItemCustomFieldById: (state, action) => {
            state.getItemCustomFieldById = action.payload;
        },
        getItemMasterCustomFieldList: (state, action) => {
            state.getItemMasterCustomFieldList = action.payload;
        },
        resetConfiguration: () => initialState // ← This resets the state
    },
    extraReducers: builder => {
        builder.addCase(fetchConfigurationList.fulfilled, (state, action) => {
            state.configuration = action.payload;
        });
        builder.addCase(fetchCustomFieldList.fulfilled, (state, action) => {
            state.customFieldList = action.payload;
        });
        builder.addCase(fetchItemMasterCustomFieldList.fulfilled, (state, action) => {
            state.itemMasterCustomFieldList = action.payload;
        });
    },
});

export const { transactionType, bankList, getCustomFieldById, getItemCustomFieldById, resetConfiguration, getItemMasterCustomFieldList } = configurationSlice.actions;

export const updateConfiguration = (url, data, handleClose, setAdditionalCharges, isBankDetailsUpdate) => async dispatch => {
    try {
        const response = await axiosApi.post(url, data, {
            withCredentials: true,
        });
        dispatch(fetchConfigurationList(url));

        if (isBankDetailsUpdate) {
            setAdditionalCharges(prev => ({
                ...prev,
                bank_id: data?.bank_id,
            }));
        }

        if (handleClose) {
            handleClose();
            dispatch(
                addToast({
                    text: response?.data?.message,
                    type: toastType.ADD_TOAST,
                })
            );
        }
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};
export const updateSingleConfiguration = (url, data) => async dispatch => {
    try {
        await axiosApi.post(url + "/1", data, {
            withCredentials: true,
        });
        dispatch(fetchConfigurationList(url));
    } catch (err) {
        throw new Error(err);
    }
};
export const createCustomField = (data, url, closeCustomFieldModel, setIsDisable) => async dispatch => {
    try {
        const response = await axiosApi.post(apiBaseURL.CUSTOM_FIELD, data, {
            withCredentials: true,
        });
        if (response?.data?.success) {
            closeCustomFieldModel();
        }
        dispatch(
            addToast({
                text: response?.data.message,
                type: toastType.ADD_TOAST,
            })
        );
        dispatch(fetchConfigurationList(url));
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
        setIsDisable(false);
    }
};
export const getCustomField = (id, type) => async dispatch => {
    try {
        const response = await axiosApi.get(`${apiBaseURL.CUSTOM_FIELD}/${id}/edit?type=${type}`, {
            withCredentials: true,
        });
        dispatch(getCustomFieldById(response.data.data));
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};
export const getItemCustomField = (id, type) => async dispatch => {
    try {
        const response = await axiosApi.get(`${apiBaseURL.CUSTOM_FIELD_ITEM}/${id}/edit?type=${type}`, {
            withCredentials: true,
        });
        dispatch(getItemCustomFieldById(response.data.data));
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};
export const updateCustomField = (data, id, url, setGetSingleCustomHeader, close, setIsEditModel, setSingleTransactionType, setIsDisable) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.CUSTOM_FIELD}/${id}/update`, data, {
            withCredentials: true,
        });
        if (response?.data?.success) {
            close();
        }
        dispatch(fetchConfigurationList(url));
        dispatch(
            addToast({
                text: response?.data.message,
                type: toastType.ADD_TOAST,
            })
        );
        setGetSingleCustomHeader("");
        setIsEditModel(false);
        setSingleTransactionType("");
        dispatch(getCustomFieldById(""));
    } catch ({response}) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
        setIsDisable(false);
    }
};
export const deleteCustomHeaderField = (id, close, url, setGetSingleCustomHeader) => async dispatch => {
    try {
        const response = await axiosApi.get(`${apiBaseURL.CUSTOM_FIELD}/${id}/delete`, {
            withCredentials: true,
        });
        setGetSingleCustomHeader("")
        dispatch(
            addToast({
                text: response?.data.message,
                type: toastType.ADD_TOAST,
            })
        );
        close(false)
        dispatch(fetchConfigurationList(url));
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};
export const updateCustomFieldStatus = (data, url) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.CUSTOM_FIELD}/update-status`, data, {
            withCredentials: true,
        });
        dispatch(fetchConfigurationList(url));
        addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            })
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};

export const rearrangeItemList = (type, invoice_type, isUpdate) => async dispatch => {
    try {
        const response = await axiosApi.get(
            `${apiBaseURL.REARRANGE_ITEMS}/${type}/${invoice_type}`,
            {
                withCredentials: true,
            }
        );
        const defaultItemTableHeader = [
            { header: TABLE_HEADER_TYPE.ITEM, id: 0 },
            { header: TABLE_HEADER_TYPE.LEDGER, id: 1 },
        ];
        const defaultDeliveryItemTableHeader = [{ header: TABLE_HEADER_TYPE.ITEM, id: 0 }];
        const defaultAccountTableHeader = [{ header: TABLE_HEADER_TYPE.LEDGER, id: 0 }];
        const customTableHeader =
        response.data?.data?.items
            ?.sort((a, b) => (a.order ?? 0) - (b.order ?? 0)) // sort by item.order
            .map((item, index) => ({
            ...item,
            header: item.name,
            id: index + 2,
            })) || [];

        const baseHeaders =
            TRANSACTION_TYPE.DELIVERY_CHALLAN == type && invoice_type == 2
                ? defaultDeliveryItemTableHeader
                : invoice_type == 2
                ? defaultAccountTableHeader
                : defaultItemTableHeader;
                const mergedHeaders = [
                    ...baseHeaders,
                    ...customTableHeader,
            ...(TRANSACTION_TYPE.DELIVERY_CHALLAN == type && invoice_type == 2
                ? []
                : [
                    {
                          header: "Total",
                          id: defaultItemTableHeader.length + customTableHeader.length + 1,
                        },
                    ]),
                ].filter(Boolean);
        dispatch(getTableHeaderDetail(mergedHeaders));
        dispatch(transactionType(type));
        if(isUpdate) {
            dispatch(updateRearrangeItemList({type: type, invoice_type: invoice_type, meta: response?.data?.data?.items}))
        }
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};
export const updateRearrangeItemList = (data) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.REARRANGE_ITEMS}`, data, {
            withCredentials: true,
        });
        const defaultItemTableHeader = [
            { header: TABLE_HEADER_TYPE.ITEM, id: 0 },
            { header: TABLE_HEADER_TYPE.LEDGER, id: 1 },
        ];
        const defaultDeliveryItemTableHeader = [{ header: TABLE_HEADER_TYPE.ITEM, id: 0 }];
        const defaultAccountTableHeader = [{ header: TABLE_HEADER_TYPE.LEDGER, id: 0 }];
        const customTableHeader =
            response.data?.data?.items?.map((item, index) => {
                return {
                    ...item,
                    header: item.name,
                    id: index + 2,
                };
            }) || [];
        const baseHeaders =
            TRANSACTION_TYPE.DELIVERY_CHALLAN == data?.type && data?.invoice_type == 2
                ? defaultDeliveryItemTableHeader
                : data?.invoice_type == 2
                ? defaultAccountTableHeader
                : defaultItemTableHeader;

        const mergedHeaders = [
            ...baseHeaders,
            ...customTableHeader,
            ...(TRANSACTION_TYPE.DELIVERY_CHALLAN == data?.type && data?.invoice_type == 2
                ? []
                : [
                      {
                          header: "Total",
                          id: defaultItemTableHeader.length + customTableHeader.length + 1,
                      },
                  ]),
        ];
        dispatch(getTableHeaderDetail(mergedHeaders));
        if(data?.message){
            dispatch(
                addToast({
                    text: response?.data?.message,
                    type: toastType.ADD_TOAST,
                })
            );
        }
    } catch ({ response }) {
        if (response) {
            dispatch(
                errorToast({
                    text: response?.data?.message,
                    type: toastType.ERROR,
                })
            );
        }
    }
};
export const negativeStock = (id, data, openIsNegativeStockModel) => async dispatch => {
    try {
        if (!id || (typeof id !== "string" && typeof id !== "number")) {
            console.error("Invalid ID provided:", id);
            return;
        }

        const response = await axiosApi.post(
            `${apiBaseURL.NEGATIVE_STOCK}${id}`,
            { quantity: data },
            { withCredentials: true }
        );
        if (response.data?.data?.is_warn_on_negative_stock) {
            openIsNegativeStockModel();
        }
    } catch (err) {
        throw new Error(err);
    }
};

export const createItemCustomField = (data, url, closeCustomFieldModel, setIsDisable) => async dispatch => {
    try {
        const response = await axiosApi.post(apiBaseURL.CUSTOM_FIELD_ITEM, data, {
            withCredentials: true,
        });
        if (response?.data?.success) {
            closeCustomFieldModel()
        }
        dispatch(
            addToast({
                text: response?.data.message,
                type: toastType.ADD_TOAST,
            })
        );
        dispatch(rearrangeItemList(url, data?.invoice_type, true));
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
        setIsDisable(false);
    }
}
export const fetchBankList = (data = {}) => async (dispatch) => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.BANK_LEDGER_LIST}`, data, { withCredentials: true });

        if(response.status === 200) {
            dispatch(bankList(response.data?.data));
        }

    } catch ({response}) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};
export const updateItemCustomField = (data, invoice_type, id, url, setGetSingleCustomField, close, setIsEditModel, setIsDisable) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.CUSTOM_FIELD_ITEM}/${id}/update`, data, {
            withCredentials: true,
        });
        if (response?.data?.success) {
            close();
        }
        dispatch(
            addToast({
                text: response?.data.message,
                type: toastType.ADD_TOAST,
            })
        );
        dispatch(fetchConfigurationList(url));
        dispatch(rearrangeItemList(data?.type, invoice_type, true));
        setIsEditModel(false);
        setGetSingleCustomField("");
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
        setIsDisable(false);
    }
};
export const updateItemStatusCustomField = (data, invoice_type) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.CUSTOM_FIELD_UPDATE_STATUS}`, data, {
            withCredentials: true,
        });
        dispatch(
            addToast({
                text: response?.data.message,
                type: toastType.ADD_TOAST,
            })
        );
        // dispatch(fetchConfigurationList(url));
        dispatch(rearrangeItemList(data?.type, invoice_type));
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};
export const updateTransactionStatusCustomField = (data, invoice_type) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.CUSTOM_FIELD_TRANSACTION_UPDATE_STATUS}`, data, {
            withCredentials: true,
        });
        dispatch(
            addToast({
                text: response?.data.message,
                type: toastType.ADD_TOAST,
            })
        );
        // dispatch(fetchConfigurationList(url));
        dispatch(rearrangeItemList(data?.transaction_type, invoice_type));
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};

export const updateItemCustomFieldStatus = (data) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.CUSTOM_FIELD_ITEM_MASTER_UPDATE_STATUS}`, data, {
            withCredentials: true,
        });
        dispatch(getAllItemMasterCustomField());
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};
export const deleteItemCustomFieldStatus = (data, url, type, invoice_type) => async dispatch => {
    try {
        if(!data?.custom_field_item_id) return
        const response = await axiosApi.get(`${apiBaseURL.CUSTOM_FIELD_ITEM}/${data?.custom_field_item_id}/delete`, {
            withCredentials: true,
        });
        dispatch(fetchConfigurationList(url));
        dispatch(rearrangeItemList(type, invoice_type, url));
        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            })
        );
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        )
    }
};

export const addEstimateOrderTitle = ( data) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.ESTIMATE_ORDER_TITLE}`, data, {
            withCredentials: true,
        });

        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            }),
        );
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    }
};

export const updateEstimateOrderTitle = (id, data) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.ESTIMATE_ORDER_TITLE}/${id}`, data, {
            withCredentials: true,
        });

        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            }),
        );
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    }
};

export const deleteEstimateOrderTitle = (id) => async dispatch => {
    try {
        const response = await axiosApi.delete(`${apiBaseURL.ESTIMATE_ORDER_TITLE}/${id}/delete`, {
            withCredentials: true,
        });

        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            }),
        );
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    }
};
export const addPurchaseOrderTitle = ( data) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.PURCHASE_ORDER_TITLE}`, data, {
            withCredentials: true,
        });

        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            }),
        );
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    }
};

export const updatePurchaseOrderTitle = (id, data) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.PURCHASE_ORDER_TITLE}/${id}`, data, {
            withCredentials: true,
        });

        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            }),
        );
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    }
};

export const deletePurchaseOrderTitle = (id) => async dispatch => {
    try {
        const response = await axiosApi.delete(`${apiBaseURL.PURCHASE_ORDER_TITLE}/${id}/delete`, {
            withCredentials: true,
        });

        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            }),
        );
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    }
};
export const addCustomFieldItemFormula = (data, handleClose, type, invoice_type) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.CUSTOM_FIELD_ITEM_FORMULA}`, data, {
            withCredentials: true,
        });

        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            }),
        );
        handleClose()
        dispatch(rearrangeItemList(type, invoice_type, true))
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    }
};
export const deleteCustomFieldItemFormula = (id, handleClose) => async dispatch => {
    try {
        const response = await axiosApi.get(`${apiBaseURL.DELETE_CUSTOM_FIELD_ITEM_FORMULA}/${id}`, {
            withCredentials: true,
        });
        handleClose();
        dispatch(getAllItemMasterCustomField())
        return
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    }
};

export const addItemMasterCustomField = (data, handleClose, id, transaction_type) => async dispatch => {
    try {
        const response = await axiosApi.post(apiBaseURL.CUSTOM_FIELD_ITEM_MASTER, data, {
            withCredentials: true,
        });
        dispatch(
            addToast({
                text: response?.data.message,
                type: toastType.ADD_TOAST,
            })
        );
        handleClose()
        dispatch(getAllItemMasterCustomField(id));
        if(transaction_type){
            dispatch(rearrangeItemList(transaction_type, 1))
        }
    } catch ({response}) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
}
export const addItemMasterQtyCustomField = (data, handleClose, id) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.CUSTOM_FIELD_ITEM_MASTER}/formula`, data, {
            withCredentials: true,
        });
        dispatch(
            addToast({
                text: response?.data.message,
                type: toastType.ADD_TOAST,
            })
        );
        handleClose()
        dispatch(getAllItemMasterCustomField(id));
    } catch ({response}) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
}
export const getItemMasterCustomField = (id) => async dispatch => {
    try {
        const response = await axiosApi.get(`${apiBaseURL.CUSTOM_FIELD_ITEM_MASTER}/${id}/edit`, {
            withCredentials: true,
        });
        return response.data.data;
    } catch ({response}) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
}

export const updateItemMasterCustomField = (id, data, handleClose, item_id, transaction_type) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.CUSTOM_FIELD_ITEM_MASTER}/${id}/update`, data, {
            withCredentials: true,
        });
        dispatch(
            addToast({
                text: response?.data.message,
                type: toastType.ADD_TOAST,
            })
        );
        handleClose()
        dispatch(getAllItemMasterCustomField(item_id));
        if(transaction_type){
            dispatch(rearrangeItemList(transaction_type, 1))
        }
    }
    catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
}

export const getAllItemMasterCustomField = () => async dispatch => {
    try {
        const response = await axiosApi.get(`${apiBaseURL.CUSTOM_FIELD_ITEM_MASTER}`, {
            withCredentials: true,
        });
        dispatch(getItemMasterCustomFieldList(response.data.data));
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
}
export const deleteItemMasterCustomField = (id, handleClose, item_id, setCustomItemConfigurationList) => async dispatch => {
    try {
        const response = await axiosApi.get(`${apiBaseURL.CUSTOM_FIELD_ITEM_MASTER}/${id}/delete`, {
            withCredentials: true,
        });
        handleClose()
        dispatch(
            addToast({
                text: response?.data.message,
                type: toastType.ADD_TOAST,
            })
        );
        dispatch(getAllItemMasterCustomField(item_id));
        setCustomItemConfigurationList(prev => prev?.filter(item => item?.id !== id));
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
        handleClose()
    }
}

export default configurationSlice.reducer;
