import { combineReducers, configureStore } from "@reduxjs/toolkit";
import { persistStore, persistReducer } from "redux-persist";
import storage from "redux-persist/lib/storage";
import brokerReducer from "./broker/brokerSlice";
import classificationReducer from "./classification/classificationSlice";
import companyReducer from "./company/companySlice";
import configurationReducer from "./configuration/configurationSlice";
import dispatchAddressReducer from "./dispatchAddress/dispatchAddressSlice";
import invoiceReducer from "./invoice/invoiceSlice";
import itemReducer from "./item/itemSlice";
import ledgerReducer from "./ledger/ledgerSlice";
import rateReducer from "./rate/rateSlice";
import saleReducer from "./sale/saleSlice";
import transportReducer from "./transport/transportSlice";
import tableReducer from "./table/tableSlice";
import gstReducer from "./gst/gstSlice";
import incomeReducer from "./income-cn-dn/incomeSlice";
import expenseReducer from "./expense-cn-dn/expenseSlice";
import purchaseOrderReducer from "./purchase-order/purchaseOrderSlice";
import purchaseReducer from "./purchase/purchaseSlice";
import purchaseReturnReducer from "./purchase-return/purchaseReturnSlice";
import estimateReducer from "./estimate-quote/estimateSlice";
import shippingAddressReducer from "./shippingAddress/shippingAddressSlice";
import deliveryChallanReducer from "./delivery-challan/deliveryChallanSlice";
import unitReducer from "./unit/unitSlice";
import prevNextUrlReducer from "./prev-next/prev-nextSlice";
import settingReducer from "./setting/settingSlice";
import dashboardReducer from "./dashboard/dashboardSlice";
import advancePaymentReducer from "./advance-payment/advancePaymentSlice";
import recurringMasterReducer from "./recurring-master/RecurringMasterSlice";
import customerMasterReducer from "./customer-master/customerMasterSlice"
import supplierMasterReducer from "./supplier-master/supplierMasterSlice"
import vastraReducer from "./vastra/vastraSlice"
import printBarcodeReducer from "./print-barcode/printBarcodeSlice"
import purchaseOcrReducer from "./purchase-ocr/purchaseOcrSlice";
import lowStockReportReducer from "./low-stock-report/LowStockReportSlice";
import thirdParty11ZAReducer from "./thirdParty11za/thirdParty11ZASlice";
import paymentModeReducer from "./payment-mode/paymentModeSlice";
import gstr2bReducer from "./gstr-2b/Gstr2bSlice";

const persistConfig = {
    key: "root",
    storage,
    whitelist: [
        "broker",
        "classification",
        "company",
        "configuration",
        "dispatchAddress",
        "invoice",
        "item",
        "ledger",
        "rate",
        "sale",
        "transport",
        "table",
        "gst",
        "incomeNote",
        "expenseNote",
        "purchaseOrder",
        "purchaseReturn",
        "purchase",
        "setting",
        "estimate",
        "purchaseOcr"
    ],
};

const rootReducer = combineReducers({
    dashboard: dashboardReducer,
    broker: brokerReducer,
    classification: classificationReducer,
    company: companyReducer,
    configuration: configurationReducer,
    customerMaster: customerMasterReducer,
    supplierMaster: supplierMasterReducer,
    dispatchAddress: dispatchAddressReducer,
    invoice: invoiceReducer,
    item: itemReducer,
    ledger: ledgerReducer,
    rate: rateReducer,
    sale: saleReducer,
    transport: transportReducer,
    table: tableReducer,
    gst: gstReducer,
    incomeNote: incomeReducer,
    expenseNote: expenseReducer,
    purchase: purchaseReducer,
    purchaseOrder: purchaseOrderReducer,
    purchaseReturn: purchaseReturnReducer,
    estimate: estimateReducer,
    shippingAddresses: shippingAddressReducer,
    deliveryChallan: deliveryChallanReducer,
    units: unitReducer,
    prevNext: prevNextUrlReducer,
    setting: settingReducer,
    advancePayment: advancePaymentReducer,
    recurringMaster : recurringMasterReducer,
    vastra : vastraReducer,
    printBarcode: printBarcodeReducer,
    purchaseOcr : purchaseOcrReducer,
    lowStockReport : lowStockReportReducer,
    thirdParty11ZA : thirdParty11ZAReducer,
    paymentMode : paymentModeReducer,
    gstr2b : gstr2bReducer,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

const store = configureStore({
    reducer: persistedReducer,
    middleware: getDefaultMiddleware =>
        getDefaultMiddleware({
            serializableCheck: false,
        }),
});

const persistor = persistStore(store);
persistor.purge();

export { store, persistor };
