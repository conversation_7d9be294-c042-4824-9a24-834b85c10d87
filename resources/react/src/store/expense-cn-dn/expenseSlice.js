import { createSlice } from "@reduxjs/toolkit";
import axiosApi from "../../config/apiConfig";
import { apiBaseURL, toastType } from "../../constants";
import { addToast, errorToast } from "../actions/toastAction";
import { prepareDeleteTransactions } from "../../shared/prepareData";

const initialState = {
    getExpenseById: [],
    status: ""
};

export const expenseSlice = createSlice({
    name: "Expense",
    initialState,
    reducers: {
        getExpenseById: (state, action) => {
            state.getExpenseById = action.payload;
        },
        status: (state, action) => {
            state.status = action.payload;
        },
    },
});

export const { getExpenseById, status } = expenseSlice.actions;

export const addExpense = (formdata, saveType, type, submit_button, setIsDisable, submitData, submit_button_type) => async dispatch => {
    try {
        const response = await axiosApi.post(
            type ? apiBaseURL.EXPENSE_CN : apiBaseURL.EXPENSE_DN,
            formdata,
            {
                withCredentials: true,
            },
        );
        if (submit_button === 3) {
            localStorage.setItem("print_sale_id", response.data?.data?.lastTransactionId);
            localStorage.setItem("transaction_type", response.data?.data?.transactionType);
        };

        if (submit_button_type === "saveAndNew") {
            localStorage.setItem('saveAndNewData', JSON.stringify(submitData));
            window.location.reload();
        };

        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            }),
        );
        if (saveType === "saveAndNew") {
            window.location.reload();
        } else {
            if (type) {
                if (saveType === "duplicate") {
                    window.location.href = `${window.location.origin}/company/expense-credit-notes/create`;
                } else {
                    window.location.href = `${window.location.origin}/company/expense-credit-notes`;
                    localStorage.removeItem("saveAndNewData");
                }
            } else {
                if (saveType === "duplicate") {
                    window.location.href = `${window.location.origin}/company/expense-debit-notes/create`;
                } else {
                    window.location.href = `${window.location.origin}/company/expense-debit-notes`;
                    localStorage.removeItem("saveAndNewData");
                }
            }
        }
    } catch ({ response }) {
        if (setIsDisable) {
            setIsDisable(false);
        };
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    }
};
export const fetchExpenseById = (id, type, setLoader) => async dispatch => {
    try {
        if (setLoader) {
            setLoader(true);
        };
        const response = await axiosApi.get(
            type ? apiBaseURL.EXPENSE_CN + "/" + id : apiBaseURL.EXPENSE_DN + "/" + id,
            { withCredentials: true },
        );
        dispatch(getExpenseById(response.data.data));
        if(setLoader){
            setLoader(false);
        }
    } catch (error) {

        if (error?.response?.data?.status) {
            return dispatch(status(error?.response?.data?.status));
        }
        if(setLoader){
            setLoader(false);
        }
        throw new Error(error);
    } finally {
        if(setLoader){
            setLoader(false);
        }
    }
};

export const deleteExpenseCnDn = (id, type, setShowDeleteWarningModel) => async dispatch => {
    try {
        if (!id) return;
        const response = await axiosApi.get(
            `${type ? apiBaseURL.EXPENSE_CN : apiBaseURL.EXPENSE_DN}/${id}/delete`,
            {
                withCredentials: true,
            }
        );
        if (response?.data?.data?.checkTransactionExists) {
            const transactions = prepareDeleteTransactions(response?.data?.data);
            setShowDeleteWarningModel({ show: true, transactions });
        } else {
            dispatch(
                addToast({
                    text: response?.data?.message,
                    type: toastType.ADD_TOAST,
                })
            );
            window.location.href = `${window.location.origin}/company/${
                type ? "expense-credit-notes" : "expense-debit-notes"
            }`;
        }
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};

export const fetchDuplicateExpenseCreditNoteById = (id, setLoader) => async dispatch => {
    try {
        if (!id) return;
        if (setLoader) {
            setLoader(true);
        };
        const response = await axiosApi.get(apiBaseURL.EXPENSE_CN + "/" + id + "/duplicate", {
            withCredentials: true,
        });
        dispatch(getExpenseById(response.data.data));
        if(setLoader){
            setLoader(false);
        }
    } catch (error) {
        if (error?.response?.data?.status) {
            return dispatch(status(error?.response?.data?.status));
        }
        if(setLoader){
            setLoader(false);
        }
        throw new Error(error);
    } finally {
        if(setLoader){
            setLoader(false);
        }
    };
};

export const fetchDuplicateExpenseDebitNoteById = (id, setLoader) => async dispatch => {
    try {
        if (!id) return;
        if (setLoader) {
            setLoader(true);
        };
        const response = await axiosApi.get(apiBaseURL.EXPENSE_DN + "/" + id + "/duplicate", {
            withCredentials: true,
        });
        dispatch(getExpenseById(response.data.data));
        if(setLoader){
            setLoader(false);
        }
    } catch (error) {
        if (error?.response?.data?.status) {
            return dispatch(status(error?.response?.data?.status));
        }
        if(setLoader){
            setLoader(false);
        }
        throw new Error(error);
    } finally {
        if(setLoader){
            setLoader(false);
        }
    };
};

export const updateExpense = (id, formdata, saveType, type, submit_button, setIsDisable, sectionType) => async dispatch => {
    try {
        const response = await axiosApi.post(
            type ? apiBaseURL.EXPENSE_CN + "/" + id : apiBaseURL.EXPENSE_DN + "/" + id,
            formdata,
            {
                withCredentials: true,
            },
        );
        if (submit_button === 3) {
            localStorage.setItem("print_sale_id", response.data?.data?.lastTransactionId);
            localStorage.setItem("transaction_type", response.data?.data?.transactionType);
        }
        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            }),
        );
        if (sectionType === "gst-2b") {
            window.location.href = `${window.location.origin}/company/gstr-2b?section=gst-2b-reconciliation`;
        } else if (saveType === "saveAndNew") {
            window.location.reload();
        } else {
            if (type) {
                window.location.href = `${window.location.origin}/company/expense-credit-notes`;
            } else {
                window.location.href = `${window.location.origin}/company/expense-debit-notes`;
            }
        }
        // }
    } catch ({ response }) {
        if (setIsDisable) {
            setIsDisable(false);
        };
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    }
};
export default expenseSlice.reducer;
