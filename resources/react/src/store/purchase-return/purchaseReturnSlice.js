import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axiosApi from "../../config/apiConfig";
import { apiBaseURL, toastType } from "../../constants";
import { addToast, errorToast } from "../actions/toastAction";
import { prepareDeleteTransactions } from "../../shared/prepareData";

const initialState = {
    purchaseTitle: [],
    purchaseReturnById: [],
    purchaseReturnFromPurchase: [],
    status: ""
};

export const fetchPurchaseOrderTitleList = createAsyncThunk("broker/fetchPurchase", async () => {
    const response = await axiosApi.get(apiBaseURL.PURCHASE_LIST, {
        headers: {
            "Content-Type": "application/json",
        },
    });
    return response.data.data;
});

export const PurchaseReturnSlice = createSlice({
    name: "PurchaseReturn",
    initialState,
    reducers: {
        purchaseReturnById: (state, action) => {
            state.purchaseReturnById = action.payload;
        },
        purchaseReturnFromPurchase: (state, action) => {
            state.purchaseReturnFromPurchase = action.payload;
        },
        status: (state, action) => {
            state.status = action.payload;
        },
    },
    extraReducers: builder => {
        builder.addCase(fetchPurchaseOrderTitleList.fulfilled, (state, action) => {
            state.purchaseTitle = action.payload;
        });
    },
});

export const { purchaseReturnById, status, purchaseReturnFromPurchase } = PurchaseReturnSlice.actions;

export const addPurchaseReturn = (formData, saveType, submit_button, setIsDisable, submitData, submit_button_type) => async dispatch => {
    try {
        const response = await axiosApi.post(apiBaseURL.PURCHASE_RETURN, formData, {
            withCredentials: true,
        });
        if (submit_button === 3) {
            localStorage.setItem("print_sale_id", response.data?.data?.lastTransactionId);
            localStorage.setItem("transaction_type", response.data?.data?.transactionType);
        }  

        if (submit_button_type === "saveAndNew") {
            localStorage.setItem('saveAndNewData', JSON.stringify(submitData));
            window.location.reload();
        }; 

        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            }),
        );
        if (saveType === "saveAndNew") {
            window.location.reload();
        } else if (saveType === "duplicate") {
            window.location.href = `${window.location.origin}/company/purchase-returns/create`;
        } else {
            window.location.href = `${window.location.origin}/company/purchase-returns`;
            localStorage.removeItem("saveAndNewData");
        }

    } catch ({ response }) {
        if (setIsDisable) {
            setIsDisable(false);
        };
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    }
};

export const fetchPurchaseReturnById = (id, setLoader) => async dispatch => {
    try {
        if (!id) return;
        if (setLoader) {
            setLoader(true);
        };
        const resposne = await axiosApi.get(apiBaseURL.PURCHASE_RETURN + "/" + id, {
            withCredentials: true,
        });
        if(setLoader){
            setLoader(false);
        };
        dispatch(purchaseReturnById(resposne.data.data));
    } catch ({ response }) {

        if (response?.data?.status) {
            dispatch(status(response?.data?.status));
        }
        if(setLoader){
            setLoader(false);
        };
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    } finally {
        if(setLoader){
            setLoader(false);
        };
    };
};

export const deletePurchaseReturn = (id, setShowDeleteWarningModel) => async dispatch => {
    try {
        if (!id) return;
        const response = await axiosApi.get(`${apiBaseURL.PURCHASE_RETURN}/${id}/delete`, {
            withCredentials: true,
        });
        if (response?.data?.data?.checkTransactionExists) {
            const transactions = prepareDeleteTransactions(response?.data?.data);
            setShowDeleteWarningModel({ show: true, transactions });
        } else {
            dispatch(
                addToast({
                    text: response?.data?.message,
                    type: toastType.ADD_TOAST,
                })
            );
            window.location.href = `${window.location.origin}/company/purchase-returns`;
        }
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};

export const fetchDuplicatePurchaseReturnById = (id, setLoader) => async dispatch => {
    try {
        if (!id) return;
        if (setLoader) {
            setLoader(true);
        };
        const resposne = await axiosApi.get(apiBaseURL.PURCHASE_RETURN + "/" + id + "/duplicate", {
            withCredentials: true,
        });
        dispatch(purchaseReturnById(resposne.data.data));
        if(setLoader){
            setLoader(false);
        };
    } catch ({ response }) {
        if (response?.data?.status) {
            dispatch(status(response?.data?.status));
        }
        if(setLoader){
            setLoader(false);
        };
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    } finally {
        if(setLoader){
            setLoader(false);
        };
    };
};

export const fetchPurchaseReturnfromPurchase = id => async dispatch => {
    try {
        if (!id) return;
        const resposne = await axiosApi.get(apiBaseURL.PURCHASE_TRANSACTION + "/" + id + "/purchase-return", {
            withCredentials: true,
        });
        dispatch(purchaseReturnFromPurchase(resposne.data.data));
    } catch ({ response }) {
        if (response?.data?.status) {
            dispatch(status(response?.data?.status));
        }
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    }
};

export const updatePurchaseReturn = (id, formData, saveType, submit_button, setIsDisable, sectionType) => async dispatch => {
    try {
        const response = await axiosApi.post(apiBaseURL.PURCHASE_RETURN + "/" + id, formData, {
            withCredentials: true,
        });
        if (submit_button === 3) {
            localStorage.setItem("print_sale_id", response.data?.data?.lastTransactionId);
            localStorage.setItem("transaction_type", response.data?.data?.transactionType);
        }
        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            }),
        );
        if (sectionType === "gst-2b") {
            window.location.href = `${window.location.origin}/company/gstr-2b?section=gst-2b-reconciliation`;
        } else if (saveType === "saveAndNew") {
            window.location.reload();
        } else {
            window.location.href = `${window.location.origin}/company/purchase-returns`;
        }

    } catch ({ response }) {
        if (setIsDisable) {
            setIsDisable(false);
        };
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    }
};

export default PurchaseReturnSlice.reducer;
