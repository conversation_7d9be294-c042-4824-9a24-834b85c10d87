import { createSlice } from "@reduxjs/toolkit";
import axiosApi from "../../config/apiConfig";
import { apiBaseURL, toastType } from "../../constants";
import { addToast, errorToast } from "../actions/toastAction";

const initialState = {
    gstr2bData: [],
    gstr2bSummary: {}
}

export const gstr2bSlice = createSlice({
    name: "Gstr2b",
    initialState,
    reducers: {
        getGstr2bData: (state, action) => {
            state.gstr2bData = action.payload;
        },
        getGstr2bSummary: (state, action) => {
            state.gstr2bSummary = action.payload;
        },
    },
});

export const { getGstr2bData, getGstr2bSummary } = gstr2bSlice.actions;
export default gstr2bSlice.reducer;

export const fetchGstLogin = (params) => async (dispatch) => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.GST_LOGIN}`, params, { withCredentials: true });

        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            })
        );
        return response?.data;

    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};
export const fetchGstr2bData = (params, setShowLoadingModal) => async (dispatch) => {
    try {
        const { start_date, end_date } = params;
        const response = await axiosApi.get(`${apiBaseURL.GSTR_2B_FETCH_DATA}`, {
            params: {start_date, end_date}, withCredentials: true });
        return response?.data;

    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    } finally {
        setShowLoadingModal(false);
    }
};
export const fetchGstr2bSummary = (params) => async (dispatch) => {
    try {
        const { start_date, end_date } = params;

        const response = await axiosApi.get(`${apiBaseURL.GSTR_2B_SUMMARY}`, {
            params: {start_date, end_date},
            withCredentials: true
        });

        dispatch(getGstr2bSummary(response?.data?.data));

        return response?.data;

    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};

export const fetchGstr2bDetails = (params) => async (dispatch) => {
    try {
        const { start_date, end_date } = params;

        const response = await axiosApi.get(`${apiBaseURL.GSTR_2B_DETAILS}`, {
            params: {start_date, end_date},
            withCredentials: true
        });

        dispatch(getGstr2bData(response?.data?.data));

        return response?.data;

    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};

export const checkGstLogin = () => async (dispatch) => {
    try {
        const response = await axiosApi.get(`${apiBaseURL.CHECK_GST_LOGIN}`, { withCredentials: true });
        return response?.data;
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};
