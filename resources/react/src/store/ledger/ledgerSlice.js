import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axiosApi from "../../config/apiConfig";
import { apiBaseURL, toastType } from "../../constants";
import { addToast, errorToast } from "../actions/toastAction";
import { getItemModelDetail } from "../item/itemSlice";
import axios from "axios";
import { fetchTcsList, fetchTdsList, fetchTdsTcsRate } from "../rate/rateSlice";
import { calculateTotal, RoundOffMethodForTds } from "../../shared/calculation";
import { useParams } from "react-router-dom";
import { updatePurchaseOcrStatementList } from "../purchase-ocr/purchaseOcrSlice";
import { fetchBankList } from "../configuration/configurationSlice";
import { isPaymentModeList } from "../../shared/sharedFunction";

const initialState = {
    additionalLedger: [],
    addless: [],
    party: [],
    ledgerModelDetail: [],
    getLedgerById: [],
    payment: [],
    paymentMode: [],
    ledgerGroupDetail: [],
    partyDetail: [],
    itemDetail: [],
    ledgerGroupList: [],
    ifscDetail: [],
    itemStockDetail: [],
    status: "",
};

export const fetchAdditionalLedgerList = createAsyncThunk(
    "additional/fetchAdditionalLedgerList",
    async (data) => {
        const response = await axiosApi.post(apiBaseURL.ADDITIONAL_CHARGES_LEDGER_LIST, data, {
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response.data.data;
    }
);

export const fetchAddlessLedgerList = createAsyncThunk(
    "addless/fetchAddlessLedgerList",
    async (data) => {
        const response = await axiosApi.post(apiBaseURL.ADD_LESS_LEDGER_LIST, data, {
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response.data.data;
    }
);

export const fetchPartyList = createAsyncThunk("addless/fetchPartyList", async data => {
    const response = await axiosApi.post(apiBaseURL.PARTY_LIST, data, {
        headers: {
            "Content-Type": "application/json",
        },
    });
    return response.data.data;
});
export const fetchPaymentLedgerList = createAsyncThunk("addless/fetchPaymentLedgerList", async (data) => {
    const response = await axiosApi.post(apiBaseURL.PAYMENT_LEDGER_LIST, data, {
        headers: {
            "Content-Type": "application/json",
        },
    });
    return response.data.data;
});
export const fetchPaymentModeList = createAsyncThunk("addless/fetchPaymentModeList", async (id) => {
    const response = await axiosApi.get(`${apiBaseURL.PAYMENT_MODE}/${id}`, {
        headers: {
            "Content-Type": "application/json",
        },
    });
    return response.data.data;
});

export const fetchLedgerGroupList = createAsyncThunk("item/fetchLedgerGroup", async () => {
    const response = await axiosApi.get(apiBaseURL.LEDGER_GROUP_LIST, {
        headers: {
            "Content-Type": "application/json",
        },
    });
    return response.data.data;
});

export const ledgerSlice = createSlice({
    name: "Ledger",
    initialState,
    reducers: {
        getLedgerById: (state, action) => {
            state.getLedgerById = action.payload;
        },
        ledgerModelDetail: (state, action) => {
            state.ledgerModelDetail = action.payload;
        },
        ledgerGroupDetail: (state, action) => {
            state.ledgerGroupDetail = action.payload;
        },
        partyDetail: (state, action) => {
            state.partyDetail = action.payload;
        },
        updatePartyDetail: (state, action) => {
            state.partyDetail = {
                ...state.partyDetail,
                billingAddress: action.payload,
            };
        },
        itemDetail: (state, action) => {
            state.itemDetail = action.payload;
        },
        ifscDetail: (state, action) => {
            state.ifscDetail = action.payload;
        },
        itemStockDetail: (state, action) => {
            state.itemStockDetail = action.payload;
        },

        status: (state, action) => {
            state.status = action.payload;
        },
    },
    extraReducers: builder => {
        builder.addCase(fetchPartyList.fulfilled, (state, action) => {
            const combinedParties = [...state.party, ...action.payload];

            const uniqueParties = Array.from(
                new Map(combinedParties.map(party => [party.partyId, party])).values()
            );

            state.party = uniqueParties;
        });
        builder.addCase(fetchAdditionalLedgerList.fulfilled, (state, action) => {
            state.additionalLedger = action.payload;
        });
        builder.addCase(fetchAddlessLedgerList.fulfilled, (state, action) => {
            state.addless = action.payload;
        });
        builder.addCase(fetchPaymentLedgerList.fulfilled, (state, action) => {
            state.payment = action.payload;
        });
        builder.addCase(fetchPaymentModeList.fulfilled, (state, action) => {
            state.paymentMode = action.payload;
        });
        builder.addCase(fetchLedgerGroupList.fulfilled, (state, action) => {
            state.ledgerGroupList = action.payload;
        });
    },
});

export const {
    getLedgerById,
    ledgerModelDetail,
    ledgerGroupDetail,
    partyDetail,
    itemDetail,
    resetLedgerDetail,
    updatePartyDetail,
    ifscDetail,
    itemStockDetail,
    status,
} = ledgerSlice.actions;

export const addMasterLedger = (values, type, setIsDisable, redirectType) => async dispatch => {
    try {
        const response = await axiosApi.post(apiBaseURL.LEDGER, values, {
            withCredentials: true,
        });
        dispatch(addToast({ text: response.data?.message, type: toastType.ADD_TOAST }));
        setTimeout(() => {
            if (type == "saveAndNew") {
                window.location.reload();
            } else if(redirectType){
                if(redirectType == "Supplier"){
                    window.location.href = `${window.location.origin}/company/supplier-master`;
                }else{
                    window.location.href = `${window.location.origin}/company/customer-master`;
                }
            } else {
                window.location.href = `${window.location.origin}/company/ledgers`;
            }
        }, 500);
        dispatch(fetchLedgerGroupList());
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
        setIsDisable(false)
    }
};

export const addLedger =
    (
        isPurchase,
        formData,
        handleClose,
        setModalType,
        setGstQuote,
        getId,
        index,
        setIndex,
        items,
        setItems,
        addLessChanges,
        setAddLessChanges,
        additionalCharges,
        setAdditionalCharges,
        paymentLedgerDetail,
        setPaymentLedgerDetail,
        tcsRate,
        setTcsRate,
        changeLedgerName,
        taxableValue = 0,
        invoiceValue = 0,
        itemType,
        changeTax,
        partyLedgerId,
        setIsDisable,
        ocrData,
        setOcrData,
        id,
        statementId,
        configurationSale,
        setConfigurationSale,
        isBankDetails
    ) =>
    async dispatch => {
        try {
            const response = await axiosApi.post(apiBaseURL.LEDGER, formData, {
                withCredentials: true,
            });
            dispatch(
                addToast({
                    text: response?.data?.message,
                    type: toastType.ADD_TOAST,
                })
            );
            const ledgerResponse = response.data.data;
            if (handleClose) {
                handleClose(response.data.data.id);
            }
            if (getId && ledgerResponse?.flag !== "tcs") {
                getId(response.data.data.id);
            }
            if (setGstQuote) {
                setGstQuote(prev => ({
                    ...prev,
                    party_ledger_id: response.data.data.id,
                    gstin:
                        response.data.data?.ledgers_data?.Customer?.gstin ??
                        response.data.data?.ledgers_data?.Supplier?.gstin,
                    closing_balance: 0,
                }));
            }
            if (setModalType) {
                setModalType(false);
            }
            if (ledgerResponse?.flag == "addless") {
                dispatch(fetchAddlessLedgerList());
            } else if (
                ledgerResponse?.flag === "additional" ||
                ledgerResponse?.flag === "addless"
            ) {
                dispatch(fetchItemLedgerDetail());
                dispatch(fetchAdditionalLedgerList());
                dispatch(fetchAddlessLedgerList());
            } else if (ledgerResponse?.flag === "party" || ledgerResponse?.flag === "addless") {
                dispatch(fetchPartyList({ ids: [response.data.data.id] }));
                dispatch(fetchAddlessLedgerList());
                dispatch(fetchPartyDetail(ledgerResponse?.id));
            } else if (ledgerResponse?.flag === "payment" || ledgerResponse?.flag === "addless") {
                dispatch(fetchPaymentLedgerList());
                dispatch(fetchPaymentModeList(isPaymentModeList ? 2 : 1));
                dispatch(fetchAddlessLedgerList());
            } else if (ledgerResponse?.flag === "tds" || ledgerResponse?.flag === "addless") {
                dispatch(fetchTdsList(isPurchase ? 1 : 2));
                dispatch(fetchAddlessLedgerList());
            } else if (ledgerResponse?.flag === "tcs" || ledgerResponse?.flag === "addless") {
                dispatch(fetchAddlessLedgerList());
                dispatch(fetchTcsList(isPurchase ? {id:2} : {id:1}));
            }
            dispatch(getItemModelDetail());
            if (
                setPaymentLedgerDetail &&
                ledgerResponse?.flag === "payment" &&
                changeLedgerName !== "addless"
            ) {
                let paymentLedger = [...paymentLedgerDetail?.payment_detail];
                paymentLedger[index].pd_ledger_id = response.data.data.id;
                setPaymentLedgerDetail({
                    ...paymentLedgerDetail,
                    payment_detail: paymentLedger,
                });
            }
            if(setConfigurationSale){
                setConfigurationSale({
                    ...configurationSale,
                    document: {...configurationSale?.document, bank_id: response.data.data.id}
                })
                dispatch(fetchBankList())
            }
            if (
                setPaymentLedgerDetail &&
                ledgerResponse?.flag === "tds" &&
                changeLedgerName !== "addless"
            ) {
                setPaymentLedgerDetail({
                    ...paymentLedgerDetail,
                    tds_tax_id: response.data.data.id,
                    tds_rate: formData.for_other,
                });
            }
            if (
                setItems &&
                itemType === "accounting" &&
                changeLedgerName !== "addless" &&
                ledgerResponse?.flag === "additional"
            ) {
                let item = [...items];
                item[index].selectedLedger = response.data.data.id;
                item[index].gst_id = response.data.data.model?.gst_tax_id ?? 0;
                item[index].gst = response.data.data.model?.gst_tax_rate ?? 0;
                item[index].cessRate = response.data.data.model?.gst_cess ?? 0;
                item[index].notExistsLedger = false;
                item[index].additional_description = response.data.data.model?.description;
                const calculatedTotal = calculateTotal(item[index], false, changeTax, true);

                item[index].total = calculatedTotal.total;
                item[index].updatedTotal = calculatedTotal.total;
                item[index].sgstValue = calculatedTotal.sgst;
                item[index].cgstValue = calculatedTotal.sgst;
                item[index].cessValue = calculatedTotal.cess;
                setItems(item);
                setIndex(0);
            }
            if (
                setOcrData
            ) {
                let item = [...ocrData];
                let updatedItem = {
                    ...item[index],
                    ledger_id: response?.data?.data?.id,
                };
                item[index] = updatedItem;
                setOcrData(item);
                setIndex(0);
                dispatch(updatePurchaseOcrStatementList(id, {id: statementId, ledger:{ledger_id:response?.data?.data?.id, ledger_name:response?.data?.data?.name}}))
            }
            if (setTcsRate && ledgerResponse?.flag === "tcs" && changeLedgerName !== "addless") {
                setTcsRate({
                    ...tcsRate,
                    tcs_tax_id: response.data.data.id,
                    tcs_rate: formData.for_other,
                });
                dispatch(
                    fetchTdsTcsRate(
                        response.data?.data.id,
                        partyLedgerId,
                        setTcsRate,
                        taxableValue,
                        invoiceValue
                    )
                );
            }
            if (isBankDetails && setAdditionalCharges) {
                dispatch(fetchBankList());
                setAdditionalCharges(prev => ({
                    ...prev,
                    bank_id: response.data.data.id
                }))
            };
            if (
                setAdditionalCharges &&
                ledgerResponse?.flag === "additional" &&
                changeLedgerName !== "addless"
            ) {
                let additionalCharge = [...additionalCharges?.additional_detail];
                additionalCharge[index].ac_ledger_id = response.data.data.id;
                (additionalCharge[index].ac_gst_rate_id = {
                    label: response.data?.data.model.gst_tax_rate,
                    value: response.data?.data.model.gst_tax_id,
                    rate: response.data?.data.model.gst_tax_rate,
                }),
                    setAdditionalCharges({
                        ...additionalCharges,
                        additional_detail: additionalCharge,
                    });
            }
            if (setAddLessChanges && changeLedgerName === "addless") {
                let addLessChange = [...addLessChanges];
                addLessChange[index].al_ledger_id = response.data.data.id;
                setAddLessChanges(addLessChanges);
            }
            if (setIsDisable) {
                setIsDisable(false);
            };
        } catch ({ response }) {
            if (setIsDisable) {
                setIsDisable(false);
            };
            dispatch(
                errorToast({
                    text: response?.data?.message,
                    type: toastType.ERROR,
                })
            );
        }
    };
export const fetchPartyDetail = id => async dispatch => {
    try {
        if (!id) return;
        const response = await axiosApi.get(apiBaseURL.PARTY_DETAIL + "/" + id, {
            withCredentials: true,
        });
        dispatch(partyDetail(response.data.data));
    } catch (err) {
        throw new Error(err);
    }
};
export const addItemStock = (data, close, start_date, end_date) => async dispatch => {
    try {
        const response = await axiosApi.post(apiBaseURL.ITEM_STOCK, data, {
            withCredentials: true,
        });
        dispatch(fetchItemStockList(data?.ledger_id, start_date, end_date));
        close();
        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            })
        );
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};
export const updateItemStock = (id, values, close, start_date, end_date) => async dispatch => {
    try {
        if (!id) return;
        const response = await axiosApi.post(apiBaseURL.ITEM_STOCK + "/" + id, values, {
            withCredentials: true,
        });
        close();
        dispatch(fetchItemStockList(values?.ledger_id, start_date, end_date));
        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            })
        );
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};
export const fetchItemStockList = (id, start_date, end_date) => async dispatch => {
    try {
        if (!id || !start_date || !end_date) return;
        const response = await axiosApi.get(
            `${apiBaseURL.ITEM_STOCK_LIST}/${id}?start_date=${start_date}&end_date=${end_date}`,
            {
                withCredentials: true,
            }
        );
        dispatch(itemStockDetail(response.data.data));
        return response.data.data;
    } catch (err) {
        throw new Error(err);
    }
};
export const deleteItemStock = (id, data, start_date, end_date) => async dispatch => {
    try {
        if (!id) return;
        const response = await axiosApi.get(apiBaseURL.ITEM_STOCK + "/" + id + "/delete", {
            withCredentials: true,
        });
        dispatch(fetchItemStockList(data?.ledger_id, start_date, end_date));
        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            })
        );
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};
export const fetchTdsRateDetail = id => async dispatch => {
    try {
        if (!id) return;
        const response = await axiosApi.get(apiBaseURL.TDS_TAX_DATA + "/" + id, {
            withCredentials: true,
        });
        return response.data.data;
    } catch (err) {
        throw new Error(err);
    }
};
export const fetchTcsRateDetail = id => async dispatch => {
    try {
        if (!id) return;
        const response = await axiosApi.get(apiBaseURL.TCS_TAX_DATA + "/" + id, {
            withCredentials: true,
        });
        return response.data.data;
    } catch (err) {
        throw new Error(err);
    }
};
export const fetchBankDetail = ifsc => async dispatch => {
    const ifscUrl = "https://ifsc.razorpay.com";
    try {
        if (!ifsc) return;
        const response = await axios.get(ifscUrl + "/" + ifsc);
        dispatch(ifscDetail(response.data));
    } catch (err) {
        throw new Error(err);
    }
};

export const fetchItemLedgerDetail = (data) => async dispatch => {
    try {
        const response = await axiosApi.post(apiBaseURL.ITEM_LEDGER, data, {
            withCredentials: true,
        });
        dispatch(itemDetail(response.data.data));
    } catch (err) {
        throw new Error(err);
    }
};

export const fetchLedgerById = (id, setLoader) => async dispatch => {
    try {
        if (!id) return;
        if (setLoader) {
            setLoader(true);
        }
        const response = await axiosApi.get(apiBaseURL.LEDGER + "/" + id, {
            withCredentials: true,
        });
        dispatch(getLedgerById(response.data.data));
        if (setLoader) {
            setLoader(false);
        }
    } catch (error) {
        if (error?.response?.data?.status) {
            return dispatch(status(error?.response?.data?.status));
        }
        if (setLoader) {
            setLoader(false);
        }
        throw new Error(error);
    } finally {
        if (setLoader) {
            setLoader(false);
        }
    }
};

export const updateMasterLedger = (id, values, setIsDisable, redirectType) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.LEDGER}/${id}`, values, {
            withCredentials: true,
        });
        dispatch(addToast({ text: response?.data?.message, type: toastType.ADD_TOAST }));
        setTimeout(() => {
            if(redirectType){
                if(redirectType == "Supplier"){
                    window.location.href = `${window.location.origin}/company/supplier-master`;
                }else{
                    window.location.href = `${window.location.origin}/company/customer-master`;
                }
            }else{
                window.location.href = `${window.location.origin}/company/ledgers`;
            }
        }, 500);
        dispatch(fetchLedgerGroupList());
    } catch ({ response }) {
        dispatch(errorToast({ text: response?.data?.message, type: toastType.ERROR }));
        setIsDisable(false)
    }
};
export const updateLedger =
    (
        id,
        isPurchase,
        formdata,
        setModalType,
        index,
        setIndex,
        setGstQuote,
        setTcsRate,
        setTdsRate,
        items,
        setItems,
        addLessChanges,
        setAddLessChanges,
        additionalCharges,
        setAdditionalCharges,
        paymentLedgerDetail,
        setPaymentLedgerDetail,
        tcsRate,
        changeLedgerName,
        taxableValue = 0,
        invoiceValue = 0,
        setIsEditCalculation,
        itemType,
        changeTax,
        partyLedgerId,
        setIsDisable,
        ocrData,
        setOcrData,
        ocrId,
        statementId,
        configurationSale,
        setConfigurationSale,
        isBankDetails
    ) =>
    async dispatch => {
        try {
            const response = await axiosApi.post(`${apiBaseURL.LEDGER}/${id}`, formdata, {
                withCredentials: true,
            });
            const ledgerResponse = response.data?.data;

            if (ledgerResponse?.flag == "addless") {
                dispatch(fetchAddlessLedgerList({ids:[id]}));
            } else if (
                ledgerResponse?.flag === "additional" ||
                ledgerResponse?.flag === "addless"
            ) {
                dispatch(fetchItemLedgerDetail({ids:[id]}));
                dispatch(fetchAdditionalLedgerList({ids:[id]}));
                dispatch(fetchAddlessLedgerList({ids:[id]}));
            } else if (ledgerResponse?.flag === "party" || ledgerResponse?.flag === "addless") {
                dispatch(fetchPartyList({ids:[id]}));
                dispatch(fetchAddlessLedgerList({ids:[id]}));
                dispatch(fetchPartyDetail(response.data?.data.id));
            } else if (ledgerResponse?.flag === "payment" || ledgerResponse?.flag === "addless") {
                dispatch(fetchPaymentLedgerList({ids:[id]}));
                dispatch(fetchPaymentModeList(isPaymentModeList ? 2 : 1));
                dispatch(fetchAddlessLedgerList({ids:[id]}));
            } else if (ledgerResponse?.flag === "tds" || ledgerResponse?.flag === "addless") {
                dispatch(fetchTdsList(isPurchase ? {id:1, ids:[id]} : {id:2, ids:[id]}));
                dispatch(fetchAddlessLedgerList({ids:[id]}));
                dispatch(fetchTdsTcsRate(response.data?.data.id, partyLedgerId, setTdsRate, taxableValue, invoiceValue, false));
            } else if (ledgerResponse?.flag === "tcs" || ledgerResponse?.flag === "addless") {
                dispatch(fetchAddlessLedgerList({ids:[id]}));
                dispatch(fetchTcsList(isPurchase ? {id:2, ids:[id]} : {id:1, ids:[id]}));
                dispatch(
                    fetchTdsTcsRate(
                        response.data?.data.id,
                        partyLedgerId,
                        setTcsRate,
                        taxableValue,
                        invoiceValue
                    )
                );
            }

            if (
                setGstQuote &&
                (ledgerResponse?.flag === "party" || ledgerResponse?.flag === "addless")
            ) {
                setGstQuote(prev => ({
                    ...prev,
                    party_ledger_id: response.data.data.id,
                    gstin:
                        response.data.data?.ledgers_data?.Customer?.gstin ??
                        response.data.data?.ledgers_data?.Supplier?.gstin,
                }));
            }
            dispatch(
                addToast({
                    text: response?.data?.message,
                    type: toastType.ADD_TOAST,
                })
            );
            if (setModalType) {
                setModalType("");
            }

            if (
                setPaymentLedgerDetail &&
                ledgerResponse?.flag === "tds" &&
                changeLedgerName !== "addless"
            ) {
                setPaymentLedgerDetail({
                    ...paymentLedgerDetail,
                    tds_tax_id: response.data?.data.id,
                    tds_rate: formdata.for_other,
                    is_status: response?.data?.data?.status
                });
            }
            if (
                setPaymentLedgerDetail &&
                ledgerResponse?.flag === "tcs" &&
                changeLedgerName !== "addless"
            ) {
                setPaymentLedgerDetail({
                    ...paymentLedgerDetail,
                    tcs_tax_id: response.data?.data.id,
                    tcs_rate: formdata.tcs_rate,
                    is_status: response?.data?.data?.status
                });
            }
            if(setConfigurationSale){
                setConfigurationSale({
                    ...configurationSale,
                    document: {...configurationSale?.document, bank_id: response.data.data.id}
                })
                dispatch(fetchBankList())
            }
            if (setItems && itemType === "accounting" && changeLedgerName !== "addless") {
                let item = [...items];
                item[index].selectedLedger = response.data?.data.id;
                item[index].gst_id = response.data.data.model.gst_tax_id ?? 0;
                item[index].gst = response.data.data.model.gst_tax_rate ?? 0;
                item[index].cessRate = response.data.data.model.gst_cess ?? 0;
                item[index].additional_description = formdata.description;
                const calculatedTotal = calculateTotal(item[index], false, changeTax, true);
                item[index].total = calculatedTotal.total;
                item[index].updatedTotal = calculatedTotal.total;
                item[index].sgstValue = calculatedTotal.sgst;
                item[index].cgstValue = calculatedTotal.sgst;
                item[index].cessValue = calculatedTotal.cess;
                setItems(item);
                setIsEditCalculation(true);
                setIndex(0);
            }
            if (
                setOcrData
            ) {
                let item = [...ocrData];
                let updatedItem = {
                    ...item[index],
                    ledger_id: response?.data?.data?.id,
                };
                item[index] = updatedItem;
                setOcrData(item);
                setIndex(0);
                dispatch(updatePurchaseOcrStatementList(ocrId, {id: statementId, ledger:{ledger_id:response?.data?.data?.id, ledger_name:response?.data?.data?.name}}))
            }

            if (isBankDetails && setAdditionalCharges) {
                dispatch(fetchBankList());
                setAdditionalCharges(prev => ({
                    ...prev,
                    bank_id: response.data.data.id
                }))
            };

            if (
                setAdditionalCharges &&
                ledgerResponse?.flag === "additional" &&
                changeLedgerName !== "addless"
            ) {
                setIsEditCalculation(true);
                let additionalCharge = [...additionalCharges?.additional_detail];
                additionalCharge[index].ac_ledger_id = response.data?.data.id;
                (additionalCharge[index].ac_gst_rate_id = {
                    label: response.data?.data.model.gst_tax_rate,
                    value: response.data?.data.model.gst_tax_id,
                    rate: response.data?.data.model.gst_tax_rate,
                    is_status: response?.data?.data?.status
                }),
                    setAdditionalCharges({
                        ...additionalCharges,
                        additional_detail: additionalCharge,
                    });
            }
            if (setAddLessChanges && changeLedgerName === "addless") {
                let addLessChange = [...addLessChanges];
                addLessChange[index].al_ledger_id = response.data?.data.id;
                setAddLessChanges(addLessChanges);
            }
            if (setIsDisable) {
                setIsDisable(false);
            };
        } catch ({ response }) {
            if (setIsDisable) {
                setIsDisable(false);
            };
            if (response) {
                dispatch(
                    errorToast({
                        text: response?.data?.message,
                        type: toastType.ERROR,
                    })
                );
            }
        }
    };
export const getLedgerModelDetail = type => async dispatch => {
    try {
        if (!type) return;
        const response = await axiosApi.get(apiBaseURL.LEDGER_GROUP_DETAIL + type);
        dispatch(ledgerGroupDetail(response.data.data?.groupList));
        return response.data.data?.groupList
    } catch (err) {
        throw new Error(err);
    }
};
export const createLocationOfAssets = (formdata, close, partyFormik) => async dispatch => {
    try {
        const response = await axiosApi.post(apiBaseURL.LOCATION_OF_ASSETS, formdata);
        close();
        partyFormik.setFieldValue("location_of_asset_id", response.data.data?.location?.id);
        dispatch(getLedgerGroupDetail(partyFormik.values.group_id));
    } catch (err) {
        throw new Error(err);
    }
};
export const getLedgerGroupDetail = id => async dispatch => {
    try {
        if (!id) return;
        const response = await axiosApi.get(apiBaseURL.LEDGER_DETAIL + "/" + id);
        dispatch(ledgerModelDetail(response.data.data));
        return response.data.data
    } catch (err) {
        throw new Error(err);
    }
};
export const checkCapitalLedgerRatio = (id, holding_ratio, profit_ratio) => async dispatch => {
    try {
        const params = new URLSearchParams();
        if (holding_ratio) params.append("holding_ratio", holding_ratio);
        if (profit_ratio) params.append("profit_ratio", profit_ratio);
        if (id) params.append("id", id);
        const response = await axiosApi.get(`${apiBaseURL.CAPITAL_RATIO}/?${params.toString()}`);
        return response.data.data;
    } catch (err) {
        throw new Error(err);
    }
};
export const getTcsTdsGroupList = (id, type) => async dispatch => {
    try {
        if (!id) return;
        const response = await axiosApi.get(
            `${type ? apiBaseURL.TCS_GROUP_LIST : apiBaseURL.TDS_GROUP_LIST}/${id}`
        );
        dispatch(ledgerGroupDetail(response.data.data?.groupList));
    } catch (err) {
        throw new Error(err);
    }
};
export const updateDispatchAddress = (formdata, handleClose) => async dispatch => {
    try {
        dispatch(partyDetail(formdata));
        handleClose();
    } catch (err) {
        throw new Error(err);
    }
};

export const fetchSwiftCodeLabel = () => async dispatch => {
    try {
        const response = await axiosApi.get(apiBaseURL.GET_SWIFT_CODE_LABEL, {
            withCredentials: true,
        });
        return response.data;
    } catch (err) {
        throw new Error(err);
    }
};


export const updateSwiftCodeLabel = (params) => async dispatch => {
    try {
        const response = await axiosApi.post(apiBaseURL.UPDATE_SWIFT_CODE_LABEL, params, {
            withCredentials: true,
        });

        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            })
        );

        return response.data;

    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};

export const fetchLedgerBillWishExcel = (params, setExcelFiles, fileInputRef) => async dispatch => {
    try {
        const response = await axiosApi.post(apiBaseURL.LEDGER_BILL_WISH_EXCEL, params, {
            withCredentials: true,
        });

        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            })
        );

        setExcelFiles("")
        return response.data;

    } catch ({ response }) {
        setExcelFiles("")
        if (fileInputRef.current) {
            fileInputRef.current.value = "";
        }
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};

export default ledgerSlice.reducer;
