import moment from "moment";
import { useEffect } from "react";
import { Decimal } from "decimal.js";

// This function is updated because old is not handled proper fixing for negative values (Ex. -156.145)
// export const customToFixed = (number, precision) => {
//     // Coerce inputs to numbers
//     number = Number(number);
//     precision = Number(precision);

//     // Handle edge cases
//     if (isNaN(number) || isNaN(precision) || precision < 0) {
//         return "0";
//     }

//     // Scale the number
//     const multiplier = Math.pow(10, precision);

//     // Perform rounding with consistent behavior for both positive and negative numbers
//     const roundedNumber =
//         (Math.sign(number) * Math.round((Math.abs(number) * multiplier) + 1e-10)) / multiplier;

//     // Convert the rounded number to a string
//     let [integerPart, decimalPart] = roundedNumber.toString().split(".");

//     // If no decimal part exists, handle precision
//     decimalPart = decimalPart || "";

//     // Pad decimal part with zeros if necessary
//     while (decimalPart.length < precision) {
//         decimalPart += "0";
//     }

//     return precision > 0 ? `${integerPart}.${decimalPart}` : integerPart;
// };

// 16566.225
export const customToFixed = (number, precision) => {

    // Validate number
    if (!number || number === null || number === undefined || isNaN(Number(number))) {
        return "0.00";
    }

    // Validate precision
    precision = Number(precision);
    if (isNaN(precision) || precision < 0) {
        return "0.00";
    }

    // Use Decimal for safe math
    const decimalNumber = new Decimal(number);

    if (!decimalNumber.isFinite()) {
        return "0.00";
    }

    const rounded = decimalNumber.toDecimalPlaces(precision, Decimal.ROUND_HALF_UP);
    return rounded.toFixed(precision);
};

export function convertToZeroStringWithOne(decimalPlaces) {
    // Ensure the input is a valid number
    const count = parseInt(decimalPlaces, 10);

    if(count == 0){
        return "0";
    }

    // If the input is invalid or less than 1, return "1" (minimum valid output)
    if (isNaN(count) || count < 1) {
        return "1";
    }

    // Generate a string of zeroes with the last character as "1"
    return "0".repeat(count - 1) + "1";
}

export const calculatePriceWithoutGst = (rateWithGst, gstRate) => {
    const gstAmount = (rateWithGst * gstRate) / (100 + gstRate);
    return parseFloat(customToFixed(rateWithGst - gstAmount, 2));
};

export const calculatePriceWithGst = (rateWithoutGst, gstRate) => {
    const gstAmount = (parseFloat(rateWithoutGst) * parseFloat(gstRate)) / 100;
    return parseFloat(customToFixed(rateWithoutGst + gstAmount, 2));
};

const multiply = function(a, b) {
    var commonMultiplier = 1000000;

    a *= commonMultiplier;
    b *= commonMultiplier;

    return (a * b) / (commonMultiplier * commonMultiplier);
};

const customMinus = function(a, b) {
  const multiplier = 1000000;
  const aInt = Math.round(a * multiplier);
  const bInt = Math.round(b * multiplier);

  return (aInt - bInt) / multiplier;
};

const customPlus = (a, b) => {
  const multiplier = 1000000;
  return (Math.round(a * multiplier) + Math.round(b * multiplier)) / multiplier;
};

export const calculateTotal = (
    item,
    isAccounting = false,
    changeTax,
    ledger,
    defaultTaxCalculation = false,
    rcmApplicable,
    isPurchase,
) => {
    if (isAccounting) {
        let subtotal = item.updatedAmountWithoutGst ? parseFloat(item.updatedAmountWithoutGst) : 0;
        let discountAmount = 0;
        if (item.discountType === 1 && item.discountValue) {
            discountAmount = item.discountValue;
        } else if (item.discountType === 2 && item.discountValue) {
            discountAmount = customToFixed(
                (item.updatedAmountWithoutGst * item.discountValue) / 100,
                2,
            );
        }

        subtotal -= discountAmount;
        const gstAmount = (subtotal * item.gst) / 100;
        const sgst = gstAmount / 2;

        return {
            total: parseFloat(customToFixed(subtotal, 2)),
            sgst: parseFloat(customToFixed(sgst, 2)),
        };
    } else if (ledger) {
        let quantity = 1;
        let discountValue = parseFloat(item?.discountValue || 0);
        let discountValue_2 = parseFloat(item?.discountValue_2 || 0);
        let gstAmount = 0;
        // let subtotal = quantity * item?.updatedRateWithoutGst;
        let productPrice = parseFloat(item?.amountWithoutGst);
        const decimal = item?.decimal_places_for_rate ?? 2;
        let subtotal = customToFixed(quantity * productPrice, decimal);

        let totalDiscountAmount = 0;
        let totalDiscount2Amount = 0;
        let rpuWithoutGSTAfterDiscount = productPrice;

        if (!changeTax) {
            if (item?.discountType === 1) {
                totalDiscountAmount = discountValue * quantity;
                rpuWithoutGSTAfterDiscount = customToFixed(productPrice - discountValue, 2);
            } else if (item?.discountType === 2) {
                totalDiscountAmount = (productPrice * discountValue) / 100;
                rpuWithoutGSTAfterDiscount = customToFixed(productPrice - totalDiscountAmount, 2);
                totalDiscountAmount = totalDiscountAmount * quantity;
            }

            if (item?.discountType_2 === 1) {
                totalDiscount2Amount = discountValue_2 * quantity;
            } else if (item?.discountType_2 === 2) {
                totalDiscount2Amount = (rpuWithoutGSTAfterDiscount * discountValue_2) / 100;
                totalDiscount2Amount = totalDiscount2Amount * quantity;
            }
            totalDiscountAmount += totalDiscount2Amount;
            totalDiscountAmount = customToFixed(totalDiscountAmount, 2);

            subtotal -= parseFloat(totalDiscountAmount);
        }

        // Calculate GST
        if (item?.gst !== 0) {
            if (defaultTaxCalculation) {
                gstAmount = ((changeTax ? productPrice : subtotal) * (item?.gst || 0)) / 100;
                gstAmount = customToFixed(gstAmount, decimal);
            } else {
                gstAmount =
                    (productPrice * (item?.gst || 0)) / (100 + (changeTax ? item?.gst : 0 || 0));
            }
            if (changeTax) {
                const singlePriceWithoutGST = customToFixed(productPrice - gstAmount, decimal);
                productPrice = singlePriceWithoutGST;
                subtotal = customToFixed(quantity * parseFloat(singlePriceWithoutGST), 2);
            }
        }

        if (changeTax) {
            if (item?.discountType === 1) {
                totalDiscountAmount = discountValue * quantity;
                rpuWithoutGSTAfterDiscount = customToFixed(productPrice - discountValue, 2);
            } else if (item?.discountType === 2) {
                totalDiscountAmount = (productPrice * discountValue) / 100;
                rpuWithoutGSTAfterDiscount = customToFixed(productPrice - totalDiscountAmount, 2);

                totalDiscountAmount = totalDiscountAmount * quantity;
            }

            if (item?.discountType_2 === 1) {
                totalDiscount2Amount = discountValue_2 * quantity;
            } else if (item?.discountType_2 === 2) {
                totalDiscount2Amount = (rpuWithoutGSTAfterDiscount * discountValue_2) / 100;
                totalDiscount2Amount = totalDiscount2Amount * quantity;
            }
            totalDiscountAmount += totalDiscount2Amount;
            totalDiscountAmount = customToFixed(totalDiscountAmount, 2);
            subtotal -= parseFloat(totalDiscountAmount);
        }
        const gstFromSubTotal = (subtotal * item?.gst) / 100;
        const sgst = gstFromSubTotal / 2;
        const cessRate = (subtotal * parseFloat(item?.cessRate)) / 100;

        return {
            total: parseFloat(customToFixed(subtotal, 2)),
            sgst: parseFloat(customToFixed(sgst, 2)),
            cess: parseFloat(customToFixed(cessRate, 2)),
            igst: parseFloat(customToFixed(gstFromSubTotal, 2))
        };
    } else {
        let quantity = parseFloat(item?.quantity) ?? 0;
    let discountValue = parseFloat(item?.discountValue || 0);
    let discountValue_2 = parseFloat(item?.discountValue_2 || 0);
    let gstAmount = 0;
    // let subtotal = quantity * item?.updatedRateWithoutGst;
    let productPrice = parseFloat(item?.updatedRateWithoutGst);
    const decimal = item?.decimal_places_for_rate ?? 2;
    let subtotal = multiply(quantity, productPrice);
    subtotal = customToFixed(subtotal, decimal);

    // Working fine for (2045.47 discount issue)
    let totalDiscountAmount = 0;
    let totalDiscount2Amount = 0;
    let rpuWithoutGSTAfterDiscount = productPrice;

    if (!changeTax) {
        if (item?.discountType === 1 && discountValue > 0) {
            totalDiscountAmount = discountValue * quantity;
            rpuWithoutGSTAfterDiscount = customToFixed(productPrice - discountValue, 2);
        } else if (item?.discountType === 2 && discountValue > 0) {

            totalDiscountAmount = (productPrice * discountValue / 100) ;
            const minusPrice = customMinus(productPrice, totalDiscountAmount);
            rpuWithoutGSTAfterDiscount = customToFixed(minusPrice, 2);
            totalDiscountAmount = multiply(totalDiscountAmount, quantity);
        }

        if (item?.discountType_2 === 1 && discountValue_2 > 0) {
            totalDiscount2Amount = discountValue_2 * quantity;
        } else if (item?.discountType_2 === 2 && discountValue_2 > 0) {
            totalDiscount2Amount = (rpuWithoutGSTAfterDiscount * discountValue_2  / 100);
            totalDiscount2Amount = multiply(totalDiscount2Amount, quantity);
        }
        totalDiscountAmount = customPlus(parseFloat(totalDiscountAmount), parseFloat(totalDiscount2Amount));
        totalDiscountAmount = customToFixed(totalDiscountAmount, 2);

        subtotal -= parseFloat(totalDiscountAmount);
    }

    // Calculate GST
    if (item?.gst !== 0) {
        if (defaultTaxCalculation) {
            // Default GST calculation
            gstAmount = (changeTax ? productPrice : subtotal) * (item?.gst || 0) / 100;
            gstAmount = customToFixed(gstAmount, decimal);
        } else {
            // GST calculation with changeTax consideration
            // gstAmount = (changeTax ? productPrice : subtotal) * (item?.gst || 0) / // Comment by ridham for calculation error using delivery challan
            gstAmount = (productPrice) * (item?.gst || 0) /
                (100 + (changeTax ? item?.gst : 0 || 0));
            // gstAmount = customToFixed(gstAmount, decimal); // commented for GST calculation with decimal
        }
        // Adjust subtotal if changeTax is true
        if (changeTax) {
            const singlePriceWithoutGST = customToFixed(productPrice - gstAmount, decimal);
            productPrice = singlePriceWithoutGST;
            rpuWithoutGSTAfterDiscount = singlePriceWithoutGST;
            const multiplyTotal = multiply(quantity, parseFloat(singlePriceWithoutGST));
            subtotal = customToFixed(multiplyTotal, 2);
        }
    }

    // Apply discounts after GST when changeTax is true
    if (changeTax) {
        if (item?.discountType === 1 && discountValue > 0) {
            totalDiscountAmount = discountValue * quantity;
            rpuWithoutGSTAfterDiscount = customToFixed(productPrice - discountValue, 2);
        } else if (item?.discountType === 2 && discountValue > 0) {

            totalDiscountAmount = (productPrice * discountValue) / 100;
            const minusPrice = customMinus(productPrice, totalDiscountAmount);
            rpuWithoutGSTAfterDiscount = customToFixed(minusPrice, 2);

            totalDiscountAmount = multiply(totalDiscountAmount, quantity);
        }

        if (item?.discountType_2 === 1 && discountValue_2 > 0) {
            totalDiscount2Amount = discountValue_2 * quantity;
        } else if (item?.discountType_2 === 2 && discountValue_2 > 0) {
            totalDiscount2Amount = (rpuWithoutGSTAfterDiscount * discountValue_2) / 100;
            totalDiscount2Amount = multiply(totalDiscount2Amount, quantity);
        }

        totalDiscountAmount = customPlus(parseFloat(totalDiscountAmount), parseFloat(totalDiscount2Amount));
        totalDiscountAmount = customToFixed(totalDiscountAmount, 2);

        subtotal -= parseFloat(totalDiscountAmount);
    }

    // Final calculations for SGST and cess
    // const sgst = (gstAmount * quantity) / 2;
    const gstFromSubTotal = (subtotal * item?.gst) / 100;
    // const sgst = (gstAmount) / 2;
    const sgst = (gstFromSubTotal) / 2;
    const cessRate = (subtotal * parseFloat(item?.cessRate)) / 100;
    return {
        total: parseFloat(customToFixed(subtotal, 2)),
        sgst: parseFloat(customToFixed(sgst, 2)),
        cess: parseFloat(customToFixed(cessRate, 2)),
        igst: parseFloat(customToFixed(gstFromSubTotal, 2))
    };
    }
};

export const calculateClassification = (classification, rate) => {
    let fixDigit = 2;
    let totalCgstTax = 0.0;
    let totalSgstTax = 0.0;
    let totalIgstTax = 0.0;
    rate?.rate?.forEach(rate => {
        const gstRate = rate?.rate || 0;
        const subTotal = rate?.total;
        const intraStateArray = [
            "Intrastate Sales Taxable",
            "Intrastate Sales Exempt",
            "Intrastate Sales Nil Rated",
        ];
        const interStateArray = [
            "Interstate Sales Taxable",
            "Interstate Sales Exempt",
            "Interstate Sales Nil Rated",
        ];
        const exportArray = [
            "Export Sales Taxable",
            "Export Sales Exempt",
            "Export Sales Nil Rated",
            "Export Sales under LUT/Bond",
        ];
        const sezArray = [
            "Sales to SEZ Taxable",
            "Sales to SEZ Exempt",
            "Sales to SEZ Nil Rated",
            "Sales to SEZ under LUT/Bond",
        ];
        const purchaseIntraStateTaxArray = [
            "Intrastate Purchase Taxable",
            "Intrastate Purchase URD Taxable",
        ];
        const purchaseInterstateTaxArray = [
            "Interstate Purchase Taxable",
            "Interstate Purchase URD Taxable",
            "Purchase - Import of goods",
            "Purchase - Import of Service",
        ];
        if (classification.classification_nature_name === "Deemed Export - Intrastate") {
            let finalValue = "";
            let cgstTax = parseFloat(gstRate) / 2;
            finalValue += customToFixed((subTotal * cgstTax) / 100, fixDigit);
            totalCgstTax += parseFloat(finalValue);
            totalSgstTax += parseFloat(finalValue);
        } else if (classification.classification_nature_name === "Deemed Export - Interstate") {
            let finalValue = "";
            let igstTax = parseFloat(gstRate);
            finalValue = customToFixed((subTotal * igstTax) / 100, fixDigit);
            totalIgstTax += parseFloat(finalValue);
        } else if (intraStateArray.includes(classification.classification_nature_name)) {
            if (
                classification.classification_nature_name === "Intrastate Sales Exempt" ||
                classification.classification_nature_name === "Intrastate Sales Nil Rated"
            ) {
                totalIgstTax += 0.0;
                totalCgstTax += 0.0;
                totalSgstTax += 0.0;
            } else {
                let finalValue = "";
                let cgstTax = parseFloat(gstRate) / 2;
                finalValue = customToFixed((subTotal * cgstTax) / 100, fixDigit);
                totalCgstTax += parseFloat(finalValue);
                totalSgstTax += parseFloat(finalValue);
            }
        } else if (interStateArray.includes(classification.classification_nature_name)) {
            if (
                classification.classification_nature_name === "Interstate Sales Nil Rated" ||
                classification.classification_nature_name === "Interstate Sales Exempt"
            ) {
                totalIgstTax += 0.0;
                totalCgstTax += 0.0;
                totalSgstTax += 0.0;
            } else if (classification.classification_nature_name === "Interstate Sales Taxable") {
                let finalValue = "";
                let igstTax = parseFloat(gstRate);
                finalValue = customToFixed((subTotal * igstTax) / 100, fixDigit);
                totalIgstTax += parseFloat(finalValue);
            } else {
                let finalValue = "";
                let igstTax = parseFloat(gstRate);
                finalValue = customToFixed((subTotal * igstTax) / 100, fixDigit);
                totalCgstTax += parseFloat(finalValue);
                totalSgstTax += parseFloat(finalValue);
            }
        } else if (exportArray.includes(classification.classification_nature_name)) {
            if (
                classification.classification_nature_name === "Export Sales Nil Rated" ||
                classification.classification_nature_name === "Export Sales Exempt" ||
                classification.classification_nature_name === "Export Sales under LUT/Bond"
            ) {
                totalIgstTax += 0.0;
                totalCgstTax += 0.0;
                totalSgstTax += 0.0;
            } else if (classification.classification_nature_name === "Export Sales Taxable") {
                let finalValue = "";
                let igstTax = parseFloat(gstRate);
                finalValue = customToFixed((subTotal * igstTax) / 100, fixDigit);
                totalIgstTax += parseFloat(finalValue);
            } else {
                let finalValue = "";
                let igstTax = parseFloat(gstRate);
                finalValue = customToFixed((subTotal * igstTax) / 100, fixDigit);
                totalCgstTax += parseFloat(finalValue);
                totalSgstTax += parseFloat(finalValue);
            }
        } else if (sezArray.includes(classification.classification_nature_name)) {
            if (classification.classification_nature_name === "Sales to SEZ Taxable") {
                let finalValue = "";
                let igstTax = parseFloat(gstRate);
                finalValue = customToFixed((subTotal * igstTax) / 100, fixDigit);
                totalIgstTax += parseFloat(finalValue);
            }
        } else if (purchaseIntraStateTaxArray.includes(classification.classification_nature_name)) {
            let cgstTax = parseFloat(gstRate) / 2;
            let finalValue = customToFixed((subTotal * cgstTax) / 100, fixDigit);
            if (finalValue == "NaN") {
                finalValue = "";
            }
            totalCgstTax += parseFloat(finalValue);
            totalSgstTax += parseFloat(finalValue);
        } else if (purchaseInterstateTaxArray.includes(classification.classification_nature_name)) {
            let igstTax = parseFloat(gstRate);
            let finalValue = customToFixed((subTotal * igstTax) / 100, fixDigit);
            if (finalValue == "NaN") {
                finalValue = "";
            }
            totalCgstTax += parseFloat(finalValue);
            totalSgstTax += parseFloat(finalValue);
            totalIgstTax += parseFloat(finalValue);
        }
    });

    return {
        totalCgstTax: parseFloat(customToFixed(totalCgstTax, fixDigit)),
        totalSgstTax: parseFloat(customToFixed(totalSgstTax, fixDigit)),
        totalIgstTax: parseFloat(customToFixed(totalIgstTax, fixDigit)),
    };
};

export const calculateAdditionalClassification = (classification, total) => {
    let totalAdditionalGST = 0.0;
    const intraStateArray = [
        "Intrastate Sales Taxable",
        "Intrastate Sales Exempt",
        "Intrastate Sales Nil Rated",
    ];
    const interStateArray = [
        "Interstate Sales Taxable",
        "Interstate Sales Exempt",
        "Interstate Sales Nil Rated",
    ];
    const exportArray = [
        "Export Sales Taxable",
        "Export Sales Exempt",
        "Export Sales Nil Rated",
        "Export Sales under LUT/Bond",
    ];
    const sezArray = [
        "Sales to SEZ Taxable",
        "Sales to SEZ Exempt",
        "Sales to SEZ Nil Rated",
        "Sales to SEZ under LUT/Bond",
    ];
    const purchaseIntraStateTaxArray = [
        "Intrastate Purchase Taxable",
        "Intrastate Purchase URD Taxable",
    ];
    const purchaseInterstateTaxArray = [
        "Interstate Purchase Taxable",
        "Interstate Purchase URD Taxable",
        "Purchase - Import of goods",
        "Purchase - Import of Service",
    ];
    if (classification.classification_nature_name === "Deemed Export - Intrastate") {
        totalAdditionalGST = total;
    } else if (classification.classification_nature_name === "Deemed Export - Interstate") {
        totalAdditionalGST = total;
    } else if (intraStateArray.includes(classification.classification_nature_name)) {
        if (
            classification.classification_nature_name === "Intrastate Sales Exempt" ||
            classification.classification_nature_name === "Intrastate Sales Nil Rated"
        ) {
            totalAdditionalGST = 0.0;
        } else {
            totalAdditionalGST = total;
        }
    } else if (interStateArray.includes(classification.classification_nature_name)) {
        if (
            classification.classification_nature_name === "Interstate Sales Nil Rated" ||
            classification.classification_nature_name === "Interstate Sales Exempt"
        ) {
            totalAdditionalGST = 0.0;
        } else if (classification.classification_nature_name === "Interstate Sales Taxable") {
            totalAdditionalGST = total;
        } else {
            totalAdditionalGST = total;
        }
    } else if (exportArray.includes(classification.classification_nature_name)) {
        if (
            classification.classification_nature_name === "Export Sales Nil Rated" ||
            classification.classification_nature_name === "Export Sales Exempt" ||
            classification.classification_nature_name === "Export Sales under LUT/Bond"
        ) {
            totalAdditionalGST = 0.0;
        } else if (classification.classification_nature_name === "Export Sales Taxable") {
            totalAdditionalGST = total;
        } else {
            totalAdditionalGST = total;
        }
    } else if (sezArray.includes(classification.classification_nature_name)) {
        totalAdditionalGST = total;
    } else if (purchaseIntraStateTaxArray.includes(classification.classification_nature_name)) {
        totalAdditionalGST = total;
    } else if (purchaseInterstateTaxArray.includes(classification.classification_nature_name)) {
        totalAdditionalGST = total;
    } else if(!classification.classification_nature_name){
        totalAdditionalGST = total;
    }

    return parseFloat(customToFixed(totalAdditionalGST, 2));
};

export const gstCalculate = (rate, subTotal, returnType) => {
    let fixDigit = 2;
    let totalCgstTax = 0.0;
    let totalSgstTax = 0.0;
    let totalIgstTax = 0.0;
    rate?.rate?.forEach(rate => {
        const gstRate = rate?.rate;
        const subTotal = rate?.total;

        let cgstValue = "";
        let cgstTax = parseFloat(gstRate) / 2;
        cgstValue += customToFixed((subTotal * cgstTax) / 100, fixDigit);
        totalCgstTax += parseFloat(cgstValue);
        totalSgstTax += parseFloat(cgstValue);

        let finalValue = "";
        let igstTax = parseFloat(gstRate);
        finalValue = customToFixed((subTotal * igstTax) / 100, fixDigit);
        totalIgstTax += parseFloat(finalValue);
    });
    return {
        totalCgstTax: parseFloat(customToFixed(totalCgstTax, fixDigit)),
        totalSgstTax: parseFloat(customToFixed(totalSgstTax, fixDigit)),
        totalIgstTax: parseFloat(customToFixed(totalIgstTax, fixDigit)),
    };
};

export const RoundOffMethod = (totalAmount, roundOffMethodType) => {
    const fixDigit = 2;
    let grandFinalAmount = customToFixed(totalAmount, fixDigit);
    let roundOffAmount = 0;

    switch (roundOffMethodType) {
        case 1: // Normal rounding to 2 decimal places
            grandFinalAmount = parseFloat(totalAmount.toFixed(fixDigit));
            roundOffAmount = parseFloat((grandFinalAmount - totalAmount).toFixed(fixDigit));
            break;
        case 2: // Floor the value
            grandFinalAmount = parseFloat(Math.floor(totalAmount).toFixed(fixDigit));
            roundOffAmount = parseFloat((grandFinalAmount - totalAmount).toFixed(fixDigit));
            break;
        case 3: // Round to the nearest integer
            grandFinalAmount = customToFixed(
                parseFloat(Math.round(customToFixed(totalAmount, fixDigit))),
                fixDigit
            );
            roundOffAmount = parseFloat((grandFinalAmount - totalAmount).toFixed(fixDigit));
            break;
        case 4: // Ceil the value
            grandFinalAmount = parseFloat(Math.ceil(totalAmount).toFixed(fixDigit));
            roundOffAmount = parseFloat((grandFinalAmount - totalAmount).toFixed(fixDigit));
            break;
        default: // Default to rounding to 2 decimal places
            grandFinalAmount = parseFloat(totalAmount.toFixed(fixDigit));
    }

    return {
        grandFinalAmount,
        roundOffAmount,
    };
};
export const RoundOffMethodForTds = (totalAmount, roundOffMethodType) => {
    const fixDigit = 2;
    let grandFinalAmount = customToFixed(totalAmount, fixDigit);
    let roundOffAmount = 0;

    switch (roundOffMethodType) {
        case 1: // Normal rounding to 2 decimal places
            grandFinalAmount = parseFloat(totalAmount.toFixed(fixDigit));
            break;
        case 2: // Floor the value
            grandFinalAmount = parseFloat(Math.floor(totalAmount).toFixed(fixDigit));
            break;
        case 3: // Round to the nearest integer
            grandFinalAmount = customToFixed(
                parseFloat(Math.round(customToFixed(totalAmount, fixDigit))),
                fixDigit
            );
            break;
        case 4: // Ceil the value
            grandFinalAmount = parseFloat(Math.ceil(totalAmount).toFixed(fixDigit));
            break;
        default:
            grandFinalAmount = parseFloat(totalAmount.toFixed(fixDigit));
    }

    roundOffAmount = parseFloat((grandFinalAmount - totalAmount).toFixed(fixDigit));
    return grandFinalAmount;
};


export function CheckGstValidate(g) {
    let gstinFormat = new RegExp("^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z][a-zA-Z0-9]{3}$");
    return gstinFormat.test(g);
}

export function CheckPanValidate(g) {
    let panCardFormat = new RegExp("^[A-Z]{5}[0-9]{4}[A-Z]{1}$");
    return panCardFormat.test(g);
}

export function formattedDate(date) {
    return moment(date).format("DD-MM-YYYY");
}

export const setGSTCalculationType = (
    classfication,
    setIsIGSTCalculation,
    setIsSGSTCalculation,
    isGSTApplicable=true,
    itemsList
) => {
    if(!isGSTApplicable || itemsList?.length <= 0){
        return {calculateIGST: false, calculateSGST: false}
    }
    if (
        classfication.classification_nature_name === "Interstate Sales Taxable" ||
        classfication.classification_nature_name === "Interstate Purchase Taxable" ||
        classfication.classification_nature_name === "Interstate Purchase URD Taxable" ||
        classfication.classification_nature_name === "Sales to SEZ Exempt" ||
        classfication.classification_nature_name === "Sales to SEZ Nil Rated" ||
        classfication.classification_nature_name === "Sales to SEZ under LUT/Bond" ||
        classfication.classification_nature_name === "Sales to SEZ Taxable" ||
        classfication.classification_nature_name === "Export Sales Taxable" ||
        classfication.classification_nature_name === "Export Sales Exempt" ||
        classfication.classification_nature_name === "Export Sales Nil Rated" ||
        classfication.classification_nature_name === "Export Sales under LUT/Bond" ||
        classfication.classification_nature_name === "Purchase - Import of goods" ||
        classfication.classification_nature_name === "Purchase - Import of Service"
    ) {
        setIsIGSTCalculation(true);
        setIsSGSTCalculation(false);
        return {calculateIGST: true, calculateSGST: false}
    } else if (
        classfication.classification_nature_name === "Intrastate Sales Taxable" ||
        classfication.classification_nature_name === "Intrastate Purchase Taxable" ||
        classfication.classification_nature_name === "Intrastate Purchase URD Taxable"
    ) {
        setIsIGSTCalculation(false);
        setIsSGSTCalculation(true);
        return {calculateIGST: false, calculateSGST: true}
    } else if (
        classfication.classification_nature_name === "Intrastate Sales Exempt" ||
        classfication.classification_nature_name === "Interstate Sales Exempt" ||
        classfication.classification_nature_name === "Intrastate Sales Nil Rated" ||
        classfication.classification_nature_name === "Interstate Purchase Exempt" ||
        classfication.classification_nature_name === "Intrastate Purchase Exempt" ||
        classfication.classification_nature_name === "Intrastate Purchase Nil Rated"
    ) {
        setIsSGSTCalculation(false);
        setIsIGSTCalculation(false);
        return {calculateIGST: false, calculateSGST: false}
    } else if (
        classfication.classification_nature_name === "Export Sales Taxable" ||
        classfication.classification_nature_name === "Export Taxable" ||
        classfication.classification_nature_name === "Export Exempt" ||
        classfication.classification_nature_name === "Export Nilrated" ||
        classfication.classification_nature_name === "Export LUT/Bond"
    ) {
        setIsSGSTCalculation(false);
        setIsIGSTCalculation(true);
        return {calculateIGST: true, calculateSGST: false}
    } else {
        return {calculateIGST: true, calculateSGST: false}
    }
};

export const calculateAdditionalCharges = (charges, grandTotal, isIGSTCalculation) => {
    const formattedGrandTotal = customToFixed(grandTotal, 2);
    const updatedCharges = charges?.additional_detail?.map(item => {
        let currentValue = parseFloat(item?.ac_value == "-" ? 0 : item?.ac_value) || 0;
        currentValue = parseFloat(customToFixed(currentValue, 3));
        let gstRate = parseFloat(item?.ac_gst_rate_id?.rate / 2) || 0;
        if (isIGSTCalculation) {
            gstRate = parseFloat(item?.ac_gst_rate_id?.rate) || 0;
        }
        const acType = parseFloat(item?.ac_type) || 1;

        if (acType === 1) {
            let gstAmount = (currentValue * gstRate / 100);
            gstAmount = customToFixed(gstAmount, 2);
            item.ac_total = Number(customToFixed(currentValue + parseFloat(gstAmount), 2));
        } else if (acType === 2) {
            const gstAmount = (formattedGrandTotal * currentValue / 100);
            item.ac_total = Number(customToFixed(gstAmount, 2));
        }
        return item;
    });

    const acTotal = updatedCharges?.reduce((total, item) => {
        let currentValue = parseFloat(item?.ac_value == "-" ? 0 : item?.ac_value) || 0;
        currentValue = parseFloat(customToFixed(currentValue, 3));
        let gstRate = parseFloat(item?.ac_gst_rate_id?.rate / 2) || 0;
        if (isIGSTCalculation) {
            gstRate = parseFloat(item?.ac_gst_rate_id?.rate) || 0;
        }
        if (item.ac_type === 1) {
            total = parseFloat(total) + parseFloat(item?.ac_total - (currentValue == "-" ? 0 : currentValue));
        } else if (item?.ac_type == 2 && item?.ac_gst_rate_id) {
            const customTotal = customToFixed(item?.ac_total * gstRate / 100, 2)
            total = parseFloat(total) + parseFloat(customTotal);
        }
        return total;
    }, 0);

    const taxableValue = updatedCharges?.reduce((acc, item) => {
        if (item.ac_type === 1) {
            return acc + parseFloat(item?.ac_value == "-" ? 0 : item?.ac_value || 0);
        }
        return acc + parseFloat(item?.ac_total || 0);
    }, formattedGrandTotal);

    const acTotalOnly = updatedCharges?.reduce((acc, item) => {
        return acc + parseFloat(item?.ac_total || 0);
    }, 0);

    const addition_charges = updatedCharges?.reduce(
        (acc, item) =>
            (acc +=
                item.ac_type == 2
                    ? parseFloat(item?.ac_total || 0)
                    : parseFloat(item?.ac_value == "-" ? 0 : item?.ac_value || 0)),
        0
    );

    return {
        updatedCharges,
        acTotal: customToFixed(acTotal, 2),
        taxableValue: parseFloat(taxableValue),
        addition_charges: addition_charges,
        acTotalOnly,
    };
};

export const calculateAddLessCharges = (charges, grandTotal) => {
    let tempGrandTotal = parseFloat(customToFixed(grandTotal, 2));
    const updatedCharges = charges?.map(item => {
        const currentValue = parseFloat(item?.al_value || 0);
        const alType = parseFloat(item?.al_type || 0);
        if (alType === 1) {
            item.al_value = currentValue;
            item.al_total = currentValue;
        } else if (alType === 2) {
            const calculatedTotal = (tempGrandTotal * currentValue / 100);
            item.al_total = customToFixed(calculatedTotal, 2);
            item.al_value = currentValue;
        }
        return item;
    });

    return updatedCharges;
};

export const calculateTotals = (items, classificationType) => {
    let total = 0;
    let totalSgst = 0;
    let totalCgst = 0;
    let totalCess = 0;
    let totalIgst = 0;

    items?.forEach(item => {
        total += Number(item?.total || 0);
        totalSgst += Number(item?.sgstValue || 0.0);
        totalCgst += Number(item?.cgstValue || 0.0);
        totalCess += Number(customToFixed(item.cessValue, 2) || 0.0);
        totalIgst += Number(item.igstValue || 0.0);
    });

    // const gstTotal =
    //     classificationType?.classificationSelectedType !== 0 ? totalSgst + totalCgst : 0;
    return {
        itemTotal: total,
        gstTotal: totalSgst,
        totalIgst,
        cessTotal: totalCess.toFixed(2),
    };
};

export const IsFormulaValid = (backendFormula) => {
    try {
        if (typeof backendFormula !== "string") return false;

        // Match all { ... } tokens
        const allPlaceholders = backendFormula.match(/\{\s*[^{}]+\s*\}/g) || [];

        // Updated regex to include % after placeholder
        const invalidCurlyPattern = /(?<![+\-*/%(\s])\{\s*[^{}]+\s*\}(?![+\-*/%)]|\s)/;
        if (invalidCurlyPattern.test(backendFormula)) return false;

        // Replace all placeholders with dummy number
        let sanitized = backendFormula;
        for (const token of allPlaceholders) {
            sanitized = sanitized.replace(token, "1");
            sanitized = sanitized.replace(/%/g, "/100"); // interpret % as percent

        }

        // Only allow valid math characters
        if (/[^0-9+\-*/%().\s]/.test(sanitized)) return false;

        // Check for balanced parentheses
        const stack = [];
        for (const char of sanitized) {
            if (char === "(") stack.push(char);
            else if (char === ")") {
                if (!stack.length) return false;
                stack.pop();
            }
        }
        if (stack.length !== 0) return false;

        // Validate the math
        new Function(`return (${sanitized})`)();

        return true;
    } catch {
        return false;
    }
};

export function replaceIdsWithLabels(formula, fields) {
  return formula?.replace(/\{\s*(\d+)\s*\}/g, (match, id) => {
    const field = fields.find(f => f?.id === parseInt(id));
    return field ? `{${field?.label_name}}` : match;
  });
}

const convertPercent = (formula) => {
  // 0. Convert `{a} / {b}%` to `({a} / {b}) / 100`
  formula = formula.replace(/(\{\s*[^{}]+\s*\})\s*\/\s*(\{\s*[^{}]+\s*\})\s*%/g, (_, a, b) => {
    return `(${a} / ${b}) / 100`;
  });

  // 1. Convert `{a} + {b}%` to `{a} + ({a} * {b} / 100)`
  formula = formula.replace(/(\{\s*[^{}]+\s*\})\s*\+\s*(\{\s*[^{}]+\s*\})\s*%/g, (_, a, b) => {
    return `${a} + (${a} * ${b} / 100)`;
  });

  // 2. Convert `{a} * {b}%` to `{a} * ({b} / 100)`
  formula = formula.replace(/(\{\s*[^{}]+\s*\})\s*\*\s*(\{\s*[^{}]+\s*\})\s*%/g, (_, a, b) => {
    return `${a} * (${b} / 100)`;
  });

  // 3. Convert `{a} - {b}%` to `{a} - ({a} * {b} / 100)`
  formula = formula.replace(/(\{\s*[^{}]+\s*\})\s*-\s*(\{\s*[^{}]+\s*\})\s*%/g, (_, a, b) => {
    return `${a} - (${a} * ${b} / 100)`;
  });

  // 4. Convert `{x}%` to `({x} / 100)`
  formula = formula.replace(/(\{\s*[^{}]+\s*\})\s*%/g, '($1 / 100)');

  return formula;
};

export const updateCustomFieldCalculation = custom_fields => {
    if (!Array.isArray(custom_fields)) return [];

    const fieldValueMap = custom_fields.reduce((map, field) => {
        map[field.custom_field_id] = Number(field.value) || 0;
        return map;
    }, {});

    return custom_fields.map(field => {
        let formula = field?.default_formula?.formula;
        if (formula) {
            formula = convertPercent(formula); // convert {id}% → {id} / 100
            const matches = [...formula.matchAll(/\{\s*(\d+)\s*\}/g)];

            let parsedFormula = formula;

            matches.forEach(match => {
                const id = Number(match[1]);
                const value = fieldValueMap[id] ?? 0;
                parsedFormula = parsedFormula.replace(match[0], value);
            });

            let calculatedValue = null;
            try {
                calculatedValue = Function('"use strict"; return (' + parsedFormula + ")")();
            } catch (e) {
                console.error(`Error evaluating formula "${parsedFormula}"`, e);
            }
            return {
                ...field,
                value: calculatedValue,
            };
        }

        return field;
    });
};

export const getCalculatedQuantity = (customFields = []) => {
    const quantityField = customFields.find(cf => cf.name === "Quantity" || cf?.label_name === "Quantity" || cf?.system_field_name === "quantity");
    if (!quantityField) return null;

    const hasFormula = !!quantityField.default_formula?.formula;
    const manualValue = parseFloat(quantityField.value) || 0;

    if (!hasFormula && manualValue != null) {
        return manualValue;
    }

    if (hasFormula) {
        let formula = quantityField.default_formula.formula;
        formula = convertPercent(formula); // convert {id}% → {id} / 100

        const usedIds = quantityField.default_formula.used_cf_ids_for_formula || [];

        usedIds.forEach(cfId => {
            const refField = customFields.find(f => f.custom_field_id === cfId);
            const value = parseFloat(refField?.value) ?? 0;
            formula = formula.replace(new RegExp(`{\\s*${cfId}\\s*}`, "g"), (parseFloat(value) || 0));
        });
        try {
            // eslint-disable-next-line no-eval
            return eval(formula);
        } catch (e) {
            console.error("Failed to evaluate quantity formula:", e);
            return null;
        }
    }

    return null;
};
