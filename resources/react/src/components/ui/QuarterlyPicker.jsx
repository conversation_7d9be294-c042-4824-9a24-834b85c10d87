import React, { useEffect, useRef, useState } from "react";

const monthMap = [
  { name: "<PERSON>", quarter: 4 },
  { name: "Feb", quarter: 4 },
  { name: "<PERSON>", quarter: 4 },
  { name: "Apr", quarter: 1 },
  { name: "May", quarter: 1 },
  { name: "<PERSON>", quarter: 1 },
  { name: "<PERSON>", quarter: 2 },
  { name: "Aug", quarter: 2 },
  { name: "Sep", quarter: 2 },
  { name: "Oct", quarter: 3 },
  { name: "Nov", quarter: 3 },
  { name: "Dec", quarter: 3 },
];

const groupedQuarters = [
  [0, 1, 2],   // Q4
  [3, 4, 5],   // Q1
  [6, 7, 8],   // Q2
  [9, 10, 11], // Q3
];

const getCurrentQuarter = (month) => {
  if (month >= 3 && month <= 5) return 1;
  if (month >= 6 && month <= 8) return 2;
  if (month >= 9 && month <= 11) return 3;
  return 4;
};

const QuarterPicker = ({ value, onChange, minDate, maxDate }) => {
  const containerRef = useRef(null);
  const today = new Date();
  const currentMonth = today.getMonth();
  const currentYear = today.getFullYear();
  const currentQuarter = getCurrentQuarter(currentMonth);

  const [isOpen, setIsOpen] = useState(false);
  const [year, setYear] = useState(currentYear);
  const [hoverQuarter, setHoverQuarter] = useState(null);
  const [selectedQuarter, setSelectedQuarter] = useState({
    quarter: currentQuarter,
    year: currentQuarter === 4 && currentMonth < 3 ? currentYear + 1 : currentYear,
  });

  const handleClickOutside = (e) => {
    if (containerRef.current && !containerRef.current.contains(e.target)) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleQuarterClick = (quarter) => {
    setSelectedQuarter({ quarter, year });
    onChange({ quarter, year });
    setIsOpen(false);
  };

  return (
    <div className="quarter-picker-container" ref={containerRef}>
      <input
        readOnly
        className="quarter-input"
        onClick={() => setIsOpen(!isOpen)}
        value={`Q${value.quarter} ${value.year}`}
        placeholder="Select Quarter"
      />

      {isOpen && (
        <div className="quarter-popup">
          <div className="quarter-header">
            <button onClick={() => setYear((y) => y - 1)} className="quarter-header-button">&laquo;</button>
            <span>{year}</span>
            <button onClick={() => setYear((y) => y + 1)} className="quarter-header-button">&raquo;</button>
          </div>

          {/* <div className="quarter-grid">
            {groupedQuarters.map((group, qIndex) => {
              const quarterNumber = ((qIndex + 3) % 4) + 1; // Q1 = Apr-Jun
              const isHovered = hoverQuarter === quarterNumber;
              const isSelected =
                value.quarter === quarterNumber &&
                value.year === year;

              return (
                <div
                  key={quarterNumber}
                  className={`quarter-row ${isSelected ? "selected" : isHovered ? "hovered" : ""
                    }`}
                  onMouseEnter={() => setHoverQuarter(quarterNumber)}
                  onMouseLeave={() => setHoverQuarter(null)}
                  onClick={() => handleQuarterClick(quarterNumber)}
                >
                  {group.map((monthIndex) => (
                    <div key={monthIndex} className="month-cell">
                      {monthMap[monthIndex].name}
                    </div>
                  ))}
                </div>
              );
            })}
          </div> */}

          <div className="quarter-grid">
            {[1, 2, 3, 4].map((quarterNumber) => {
              const isHovered = hoverQuarter === quarterNumber;
              const isSelected =
                value.quarter === quarterNumber && value.year === year;

              return (
                <div
                  key={quarterNumber}
                  className={`quarter-option ${isSelected ? "selected" : isHovered ? "hovered" : ""}`}
                  onMouseEnter={() => setHoverQuarter(quarterNumber)}
                  onMouseLeave={() => setHoverQuarter(null)}
                  onClick={() => handleQuarterClick(quarterNumber)}
                >
                  {`Q${quarterNumber}`}
                  {/* {`Q${quarterNumber} ${year}`} */}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default QuarterPicker;
