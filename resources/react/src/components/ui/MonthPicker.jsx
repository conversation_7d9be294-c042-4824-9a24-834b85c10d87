import React, { useEffect, useState } from "react";
import Flatpickr from "react-flatpickr";
// import "flatpickr/dist/themes/material_blue.css";
import monthSelectPlugin from "flatpickr/dist/plugins/monthSelect";
import "flatpickr/dist/plugins/monthSelect/style.css";
import moment from "moment";

const MonthPicker = ({ value, onChange, minDate, maxDate }) => {
  const [selectedDate, setSelectedDate] = useState(value || new Date());

  useEffect(() => {
    if (!value) {
      const now = new Date();
      setSelectedDate(new Date(now.getFullYear(), now.getMonth(), 1));
      triggerOnChange(new Date(now.getFullYear(), now.getMonth(), 1));
    }
  }, []);

  const triggerOnChange = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth(); // 0-based index

    const startDate = new Date(year, month, 1);
    const endDate = new Date(year, month + 1, 0); // last day of the month

    onChange?.({ startDate, endDate });
  };
console.log(minDate, maxDate);
  return (
    <div className="w-60">
      <Flatpickr
        value={selectedDate}
        options={{
          plugins: [
            new monthSelectPlugin({
              shorthand: false,
              dateFormat: "F Y",
              altFormat: "F Y",
              theme: "light",
            }),
          ],
          minDate: moment(minDate, "YYYY-MM").toDate(),
          maxDate: moment(maxDate, "YYYY-MM").toDate(),
        }}
        onChange={([date]) => {
          setSelectedDate(date);
          triggerOnChange(date);
        }}
        className="w-full border border-gray-300 px-3 py-2 rounded focus:outline-none custome-monthpicker"
        placeholder="Select Month"
      />
    </div>
  );
};

export default MonthPicker;
