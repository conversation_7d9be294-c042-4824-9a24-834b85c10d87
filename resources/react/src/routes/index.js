import { lazy } from "react";
import { ROUTES } from "../constants";

const SalesTransaction = lazy(() => import("../app/sale/index"));
const PurchaseToSale = lazy(() => import("../app/sale/PurchaseToSale"));
const LedgerTransaction = lazy(() => import("../app/ledger-master/index"));
const EditLedgerTransaction = lazy(() => import("../app/ledger-master/EditLedgerMaster"));
const AddItemTransaction = lazy(() => import("../app/items-master/index"));
const EditItemTransaction = lazy(() => import("../app/items-master/EditItemMaster"));
const EditSaleTransaction = lazy(() => import("../app/sale/EditSale"));
const DuplicateSaleTransaction = lazy(() => import("../app/sale/DuplicateSale"));
const DeliveryChallanToSale = lazy(() => import("../app/sale/DeliveryChallanToSale"));
const EstimateQuoteToSale = lazy(() => import("../app/sale/EstimateQuoteToSale"));
const DuplicateSaleReturnTransaction = lazy(() => import("../app/sale/DuplicateSaleReturn"));
const AddIncomeTransaction = lazy(() => import("../app/income-cn-dn/index"));
const EditIncomeTransaction = lazy(() => import("../app/income-cn-dn/EditIncome"));
const DuplicateIncomeDebitNote = lazy(() => import("../app/income-cn-dn/DuplicateIncomeDebitNote"));
const DuplicateIncomeCreditNote = lazy(() =>
    import("../app/income-cn-dn/DuplicateIncomeCreditNote")
);
const EstimateQuoteTransaction = lazy(() => import("../app/estimate-quote"));
const DuplicateEstimateQuoteTransaction = lazy(() =>
    import("../app/estimate-quote/DuplicateEstimateQuote")
);
const EditEstimateQuoteTransaction = lazy(() => import("../app/estimate-quote/EditEstimateQuote"));
const PurchaseOrderTransaction = lazy(() => import("../app/purchase-order"));
const EditPurchaseOrderTransaction = lazy(() => import("../app/purchase-order/EditPurchaseOrder"));
const DuplicatePurchaseOrderTransaction = lazy(() =>
    import("../app/purchase-order/DuplicatePurchaseOrder")
);
const PurchaseReturnTransaction = lazy(() => import("../app/purchase-return"));
const EditPurchaseReturnTransaction = lazy(() =>
    import("../app/purchase-return/EditPurchaseReturn")
);
const DuplicatePurchaseReturnTransaction = lazy(() =>
    import("../app/purchase-return/DuplicatePurchaseReturn")
);
const DeliveryChallanTransaction = lazy(() => import("../app/delivery-challan"));
const EditDeliveryChallanTransaction = lazy(() =>
    import("../app/delivery-challan/EditDeliveryChallan")
);
const DuplicateDeliveryChallanTransaction = lazy(() =>
    import("../app/delivery-challan/DuplicateDeliveryChallan")
);
const SaleToDeliveryChallan = lazy(() => import("../app/delivery-challan/SaleToDeliveryChallan"));
const CustomerMaster = lazy(() => import("../app/customer-master/index"));
const SupplierMaster = lazy(() => import("../app/supplier-master"));
const PrintBarcode = lazy(() => import("../app/print-barcode"));
const PurchaseTransaction = lazy(() => import("../app/purchase"));
const PurchaseOrderToPurchase = lazy(() => import("../app/purchase/PurchaseOrderToPurchase"));
const EditPurchaseTransaction = lazy(() => import("../app/purchase/EditPurchase"));
const DuplicatePurchaseTransaction = lazy(() => import("../app/purchase/DuplicatePurchase"));
const AddExpenseCreditDebitTransaction = lazy(() => import("../app/expense-cn-dn/index"));
const EditExpenseCreditDebitTransaction = lazy(() => import("../app/expense-cn-dn/EditExpense"));
const DuplicateExpenseCreditTransaction = lazy(() =>
    import("../app/expense-cn-dn/DuplicateExpenseCredit")
);
const DuplicateExpenseDebitTransaction = lazy(() =>
    import("../app/expense-cn-dn/DuplicateExpenseDebit")
);
const OCRScreen = lazy(() => import("../app/purchase-ocr/index"));
const PurchaseToPurchaseReturn = lazy(() =>
    import("../app/purchase-return/PurchaseToPurchaseReturn")
);
const SaleToSaleReturn = lazy(() => import("../app/sale/SaleToSaleReturn"));
const Settings = lazy(() => import("../app/setting/index"));
const Dashboard = lazy(() => import("../app/dashboard/index"));
const WhatsNew = lazy(() => import("../app/whats-new/index"));
const RecurringMaster = lazy(() => import("../app/recurring-master"));
const RecurringMasterCreate = lazy(() => import("../app/recurring-master/recurring-master-create"));
const RecurringMasterEdit = lazy(() => import("../app/recurring-master/recurring-master-edit"));
const ThirdPartyVastra = lazy(() => import("../app/third-party/third-party-vastra"));
const ThirdParty = lazy(() => import("../app/third-party"));
const LeastSellingItems = lazy(() => import("../app/least-selling-items"));
const TopSellingItems = lazy(() => import("../app/top-selling-items"));
const LowStockReport = lazy(() => import("../app/low-stock-report"));
const OCRBankReview = lazy(() => import("../app/purchase-ocr-bank"));
const ThirdParty11ZA = lazy(() => import("../app/third-party/third-party-11za"));
const GeneralSettings = lazy(() => import("../app/setting/general-settings"));
const EWayBillEInvoiceSettings = lazy(() => import("../app/setting/eway-bill-settings"));
const PrintSettings = lazy(() => import("../app/setting/print-settings"));
const EmailAndWhatsappConfiguration = lazy(() => import("../app/setting/email-and-whatsapp-configuration"));
const ChequePrintingSettings = lazy(() => import("../app/setting/cheque-printing"));
const Gstr2b = lazy(() => import("../app/gst-2b"));

export const routes = [
    {
        path: `${ROUTES.DASHBOARD}`,
        Element: Dashboard,
    },
    {
        path: `${ROUTES.WHATS_NEW}`,
        Element: WhatsNew,
    },

    // OCR Routes
    {
        path: `${ROUTES.PURCHASE_OCR}/create/:id`,
        Element: OCRScreen,
    },
    {
        path: `${ROUTES.PURCHASE_BANK_OCR}/:id`,
        Element: OCRBankReview,
    },

    // Sales Routes
    {
        path: `${ROUTES.SALES}/create`,
        Element: SalesTransaction,
    },
    {
        path: `${ROUTES.SALES}/:id/edit`,
        Element: EditSaleTransaction,
    },
    {
        path: `${ROUTES.SALES_CREATE_FROM_CHALLAN}/:id/duplicate`,
        Element: DuplicateSaleTransaction,
    },
    {
        path: `${ROUTES.PURCHASES_TO_SALE}/:id/create`,
        Element: PurchaseToSale,
    },

    // Sale Returns Routes
    {
        path: `${ROUTES.SALE_RETURNS}/create`,
        Element: SalesTransaction,
    },
    {
        path: `${ROUTES.SALE_RETURNS}/:id/edit`,
        Element: EditSaleTransaction,
    },
    {
        path: `${ROUTES.SALE_RETURNS_CREATE}/:id/duplicate`,
        Element: DuplicateSaleReturnTransaction,
    },
    {
        path: `${ROUTES.CREATE_SALE_RETURN}/:id`,
        Element: SaleToSaleReturn,
    },

    // Ledgers Routes
    {
        path: `${ROUTES.LEDGERS}/create`,
        Element: LedgerTransaction,
    },
    {
        path: `${ROUTES.LEDGERS}/:id/edit`,
        Element: EditLedgerTransaction,
    },

    // Item Masters Routes
    {
        path: `${ROUTES.ITEM_MASTERS}/create`,
        Element: AddItemTransaction,
    },
    {
        path: `${ROUTES.ITEM_MASTERS}/:id/edit`,
        Element: EditItemTransaction,
    },

    // Income Credit Notes Routes
    {
        path: `${ROUTES.INCOME_CREDIT_NOTES}/create`,
        Element: AddIncomeTransaction,
    },
    {
        path: `${ROUTES.INCOME_CREDIT_NOTES}/:id/edit`,
        Element: EditIncomeTransaction,
    },
    {
        path: `${ROUTES.INCOME_CREDIT_NOTES_CREATE}/:id/duplicate`,
        Element: DuplicateIncomeCreditNote,
    },

    // Income Debit Notes Routes
    {
        path: `${ROUTES.INCOME_DEBIT_NOTES}/create`,
        Element: AddIncomeTransaction,
    },
    {
        path: `${ROUTES.INCOME_DEBIT_NOTES}/:id/edit`,
        Element: EditIncomeTransaction,
    },
    {
        path: `${ROUTES.INCOME_DEBIT_NOTES_CREATE}/:id/duplicate`,
        Element: DuplicateIncomeDebitNote,
    },

    // Expense Debit Notes Routes
    {
        path: `${ROUTES.EXPENSE_DEBIT_NOTES}/create`,
        Element: AddExpenseCreditDebitTransaction,
    },
    {
        path: `${ROUTES.EXPENSE_DEBIT_NOTES}/:id/edit`,
        Element: EditExpenseCreditDebitTransaction,
    },
    {
        path: `${ROUTES.EXPENSE_DEBIT_NOTES_CREATE}/:id/duplicate`,
        Element: DuplicateExpenseDebitTransaction,
    },

    // Income Estimate Quote Routes
    {
        path: `${ROUTES.INCOME_ESTIMATE_QUOTE}/create`,
        Element: EstimateQuoteTransaction,
    },
    {
        path: `${ROUTES.INCOME_ESTIMATE_QUOTE}/:id/edit`,
        Element: EditEstimateQuoteTransaction,
    },
    {
        path: `${ROUTES.INCOME_ESTIMATE_QUOTE_CREATE}/:id/duplicate`,
        Element: DuplicateEstimateQuoteTransaction,
    },

    // Purchase Order Routes
    {
        path: `${ROUTES.PURCHASE_ORDER}/create`,
        Element: PurchaseOrderTransaction,
    },
    {
        path: `${ROUTES.PURCHASE_ORDER}/:id/edit`,
        Element: EditPurchaseOrderTransaction,
    },
    {
        path: `${ROUTES.PURCHASE_ORDER_CREATE}/:id/duplicate`,
        Element: DuplicatePurchaseOrderTransaction,
    },

    // Purchase Returns Routes
    {
        path: `${ROUTES.PURCHASE_RETURNS}/create`,
        Element: PurchaseReturnTransaction,
    },
    {
        path: `${ROUTES.PURCHASE_RETURNS}/:id/edit`,
        Element: EditPurchaseReturnTransaction,
    },
    {
        path: `${ROUTES.PURCHASE_RETURNS_CREATE}/:id/duplicate`,
        Element: DuplicatePurchaseReturnTransaction,
    },
    {
        path: `${ROUTES.CREATE_PURCHASE_RETURN}/:id`,
        Element: PurchaseToPurchaseReturn,
    },

    // Delivery Challan Routes
    {
        path: `${ROUTES.DELIVERY_CHALLAN}/create`,
        Element: DeliveryChallanTransaction,
    },
    {
        path: `${ROUTES.DELIVERY_CHALLAN}/:id/edit`,
        Element: EditDeliveryChallanTransaction,
    },
    {
        path: `${ROUTES.DELIVERY_CHALLAN}/:id/duplicate`,
        Element: DuplicateDeliveryChallanTransaction,
    },

    // Purchases Routes
    {
        path: `${ROUTES.PURCHASES}/create`,
        Element: PurchaseTransaction,
    },
    {
        path: `${ROUTES.PURCHASES}/:id/edit`,
        Element: EditPurchaseTransaction,
    },
    {
        path: `${ROUTES.PURCHASES}-create/:id/duplicate`,
        Element: DuplicatePurchaseTransaction,
    },

    // Expense Credit Notes Routes
    {
        path: `${ROUTES.EXPENSE_CREDIT_NOTES}/create`,
        Element: AddExpenseCreditDebitTransaction,
    },
    {
        path: `${ROUTES.EXPENSE_CREDIT_NOTES}/:id/edit`,
        Element: EditExpenseCreditDebitTransaction,
    },
    {
        path: `${ROUTES.EXPENSE_CREDIT_NOTES_CREATE}/:id/duplicate`,
        Element: DuplicateExpenseCreditTransaction,
    },

    // Create Sale Delivery Challan Routes
    {
        path: `${ROUTES.CREATE_SALE_DELIVERY_CHALLAN}/:id`,
        Element: SaleToDeliveryChallan,
    },
    {
        path: `${ROUTES.SALES_CREATE_FROM_CHALLAN}/:challanId/challan`,
        Element: DeliveryChallanToSale,
    },
    {
        path: `${ROUTES.SALES_CREATE_FROM_CHALLAN}/:estimateId/estimate-quote`,
        Element: EstimateQuoteToSale,
    },

    // Purchase Create from Order
    {
        path: `${ROUTES.PURCHASE_CREATE_FROM_ORDER}/:id`,
        Element: PurchaseOrderToPurchase,
    },
    // Recurring Create from Order
    {
        path: `${ROUTES.RECURRING_MASTER}`,
        Element: RecurringMaster,
    },
    {
        path: `${ROUTES.RECURRING_MASTER}/create`,
        Element: RecurringMasterCreate,
    },
    {
        path: `${ROUTES.RECURRING_MASTER}/:id/edit`,
        Element: RecurringMasterEdit,
    },
    // setting
    {
        path: `${ROUTES.SETTING}`,
        Element: Settings,
    },
    {
        path: `${ROUTES.GENERAL_SETTING}`,
        Element: GeneralSettings,
    },
    {
        path: `${ROUTES.E_WHY_BILL_E_INVOICE_SETTING}`,
        Element: EWayBillEInvoiceSettings,
    },
    {
        path: `${ROUTES.PRINT_SETTING}`,
        Element: PrintSettings,
    },
    {
        path: `${ROUTES.EMAIL_AND_WHATSAPP_CONFIGURATION}`,
        Element: EmailAndWhatsappConfiguration,
    },
    {
        path: `${ROUTES.CHEQUE_PRINTING}`,
        Element: ChequePrintingSettings,
    },
    // Customer master report
    {
        path: `${ROUTES.CUSTOMER_MASTER}`,
        Element: CustomerMaster,
    },
    // Supplier master report
    {
        path: `${ROUTES.SUPPLIER_MASTER}`,
        Element: SupplierMaster,
    },
    // VASTRA
    {
        path: `${ROUTES.VASTRA}`,
        Element: ThirdParty,
    },
    {
        path: `${ROUTES.THIRD_PARTY_VASTRA}`,
        Element: ThirdPartyVastra,
    },
    {
        path: `${ROUTES.THIRD_PARTY_11ZA}`,
        Element: ThirdParty11ZA,
    },
    // print barcode
    {
        path: `${ROUTES.PRINT_BARCODE}`,
        Element: PrintBarcode,
    },
    {
        path: `${ROUTES.TOP_SELLING_ITEMS}`,
        Element: TopSellingItems,
    },
    {
        path: `${ROUTES.LEAST_SELLING_ITEMS}`,
        Element: LeastSellingItems,
    },
    {
        path: `${ROUTES.LOW_STOCK_REPORT}`,
        Element: LowStockReport,
    },
    // gstr 2b
    {
        path: `${ROUTES.GSTR_2B}`,
        Element: Gstr2b,
    },
];
