import { createContext, useCallback, useRef, useState } from "react";
import { formattedDate } from "../shared/calculation";
import { CUSTOM_FIELD_ITEM_TRANSACTION_TYPE, ITEM_STATUS, TRANSACTION_TYPE } from "../constants";

export const StateContext = createContext();

export const StateProvider = ({ children }) => {
    const itemRef = useRef(null);
    const unitRef = useRef(null);
    const quantityRef = useRef(null);
    const totalRef = useRef(null);
    const mrpRef = useRef(null);
    const amountRef = useRef(null);
    const priceWithGstRef = useRef(null);
    const discountTypeRef = useRef(null);
    const discountTypeRef2 = useRef(null);
    const discountValueRef = useRef(null);
    const discountValueRef2 = useRef(null);
    const gstRef = useRef(null);
    const ledgerRef = useRef(null);
    const [isFocusedInvoiceNumber, setIsFocusedInvoiceNumber] = useState(false);
    const [isDisable, setIsDisable] = useState(false);
    const [dispatchAddressId, setDispatchAddressId] = useState("");
    const [loader, setLoader] = useState(true);
    const [isLocationOfAsset, setIsLocationOfAsset] = useState(false);
    const [showInvoice, setShowInvoice] = useState(false);
    const [showPurchaseNumber, setShowPurchaseNumber] = useState(false);
    const [isBrokerModel, setIsBrokerModel] = useState(false);
    const [isCustomFieldModel, setIsCustomeFieldModel] = useState(false);
    const [isRearrangeCustomModel, setIsRearrangeCustomModel] = useState(false);
    const [isCustomFormula, setIsCustomFormula] = useState(false)
    const [isIncomeModel, setIsIncomeModel] = useState(false);
    const [saveFeature, setSaveFeature] = useState(false);
    const [isCreateParty, setIsCreateParty] = useState(false);
    const [isItemCatModel, setIsItemCatModel] = useState(false);
    const [isItemModel, setIsItemModel] = useState(false);
    const [isLedgerModel, setIsLedgerModel] = useState(false);
    const [isItemsLedgerModel, setIsItemsLedgerModel] = useState(false);
    const [isShipLedgerModel, setIsShipLedgerModel] = useState(false);
    const [isTransportModel, setIsTransportModel] = useState(false);
    const [isUomModel, setIsUomModel] = useState(false);
    const [taxableValue, setTaxableValue] = useState(0);
    const [invoiceValue, setInvoiceValue] = useState(0);
    const [isClassificationModel, setIsClassificationModel] = useState(false);
    const [isDispatchAddressModel, setIsDispatchAddressModel] = useState(false);
    const [isDispatchFromModel, setIsDispatchFromModel] = useState(false);
    const [isShippingFromModel, setIsShippingFromModel] = useState(false);
    const [isFooterSettingModel, setIsFooterSettingModel] = useState(false);
    const [isCustomizeFormatModal, setIsCustomizeFormatModal] = useState(false);
    const [isHeaderSettingModel, setIsHeaderSettingModel] = useState(false);
    const [isTableSettingModal, setIsTableSettingModal] = useState(false);
    const [isBillWiseModal, setIsBillWiseModal] = useState(false);
    const [isInvoiceSettingModal, setIsInvoiceSettingModal] = useState(false);
    const [isLogoEditModal, setIsLogoEditModal] = useState(false);
    const [viewRecurringInvoice, setViewRecurringInvoice] = useState(false)
    const [isMobileEditModal, setIsMobileEditModal] = useState(false);
    const [isEmailEditModal, setIsEmailEditModal] = useState(false);
    const [isSloganEditModal, setIsSloganEditModal] = useState(false);
    const [isItemTableModel, setIsItemTableModel] = useState(false);
    const [isRearrangeModel, setIsRearrangeModel] = useState(false);
    const [isOpenQuantityModel, setIsOpenQuantityModel] = useState(false);
    const [isDeleteCustomItem, setIsDeleteCustomItem] = useState(false);
    const [changeTax, setChangeTax] = useState(false);
    const [isNegativeStock, setIsNegativeStock] = useState(false);
    const [isSaleModel, setIsSaleModel] = useState(false);
    const [sameAsBill, setSameAsBill] = useState(false);
    const [phoneNumber, setPhoneNumber] = useState("");
    const [selectedAddress, setSelectedAddress] = useState(0);
    const [selectShippingAddress, setSelectShippingAddress] = useState("");
    const [grandTotal, setGrandTotal] = useState(0);
    const [mainGrandTotal, setMainGrandTotal] = useState(0);
    const [partyTitle, setPartyTitle] = useState({ name: "", id: "" });
    const [itemType, setItemType] = useState("item");
    const [quantityId, setQuantityId] = useState("");
    const [dispatchAddressName, setDispatchAddressName] = useState("");
    const [transportTitle, setTransportTitle] = useState("");
    const [modalType, setModalType] = useState("");
    const [countryId, setCountryId] = useState(101);
    const [stateId, setStateId] = useState("");
    const [cityId, setCityId] = useState("");
    const [countryName, setCountryName] = useState("india");
    const [stateName, setStateName] = useState("");
    const [cityName, setCityName] = useState("");
    const [countryId2, setCountryId2] = useState(101);
    const [stateId2, setStateId2] = useState("");
    const [cityId2, setCityId2] = useState("");
    const [cessValue, setCessValue] = useState(0.0);
    const [isEditCalculation, setIsEditCalculation] = useState(false);
    const [isChangeShippingAddress, setIsChangeShippingAddress] = useState(0)
    const [finalAmount, setFinalAmount] = useState(0);
    const [invoiceNumber, setInvoiceNumber] = useState("");
    const [configurationModalName, setConfigurationModalName] = useState("");
    const [isShowDocumentPrefixes, setIsShowDocumentPrefixes] = useState(true);
    const [configurationHeaderList, setConfigurationHeaderList] = useState([]);
    const [displayFormula, setDisplayFormula] = useState(null);
    const [backendFormula, setBackendFormula] = useState(null);
    const [customItemMasterConfigurationList, setCustomItemMasterConfigurationList] = useState([]);
    const [customItemConfigurationList, setCustomItemConfigurationList] = useState([]);
    const [customHeaderList, setCustomHeaderList] = useState([]);
    const [customHeaderListTransaction, setCustomHeaderListTransaction] = useState([]);
    const [customFieldListTransaction, setCustomFieldListTransaction] = useState([]);
    const [customFieldItemMaster, setCustomFieldItemMaster] = useState("")
    const [customFieldItemMasterList, setCustomFieldItemMasterList] = useState([])
    const [isManageCustomItemMaster, setIsManageCustomItemMaster] = useState(false)
    const [configurationTableList, setConfigurationTableList] = useState([]);
    const [configurationFooterList, setConfigurationFooterList] = useState([]);
    const [configurationURL, setConfigurationURL] = useState("");
    const [checkGroupLedgerType, setCheckGroupLedgerType] = useState("");
    const [isShippingAddressModel, setIsShippingAddressModel] = useState(false)
    const [isBankStateMentModel, setIsBankStateMentModel] = useState(false)
    const [transactionShippingAddressId, setTransactionShippingAddressId] = useState("")
    const [getSingleCustomHeader, setGetSingleCustomHeader] = useState("")
    const [storeAddedVariableId, setStoreAddedVariableId] = useState("");
    const [GSTError, setGSTError] = useState(null);
    const [isShowSelectedService, setIsShowSelectedService] = useState(false);
    const [isShowSelectedPurchaseNumber, setIsShowSelectedPurchaseNumber] = useState(false);
    const [isChangePartyId, setIsChangePartyId] = useState(false);
    const [isChangeParty, setIsChangeParty] = useState(false);
    const [isChangeGroupType, setIsChangeGroupType] = useState(false);
    const [deleteTransaction, setDeleteTransaction] = useState(false);
    const [updateParty_quotesOptions, setUpdateParty_quotesOptions] = useState([]);
    const [updatePurchaseNumberOptions, setUpdatePurchaseNumberOptions] = useState([]);
    const [isShowItemStockModel, setIsShowItemStockModel] = useState(false);
    const [isInitialDataLoaded, setIsInitialDataLoaded] = useState(false);
    const [isEditModel, setIsEditModel] = useState(false)
    const [isInitialDataLoaded2, setIsInitialDataLoaded2] = useState(false);
    const [ocrResponse, setOcrResponse] = useState(null);
    const [isOcrDetailExist, setIsOcrDetailExist] = useState(false);
    const [selectInvoiceType, setSelectInvoiceType] = useState(false)
    const [userPermission, setUserPermission] = useState({});
    const [getSingleCustomField, setGetSingleCustomField] = useState("")
    const [itemIndex, setItemIndex] = useState(0);
    const [isrenderDuplicateTitle, setIsrenderDuplicateTitle] = useState(false)
    const [isCustomFieldItemModel, setIsCustomFieldItemModel] = useState(false)
    const [isCustomFieldCalculation, setIsCustomFieldCalculation] = useState(false)
    const [partyLedgerTrigger, setPartyLedgerTrigger] = useState(0);
    const [dragHeaderList, setDragHeaderList] = useState([]);
    const [itemTableCustomFieldList, setItemTableCustomFieldList] = useState([]);
    const [isConnectToGstModal, setIsConnectToGstModal] = useState(false);
    const [isConnectSuccessModal, setIsConnectSuccessModal] = useState(false);
    const defaultCustomFieldCheckBox = [
        { name: "Estimate Quote", value: 0, type: CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.INCOME_ESTIMATE_QUOTE, default: false },
        { name: "Sale", value: 0, type: CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.SALE, default: false },
        { name: "Delivery Challan", value: 0, type: CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.DELIVERY_CHALLAN, default: false },
        { name: "Sale Return", value: 0, type: CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.SALE_RETURN, default: false },
        { name: "Income Debit Note", value: 0, type: CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.INCOME_DEBIT_NOTE, default: false },
        { name: "Income Credit Note", value: 0, type: CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.INCOME_CREDIT_NOTE, default: false },
        { name: "Purchase Order", value: 0, type: CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.PURCHASE_ORDER, default: false },
        { name: "Purchase", value: 0, type: CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.PURCHASE, default: false },
        { name: "Purchase Return", value: 0, type: CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.PURCHASE_RETURN, default: false },
        { name: "Expense Debit Note", value: 0, type: CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.EXPENSE_DEBIT_NOTE, default: false },
        { name: "Expense Credit Note", value: 0, type: CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.EXPENSE_CREDIT_NOTE, default: false },
        { name: "Recurring Invoice", value: 0, type: CUSTOM_FIELD_ITEM_TRANSACTION_TYPE.RECURRING, default: false },
    ]
    const [customFieldTransactionCheckBox, setCustomFieldTransactionCheckBox] = useState(defaultCustomFieldCheckBox)
    const defaultItemCustomFieldCheckBox = [
        { name: "Estimate Quote", value: 0, type: TRANSACTION_TYPE.INCOME_ESTIMATE_QUOTE, default: false },
        { name: "Sale", value: 0, type: TRANSACTION_TYPE.SALE, default: false },
        { name: "Delivery Challan", value: 0, type: TRANSACTION_TYPE.DELIVERY_CHALLAN, default: false },
        { name: "Sale Return", value: 0, type: TRANSACTION_TYPE.SALE_RETURN, default: false },
        { name: "Income Debit Note", value: 0, type: TRANSACTION_TYPE.INCOME_DEBIT_NOTE, default: false },
        { name: "Income Credit Note", value: 0, type: TRANSACTION_TYPE.INCOME_CREDIT_NOTE, default: false },
        { name: "Purchase Order", value: 0, type: TRANSACTION_TYPE.PURCHASE_ORDER, default: false },
        { name: "Purchase", value: 0, type: TRANSACTION_TYPE.PURCHASE, default: false },
        { name: "Purchase Return", value: 0, type: TRANSACTION_TYPE.PURCHASE_RETURN, default: false },
        { name: "Expense Debit Note", value: 0, type: TRANSACTION_TYPE.EXPENSE_DEBIT_NOTE, default: false },
        { name: "Expense Credit Note", value: 0, type: TRANSACTION_TYPE.EXPENSE_CREDIT_NOTE, default: false },
        { name: "Recurring Invoice", value: 0, type: TRANSACTION_TYPE.RECURRING, default: false },
    ]
    const [customFieldItemTransactionCheckBox, setCustomFieldItemTransactionCheckBox] = useState(defaultItemCustomFieldCheckBox)
    const [isNewAddressforLedgerMaster, setIsNewAddressforLedgerMaster] = useState(false)
    const [selectTransactionType, setSelectTransactionType] = useState("1");
    const [selectPdfFormat, setSelectPdfFormat] = useState("");
    const [thermalPageSize, setThermalPageSize] = useState("2")
    const [addCustomField, setAddCustomField] = useState([]);
    const [storeSettingEditDetail, setStoreSettingEditDetail] = useState("");
    const [selectedIndex, setSelectedIndex] = useState("");
    const [notExistItems, setNotExistItems] = useState("");
    const [notExistParty, setNotExistParty] = useState("");
    const [ocrInvoiceDetail, setOcrInvoiceDetail] = useState({
        transaction_type: "",
        invoice_date: "",
        invoice_number: "",
        voucher_number: "",
        voucher_date: "",
    });
    const [itemOnFly, setItemOnFly] = useState({
        item_name: "",
        group_id: "",
        item_type: 1,
        primary_unit_of_measurement: "",
        primary_unit_of_measurement_name: "",
        secondary_unit_of_measurement: null,
        secondary_unit_of_measurement_name: "",
        conversion_rate: 1,
        decimal_places: 2,
        sku: null,
        description: null,
        same_description: "",
        is_description_same_as_item_name: 0,
        is_gst_applicable: 0,
        gst_tax_id: null,
        hsn_sac_code: "",
        gst_cess_rate: null,
        is_rcm_applicable: 0,
        mrp: "",
        sale_price: "",
        sale_price_type: 2,
        discount_value: "",
        discount_type: 1,
        income_ledger_id: "",
        purchase_price: "",
        purchase_discount_type: 1,
        purchase_discount_value: "",
        purchase_price_type: 2,
        expense_ledger_id: "",
        decimal_places_for_rate: 2,
        quantity_unit: "",
        quantity: null,
        rate: null,
        opening_balance_type: 2,
        item_image: null,
    });
    // complete
    const IncomeA4A5HeaderShowHide = [
        {
            label: "Slogan",
            placeholder: "Slogan",
            value: "",
            name: "show_slogan",
            is_edit: "Change Slogan",
            estimate_quote: "show_slogan",
            delivery_challan: "show_slogan",
            expense: "expense_logo"
        },
        {
            label: "Proprietor",
            placeholder: "Proprietor Label",
            value: "",
            name: "prop_details",
            is_edit: "Change Proprietor Detail",
            estimate_quote: "prop_details",
            delivery_challan: "prop_details",
            expense: "prop_details"
        },
        {
            label: "Logo",
            placeholder: "Logo",
            value: "",
            name: "logo",
            is_edit: "Logo",
            estimate_quote: "estimate_logo",
            delivery_challan: "delivery_challan_logo",
            expense: "expense_logo"
        },
        {
            label: "Mobile Number",
            placeholder: "Mobile Number",
            value: "",
            name: "mobile_number",
            is_edit: "Change Phone For invoice",
            estimate_quote: "estimate_mobile_number",
            delivery_challan: "delivery_challan_mobile_number",
            expense: "expense_mobile_number"
        },
        {
            label: "Email",
            placeholder: "Email",
            value: "",
            name: "email",
            is_edit: "Change Email For invoice",
            estimate_quote: "estimate_email",
            delivery_challan: "delivery_challan_email",
            expense: "expense_email"
        },
        {
            label: "Broker Details",
            value: "",
            name: "broker_details",
            delivery_challan: "delivery_challan_broker_details",
            estimate_quote: "estimate_broker_details",
        },
        {
            label: "PAN",
            value: "",
            name: "pan_no",
            estimate_quote: "estimate_pan_no",
            delivery_challan: "delivery_challan_pan_no",
        },
        {
            label: "Duplicate",
            placeholder: "Duplicate",
            value: "",
            name: "duplicate",
            estimate_quote: "duplicate",
            delivery_challan: "duplicate",
            is_edit: "Duplicate",
        },
        {
            label: "Triplicate",
            placeholder: "Triplicate",
            value: "",
            name: "triplicate",
            estimate_quote: "triplicate",
            delivery_challan: "triplicate",
            is_edit: "Triplicate",
        },
    ];

    const ExpenseA4A5HeaderShowHide = [
        { label: "Slogan", placeholder: "Slogan", value: "", name: "show_slogan", is_edit: "Change Slogan" },
        {
            label: "Proprietor",
            placeholder: "Proprietor Label",
            value: "",
            name: "prop_details",
            is_edit: "Change Proprietor Detail",
            estimate_quote: "prop_details",
            delivery_challan: "prop_details",
            expense: "prop_details"
        },
        { label: "Logo", placeholder: "Logo", value: "", name: "expense_logo", is_edit: "Logo" },
        {
            label: "Mobile Number",
            placeholder: "Mobile Number",
            value: "",
            name: "expense_mobile_number",
            is_edit: "Change Phone For invoice",
        },
        {
            label: "Email",
            placeholder: "Email",
            value: "",
            name: "expense_email",
            is_edit: "Change Email For invoice",
        },
        { label: "Broker Details", value: "", name: "expense_broker_details" },
        { label: "PAN", value: "", name: "expense_pan_no" },
    ];

    const IncomeA4A5HeaderLabel = [
        { label: "GSTIN", value: "", name: "gstin", estimate_quote: "estimate_gstin" },
        { label: "Tel", value: "", name: "tel", estimate_quote: "estimate_tel" },
        { label: "Bill To", value: "", name: "bill_to", estimate_quote: "estimate_bill_to" },
        { label: "Ship To", value: "", name: "ship_to", estimate_quote: "estimate_ship_to", check_key: "ship_to_details", check_value: 0, estimate_check_key: "estimate_ship_to_details" },
        {
            label: "Invoice Details",
            value: "",
            name: "invoice_details",
            estimate_quote: "estimate_invoice_details",
        },
        {
            label: "Invoice Number",
            value: "",
            name: "invoice_number",
            estimate_quote: "estimate_invoice_number",
        },
        {
            label: "Invoice Date",
            value: "",
            name: "invoice_date",
            estimate_quote: "estimate_invoice_date",
        },
        {
            label: "PO No",
            value: "",
            name: "po_number_label",
            estimate_quote: "estimate_po_number_label",
            estimate_check_key: "estimate_po_number",
            check_key: "po_number",
            check_value: 0,
        },
        { label: "PO Date", value: "", name: "po_date", estimate_quote: "estimate_po_date", check_key: "show_po_date", check_value: 0, estimate_check_key: "show_estimate_po_date" },
        {
            label: "Dispatch From",
            value: "",
            name: "dispatch_from",
            estimate_quote: "estimate_dispatch_from",
            estimate_check_key: "estimate_dispatch_from_details",
            check_key: "dispatch_from_details",
            check_value: 0,
        },
        {
            label: "Transport Name",
            value: "",
            name: "transport_name",
            estimate_quote: "estimate_transport_name",
            estimate_check_key: "estimate_transport_details",
            check_key: "transport_details",
            check_value: 0,
        },
        {
            label: "Vehicle Number",
            value: "",
            name: "transport_vehicle_number",
            estimate_quote: "estimate_transport_vehicle_number",
        },
        {
            label: "Credit Period",
            value: "",
            name: "credit_period",
            estimate_quote: "estimate_credit_period",
            check_value: 0,
            check_key: "show_credit_period",
            estimate_check_key: "show_estimate_credit_period",
        },
        {
            label: "Due Date",
            value: "",
            name: "due_date",
            estimate_quote: "estimate_due_date",
            check_value: 0,
            check_key: "show_due_date",
            estimate_check_key: "show_estimate_due_date",
        },
    ];
    const IncomeA4A5HeaderWithField = [
        ...IncomeA4A5HeaderLabel,
        { label: "Ack No", value: "", name: "ack_no", estimate_quote: "estimate_signature" },
        { label: "Ack Date", value: "", name: "ack_date", estimate_quote: "estimate_signature" }
    ]
    const DeliveryChallanA4A5HeaderLabel = [
        { label: "GSTIN", value: "", name: "delivery_challan_gstin_label" },
        { label: "Tel", value: "", name: "delivery_challan_tel_label" },
        { label: "Bill To", value: "", name: "delivery_challan_bill_to_label" },
        { label: "Ship To", value: "", name: "delivery_challan_ship_to_label", check_key: "delivery_challan_ship_to_details", check_value: 0, },
        { label: "Challan Details", value: "", name: "delivery_challan_details_label" },
        { label: "Challan Number", value: "", name: "delivery_challan_number_label" },
        { label: "Challan Date", value: "", name: "delivery_challan_date_label" },
        { label: "PO No", value: "", name: "delivery_challan_po_number_label", check_key: "delivery_challan_po_number", check_value: 0, },
        { label: "PO Date", value: "", name: "delivery_challan_po_date_label", check_key: "show_delivery_challan_po_date", check_value: 0, },
        { label: "Transport Name", value: "", name: "delivery_challan_transport_name_label", check_key: "delivery_challan_transport_details", check_value: 0, },
        { label: "Dispatch From", value: "", name: "delivery_challan_dispatch_from_label", check_key: "delivery_challan_dispatch_from_details", check_value: 0, },
        { label: "Vehicle Number", value: "", name: "delivery_challan_transport_vehicle_number_label" },
    ];

    const EstimateA4A5HeaderLabel = [
        { label: "GSTIN", value: "", name: "gstin", expense: "expense_gstin" },
        { label: "Tel", value: "", name: "tel", expense: "expense_tel" },
        { label: "Bill To", value: "", name: "bill_to", expense: "expense_bill_to" },
        { label: "Ship To", value: "", name: "ship_to", expense: "expense_ship_to", check_key: "expense_ship_to_details", check_value: 0, },
        { label: "Invoice Details", value: "", name: "invoice_details", expense: "expense_invoice_details" },
        { label: "Invoice Number", value: "", name: "invoice_number", expense: "expense_invoice_number" },
        { label: "Invoice Date", value: "", name: "invoice_date", expense: "expense_invoice_date" },
        { label: "Transport Name", value: "", name: "transport_name", expense: "expense_transport_name", check_key: "expense_transport_details", check_value: 0, },
        { label: "Supplier Inv Number", value: "", name: "estimate_invoice_number", expense: "expense_supplier_inv_no" },
        { label: "Supplier Inv Date", value: "", name: "estimate_invoice_date", expense: "expense_supplier_inv_date" },
        { label: "Vehicle Number", value: "", name: "transport_vehicle_number", expense: "expense_vehicle_number" },
        { label: "Credit Period", value: "", name: "estimate_credit_period", expense: "expense_credit_period" },
    ];

    const ThermalLandscapeHeaderShowHide = [
        { label: "Logo", placeholder: "Logo", name: "thermal_logo", is_edit: "Logo", value: "" },
        {
            label: "Mobile Number",
            placeholder: "Mobile Number",
            name: "thermal_mobile_number",
            is_edit: "Change Phone For invoice",
            value: ""
        },
        { label: "Email", placeholder: "Email", name: "thermal_email", is_edit: "Change Email For invoice", value: "" },
        { label: "PAN", name: "thermal_pan_no", value: "" },
    ];

    const ThermalLandscapeHeaderLabel = [
        { label: "GSTIN", name: "thermal_gstin", value: "" },
        { label: "Tel", name: "thermal_tel", value: "" },
        { label: "Invoice Number", name: "thermal_invoice_no", value: "" },
        { label: "Date", name: "thermal_date", value: "" },
        { label: "Customer", name: "thermal_customer", value: "" },
        { label: "Address", name: "thermal_address", value: "" },
    ];

    const A4A5DetailShowHide = [
        { label: "Currency Symbol", value: "", name: "currency_symbol" },
        { label: "Item Image", value: "", name: "show_item_image" },
        { label: "SKU (Item Code)", value: "", name: "show_item_sku" },
    ];

    const IncomeA4A5FooterShowHide = [
        {
            label: "Signature",
            value: "",
            name: "signature",
            is_edit: "Signature",
            estimate_quote: "estimate_signature",
        },
        {
            label: "Payment QR Code",
            value: "",
            name: "qr_code",
            is_dropdown: true,
            qr_code_with_amount: "",
            estimate_quote: "estimate_qr_code",
        },
        {
            label: "Bank Details",
            value: "",
            name: "bank_details",
            estimate_quote: "estimate_bank_details",
        },
        {
            label: "HSN Summary",
            value: "",
            name: "hsn_summary",
            estimate_quote: "estimate_hsn_summary",
        },
        { label: "Current OutStanding", value: "", name: "current_outstanding" },
        { label: "Payment Status", value: "", name: "show_payment_status" },
    ];
    const ExpenseA5FooterShowHide = [
        { label: "Signature", value: "", name: "expense_signature", is_edit: "Signature" },
        { label: "HSN Summary", value: "", name: "expense_hsn_summary" },
    ];
    const DeliveryChallanA4A5FooterShowHide = [
        { label: "Signature", value: "", name: "delivery_challan_signature", is_edit: "Signature" },
        { label: "Received By Details", value: "", name: "delivery_challan_received_by" },
        { label: "Delivered By Details", value: "", name: "delivery_challan_delivered_by" },
    ];
    const EstimateA4A5FooterShowHide = [
        { label: "Signature", value: "", name: "estimate_signature", is_edit: "Signature" },
        { label: "Payment QR Code", value: "", name: "estimate_qr_code", is_dropdown: true, qr_code_with_amount: "", },
        { label: "Bank Details", value: "", name: "estimate_bank_details" },
        {
            label: "HSN Summary",
            value: "",
            name: "estimate_hsn_summary",
        }
    ];
    const EstimateA4A5HeaderShowHide = [
        { label: "Slogan", placeholder: "Slogan", value: "", name: "show_slogan", is_edit: "Change Slogan" },
        {
            label: "Proprietor",
            placeholder: "Proprietor Label",
            value: "",
            name: "prop_details",
            is_edit: "Change Proprietor Detail",
            estimate_quote: "prop_details",
            delivery_challan: "prop_details",
            expense: "prop_details"
        },
        { label: "Logo", placeholder: "Logo", value: "", name: "estimate_logo", is_edit: "Logo" },
        {
            label: "Mobile Number",
            placeholder: "Mobile Number",
            value: "",
            name: "estimate_mobile_number",
            is_edit: "Change Phone For invoice",
        },
        {
            label: "Email",
            placeholder: "Email",
            value: "",
            name: "estimate_email",
            is_edit: "Change Email For invoice",
        },
        {
            label: "Broker Details",
            value: "",
            name: "estimate_broker_details",
        },
        { label: "PAN", value: "", name: "estimate_pan_no" },
        {
            label: "Duplicate",
            placeholder: "Duplicate",
            value: "",
            name: "duplicate",
            is_edit: "Duplicate",
        },
        {
            label: "Triplicate",
            placeholder: "Triplicate",
            value: "",
            name: "triplicate",
            is_edit: "Triplicate",
        },
        // { label: "Dispatch Address", value: "", name: "estimate_dispatch_from_details" },
    ];

    const EstimateA4A5DetailShowHide = [
        { label: "Item Image", value: "", name: "show_estimate_item_image" },
        { label: "SKU (Item Code)", value: "", name: "show_estimate_item_sku" },
    ];

    const DeliveryA4A5DetailShowHide = [
        { label: "Item Image", value: "", name: "show_delivery_challan_item_image" },
        { label: "SKU (Item Code)", value: "", name: "show_delivery_challan_item_sku" },
    ];
    const ExpenseA4A5DetailShowHide = [
        { label: "SKU (Item Code)", value: "", name: "show_expense_item_sku" },
    ];
    const DeliveryChallanA4A5DetailShowHide = [
        { label: "Item Image", value: "", name: "show_delivery_challan_item_image" },
        { label: "SKU (Item Code)", value: "", name: "show_delivery_challan_item_sku" },
    ];
    const LandscapeA5Detail = [
        {
            label: "Total Amount",
            value: "",
            name: "total_amount",
            check_key: "show_sale_total_amount",
            check_value: 0,
            expense_check_key: "show_purchase_total_discount",
            estimate_check_key: "show_estimate_total_amount",
            expense: "expense_total_discount",
            estimate: "estimate_total_amount",
            is_gst_applicable:""
        },
    ]
    const A4A5DetailLabel = [
        { label: "SN", value: "", name: "sn", expense: "expense_sn", estimate: "estimate_sn" },
        {
            label: "Item Name",
            value: "",
            name: "item_name",
            expense: "expense_item_name",
            estimate: "estimate_item_name",
        },
        {
            label: "HSN/SAC",
            value: "",
            name: "hsn_sac",
            check_key: "show_sale_hsn_sac",
            expense_check_key: "show_purchase_hsn_sac",
            estimate_check_key: "show_estimate_hsn_sac",
            check_value: 0,
            expense: "expense_hsn_sac",
            estimate: "estimate_hsn_sac",
            is_gst_applicable: ""
        },
        {
            label: "GST (%)",
            value: "",
            name: "gst",
            check_key: "show_sale_gst",
            expense_check_key: "show_purchase_gst",
            estimate_check_key: "show_estimate_gst",
            check_value: 0,
            expense: "expense_gst",
            estimate: "estimate_gst",
            is_gst_applicable: ""
        },
        {
            label: "Gst Amt",
            value: "",
            name: "gst_amount",
            check_key: "show_sale_gst_amount",
            estimate_check_key: "show_estimate_gst_amount",
            check_value: 0,
            estimate: "estimate_gst_amount",
            is_gst_applicable: ""
        },
        {
            label: "Qty",
            value: "",
            name: "qty",
            check_key: "show_sale_qty",
            expense_check_key: "show_purchase_qty",
            estimate_check_key: "show_estimate_qty",
            check_value: 0,
            expense: "expense_qty",
            estimate: "estimate_qty",
            sub_check_key: "show_sale_unit",
            sub_expense_check_key: "show_purchase_unit",
            sub_estimate_check_key: "show_estimate_unit",
            sub_check_value: 0
        },
        {
            label: "Secondary Qty(UOM)",
            value: "",
            name: "sale_uom_label",
            check_key: "sale_uom_enable",
            expense_check_key: "expense_uom_enable",
            estimate_check_key: "estimate_uom_enable",
            check_value: 0,
            expense: "expense_uom_label",
            estimate: "estimate_uom_label"
        },
        {
            label: "MRP",
            value: "",
            name: "mrp",
            check_key: "show_sale_mrp",
            expense_check_key: "show_purchase_mrp",
            estimate_check_key: "show_estimate_mrp",
            check_value: 0,
            expense: "expense_mrp",
            estimate: "estimate_mrp"
        },
        {
            label: "Rate",
            value: "",
            name: "rate",
            check_key: "show_sale_rate",
            expense_check_key: "show_purchase_rate",
            estimate_check_key: "show_estimate_rate",
            check_value: 0,
            expense: "expense_rate",
            estimate: "estimate_rate"
        },
        {
            label: "Rate With GST",
            value: "",
            name: "rate_with_gst",
            check_key: "show_sale_rate_with_gst",
            expense_check_key: "show_purchase_rate_with_gst",
            estimate_check_key: "show_estimate_rate_with_gst",
            check_value: 0,
            expense: "expense_rate_with_gst",
            estimate: "estimate_rate_with_gst",
            is_gst_applicable: ""
        },
        {
            label: "Discount",
            value: "",
            name: "discount",
            check_key: "show_sale_discount",
            expense_check_key: "show_purchase_discount",
            estimate_check_key: "show_estimate_discount",
            check_value: 0,
            expense: "expense_discount",
            estimate: "estimate_discount"
        },
        {
            label: "Dis 2.",
            value: "",
            name: "sale_dis_2_label",
            check_key: "sale_dis_2_enable",
            expense_check_key: "expense_dis_2_enable",
            estimate_check_key: "estimate_dis_2_enable",
            check_value: 0,
            expense: "expense_dis_2_label",
            estimate: "estimate_dis_2_label"
        },
        {
            label: "Total Discount",
            value: "",
            name: "total_discount",
            check_key: "show_sale_total_discount",
            expense_check_key: "show_purchase_total_discount",
            estimate_check_key: "show_estimate_total_discount",
            check_value: 0,
            expense: "expense_total_discount",
            estimate: "estimate_total_discount"
        },
        { label: "Taxable Value", value: "", name: "taxable_value", expense: "expense_taxable_value", estimate: "estimate_taxable_value" },
    ];
    const ExpenseA4A5DetailLabel = [
        { label: "SN", value: "", name: "sn", expense: "expense_sn", estimate: "estimate_sn" },
        {
            label: "Item Name",
            value: "",
            name: "item_name",
            expense: "expense_item_name",
            estimate: "estimate_item_name",
        },
        {
            label: "HSN/SAC",
            value: "",
            name: "hsn_sac",
            check_key: "show_sale_hsn_sac",
            expense_check_key: "show_purchase_hsn_sac",
            estimate_check_key: "show_estimate_hsn_sac",
            check_value: 0,
            expense: "expense_hsn_sac",
            estimate: "estimate_hsn_sac",
            is_gst_applicable: ""
        },
        {
            label: "GST (%)",
            value: "",
            name: "gst",
            check_key: "show_sale_gst",
            expense_check_key: "show_purchase_gst",
            estimate_check_key: "show_estimate_gst",
            check_value: 0,
            expense: "expense_gst",
            estimate: "estimate_gst",
            is_gst_applicable: ""
        },
        {
            label: "Qty",
            value: "",
            name: "qty",
            check_key: "show_sale_qty",
            expense_check_key: "show_purchase_qty",
            estimate_check_key: "show_estimate_qty",
            check_value: 0,
            expense: "expense_qty",
            estimate: "estimate_qty",
            sub_check_key: "show_sale_unit",
            sub_expense_check_key: "show_purchase_unit",
            sub_estimate_check_key: "show_estimate_unit",
            sub_check_value: 0
        },
        {
            label: "Secondary Qty(UOM)",
            value: "",
            name: "sale_uom_label",
            check_key: "sale_uom_enable",
            expense_check_key: "expense_uom_enable",
            estimate_check_key: "estimate_uom_enable",
            check_value: 0,
            expense: "expense_uom_label",
            estimate: "estimate_uom_label"
        },
        {
            label: "MRP",
            value: "",
            name: "mrp",
            check_key: "show_sale_mrp",
            expense_check_key: "show_purchase_mrp",
            estimate_check_key: "show_estimate_mrp",
            check_value: 0,
            expense: "expense_mrp",
            estimate: "estimate_mrp"
        },
        {
            label: "Rate",
            value: "",
            name: "rate",
            check_key: "show_sale_rate",
            expense_check_key: "show_purchase_rate",
            estimate_check_key: "show_estimate_rate",
            check_value: 0,
            expense: "expense_rate",
            estimate: "estimate_rate"
        },
        {
            label: "Rate With GST",
            value: "",
            name: "rate_with_gst",
            check_key: "show_sale_rate_with_gst",
            expense_check_key: "show_purchase_rate_with_gst",
            estimate_check_key: "show_estimate_rate_with_gst",
            check_value: 0,
            expense: "expense_rate_with_gst",
            estimate: "estimate_rate_with_gst",
            is_gst_applicable: ""
        },
        {
            label: "Discount",
            value: "",
            name: "discount",
            check_key: "show_sale_discount",
            expense_check_key: "show_purchase_discount",
            estimate_check_key: "show_estimate_discount",
            check_value: 0,
            expense: "expense_discount",
            estimate: "estimate_discount"
        },
        {
            label: "Dis 2.",
            value: "",
            name: "sale_dis_2_label",
            check_key: "sale_dis_2_enable",
            expense_check_key: "expense_dis_2_enable",
            estimate_check_key: "estimate_dis_2_enable",
            check_value: 0,
            expense: "expense_dis_2_label",
            estimate: "estimate_dis_2_label"
        },
        {
            label: "Total Discount",
            value: "",
            name: "total_discount",
            check_key: "show_sale_total_discount",
            expense_check_key: "show_purchase_total_discount",
            estimate_check_key: "show_estimate_total_discount",
            check_value: 0,
            expense: "expense_total_discount",
            estimate: "estimate_total_discount"
        },
        { label: "Taxable Value", value: "", name: "taxable_value", expense: "expense_taxable_value", estimate: "estimate_taxable_value" },
    ];
    const DeliveryChallanA5Detail = [
        {
            label: "Total Amount",
            value: "",
            name: "delivery_challan_total_amount_label",
            is_gst_applicable:"",
            check_key: "show_delivery_challan_total_amount",
            check_value: 0,
        },
    ]
    const DeliveryChallanA4A5DetailLabel = [
        { label: "SN", value: "", name: "delivery_challan_sn_label" },
        { label: "Item Name", value: "", name: "delivery_challan_item_name_label" },
        {
            label: "Qty",
            value: "",
            name: "delivery_challan_qty_label",
            check_key: "show_delivery_challan_qty",
            check_value: 0,
            sub_check_key: "show_delivery_challan_unit",
            sub_check_value: 0
        },
        {
            label: "Secondary Qty(UOM)",
            value: "",
            name: "delivery_challan_uom_label",
            check_key: "delivery_challan_uom_enable",
            check_value: 0,
        },
        {
            label: "HSN/SAC",
            value: "",
            name: "delivery_challan_hsn_sac_label",
            check_key: "show_delivery_challan_hsn_sac",
            check_value: 0,
            is_gst_applicable: ""
        },
        {
            label: "GST (%)",
            value: "",
            name: "delivery_challan_gst_label",
            check_key: "show_delivery_challan_gst",
            check_value: 0,
            is_gst_applicable: ""
        },
        {
            label: "Gst Amt",
            value: "",
            name: "delivery_challan_gst_amount_label",
            check_key: "show_delivery_challan_gst_amount",
            check_value: 0,
            is_gst_applicable: ""
        },
        {
            label: "Rate",
            value: "",
            name: "delivery_challan_rate_label",
            check_key: "show_delivery_challan_rate",
            check_value: 0,
        },
        {
            label: "Rate With GST",
            value: "",
            name: "delivery_challan_rate_with_gst_label",
            check_key: "show_delivery_challan_rate_with_gst",
            check_value: 0,
            is_gst_applicable: ""
        },
        {
            label: "MRP",
            value: "",
            name: "delivery_challan_mrp_label",
            check_key: "show_delivery_challan_mrp",
            check_value: 0,
        },
        {
            label: "Discount",
            value: "",
            name: "delivery_challan_discount_label",
            check_key: "show_delivery_challan_discount",
            check_value: 0,
        },
        {
            label: "Dis 2.",
            value: "",
            name: "delivery_challan_dis_2_label",
            check_key: "delivery_challan_dis_2_enable",
            check_value: 0,
        },
        {
            label: "Taxable Value",
            value: "",
            name: "delivery_challan_taxable_value_label",
        }
    ];
    const DeliveryChallanLetterHeadDetailLabel = [
        { label: "SN", value: "", name: "delivery_challan_sn_label" },
        { label: "Item Name", value: "", name: "delivery_challan_item_name_label" },
        {
            label: "Qty",
            value: "",
            name: "delivery_challan_qty_label",
            check_key: "show_delivery_challan_qty",
            check_value: 0,
            sub_check_key: "show_delivery_challan_unit",
            sub_check_value: 0
        },
        {
            label: "Secondary Qty(UOM)",
            value: "",
            name: "delivery_challan_uom_label",
            check_key: "delivery_challan_uom_enable",
            check_value: 0,
        },
        {
            label: "HSN/SAC",
            value: "",
            name: "delivery_challan_hsn_sac_label",
            check_key: "show_delivery_challan_hsn_sac",
            check_value: 0,
            is_gst_applicable: ""
        },
        {
            label: "GST (%)",
            value: "",
            name: "delivery_challan_gst_label",
            check_key: "show_delivery_challan_gst",
            check_value: 0,
            is_gst_applicable: ""
        },
        {
            label: "Rate",
            value: "",
            name: "delivery_challan_rate_label",
            check_key: "show_delivery_challan_rate",
            check_value: 0,
        },
        {
            label: "Rate With GST",
            value: "",
            name: "delivery_challan_rate_with_gst_label",
            check_key: "show_delivery_challan_rate_with_gst",
            check_value: 0,
            is_gst_applicable: ""
        },
        {
            label: "MRP",
            value: "",
            name: "delivery_challan_mrp_label",
            check_key: "show_delivery_challan_mrp",
            check_value: 0,
        },
        {
            label: "Discount",
            value: "",
            name: "delivery_challan_discount_label",
            check_key: "show_delivery_challan_discount",
            check_value: 0,
        },
        {
            label: "Dis 2.",
            value: "",
            name: "delivery_challan_dis_2_label",
            check_key: "delivery_challan_dis_2_enable",
            check_value: 0,
        },
        {
            label: "Taxable Value",
            value: "",
            name: "delivery_challan_taxable_value_label",
        },
    ];

    const IncomeA4FooterLabel = [
        {
            label: "Terms And Conditions",
            value: "",
            name: "terms_and_conditions",
            check_key: "show_sale_terms_and_conditions",
            estimate_check_key: "show_estimate_terms_and_conditions",
            check_value: 0,
            estimate_quote: "estimate_terms_and_conditions",
        },
        {
            label: "Notes",
            value: "",
            name: "narration",
            check_key: "show_sale_narration",
            estimate_check_key: "show_estimate_narration",
            check_value: 0,
            estimate_quote: "estimate_narration",
        },
        {
            label: "Authorized Signatory",
            value: "",
            name: "authorized_signatory",
            check_key: "show_sale_authorized_signatory",
            estimate_check_key: "show_estimate_authorized_signatory",
            check_value: 0,
            estimate_quote: "estimate_authorized_signatory",
        },
    ];
    const IncomeA4A5TotalLabel = [
        { label: "Sub Total", value: "", name: "sub_total", estimate_quote: "estimate_sub_total" },
        { label: "IGST", value: "", name: "igst", estimate_quote: "estimate_igst", is_gst_applicable: "" },
        { label: "CGST", value: "", name: "cgst", estimate_quote: "estimate_cgst", is_gst_applicable: "" },
        { label: "SGST", value: "", name: "sgst", estimate_quote: "estimate_sgst", is_gst_applicable: "" },
        { label: "Cess", value: "", name: "cess", estimate_quote: "estimate_cess" },
        { label: "TCS", value: "", name: "tcs", estimate_quote: "estimate_tcs" },
        { label: "Round Off", value: "", name: "round_off", estimate_quote: "estimate_round_off" },
        {
            label: "In Words",
            value: "",
            name: "in_words",
            check_key: "show_sale_in_words",
            estimate_check_key: "show_estimate_in_words",
            check_value: 0,
            estimate_quote: "estimate_in_words",
        },
    ];
    const IncomeA5FooterLabel = [
        {
            label: "Terms And Conditions",
            value: "",
            check_key: "show_estimate_terms_and_conditions",
            check_value: 0,
            name: "estimate_terms_and_conditions",
        },
        {
            label: "Notes",
            value: "",
            check_key: "show_estimate_narration",
            check_value: 0,
            name: "estimate_narration",
        },
        {
            label: "Authorized Signatory",
            value: "",
            check_key: "show_estimate_authorized_signatory",
            check_value: 0,
            name: "estimate_authorized_signatory",
        },
    ];
    const A4A5DetailTotalLabel = [
        { label: "Sub Total", value: "", name: "estimate_sub_total" },
        { label: "IGST", value: "", name: "estimate_igst", is_gst_applicable: "" },
        { label: "CGST", value: "", name: "estimate_cgst", is_gst_applicable: "" },
        { label: "SGST", value: "", name: "estimate_sgst", is_gst_applicable: "" },
        { label: "Cess", value: "", name: "estimate_cess" },
        { label: "TCS", value: "", name: "estimate_tcs" },
        { label: "Round Off", value: "", name: "estimate_round_off" },
        {
            label: "In Words",
            value: "",
            check_key: "show_estimate_in_words",
            check_value: 0,
            name: "estimate_in_words",
        },
    ];
    const ExpenseA4A5FooterLabel = [
        {
            label: "Notes",
            value: "",
            name: "expense_narration",
            check_key: "show_purchase_narration",
            check_value: 0,
        },
        {
            label: "Authorized Signatory",
            value: "",
            name: "expense_authorized_signatory",
            check_key: "show_purchase_authorized_signatory",
            check_value: 0,
        },
    ];
    const ExpenseA4A5FooterTotalLabel = [
        { label: "Sub Total", value: "", name: "expense_sub_total" },
        { label: "IGST", value: "", name: "expense_igst", is_gst_applicable: "" },
        { label: "CGST", value: "", name: "expense_cgst", is_gst_applicable: "" },
        { label: "SGST", value: "", name: "expense_sgst", is_gst_applicable: "" },
        { label: "Cess", value: "", name: "expense_cess" },
        { label: "TCS", value: "", name: "expense_tcs" },
        { label: "Round Off", value: "", name: "expense_round_off" },
        {
            label: "In Words",
            value: "",
            name: "expense_in_words",
            check_key: "show_purchase_in_words",
            check_value: 0,
        },
    ];

    const DeliveryChallanA4A5FooterLabel = [
        {
            label: "Terms And Conditions",
            value: "",
            name: "delivery_challan_terms_and_conditions",
            check_key: "show_delivery_challan_terms_and_conditions",
            check_value: 0,
        },
        {
            label: "Notes",
            value: "",
            name: "delivery_challan_narration",
            check_key: "show_delivery_challan_narration",
            check_value: 0,
        },
        {
            label: "Authorized Signatory",
            value: "",
            name: "delivery_challan_authorized_signatory",
            check_key: "show_delivery_challan_authorized_signatory",
            check_value: 0,
        },
    ];
    const DeliveryChallanA4A5FooterTotalLabel = [
        {
            label: "Received By",
            value: "",
            name: "delivery_challan_recived_by",
        },
        {
            label: "Delivered By",
            value: "",
            name: "delivery_challan_delivered_by_label",
        },
        {
            label: "Sub Total",
            value: "",
            name: "delivery_challan_sub_total",
        },
        {
            label: "IGST",
            value: "",
            name: "delivery_challan_igst",
            is_gst_applicable: ""
        },
        {
            label: "CGST",
            value: "",
            name: "delivery_challan_cgst",
            is_gst_applicable: ""
        },
        {
            label: "SGST",
            value: "",
            name: "delivery_challan_sgst",
            is_gst_applicable: ""
        },
        {
            label: "CESS",
            value: "",
            name: "delivery_challan_cess",
            is_gst_applicable: ""
        },
        {
            label: "TCS",
            value: "",
            name: "delivery_challan_tcs",
            is_gst_applicable: ""
        },
        {
            label: "Round Off",
            value: "",
            name: "delivery_challan_round_off",
            is_gst_applicable: ""
        },
        {
            label: "In Words",
            value: "",
            name: "delivery_challan_in_words",
            check_key: "show_delivery_challan_in_words",
            check_value: 0,
        },
    ];

    const IncomeA3Detail = [
        { label: "Item Name", value: "", name: "item_name", thermal_name: "thermal_item_name" },
        { label: "HSN/SAC", value: "", name: "hsn_sac", thermal_name: "thermal_hsn_sac", is_gst_applicable: "" },
        { label: "GST (%)", value: "", name: "gst", thermal_name: "thermal_gst", is_gst_applicable: "" },
        { label: "Qty", value: "", name: "qty", thermal_name: "thermal_qty" },
        { label: "Rate", value: "", name: "rate", thermal_name: "thermal_rate" },
        { label: "Discount", value: "", name: "discount", thermal_name: "thermal_discount" },
        { label: "Taxable Value", value: "", name: "taxable_value", thermal_name: "thermal_taxable_value" },
    ];

    const ThermalIncomeA3FooterLabel = [
        { label: "QR Code", name: "thermal_qr_code", value: "" },
        { label: "HSN Summary", name: "thermal_hsn_summary", value: "" },
        { label: "Current OutStanding", value: "", name: "current_outstanding" },
    ];

    const IncomeA3FooterDetail = [
        { label: "Terms And Conditions", value: "", name: "thermal_terms_and_conditions" },
    ];
    const IncomeA3FooterTotalLabel = [
        { label: "IGST", value: "", name: "thermal_igst", is_gst_applicable: "" },
        { label: "CGST", value: "", name: "thermal_cgst", is_gst_applicable: "" },
        { label: "SGST", value: "", name: "thermal_sgst", is_gst_applicable: "" },
        { label: "Cess", value: "", name: "thermal_cess" },
        { label: "TCS", value: "", name: "thermal_tcs" },
        { label: "Round Off", value: "", name: "thermal_round_off" },
    ];

    const [printSettingLabel, setPrintSettingLabel] = useState({
        income_transaction: [
            {
                type: "1",
                header: {
                    settings: IncomeA4A5HeaderShowHide,
                    invoice_labels: IncomeA4A5HeaderWithField,
                    print_setting: "",
                },
                detail: {
                    settings: A4A5DetailShowHide,
                    invoice_labels: A4A5DetailLabel,
                    total_labels: IncomeA4A5TotalLabel,
                },
                footer: {
                    settings: IncomeA4A5FooterShowHide,
                    invoice_labels: IncomeA4FooterLabel,
                },
            },
            {
                type: "2",
                header: {
                    settings: IncomeA4A5HeaderShowHide,
                    invoice_labels: IncomeA4A5HeaderWithField,
                },
                detail: {
                    settings: A4A5DetailShowHide,
                    invoice_labels: A4A5DetailLabel,
                    total_labels: IncomeA4A5TotalLabel,
                },
                footer: {
                    settings: IncomeA4A5FooterShowHide,
                    invoice_labels: IncomeA4FooterLabel,
                },
            },
            {
                type: "3",
                header: {
                    settings: ThermalLandscapeHeaderShowHide,
                    invoice_labels: ThermalLandscapeHeaderLabel,
                },
                detail: {
                    settings: [],
                    invoice_labels: IncomeA3Detail,
                    total_labels: IncomeA3FooterTotalLabel,
                },
                footer: {
                    settings: ThermalIncomeA3FooterLabel,
                    invoice_labels: IncomeA3FooterDetail,
                },
            },
            {
                type: "4",
                header: {
                    settings: IncomeA4A5HeaderShowHide,
                    invoice_labels: IncomeA4A5HeaderWithField,
                },
                detail: {
                    settings: A4A5DetailShowHide,
                    invoice_labels: A4A5DetailLabel,
                    total_labels: IncomeA4A5TotalLabel,
                },
                footer: {
                    settings: IncomeA4A5FooterShowHide,
                    invoice_labels: IncomeA4FooterLabel,
                },
            },
            {
                type: "5",
                header: {
                    settings: IncomeA4A5HeaderShowHide,
                    invoice_labels: IncomeA4A5HeaderWithField,
                },
                detail: {
                    settings: A4A5DetailShowHide,
                    invoice_labels: [...A4A5DetailLabel, ...LandscapeA5Detail],
                    total_labels: IncomeA4A5TotalLabel,
                },
                footer: {
                    settings: IncomeA4A5FooterShowHide,
                    invoice_labels: IncomeA4FooterLabel,
                },
            },
            {
                type: "6",
                header: {
                    settings: IncomeA4A5HeaderShowHide,
                    invoice_labels: IncomeA4A5HeaderWithField,
                },
                detail: {
                    settings: A4A5DetailShowHide,
                    invoice_labels: A4A5DetailLabel,
                    total_labels: IncomeA4A5TotalLabel,
                },
                footer: {
                    settings: IncomeA4A5FooterShowHide,
                    invoice_labels: IncomeA4FooterLabel,
                },
            },
        ],
        estimate_quote: [
            {
                type: "1",
                header: {
                    settings: EstimateA4A5HeaderShowHide,
                    invoice_labels: IncomeA4A5HeaderLabel,
                    print_setting: "",
                },
                detail: {
                    settings: EstimateA4A5DetailShowHide,
                    invoice_labels: A4A5DetailLabel,
                    total_labels: A4A5DetailTotalLabel,
                },
                footer: {
                    settings: EstimateA4A5FooterShowHide,
                    invoice_labels: IncomeA5FooterLabel,
                },
            },
            {
                type: "2",
                header: {
                    settings: EstimateA4A5HeaderShowHide,
                    invoice_labels: IncomeA4A5HeaderLabel,
                },
                detail: {
                    settings: EstimateA4A5DetailShowHide,
                    invoice_labels: A4A5DetailLabel,
                    total_labels: A4A5DetailTotalLabel,
                },
                footer: {
                    settings: EstimateA4A5FooterShowHide,
                    invoice_labels: IncomeA5FooterLabel,
                },
            },
            {
                type: "4",
                header: {
                    settings: EstimateA4A5HeaderShowHide,
                    invoice_labels: IncomeA4A5HeaderLabel,
                },
                detail: {
                    settings: EstimateA4A5DetailShowHide,
                    invoice_labels: A4A5DetailLabel,
                    total_labels: A4A5DetailTotalLabel,
                },
                footer: {
                    settings: EstimateA4A5FooterShowHide,
                    invoice_labels: IncomeA5FooterLabel,
                },
            },
            {
                type: "5",
                header: {
                    settings: EstimateA4A5HeaderShowHide,
                    invoice_labels: IncomeA4A5HeaderLabel,
                },
                detail: {
                    settings: EstimateA4A5DetailShowHide,
                    invoice_labels: [...A4A5DetailLabel, ...LandscapeA5Detail],
                    total_labels: A4A5DetailTotalLabel,
                },
                footer: {
                    settings: EstimateA4A5FooterShowHide,
                    invoice_labels: IncomeA5FooterLabel,
                },
            },
            {
                type: "6",
                header: {
                    settings: EstimateA4A5HeaderShowHide,
                    invoice_labels: IncomeA4A5HeaderLabel,
                },
                detail: {
                    settings: EstimateA4A5DetailShowHide,
                    invoice_labels: A4A5DetailLabel,
                    total_labels: A4A5DetailTotalLabel,
                },
                footer: {
                    settings: EstimateA4A5FooterShowHide,
                    invoice_labels: IncomeA5FooterLabel,
                },
            },
        ],
        delivery_challan: [
            {
                type: "1",
                header: {
                    settings: IncomeA4A5HeaderShowHide,
                    invoice_labels: DeliveryChallanA4A5HeaderLabel,
                    print_setting: "",
                },
                detail: {
                    settings: DeliveryChallanA4A5DetailShowHide,
                    invoice_labels: DeliveryChallanA4A5DetailLabel,
                    total_labels: DeliveryChallanA4A5FooterTotalLabel,
                },
                footer: {
                    settings: DeliveryChallanA4A5FooterShowHide,
                    invoice_labels: DeliveryChallanA4A5FooterLabel,
                },
            },
            {
                type: "2",
                header: {
                    settings: IncomeA4A5HeaderShowHide,
                    invoice_labels: DeliveryChallanA4A5HeaderLabel,
                },
                detail: {
                    settings: DeliveryChallanA4A5DetailShowHide,
                    invoice_labels: DeliveryChallanA4A5DetailLabel,
                    total_labels: DeliveryChallanA4A5FooterTotalLabel,
                },
                footer: {
                    settings: DeliveryChallanA4A5FooterShowHide,
                    invoice_labels: DeliveryChallanA4A5FooterLabel,
                },
            },
            {
                type: "4",
                header: {
                    settings: IncomeA4A5HeaderShowHide,
                    invoice_labels: DeliveryChallanA4A5HeaderLabel,
                },
                detail: {
                    settings: DeliveryA4A5DetailShowHide,
                    invoice_labels: DeliveryChallanA4A5DetailLabel,
                    total_labels: DeliveryChallanA4A5FooterTotalLabel,
                },
                footer: {
                    settings: DeliveryChallanA4A5FooterShowHide,
                    invoice_labels: DeliveryChallanA4A5FooterLabel,
                },
            },
            {
                type: "5",
                header: {
                    settings: IncomeA4A5HeaderShowHide,
                    invoice_labels: DeliveryChallanA4A5HeaderLabel,
                },
                detail: {
                    settings: DeliveryA4A5DetailShowHide,
                    invoice_labels: [...DeliveryChallanA4A5DetailLabel, ...DeliveryChallanA5Detail],
                    total_labels: DeliveryChallanA4A5FooterTotalLabel,
                },
                footer: {
                    settings: DeliveryChallanA4A5FooterShowHide,
                    invoice_labels: DeliveryChallanA4A5FooterLabel,
                },
            },
            {
                type: "6",
                header: {
                    settings: IncomeA4A5HeaderShowHide,
                    invoice_labels: DeliveryChallanA4A5HeaderLabel,
                },
                detail: {
                    settings: DeliveryA4A5DetailShowHide,
                    invoice_labels: DeliveryChallanLetterHeadDetailLabel,
                    total_labels: DeliveryChallanA4A5FooterTotalLabel,
                },
                footer: {
                    settings: DeliveryChallanA4A5FooterShowHide,
                    invoice_labels: DeliveryChallanA4A5FooterLabel,
                },
            },
        ],
        expense: [
            {
                type: "1",
                header: {
                    settings: ExpenseA4A5HeaderShowHide,
                    invoice_labels: EstimateA4A5HeaderLabel,
                    print_setting: "",
                },
                detail: {
                    settings: ExpenseA4A5DetailShowHide,
                    invoice_labels: ExpenseA4A5DetailLabel,
                    total_labels: ExpenseA4A5FooterTotalLabel,
                },
                footer: {
                    settings: ExpenseA5FooterShowHide,
                    invoice_labels: ExpenseA4A5FooterLabel,
                },
            },
            {
                type: "2",
                header: {
                    settings: ExpenseA4A5HeaderShowHide,
                    invoice_labels: EstimateA4A5HeaderLabel,
                },
                detail: {
                    settings: ExpenseA4A5DetailShowHide,
                    invoice_labels: ExpenseA4A5DetailLabel,
                    total_labels: ExpenseA4A5FooterTotalLabel,
                },
                footer: {
                    settings: ExpenseA5FooterShowHide,
                    invoice_labels: ExpenseA4A5FooterLabel,
                },
            },
        ],
    });
    const defaultGst = {
        sgstValue: 0.0,
        cgstValue: 0.0,
        igstValue: 0.0,
    }
    const [gstValue, setGstValue] = useState(defaultGst);
    const [expenseDetail, setExpenseDetail] = useState({
        note_number: "",
        note_date: "",
    });
    const defaultInvoiceDetail = {
        invoice_number: "",
        invoice_date: "",
        purchase_title: "",
        estimate_title: "",
        challan_date: formattedDate(),
        note_numeber: "",
        note_date: "",
        debit_note_number: "",
        debit_note_date: "",
    }
    const [invoiceDetail, setInvoiceDetail] = useState(defaultInvoiceDetail);
    const [purchaseInvoice, setPurchaseInvoice] = useState({
        voucher_date: formattedDate(),
        voucher_number: "",
    });
    const defaultBrokerDetail = {
        broker_id: null,
        broker_percentage: null,
        brokerage_on_value: 1,
    }
    const [brokerDetail, setBrokerDetail] = useState(defaultBrokerDetail);
    const defaultTransporterDetail = {
        transport_id: null,
        transporter_document_number: null,
        transporter_document_date: null,
        transporter_vehicle_number: null,
    }
    const [transporterDetail, setTransporterDetail] = useState(defaultTransporterDetail);
    const [classificationType, setClassificationType] = useState({
        classificationSelectedType: 1,
        isRCMApplicable: false,
    });
    const [localDispatchAddress, setLocalDispatchAddress] = useState([]);
    const [shippingAddress, setShippingAddress] = useState([]);
    const defaultGstQuote = {
        party_ledger_id: "",
        mobile: {
            region_iso: "in",
            region_code: "+91",
            party_phone_number: "",
            phone_input: "+91",
        },
        gstin: "",
        quotes_id: [],
        original_inv_no: "",
        original_inv_date: "",
        valid_for: "",
        valid_for_type: 1,
        purchase_number: [],
    }
    const [gstQuote, setGstQuote] = useState(defaultGstQuote);
    const defaultAdditionalCharges = {
        note: "",
        terms_and_conditions: "",
        upload_document: "",
        additional_detail: [
            {
                ac_ledger_id: null,
                ac_type: 1,
                ac_value: null,
                ac_gst_rate_id: {
                    label: "",
                    value: 0,
                    rate: 0,
                },
                ac_total: 0,
            },
        ],
    }
    const [additionalCharges, setAdditionalCharges] = useState(defaultAdditionalCharges);
    const defaultTcsRate = {
        tcs_tax_id: null,
        tcs_rate: null,
        tcs_amount: null,
        tcs_calculated_on: 1,
        is_status: ITEM_STATUS.IN_ACTIVE,
    }
    const [tcsRate, setTcsRate] = useState(defaultTcsRate);
    const defaultAddLessChanges =
        [
            {
                al_ledger_id: null,
                al_is_show_in_print: 1,
                al_type: 1,
                al_value: null,
                al_total: 0,
            },
        ]
    const [addLessChanges, setAddLessChanges] = useState(defaultAddLessChanges);
    const [additionalGst, setAdditionalGst] = useState(0);
    const defaultPartyAddress = {
        billingAddress: {
            address_1: "",
            address_2: "",
            country_id: "",
            state_id: "",
            city_id: "",
            pin_code: "",
        },
        shippingAddress: {
            party_name: "",
            gstin: "",
            address_1: "",
            address_2: "",
            country_id: "",
            state_id: "",
            city_id: "",
            pin_code: "",
        },
        shippingCopy: {
            party_name: "",
            gstin: "",
            address_1: "",
            address_2: "",
            country_id: "",
            state_id: "",
            city_id: "",
            pin_code: "",
        },
    }
    const [partyAddress, setPartyAddress] = useState(defaultPartyAddress);
    const defaultEwayBillDetail = {
        eway_bill_number: "",
        eway_bill_date: "",
    }
    const [ewayBillDetail, setEwayBillDetail] = useState(defaultEwayBillDetail);
    const defaultOtherDetail = {
        po_number: "",
        date: "",
        creditPeriodType: 1,
        creditPeriod: "",
    }
    const [otherDetail, setOtherDetail] = useState(defaultOtherDetail);
    const defaultGstCalculation = {
        is_gst_enabled: "",
        is_cgst_sgst_igst_calculated: 0,
        is_gst_na: 0,
        is_round_off_not_changed: 1,
        round_of_amount: "",
    }
    const [gstCalculation, setGstCalculation] = useState(defaultGstCalculation);
    const defaultPaymentDetail = {
        tds_tax_id: null,
        tds_rate: null,
        tds_amount: null,
        tds_calculated_on: 1,
        payment_detail: [
            {
                pd_ledger_id: null,
                pd_date: "",
                pd_amount: "",
                pd_mode: null,
                pd_reference_number: "",
                is_show_invoice_date: true,
            },
        ],
    }
    const [paymentLedgerDetail, setPaymentLedgerDetail] = useState(defaultPaymentDetail);
    const defaultClassification = {
        classification_nature: 0,
        rcm_applicable: false,
        classification_nature_name: "",
    }
    const [classification, setClassification] = useState(defaultClassification);
    const [items, setItems] = useState([
        {
            id: 1,
            selectedItem: null,
            additional_description: "",
            mrp: 0,
            selectedLedger: null,
            quantity: 0,
            multiQuantity: [0, 0, 0, 0],
            rateWithGst: 0,
            updatedRateWithGst: 0,
            rateWithoutGst: 0,
            updatedRateWithoutGst: 0,
            gst_id: 0,
            gst: 0,
            gst_name: "",
            gst_copy: 0,
            discountType: 1,
            discountValue: 0,
            discountType_2: 1,
            discountValue_2: 0,
            hsn_code: "",
            total: 0,
            stock: 0,
            selectedUnit: null,
            cgstValue: 0,
            igstValue: 0,
            sgstValue: 0,
            shippingValue: 0,
            cessValue: 0,
            cessRate: 0,
            with_tax: 0,
            rpu: 0,
            itemUnitOption: null,
            isShowDelete: false,
            conversationRate: null,
            secondaryUnitOfMeasurement: null,
            decimal_places: 2,
            decimal_places_for_rate: 2,
            sku: "",
            itemType: 1,
            is_status: ITEM_STATUS.IN_ACTIVE
        },
    ]);
    const defaultItem = [
        {
            id: 1,
            selectedItem: null,
            additional_description: "",
            mrp: 0,
            selectedLedger: null,
            quantity: 0,
            free_quantity: 0,
            multiQuantity: [0, 0, 0, 0],
            rateWithGst: 0,
            updatedRateWithGst: 0,
            rateWithoutGst: 0,
            updatedRateWithoutGst: 0,
            gst_id: 0,
            gst: 0,
            gst_name: "",
            gst_copy: 0,
            discountType: 1,
            discountValue: 0,
            discountType_2: 1,
            discountValue_2: 0,
            total: 0,
            stock: 0,
            selectedUnit: null,
            cgstValue: 0,
            igstValue: 0,
            sgstValue: 0,
            shippingValue: 0,
            cessValue: 0,
            cessRate: 0,
            with_tax: 0,
            rpu: 0,
            itemUnitOption: null,
            isShowDelete: false,
            conversationRate: null,
            secondaryUnitOfMeasurement: null,
            decimal_places: 2,
            decimal_places_for_rate: 2,
            sku: "",
        },
    ];

    const defaultAccountingItem = [
        {
            id: 1,
            ledger: "",
            selectedLedger: "",
            amountWithGst: 0,
            amountWithoutGst: 0,
            updatedAmountWithGst: 0,
            updatedAmountWithoutGst: 0,
            discountType: 1,
            discountType_2: 1,
            discountValue: 0,
            discountValue_2: 0,
            gst_id: 0,
            gst: 0,
            gst_name: "",
            total: 0,
            cgstValue: 0,
            igstValue: 0,
            sgstValue: 0,
            updatedTotal: 0,
            additional_description: "",
            with_tax: 0,
            rpu: 0,
            isShowDelete: false,
            decimal_places: 2,
            decimal_places_for_rate: 2,
        },
    ];

    const [accountingItems, setAccountingItems] = useState([
        {
            id: 1,
            ledger: "",
            selectedLedger: "",
            amountWithGst: 0,
            amountWithoutGst: 0,
            updatedAmountWithGst: 0,
            updatedAmountWithoutGst: 0,
            discountType: 1,
            discountType_2: 1,
            discountValue: 0,
            discountValue_2: 0,
            gst_id: 0,
            gst: 0,
            gst_name: "",
            total: 0,
            cgstValue: 0,
            igstValue: 0,
            sgstValue: 0,
            updatedTotal: 0,
            additional_description: "",
            with_tax: 0,
            rpu: 0,
            isShowDelete: false,
            decimal_places: 2,
            decimal_places_for_rate: 2,
        },
    ]);

    const [showDeleteWarningModel, setShowDeleteWarningModel] = useState({
        show: false,
        transactions: [],
    });
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
    const [unsavedBackUrl, setUnsavedBackUrl] = useState("");
    const [isFieldsChanges, setisFieldsChanges] = useState(false);
    const [isBackButtonClick, setIsBackButtonClick] = useState(true);

    const [isDescription, setIsDescription] = useState(false);
    const [dynamicVariables, setDynamicVariables] = useState([]);
    const defaultRecurringInvoiceDetails = {
        template_name: "",
        recurrence_on: "approvalBasis",
        start_date: "",
        end_on: "date",
        end_date: "",
        occurrence: "",
        party_groups: [],
        parties: [],
        frequency_details: {
            repeat_frequency: 1,
            repeat_on: 1,
            on_day: 1,
            custom_type: 1,
            custom_value: 1
        },
    }
    const [recurringInvoiceDetails, setRecurringInvoiceDetails] = useState(defaultRecurringInvoiceDetails);

    const openBrokerModel = useCallback(() => setIsBrokerModel(true), []);
    const closeBrokerModel = useCallback(() => setIsBrokerModel(false), []);

    const openCustomFieldModel = useCallback(() => setIsCustomeFieldModel(true), []);
    const closeCustomFieldModel = useCallback(() => setIsCustomeFieldModel(false), []);

    const openIsRearrangeCustomModel = useCallback(() => setIsRearrangeCustomModel(true), []);
    const closeIsRearrangeCustomModel = useCallback(() => setIsRearrangeCustomModel(false), []);

    const openCustomFormulaModel = useCallback(() => setIsCustomFormula(true), []);
    const closeCustomFormulaModel = useCallback(() => setIsCustomFormula(false), []);

    const openIncomeModel = useCallback(() => setIsIncomeModel(true), []);
    const closeIncomeModel = useCallback(() => setIsIncomeModel(false), []);

    const openItemCatModel = useCallback(() => setIsItemCatModel(true), []);
    const closeItemCatModel = useCallback(() => setIsItemCatModel(false), []);

    const openItemModel = useCallback(() => setIsItemModel(true), []);
    const closeItemModel = useCallback(() => setIsItemModel(false), []);

    const openLedgerModel = useCallback(() => setIsLedgerModel(true), []);
    const closeLedgerModel = useCallback(() => setIsLedgerModel(false), []);

    const openLocationOfAssetModel = useCallback(() => setIsLocationOfAsset(true), []);
    const closeLocationOfAssetModel = useCallback(() => setIsLocationOfAsset(false), []);

    const openItemsLedgerModel = useCallback(() => setIsItemsLedgerModel(true), []);
    const closeItemsLedgerModel = useCallback(() => setIsItemsLedgerModel(false), []);

    const openShipLedgerModel = useCallback(() => setIsShipLedgerModel(true), []);
    const closeShipLedgerModel = useCallback(() => setIsShipLedgerModel(false), []);

    const openTransportModel = useCallback(() => setIsTransportModel(true), []);
    const closeTransportModel = useCallback(() => setIsTransportModel(false), []);

    const openUomModel = useCallback(() => setIsUomModel(true), []);
    const closeUomModel = useCallback(() => setIsUomModel(false), []);
    const openSelectedServiceModel = useCallback(() => setIsShowSelectedService(true), []);
    const closeSelectedServiceModel = useCallback(() => setIsShowSelectedService(false), []);

    const openSelectedPurchaseNumberModel = useCallback(
        () => setIsShowSelectedPurchaseNumber(true),
        []
    );
    const closeSelectedPurchaseNumberModel = useCallback(
        () => setIsShowSelectedPurchaseNumber(false),
        []
    );

    const openClassificationModel = useCallback(() => setIsClassificationModel(true), []);
    const closeClassificationModel = useCallback(() => setIsClassificationModel(false), []);

    const openDispatchAddressModel = useCallback(() => setIsDispatchAddressModel(true), []);
    const closeDispatchAddressModel = useCallback(() => setIsDispatchAddressModel(false), []);

    const openDispatchFromModel = useCallback(() => setIsDispatchFromModel(true), []);
    const closeDispatchFromModel = useCallback(() => setIsDispatchFromModel(false), []);

    const openOcrNotExistModel = useCallback(() => setIsOcrDetailExist(true), []);
    const closeOcrNotExistModel = useCallback(() => setIsOcrDetailExist(false), []);

    const openShippingFromModel = useCallback(() => setIsShippingFromModel(true), []);
    const closeShippingFromModel = useCallback(() => setIsShippingFromModel(false), []);

    const openviewRecurringInvoiceModel = useCallback(() => setViewRecurringInvoice(true), []);
    const closeviewRecurringInvoiceModel = useCallback(() => setViewRecurringInvoice(false), []);

    const openFooterSettingModel = useCallback(() => setIsFooterSettingModel(true), []);
    const closeFooterSettingModel = useCallback(() => setIsFooterSettingModel(false), []);

    const openCustomizeFormatModal = useCallback(() => setIsCustomizeFormatModal(true), []);
    const closeCustomizeFormatModal = useCallback(() => setIsCustomizeFormatModal(false), []);

    const openIsBankStateMentModal = useCallback(() => setIsBankStateMentModel(true), []);
    const closeIsBankStateMentModal = useCallback(() => setIsBankStateMentModel(false), []);

    const openHeaderSettingModel = useCallback(() => setIsHeaderSettingModel(true), []);
    const closeHeaderSettingModel = useCallback(() => setIsHeaderSettingModel(false), []);

    const openItemTableModel = useCallback(() => setIsItemTableModel(true), []);
    const closeItemTableModel = useCallback(() => setIsItemTableModel(false), []);

    const openDeleteTransactionModel = useCallback(() => setDeleteTransaction(true), []);
    const closeDeleteTransactionModel = useCallback(() => setDeleteTransaction(false), []);

    const openSelectInvoiceTypeModel = useCallback(() => setSelectInvoiceType(true), []);
    const closeSelectInvoiceTypeModel = useCallback(() => setSelectInvoiceType(false), []);

    const openRearrangeModel = useCallback(() => setIsRearrangeModel(true), []);
    const closeRearrangeModel = useCallback(() => setIsRearrangeModel(false), []);

    const openQuantityModel = useCallback(() => setIsOpenQuantityModel(true), []);
    const closeQuantityModel = useCallback(() => setIsOpenQuantityModel(false), []);

    const openStockModel = useCallback(() => setIsShowItemStockModel(true), []);
    const closeStockModel = useCallback(() => setIsShowItemStockModel(false), []);

    const openSaleModel = useCallback(() => setIsSaleModel(true), []);
    const closeSaleModel = useCallback(() => setIsSaleModel(false), []);

    const openIsNegativeStockModel = useCallback(() => setIsNegativeStock(true), []);
    const closeIsNegativeStockModel = useCallback(() => setIsNegativeStock(false), []);

    const openTableSettingModal = useCallback(() => setIsTableSettingModal(true), []);
    const closeTableSettingModal = useCallback(() => setIsTableSettingModal(false), []);

    const openBillWiseModal = useCallback(() => setIsBillWiseModal(true), []);
    const closeBillWiseModal = useCallback(() => setIsBillWiseModal(false), []);

    const openInvoiceSettingModal = useCallback(() => setIsInvoiceSettingModal(true), []);
    const closeInvoiceSettingModal = useCallback(() => setIsInvoiceSettingModal(false), []);

    const openSloganEditModal = useCallback(() => setIsSloganEditModal(true), []);
    const closeSloganEditModal = useCallback(() => setIsSloganEditModal(false), []);

    const openEmailEditModal = useCallback(() => setIsEmailEditModal(true), []);
    const closeEmailEditModal = useCallback(() => setIsEmailEditModal(false), []);

    const openLogoEditModal = useCallback(() => setIsLogoEditModal(true), []);
    const closeLogoEditModal = useCallback(() => setIsLogoEditModal(false), []);

    const openMobileEditModal = useCallback(() => setIsMobileEditModal(true), []);
    const closeMobileEditModal = useCallback(() => setIsMobileEditModal(false), []);

    const openConnectToGstModal = useCallback(() => setIsConnectToGstModal(true), []);
    const closeConnectToGstModal = useCallback(() => setIsConnectToGstModal(false), []);

    const openConnectSuccessModal = useCallback(() => setIsConnectSuccessModal(true), []);
    const closeConnectSuccessModal = useCallback(() => setIsConnectSuccessModal(false), []);

    const [isIGSTCalculation, setIsIGSTCalculation] = useState(false);
    const [isSGSTCalculation, setIsSGSTCalculation] = useState(false);
    const [isTcsAmountChange, setIsTcsAmountChange] = useState(false);
    const [isChangedTcs, setIsChangedTcs] = useState(false);
    const [showItemPriceChangedModal, setShowItemPriceChangedModal] = useState(false);
    const [changeItemPrice, setChangeItemPrice] = useState(true);
    const [isConfirmed, setIsConfirmed] = useState(false);
    const [isCheckGstType, setIsCheckGstType] = useState(false);
    const [selectedAdvancePayment, setSelectedAdvancePayment] = useState([]);
    const [showPaymentTable, setShowPaymentTable] = useState(false);
    const [barcodeSize, setBarcodeSize] = useState({
        pageMarginTop: '',
        pageMarginBottom: '',
        pageMarginLeft: '',
        pageMarginRight: '',
        barcodeBetweenRows: '',
        barcodeBetweenColumns: '',
    });
    return (
        <StateContext.Provider
            value={{
                itemRef,
                ledgerRef,
                unitRef,
                quantityRef,
                totalRef,
                mrpRef,
                amountRef,
                priceWithGstRef,
                discountTypeRef,
                discountTypeRef2,
                discountValueRef,
                discountValueRef2,
                gstRef,
                isLedgerModel,
                openLedgerModel,
                closeLedgerModel,
                isItemsLedgerModel,
                openItemsLedgerModel,
                closeItemsLedgerModel,
                isShipLedgerModel,
                openShipLedgerModel,
                closeShipLedgerModel,
                isCustomFieldModel,
                openCustomFieldModel,
                closeCustomFieldModel,
                isIncomeModel,
                openIncomeModel,
                closeIncomeModel,
                isItemCatModel,
                openItemCatModel,
                closeItemCatModel,
                isItemModel,
                openItemModel,
                closeItemModel,
                isBrokerModel,
                openBrokerModel,
                closeBrokerModel,
                isTransportModel,
                openTransportModel,
                closeTransportModel,
                isUomModel,
                openUomModel,
                closeUomModel,
                isClassificationModel,
                openClassificationModel,
                closeClassificationModel,
                isDispatchAddressModel,
                openDispatchAddressModel,
                closeDispatchAddressModel,
                isDispatchFromModel,
                openDispatchFromModel,
                closeDispatchFromModel,
                isFooterSettingModel,
                openFooterSettingModel,
                closeFooterSettingModel,
                isCustomizeFormatModal,
                openCustomizeFormatModal,
                closeCustomizeFormatModal,
                isHeaderSettingModel,
                openHeaderSettingModel,
                closeHeaderSettingModel,
                isTableSettingModal,
                openTableSettingModal,
                closeTableSettingModal,
                isBillWiseModal,
                openBillWiseModal,
                closeBillWiseModal,
                isInvoiceSettingModal,
                openInvoiceSettingModal,
                closeInvoiceSettingModal,
                isEmailEditModal,
                openEmailEditModal,
                closeEmailEditModal,
                isSloganEditModal,
                openSloganEditModal,
                closeSloganEditModal,
                isLogoEditModal,
                openLogoEditModal,
                closeLogoEditModal,
                isMobileEditModal,
                openMobileEditModal,
                closeMobileEditModal,
                isItemTableModel,
                openItemTableModel,
                closeItemTableModel,
                isRearrangeModel,
                openRearrangeModel,
                closeRearrangeModel,
                isSaleModel,
                openSaleModel,
                closeSaleModel,
                isOpenQuantityModel,
                openQuantityModel,
                closeQuantityModel,
                isNegativeStock,
                openIsNegativeStockModel,
                closeIsNegativeStockModel,
                isConnectToGstModal,
                openConnectToGstModal,
                closeConnectToGstModal,
                isConnectSuccessModal,
                openConnectSuccessModal,
                closeConnectSuccessModal,
                items,
                setItems,
                accountingItems,
                setAccountingItems,
                gstValue,
                setGstValue,
                defaultGst,
                countryId,
                setCountryId,
                stateId,
                setStateId,
                cityId,
                setCityId,
                countryId2,
                setCountryId2,
                stateId2,
                setStateId2,
                cityId2,
                setCityId2,
                invoiceDetail,
                setInvoiceDetail,
                partyAddress,
                setPartyAddress,
                defaultPartyAddress,
                ewayBillDetail,
                setEwayBillDetail,
                otherDetail,
                setOtherDetail,
                defaultOtherDetail,
                gstCalculation,
                setGstCalculation,
                defaultGstCalculation,
                paymentLedgerDetail,
                setPaymentLedgerDetail,
                defaultPaymentDetail,
                classification,
                setClassification,
                defaultClassification,
                additionalGst,
                setAdditionalGst,
                brokerDetail,
                setBrokerDetail,
                defaultBrokerDetail,
                transporterDetail,
                setTransporterDetail,
                defaultTransporterDetail,
                classificationType,
                setClassificationType,
                localDispatchAddress,
                setLocalDispatchAddress,
                gstQuote,
                setGstQuote,
                defaultGstQuote,
                additionalCharges,
                setAdditionalCharges,
                defaultAdditionalCharges,
                tcsRate,
                setTcsRate,
                defaultTcsRate,
                addLessChanges,
                setAddLessChanges,
                defaultAddLessChanges,
                cessValue,
                setCessValue,
                selectedAddress,
                setSelectedAddress,
                partyTitle,
                setPartyTitle,
                modalType,
                setModalType,
                dispatchAddressName,
                setDispatchAddressName,
                sameAsBill,
                setSameAsBill,
                transportTitle,
                setTransportTitle,
                itemType,
                setItemType,
                changeTax,
                setChangeTax,
                grandTotal,
                setGrandTotal,
                mainGrandTotal,
                setMainGrandTotal,
                phoneNumber,
                setPhoneNumber,
                shippingAddress,
                setShippingAddress,
                selectShippingAddress,
                setSelectShippingAddress,
                isShippingFromModel,
                openShippingFromModel,
                closeShippingFromModel,
                isEditCalculation,
                setIsEditCalculation,
                finalAmount,
                setFinalAmount,
                expenseDetail,
                setExpenseDetail,
                taxableValue,
                setTaxableValue,
                invoiceNumber,
                setInvoiceNumber,
                configurationModalName,
                setConfigurationModalName,
                configurationHeaderList,
                setConfigurationHeaderList,
                configurationURL,
                setConfigurationURL,
                configurationTableList,
                setConfigurationTableList,
                configurationFooterList,
                setConfigurationFooterList,
                isShowDocumentPrefixes,
                setIsShowDocumentPrefixes,
                isCreateParty,
                setIsCreateParty,
                isIGSTCalculation,
                setIsIGSTCalculation,
                isSGSTCalculation,
                setIsSGSTCalculation,
                purchaseInvoice,
                setPurchaseInvoice,
                loader,
                setLoader,
                checkGroupLedgerType,
                setCheckGroupLedgerType,
                isLocationOfAsset,
                openLocationOfAssetModel,
                closeLocationOfAssetModel,
                quantityId,
                setQuantityId,
                GSTError,
                setGSTError,
                isTcsAmountChange,
                setIsTcsAmountChange,
                isChangedTcs,
                setIsChangedTcs,
                isShowSelectedService,
                openSelectedServiceModel,
                closeSelectedServiceModel,
                showItemPriceChangedModal,
                setShowItemPriceChangedModal,
                changeItemPrice,
                setChangeItemPrice,
                isConfirmed,
                setIsConfirmed,
                showInvoice,
                setShowInvoice,
                updateParty_quotesOptions,
                setUpdateParty_quotesOptions,
                isChangePartyId,
                setIsChangePartyId,
                isChangeParty,
                setIsChangeParty,
                isChangeGroupType,
                setIsChangeGroupType,
                isFocusedInvoiceNumber,
                setIsFocusedInvoiceNumber,
                isShowSelectedPurchaseNumber,
                setIsShowSelectedPurchaseNumber,
                openSelectedPurchaseNumberModel,
                closeSelectedPurchaseNumberModel,
                viewRecurringInvoice,
                openviewRecurringInvoiceModel,
                closeviewRecurringInvoiceModel,
                showPurchaseNumber,
                setShowPurchaseNumber,
                defaultItem,
                defaultAccountingItem,
                updatePurchaseNumberOptions,
                setUpdatePurchaseNumberOptions,
                dispatchAddressId,
                setDispatchAddressId,
                invoiceValue,
                setInvoiceValue,
                deleteTransaction,
                openDeleteTransactionModel,
                closeDeleteTransactionModel,
                showDeleteWarningModel,
                setShowDeleteWarningModel,
                itemOnFly,
                setItemOnFly,
                isCheckGstType,
                setIsCheckGstType,
                isShowItemStockModel,
                openStockModel,
                closeStockModel,
                customHeaderList,
                setCustomHeaderList,
                isDisable,
                setIsDisable,
                ocrInvoiceDetail,
                setOcrInvoiceDetail,
                isOcrDetailExist,
                openOcrNotExistModel,
                closeOcrNotExistModel,
                ocrResponse,
                setOcrResponse,
                selectedAdvancePayment,
                setSelectedAdvancePayment,
                showPaymentTable,
                setShowPaymentTable,
                printSettingLabel,
                setPrintSettingLabel,
                selectTransactionType,
                setSelectTransactionType,
                selectPdfFormat,
                setSelectPdfFormat,
                addCustomField,
                setAddCustomField,
                storeSettingEditDetail,
                setStoreSettingEditDetail,
                selectedIndex,
                setSelectedIndex,
                countryName,
                setCountryName,
                stateName,
                setStateName,
                cityName,
                setCityName,
                defaultRecurringInvoiceDetails,
                recurringInvoiceDetails,
                setRecurringInvoiceDetails,
                isDescription,
                setIsDescription,
                isInitialDataLoaded,
                setIsInitialDataLoaded,
                isInitialDataLoaded2,
                setIsInitialDataLoaded2,
                userPermission,
                setUserPermission,
                customHeaderListTransaction,
                setCustomHeaderListTransaction,
                customFieldListTransaction,
                setCustomFieldListTransaction,
                customFieldTransactionCheckBox,
                setCustomFieldTransactionCheckBox,
                hasUnsavedChanges,
                setHasUnsavedChanges,
                unsavedBackUrl,
                setUnsavedBackUrl,
                isFieldsChanges,
                setisFieldsChanges,
                isBackButtonClick,
                setIsBackButtonClick,
                transactionShippingAddressId,
                setTransactionShippingAddressId,
                isShippingAddressModel,
                setIsShippingAddressModel,
                notExistItems,
                setNotExistItems,
                notExistParty,
                setNotExistParty,
                thermalPageSize,
                setThermalPageSize,
                isNewAddressforLedgerMaster,
                setIsNewAddressforLedgerMaster,
                isChangeShippingAddress,
                setIsChangeShippingAddress,
                defaultCustomFieldCheckBox,
                getSingleCustomField,
                setGetSingleCustomField,
                getSingleCustomHeader,
                setGetSingleCustomHeader,
                dynamicVariables,
                setDynamicVariables,
                defaultInvoiceDetail,
                saveFeature,
                setSaveFeature,
                isBankStateMentModel,
                openIsBankStateMentModal,
                closeIsBankStateMentModal,
                selectInvoiceType,
                openSelectInvoiceTypeModel,
                closeSelectInvoiceTypeModel,
                itemIndex,
                setItemIndex,
                customFieldItemTransactionCheckBox,
                setCustomFieldItemTransactionCheckBox,
                defaultItemCustomFieldCheckBox,
                barcodeSize,
                setBarcodeSize,
                isRearrangeCustomModel,
                openIsRearrangeCustomModel,
                closeIsRearrangeCustomModel,
                isEditModel,
                setIsEditModel,
                isrenderDuplicateTitle,
                setIsrenderDuplicateTitle,
                itemTableCustomFieldList,
                setItemTableCustomFieldList,
                dragHeaderList,
                setDragHeaderList,
                isDeleteCustomItem,
                setIsDeleteCustomItem,
                isCustomFormula,
                openCustomFormulaModel,
                closeCustomFormulaModel,
                customFieldItemMaster,
                setCustomFieldItemMaster,
                isManageCustomItemMaster,
                setIsManageCustomItemMaster,
                isCustomFieldItemModel,
                setIsCustomFieldItemModel,
                isCustomFieldCalculation,
                setIsCustomFieldCalculation,
                customItemConfigurationList,
                setCustomItemConfigurationList,
                customItemMasterConfigurationList,
                setCustomItemMasterConfigurationList,
                displayFormula,
                setDisplayFormula,
                backendFormula,
                setBackendFormula,
                customFieldItemMasterList,
                setCustomFieldItemMasterList,
                storeAddedVariableId,
                setStoreAddedVariableId,
                partyLedgerTrigger,
                setPartyLedgerTrigger
            }}
        >
            {children}
        </StateContext.Provider>
    );
};
