//API Base URL
export const apiBaseURL = {
    BROKER_LIST: "/broker-list",
    BROKER_DETAIL: "broker-masters-details",
    BROKER: "/broker-masters",
    SINGLE_BROKER_DETAILS: "/get-broker-details",
    PARTY_LIST: "/party-list",
    TRANSPORT_LIST: "/transport-list",
    TRANSPORT: "/transport-masters",
    SALE_INVOICE: "/sale-invoice-number",
    PREV_NEXT_URL: "get-previous-next-transactions",
    SALE_RETURN_INVOICE: "/sale-return-invoice-number",
    INCOME_CREDIT_INVOICE: "income-credit-note-invoice-number",
    INCOME_DEBIT_INVOICE: "debit-note-invoice-number",
    EXPENSE_CREDIT_INVOICE: "expense-credit-note-invoice-number",
    EXPENSE_DEBIT_INVOICE: "expense-debit-note-invoice-number",
    PURCHASE_ORDER_INVOICE: "purchase-order-invoice-number",
    PURCHASE_INVOICE: "purchase-invoice-number",
    PURCHASE_TRANSACTION: "purchase-transactions",
    PURCHASE_NUMBER_LIST: "purchase-order-number-list/1",
    ITEM_LIST: "/item-list",
    ITEM_DETAIL: "item-masters-details",
    ITEM_DETAILS: "item-details",
    ITEM: "/item-masters",
    ITEM_LEDGER: "/item-details-ledger-list",
    LEDGER: "/ledgers",
    PAYMENT_LEDGER_LIST: "/payment-ledger-list",
    LEDGER_DETAIL: "/ledgers-details",
    LEDGER_GROUP_DETAIL: "/ledgers-group-details/",
    LOCATION_OF_ASSETS: "store-location-of-assets",
    GST_RATE: "/item-details-gst-rate-list",
    GST_CESS_RATE: "get-gst-cess-rate",
    PARTY_DETAIL: "/party-details",
    TCS_RATE_LIST: "/tcs-rate-list",
    TCS_TAX_DATA: "/get-tcs-tax-data",
    TDS_TAX_DATA: "/get-tds-tax-data",
    TDS_TCS_RATE: "tds-tcs-details",
    TDS_RATE_LIST: "/tds-rate-list",
    TCS_GROUP_LIST: "/tcs-group-list",
    TDS_GROUP_LIST: "/tds-group-list",
    DISPATCH_ADDRESS: "/dispatch-address",
    DISPATCH_ADDRESS_LIST: "/dispatch-address-list",
    ADD_LESS_LEDGER_LIST: "/add-less-ledger-list",
    ADDITIONAL_CHARGES_LEDGER_LIST: "/additional-charges-ledger-list",
    SALE_PDF: "sale-pdf",
    SALE_CONFIGURATION: "sale-configuration",
    ESTIMATE_CONFIGURATION: "income-estimate-quote-configuration",
    DELIVERY_CHALLAN_CONFIGURATION: "delivery-challan-configuration",
    SALE_RETURN_CONFIGURATION: "sale-return-configuration",
    INCOME_CREDIT_NOTE_CONFIGURATION: "income-credit-note-configuration",
    INCOME_DEBIT_NOTE_CONFIGURATION: "income-debit-note-configuration",
    PURCHASE_CONFIGURATION: "purchase-configuration",
    PURCHASE_ORDER_CONFIGURATION: "purchase-order-configuration",
    PURCHASE_RETURN_CONFIGURATION: "purchase-return-configuration",
    EXPENSE_CREDIT_NOTE_CONFIGURATION: "expense-credit-note-configuration",
    EXPENSE_DEBIT_NOTE_CONFIGURATION: "expense-debit-note-configuration",
    RECURRING_INVOICE_CONFIGURATION: "recurring-invoice-configuration",
    NEGATIVE_STOCK: "is-warn-on-negative-stock/",
    CLASSIFICATION_NATURE_LIST: "classification-nature-type",
    UNIT_LIST: "item-details-unit-list",
    FETCH_GST_DETAIL: "company/get-gst-information-api",
    FETCH_ENTITY_DETAIL: "get-entity-type",
    COMPANY_DETAIL: "company-details",
    SALE_TRANSITION: "sale-transactions",
    ESTIMATE_SALE_TRANSITION: "sale-transaction",
    ESTIMATE: "estimate",
    SALE_TRANSACTION_DETAIL: "sale-transactions-details",
    PURCHASE_TRANSACTION_DETAIL: "purchase-transactions-details",
    CHALLAN: "challan",
    SALE_RETURN_TRANSACTION: "sale-return-transactions",
    INCOME_CN: "income-credit-note-transactions",
    INCOME_DN: "income-debit-notes",
    EXPENSE_CN: "expense-credit-note-transactions",
    EXPENSE_DN: "expense-debit-note-transactions",
    PAYMENT_MODE: "payment-mode-list",
    INCOME_EXPENSE_TYPE: "get-income-or-expense-type",
    ADD_PURCHASE_ORDER: "purchase-order-transactions",
    MULTI_PURCHASE_ORDER: "purchase-transaction/purchase-order",
    PURCHASE_LIST: "purchase-order-titles-list",
    PURCHASE_RETURN: "purchase-return-transactions",
    PURCHASE_RETURN_INVOICE: "purchase-return-invoice-number",
    INCOME_EXPENSE_TYPE: "get-income-or-expense-type",
    ESTIMATE_QUOTE: "income-estimate-quote-transactions",
    ESTIMATE_QUOTE_INVOICE: "income-estimate-quote-invoice-number",
    ESTIMATE_QUOTE_LIST: "estimate-quote-titles-list",
    SHIPPING_ADDRESS_LIST: "shipping-address-list",
    SELECT_SHIPPING_ADDRESS: "update-selected-shipping-address",
    SHIPPING_ADDRESS: "shipping-address",
    DELIVERY_CHALLAN_INVOICE_NUMBER: "delivery-challan-invoice-number",
    DELIVERY_CHALLAN: "delivery-challan-transactions",
    ITEM_GROUP_LIST: "get-item-groups",
    LEDGER_GROUP_LIST: "get-ledger-groups",
    ITEM_GROUP: "item-group",
    LEDGER_GROUP: "ledgers-group",
    UNIT_LIST: "get-unit-of-measurement",
    UNIT_OF_MEASUREMENT: "unit-of-measurement",
    CHECK_GST_NUMBER_EXISTS: "check-gst-number-exists",
    PRICE_LIST: "price-lists",
    DELETE_MEDIA: "delete-ledger-media",
    E_INVOICE: "generate-e-invoice",
    CHECK_CREDIT_LIMIT: "check-customer-limit-amount",
    CAPITAL_RATIO: "get-holding-profit-ratio",
    ITEM_STOCK_LIST: "item-stock-list",
    ITEM_STOCK: "item-stock",
    // custom field
    CUSTOM_FIELD_TYPE: "custom-fields/types",
    CUSTOM_FIELD: "custom-fields",
    //item-master
    CUSTOM_FIELD_ITEM_MASTER: "custom-field-item-master",
    CUSTOM_FIELD_ITEM_MASTER_TYPE: "custom-field-item-master/types",
    // rearrange
    REARRANGE_ITEMS: "rearrange-items",
    GET_UNSETTLED_INVOICES:"get-unsettled-invoices",
    ITEM_STOCK_LIST:"item-stock-list",
    ITEM_STOCK:"item-stock",
    //general setting
    GENERAL_SETTING:"general-setting",
    EWAY_BILL_SETTING:"e-way-bill-setting",
    CHEQUE_PRINTING_SETTING:"cheque-printing-setting",
    PRINT_SETTING:'get-print-settings',
    PRINT_HEADER_SETTING: "pdf-header-setting",
    PRINT_DETAIL_SETTING: "pdf-details-setting",
    PRINT_FOOTER_SETTING: "pdf-footer-setting",
    CUSTOMIZE_FORMAT: "pdf-adjustments",
    HEADER_SETTING_OPTION:"update-pdf",
    UPDATE_PDF_LOGO:"update-pdf-logo",
    GET_TRIPLICATE_INVOICE_LABEL:"get-triplicate-invoice-label",
    GET_DUPLICATE_INVOICE_LABEL:"get-duplicate-invoice-label",
    UPDATE_TRIPLICATE_INVOICE_LABEL:"update-triplicate-invoice-label",
    UPDATE_DUPLICATE_INVOICE_LABEL:"update-duplicate-invoice-label",
    SAVE_PDF_TEMPLETE:"save-pdf-template",
    RESET_TO_ORIGINAL:"reset-pdf-adjustments",
    UPDATE_PDF_SIGNATURE:"update-pdf-signature",
    UPDATE_PDF_SLOGAN:"update-pdf-slogan",
    UPDATE_PDF_MOBILE_NUMBER:"update-pdf-mobile-number",
    UPDATE_PDF_EMAIL:"update-pdf-email",
    PRINT_SETTING_TOGGLE: "update-print-show-hide-setting",
    PDF_HEADER_COMPANY_SETTING: "pdf-company-setting",
    GET_PDF_TEMPLETE: "get-pdf-template",
    MAIL_CONFIGURATION: "mail-configuration",
    AUTO_SEND_EMAIL: "auto-send-email",
    UPDATE_PDF_FORMAT: "update-pdf-format",
    REMOVE_COMPANY_LOGO:'remove-company-logo',
    REMOVE_COMPANY_SIGNATURE:'remove-company-signature',
    GET_PDF_NAME:'get-prop-name',
    UPDATE_PDF_NAME:'update-prop-name',
    WHATSAPP_CONFIGURATION: "whatsapp-configuration",
    PDF_PREVIEW: "pdf-preview",
    AUTO_SEND_WHATSAPP: "auto-send-whatsapp",
    WHATSAPP_AUTO_PAYMENT_REMINDER_STATUS: "whatsapp-auto-payment-reminder-status",
    ADD_WHATSAPP_DEVICE: "add-whatsapp-device",
    UPDATE_DEVICE_NAME: "update-device-name",
    REMOVE_WHATSAPP_DEVICE: "remove-whatsapp-device",
    WHATSAPP_CONNECTED_DEVICES: "whatsapp-connected-devices",
    CONNECT_DEVICE_WISE_CONFIGURSTION: "connect-device-wise-configuration",
    // cheque print
    PURCHASE_OCR:"ocr",
    OCR_PURCHASE: "/ocr/purchase",
    DASHBOARD:"dashboard",
    GET_REPORT_BUTTON:"get-report-button",
    ADD_REPORT_BUTTON_LIST: "add-report-button-list",
    ADD_REPORT_BUTTON: "add-report-button",
    REMOVE_REPORT_BUTTON: "remove-report-button",
    SALE_PURCHASE_DATA: "dashboard/sale-purchase-data",
    SALES_COLLECTION_DATA: "dashboard/sales-collection-data",
    PURCHASES_PAYMENT_DATA: "dashboard/purchases-payment-data",
    CASH_FLOW: "dashboard/cash-flow",
    SUGGESTION: "dashboard/suggestion",
    WHATS_NEW_DATA: "dashboard/whats-new-data",
    CUSTOMER_MASTER_REPORT:"/customer-master-report",
    CUSTOMER_MASTER_COLUMN_SELECTOR:"column-selector/customer-master-report",
    EXPORT_CUSTOMER_MASTER_REPORT:"export/customer-master-report",
    SUPPLIER_MASTER_REPORT:"supplier-master-report",
    SUPPLIER_MASTER_COLUMN_SELECTOR:"column-selector/supplier-master-report",
    EXPORT_SUPPLIER_MASTER_REPORT:"export/supplier-master-report",
    WALKTHROUGH: "dashboard/walkthrough",
    BOOK_DEMO: "dashboard/book-demo",
    COMPANY_SETTING: "dashboard/company-setting",
    STATE_LIST:"states",
    CITY_LIST:"cities",
    CURRENCY_LIST:"get-currency-list",
    BANK_LEDGER_LIST:"bank-ledger-list",
    OCR_STATEMENT_BANK_POSTED:"ocr-statement-bank-posted",
    OCR_STATEMENT_BANK_BULK_POSTED:"ocr-statement-bank/bulk-posted",
    ESTIMATE_ORDER_TITLE:"estimate-quote-title",
    PURCHASE_ORDER_TITLE:"purchase-order-title",
    BARCODE_ITEM_LIST:"barcode-item-list",
    BARCODE_ITEM_GROUP_LIST:"barcode-item-group-list",
    BARCODE_ITEM_DETAILS:"barcode-item-details",
    USER_PERMISSION:"user-permissions",
    RECURRING_INVOICE:"recurring-invoices",
    RECURRING_INVOICE_PDF_PREVIEW:"recurring-invoices-pdf-preview",
    PARTY_DETAIL_RECURRING_INVOICE:"recurring-invoices-parties",
    RECURRING_INVOICE_DESCRIPTION_TEMPLATE:"recurring-invoices-templates",
    CURRENCY_LIST:"get-currency-list",
    //custom field in item table
    CUSTOM_FIELD_ITEM:"custom-fields-item",
    CUSTOM_FIELD_ITEM_TYPE:"custom-fields-item/types",
    CUSTOM_FIELD_UPDATE_STATUS:"custom-fields-item/update-status",
    CUSTOM_FIELD_ITEM_MASTER_UPDATE_STATUS:"custom-field-item-master/update-status",
    CUSTOM_FIELD_TRANSACTION_UPDATE_STATUS:"custom-field-item-master/update-transaction-status",
    LEDGER_CUSTOM_FIELD:"ledger-custom-field",
    LEDGER_CUSTOM_FIELD_TYPE:"ledger-custom-field-type",
    VASTRA:"vastra",
    RECURRING_INVOICE_PDF_DOWNLOAD:"/company/sale-preview-pdf-download",
    DELIVERY_CHALLAN_COLUMNS:"/vastra/delivery-challans-columns",
    DELIVERY_CHALLAN_VASTRA:"/vastra/delivery-challans",
    DELIVERY_CHALLAN_DESTROY:"/vastra/delivery-challans-destroy",
    FETCH_DELIVERY_CHALLANS:"/vastra/fetch-delivery-challans",
    CREATE_SALE_FROM_DELIVERY:"/vastra/create-sale-from-delivery",
    CHECK_TRANSACTION_EXISTS:"/vastra/check-transaction-exists",
    GET_SWIFT_CODE_LABEL:"get-swift-code-label",
    UPDATE_SWIFT_CODE_LABEL:"update-swift-code-label",
    OCR_BANK_NOTE_UPDATE: "ocr-bank-note-update",
    UPDATE_OCR_BANK_STATEMENT:"update-ocr-bank-statement",
    OCR_STATEMENT_UPDATE_BULK:"ocr-statement-ledgers/update-bulk",
    OCR_STATEMENT_DESTROY:"ocr-statement-destroy",
    OCR_STATEMENT_RECEIPT_SETTLE:"ocr-statement-receipt-settle",
    OCR_STATEMENT_PAYMENT_SETTLE:"ocr-statement-payment-settle",
    OCR_STATEMENT_SETTLE:"ocr-statement-settle",
    RECURRING_INVOICE_DYNAMIC_VARIABLE:"recurring-invoices-dynamic-variables",
    RECURRING_INVOICE_PARTY_LIST:"recurring-invoices-party-list",
    RECURRING_INVOICE_PARTY_GROUP_LIST:"recurring-invoices-party-group-list",
    TOP_SELLING_ITEMS:"top-selling-items",
    LEAST_SELLING_ITEMS:"least-selling-items",
    LEDGER_BILL_WISH_EXCEL:"ledger-bill-wish-excel",
    CHECK_RECURRING_INVOICE_EXISTS:"check-recurring-invoice-exists",
    CUSTOM_FIELD_ITEM_NUMBER_INPUT:"custom-fields-item/number-inputs",
    CUSTOM_FIELD_ITEM_FORMULA:"custom-fields-item/formula",
    DELETE_CUSTOM_FIELD_ITEM_FORMULA:"custom-field-item-master/delete-formula",
    LOW_STOCK_REPORT_ITEMS:"low-stock-report-items",
    BARCODE_SETTING:"barcode-setting",
    ONE_ONE_ZA_CONFIGURATIONS:"11za-configurations",
    ONE_ONE_ZA_TEMPLATES:"11za-templates",
    ONE_ONE_ZA_TEMPLATE:"11za-template",
    ONE_ONE_ZA_TEMPLATE_NAME_LIST:"11za-templates-name-list",
    ONE_ONE_ZA_TEMPLATE_BODY:"11za-templates-body",
    CESS_RATE:"cess-rate",
    PRINTER_SIZE: "printer-size",
    PAYMENT_MODES: "payment-modes",
    ITEM_MASTERS_CONFIGURATION: "item-masters-configuration",
    GST_LOGIN: "gst-login",
    GSTR_2B_FETCH_DATA: "fetch-data",
    GSTR_2B_SUMMARY: "gstr-2b-summary",
    GSTR_2B_DETAILS: "gstr-2b-details",
    CHECK_GST_LOGIN: "check-gst-login",
};

export const ROUTES = {
    SALES: "/company/sales",
    LEDGERS: "/company/ledgers",
    ITEM_MASTERS: "/company/item-masters",
    SALE_RETURNS: "/company/sale-returns",
    INCOME_CREDIT_NOTES: "/company/income-credit-notes",
    INCOME_DEBIT_NOTES: "/company/income-debit-notes",
    EXPENSE_DEBIT_NOTES: "/company/expense-debit-notes",
    INCOME_ESTIMATE_QUOTE: "/company/income-estimate-quote",
    INCOME_ESTIMATE_QUOTE_CREATE: "/company/income-estimate-quote-create",
    PURCHASE_ORDER: "/company/purchase-order",
    PURCHASE_RETURNS: "/company/purchase-returns",
    DELIVERY_CHALLAN: "/company/delivery-challan",
    PURCHASES: "/company/purchases",
    EXPENSE_CREDIT_NOTES: "/company/expense-credit-notes",
    CREATE_SALE_DELIVERY_CHALLAN: "/company/create-sale-delivery-challan",
    SALES_CREATE_FROM_CHALLAN: "/company/sales-create",
    PURCHASES_TO_SALE: "/company/purchase-sale",
    PURCHASE_CREATE_FROM_ORDER: "/company/purchase-create",
    SETTING: "/company/setting",
    CUSTOMER_MASTER: "/company/customer-master",
    SUPPLIER_MASTER: "/company/supplier-master",
    PRINT_BARCODE: "/company/print-barcode",
    INCOME_DEBIT_NOTES_CREATE: "/company/income-debit-notes-create",
    EXPENSE_CREDIT_NOTES_CREATE: "/company/expense-credit-notes-create",
    EXPENSE_DEBIT_NOTES_CREATE: "/company/expense-debit-notes-create",
    INCOME_CREDIT_NOTES_CREATE: "/company/income-credit-notes-create",
    SALE_RETURNS_CREATE: "/company/sale-returns-create",
    PURCHASE_ORDER_CREATE: "/company/purchase-order-create",
    PURCHASE_RETURNS_CREATE: "/company/purchase-returns-create",
    PURCHASE_OCR: "/company/import-documents",
    PURCHASE_BANK_OCR: "/company/import-bank-documents/create",
    CREATE_SALE_RETURN: "/company/create-sale-return",
    CREATE_PURCHASE_RETURN: "/company/create-purchase-return",
    DASHBOARD:"/company/dashboard",
    WHATS_NEW:"/company/whats-new",
    VASTRA:"/company/third-party",
    THIRD_PARTY_VASTRA:"/company/third-party/vastra",
    THIRD_PARTY_11ZA:"/company/third-party/11za",
    RECURRING_MASTER: "/company/recurring-invoices",
    TOP_SELLING_ITEMS: "/company/top-selling-items",
    LEAST_SELLING_ITEMS: "/company/least-selling-items",
    LOW_STOCK_REPORT: "/company/low-stock-report",
    GENERAL_SETTING: "/company/general-settings",
    E_WHY_BILL_E_INVOICE_SETTING: "/company/ewaybill-einvoice-settings",
    PRINT_SETTING: "/company/print-settings",
    EMAIL_AND_WHATSAPP_CONFIGURATION: "/company/email-and-whatsapp-configuration",
    CHEQUE_PRINTING: "/company/cheque-printing",
    GSTR_2B: "/company/gstr-2b",
};

export const ACTION_TYPES = {
    ADD_BROKER: "ADD_BROKER",
    GET_ALL_BROKER: "GET_ALL_BROKER",
    GET_BROKER_DETAIL: "GET_BROKER_DETAIL",
    UPDATE_BROKER: "UPDATE_BROKER",
    REMOVE_BROKER: "REMOVE_BROKER",

    ADD_HEADER: "ADD_HEADER",
    GET_TABLE_HEADER: "GET_TABLE_HEADER",
    REMOVE_HEADER: "REMOVE_HEADER",

    GET_GST_RATE: "GET_GST_RATE",

    GET_INVOICE: "GET_INVOICE",

    ADD_LEDGER: "ADD_LEDGER",
    GET_ITEM_LEDGER: "GET_ITEM_LEDGER",
    GET_ITEM_LEDGER_LIST: "GET_ITEM_LEDGER_LIST",
    ITEM_LEDGER_DETAIL: "ITEM_LEDGER_DETAIL",
    GET_LEDGER_DETAILS: "GET_LEDGER_DETAILS",
    UPDATE_LEDGER: "UPDATE_LEDGER",
    REMOVE_LEDGER: "REMOVE_LEDGER",

    ADD_ITEM_DATA: "ADD_ITEM_DATA",
    GET_ITEM_LIST: "GET_ITEM_LIST",
    GET_ITEM_DETAIL: "GET_ITEM_DETAIL",
    UPDATE_ITEM_LIST: "UPDATE_ITEM_LIST",
    ITEM_DETAILS: "ITEM_DETAILS",

    UNIT_LIST: "UNIT_LIST",

    GET_PARTIES: "GET_PARTIES",
    PARTY_DETAIL: "PARTY_DETAIL",

    ADD_TRANSPORT: "ADD_TRANSPORT",
    GET_ID_TRANSPORT: "GET_ID_TRANSPORT",
    GET_TRANSPORTER: "GET_TRANSPORTER",
    UPDATE_TRANSPORT: "UPDATE_TRANSPORT",
    REMOVE_TRANSPORT: "REMOVE_TRANSPORT",

    PAYMENT_LEDGER_LIST: "PAYMENT_LEDGER_LIST",
    PAYMENT_MODE: "PAYMENT_MODE",

    ADD_ADD_LESS_LEDGER: "ADD_ADD_LESS_LEDGER",
    GET_ADD_LESS_LEDGER: "GET_ADD_LESS_LEDGER",
    ADD_ADDITIONAL_CHARGES_LEDGER: "ADD_ADDITIONAL_CHARGES_LEDGER",
    GET_ADDITIONAL_CHARGES_LEDGER: "GET_ADDITIONAL_CHARGES_LEDGER",

    TCS_RATE_LIST: "TCS_RATE_LIST",
    TDS_RATE_LIST: "TDS_RATE_LIST",

    DISPATCH_ADDRESS_LIST: "DISPATCH_ADDRESS_LIST",
    DISPATCH_ADDRESS: "DISPATCH_ADDRESS",

    ADDITIONAL_CHARGES_LEDGER_LIST: "ADDITIONAL_CHARGES_LEDGER_LIST",

    ADD_LESS_LEDGER_LIST: "ADD_LESS_LEDGER_LIST",

    SALE_PDF: "SALE_PDF",
    SALE_TRANSITION: "saleTransaction",
    SALE_DETAIL: "SALE_DETAIL",

    CLASSIFICATION_NATURE_LIST: "CLASSIFICATION_NATURE_LIST",

    COMPANY_DETAIL: "COMPANY_DETAIL",

    UPDATE_SALE_ITEMS: "UPDATE_SALE_ITEMS",
    UPDATE_ACC_ITEMS: "UPDATE_ACC_ITEMS",

    SALE_CONFIGURATION: "SALE_CONFIGURATION",
    UPDATE_SALE_CONFIGURATION: "UPDATE_SALE_CONFIGURATION",
    RESET_STATE: "RESET_STATE",

    ERROR: "ERROR",
};

export const toastType = {
    ADD_TOAST: "ADD_TOAST",
    REMOVE_TOAST: "REMOVE_TOAST",
    ERROR: "error",
};

export const LedgerType = {
    PARTY_LEDGER: "party-ledger",
    ITEM_LEDGER: "item-ledger",
    ADDITIONAL_LEDGER: "additional-ledger",
    ADD_LESS_LEDGER: "add-less-ledger",
    PAYMENT_LEDGER: "payment-details-ledger",
    BANK_LEDGER: "bank-ledger",
    TCS_LEDGER: "tcs-ledger",
    TDS_LEDGER: "tds-ledger",
};

export const ADDRESS_TYPE = {
    BILLING_ADDRESS: 1,
    SHIPPING_ADDRESS: 2,
    DISPATCH_ADDRESS: 3,
};

export const CALCULATION_ON_TYPE = {
    INVOICE_VALUE: 1,
    TAXABLE_VALUE: 2,
};

export const GST_TYPES_ID = {
    GST_EXEMPT: 1,
    GST_NA: 12,
};


export const UPDATE_SETTING_TYPE = {
    COMPANY_SETTING: 1,
    PRINT_SETTING:2
}
export const TRANSACTION_ITEM_TYPE = {
    ITEM: 1,
    ACCONTING: 2,
};

export const TRANSACTION_TYPE = {
    SALE: 1,
    SALE_RETURN: 2,
    INCOME_DEBIT_NOTE: 3,
    INCOME_CREDIT_NOTE: 4,
    INCOME_ESTIMATE_QUOTE: 5,
    DELIVERY_CHALLAN: 6,
    PURCHASE_ORDER: 7,
    PURCHASE: 8,
    PURCHASE_RETURN: 9,
    EXPENSE_DEBIT_NOTE: 10,
    EXPENSE_CREDIT_NOTE: 11,
    RECURRING:12
}
export const CUSTOM_FIELD_ITEM_TRANSACTION_TYPE = {
    SALE: 1,
    SALE_RETURN: 2,
    INCOME_DEBIT_NOTE: 3,
    INCOME_CREDIT_NOTE: 4,
    INCOME_ESTIMATE_QUOTE: 15,
    DELIVERY_CHALLAN: 16,
    PURCHASE_ORDER: 17,
    PURCHASE: 5,
    PURCHASE_RETURN: 6,
    EXPENSE_DEBIT_NOTE: 7,
    EXPENSE_CREDIT_NOTE: 8,
    RECURRING:18
}

export const ADVANCE_PAYMENT_TRANSACTION_TYPE = {
    DELIVERY_CHALLAN:"Delivery Challan",
    SALE_RETURN: "Sale Return",
    INCOME_DEBIT_NOTE: "Income Debit Note",
    INCOME_CREDIT_NOTE: "Income Credit Note",
    EXPENSE_DEBIT_NOTE: "Expense Debit Note",
    EXPENSE_CREDIT_NOTE: "Expense Credit Note",
    SALE: "Sale",
    RECEIPT: "Receipt",
    PURCHASE: "Purchase",
    PURCHASE_RETURN: "Purchase Return",
    PAYMENT: "Payment",
    JOURNAL: "Journal",
}

export const SHIPPING_ADDRESS_TYPE_LIST = {
    SALE:1,
    PURCHASE:2,
    INCOME_ESTIMATE_QUOTE:3,
    DELIVERY_CHALLAN:4,
    SALE_RETURN:5,
    INCOME_DEBIT_NOTE:6,
    INCOME_CREDIT_NOTE:7,
    PURCHASE_ORDER:8,
    PURCHASE_RETURN:9,
    EXPENSE_DEBIT_NOTE:10,
    EXPENSE_CREDIT_NOTE:11,
    RECURRING:1

}
export const TABLE_HEADER_TYPE = {
    ITEM: "Item",
    LEDGER: "Ledger",
    UOM: "UOM",
    HSN_SAC: "HSN-SAC",
    QUANTITY: "Quantity",
    FREE_QUANTITY: "Free Quantity",
    MRP: "MRP",
    UNIT_PRICE: "Unit Price",
    DISCOUNT_1: "Discount 1",
    DISCOUNT_2: "Discount 2",
    TAX_RATE: "GST",
    AMOUNT:"Amount"
};

export const USER_PERMISSION = {
    ADD_BROKER_MASTER: "company_add_new_broker_masters",
    EDIT_BROKER_MASTER: "company_edit_broker_masters",
    ADD_TRANSPORT_MASTER: "company_add_new_transport_masters",
    EDIT_TRANSPORT_MASTER: "company_edit_transport_masters",
    ADD_LEDGER: "company_add_new_ledgers",
    EDIT_LEDGER: "company_edit_ledgers",
    DELETE_LEDGER: "company_delete_ledgers",
    IMPORT_EXPORT_CUSTOMER_MASTER: "company_import_export_customer_master_report",
    IMPORT_EXPORT_SUPPLIER_MASTER: "company_import_export_supplier_master_report",
    ADD_ITEM_MASTER: "company_add_new_item_masters",
    EDIT_ITEM_MASTER: "company_edit_item_masters",
    VIEW_GENERAL_SETTING:"company_view_general_setting",
    EDIT_GENERAL_SETTING:"company_edit_general_setting",
    VIEW_EWAY_BILL_SETTING:"company_view_ewaybill_and_einvoice_setting",
    EDIT_EWAY_BILL_SETTING:"company_edit_ewaybill_and_einvoice_setting",
    VIEW_PRINT_SETTING: "company_view_print_setting",
    EDIT_PRINT_SETTING: "company_edit_print_setting",
    VIEW_EMAIL_WHATSAPP_SETTING:"company_view_email_and_whatsapp_configuration",
    EDIT_EMAIL_WHATSAPP_SETTING:"company_edit_email_and_whatsapp_configuration",
    VIEW_CHEQUE_PRINT_SETTING:"company_view_cheque_printing",
    EDIT_CHEQUE_PRINT_SETTING:"company_edit_cheque_printing",
};

export const gstRates = [
    {
        id: 12,
        name: "NA",
        rate: 0,
    },
    {
        id: 1,
        name: "Exempt",
        rate: 0,
    },
    {
        id: 2,
        name: "0%",
        rate: 0,
    },
    {
        id: 13,
        name: "0.1%",
        rate: 0.1,
    },
    {
        id: 3,
        name: "0.25%",
        rate: 0.25,
    },
    {
        id: 4,
        name: "1%",
        rate: 1,
    },
    {
        id: 5,
        name: "1.5%",
        rate: 1.5,
    },
    {
        id: 6,
        name: "3%",
        rate: 3,
    },
    {
        id: 7,
        name: "5%",
        rate: 5,
    },
    {
        id: 14,
        name: "6%",
        rate: 6,
    },
    {
        id: 8,
        name: "7.5%",
        rate: 7.5,
    },
    {
        id: 9,
        name: "12%",
        rate: 12,
    },
    {
        id: 15,
        name: "13.8%",
        rate: 13.8,
    },
    {
        id: 10,
        name: "18%",
        rate: 18,
    },
    {
        id: 11,
        name: "28%",
        rate: 28,
    },
];

export const WARRANTY_FIELD_TYPE = 6;

export const transactionTypeMap = {
    "/sales": TRANSACTION_TYPE.SALE,
    "/sale-returns": TRANSACTION_TYPE.SALE_RETURN,
    "/create-sale-return": TRANSACTION_TYPE.SALE_RETURN,
    "/purchase-sale": TRANSACTION_TYPE.SALE,
    "/income-estimate-quote": TRANSACTION_TYPE.INCOME_ESTIMATE_QUOTE,
    "delivery-challan": TRANSACTION_TYPE.DELIVERY_CHALLAN,
    "/income-debit-notes": TRANSACTION_TYPE.INCOME_DEBIT_NOTE,
    "/income-credit-notes": TRANSACTION_TYPE.INCOME_CREDIT_NOTE,
    "/purchase-order": TRANSACTION_TYPE.PURCHASE_ORDER,
    "/purchases": TRANSACTION_TYPE.PURCHASE,
    "/import-documents": TRANSACTION_TYPE.PURCHASE,
    "/purchase-create": TRANSACTION_TYPE.PURCHASE,
    "/purchase-returns": TRANSACTION_TYPE.PURCHASE_RETURN,
    "create-purchase-return": TRANSACTION_TYPE.PURCHASE_RETURN,
    "expense-credit-notes": TRANSACTION_TYPE.EXPENSE_CREDIT_NOTE,
    "/recurring-invoices": TRANSACTION_TYPE.RECURRING,
    "/expense-debit-notes": TRANSACTION_TYPE.EXPENSE_DEBIT_NOTE,
    "expense-debit-notes-create": TRANSACTION_TYPE.EXPENSE_DEBIT_NOTE,
};

export const ITEM_STATUS = {
    ACTIVE: 1,
    IN_ACTIVE: 0
}
