import { createRoot } from "react-dom/client";
import { Provider } from "react-redux";
import MainApp from "./MainApp";
import { PersistGate } from "redux-persist/integration/react";
import { StateProvider } from "./context/StateContext";
import { persistor, store } from "./store/store";
import NavigationGuard from "./app/common/NavigationGuard";
import { flare } from "@flareapp/js";
import { FlareErrorBoundary } from '@flareapp/react';

const container = document.getElementById("react-app");
const root = createRoot(container);

if (process.env.MIX_APP_ENV === 'production' || process.env.MIX_APP_ENV === 'staging') {
    flare.light(process.env.MIX_FLARE_KEY);
}

root.render(
    <StateProvider>
        <Provider store={store} stabilityCheck="never">
            <PersistGate persistor={persistor}>
                <FlareErrorBoundary>
                    <MainApp />
                    <NavigationGuard />
                </FlareErrorBoundary>
            </PersistGate>
        </Provider>
    </StateProvider>
);
