import React, { useContext} from "react";
import { Modal } from "react-bootstrap"
import successfully from "../../../assets/images/correct-tick-img.png";
import { StateContext } from "../../../context/StateContext";

const ConnectSuccessModal = () => {
    const {
        isConnectSuccessModal,
        openConnectSuccessModal,
        closeConnectSuccessModal,
    } = useContext(StateContext);

    return (
        <>
            <button
                type="button"
                className="btn btn-primary btn-sm fs-13"
                onClick={openConnectSuccessModal}

            >
                Connect to GST
            </button>
            <Modal
                show={isConnectSuccessModal}
                onHide={closeConnectSuccessModal}
                className={`modal fade`}
                centered
                size="sm"
            >
                <div className="modal-header bg-white justify-content-end  pt-3 pb-0 px-5 border-0">
                    <button
                        className="btn close-button p-0"
                        onClick={closeConnectSuccessModal}
                        type="button"
                    >
                        &times;
                    </button>
                </div>
                <Modal.Body className="text-center pt-0 px-5 pb-5">
                    <img src={successfully} alt="image" className="img-fluid" />
                    <h4 className="text-primary mb-0 mt-2">Connected Successfully!</h4>
                </Modal.Body>
            </Modal>
        </>
    );
};

export default ConnectSuccessModal;
