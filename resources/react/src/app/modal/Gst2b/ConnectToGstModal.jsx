import React, { useContext, useState, useEffect } from "react";
import { Form, Modal } from "react-bootstrap";
import ConnectSuccessModal from "./ConnectSuccessModal";
import { FormInput } from "../../../components/ui/Input";
import { StateContext } from "../../../context/StateContext";
import { useDispatch, useSelector } from "react-redux";
import { fetchGstLogin, checkGstLogin } from "../../../store/gstr-2b/Gstr2bSlice";

const ConnectToGstModal = ({ setIsLogin }) => {
    const {
        isConnectToGstModal,
        openConnectToGstModal,
        closeConnectToGstModal,
    } = useContext(StateContext);
    const { company } = useSelector(state => state.company);
    
    const [otpGenerated, setOtpGenerated] = useState(false);
    const [isPasswordShow, setIsPasswordShow] = useState(true);
    const [gstUserData, setGstUserData] = useState({
        user_name: "",
        otp: "",
    });
    const dispatch = useDispatch();

    useEffect(() => {
        const checkGstPortalConnection = async () => {
            const response = await dispatch(checkGstLogin());
            if (response?.success && response?.data?.status == 1) {
                setIsLogin(true);
            }
        };
        
        checkGstPortalConnection();
    }, [dispatch, setIsLogin]);

    const handleLoginChanges = (e) => {
        const { name, value } = e.target;
        setGstUserData({
            ...gstUserData,
            [name]: value,
        });
    }

    const handleGenerateOtp = async (e) => {
        e.preventDefault();

        const params = {
            user_name: gstUserData.user_name
        }
        const response = await dispatch(fetchGstLogin(params));

        if (response?.success) {
            if (response?.data?.hideModal) {
                setIsLogin(true);
                closeConnectToGstModal();
            } else {
                setOtpGenerated(true);
            }
        }
    };
    const handleConnectToGst = async (e) => {
        e.preventDefault();

        const params = {
            user_name: gstUserData.user_name,
            otp: gstUserData.otp
        }

        const response = await dispatch(fetchGstLogin(params));
        if (response?.success) {
            if (response?.data?.hideModal) {
                setIsLogin(true);
                closeConnectToGstModal();
            }
        }
    };

    return (
        <>
            <button
                type="button"
                className="btn btn-primary btn-sm fs-13"
                onClick={openConnectToGstModal}
            >
                Connect to GST Portal
            </button>
            <Modal
                show={isConnectToGstModal}
                onHide={closeConnectToGstModal}
                className="connect-to-gst-modal custom-offcanvas-modal fade p-0"
            >
                <Form onSubmit={otpGenerated ? handleConnectToGst : handleGenerateOtp}>
                    <div className="offcanvas-header bg-white">
                        <h3 className="mb-0">Connect to GST portal</h3>
                        <button
                            className="btn close-button p-0"
                            onClick={closeConnectToGstModal}
                            type="button"
                        >
                            &times;
                        </button>
                    </div>
                    <div className="offcanvas-body">
                        <div className="desc-box mb-4">
                            <div className="d-flex flex-column gap-6">
                                <div>
                                    <Form.Group className="position-relative form-floating-group connect-gstr-2b-input">
                                        <FormInput
                                            className="floating-label-input"
                                            type="text"
                                            placeholder=""
                                            name="gst_no"
                                            value={company?.company_tax?.gstin}
                                            readOnly
                                        />
                                        <Form.Label>GSTIN</Form.Label>
                                    </Form.Group>
                                </div>
                                <div>
                                    <Form.Group className="position-relative form-floating-group">
                                        <FormInput
                                            className="floating-label-input"
                                            type="text"
                                            placeholder=""
                                            name="user_name"
                                            value={gstUserData?.user_name}
                                            onChange={handleLoginChanges}

                                        />
                                        <Form.Label className="required">User ID</Form.Label>
                                    </Form.Group>
                                </div>
                                {otpGenerated && (
                                    <div>
                                        <Form.Group>
                                            <div className="input-group flex-nowrap">
                                                <div className="position-relative h-40px w-100 form-floating-group">
                                                    <FormInput
                                                        type={isPasswordShow ? "password" : "text"}
                                                        className="floating-label-input"
                                                        placeholder=""
                                                        name="otp"
                                                        value={gstUserData?.otp}
                                                        onChange={handleLoginChanges}
                                                    />
                                                    <Form.Label className="required">Enter OTP</Form.Label>
                                                </div>
                                                <div onClick={() => setIsPasswordShow(!isPasswordShow)} className="input-group-text h-100 border-0 bg-transparent custom-group-text">
                                                    <i className="fa-solid fa-eye-slash text-gray-700 cursor-pointer" ></i>
                                                </div>
                                            </div>
                                        </Form.Group>
                                        {/* <Form.Group className="position-relative form-floating-group">
                                            <FormInput
                                                className="floating-label-input"
                                                type="password"
                                                placeholder=""
                                            />
                                            <Form.Label className="required">Password</Form.Label>
                                        </Form.Group> */}
                                    </div>
                                )}
                            </div>
                        </div>
                        {otpGenerated && (
                            <div className="text-end">
                                <p className="fw-5 mb-0">Didn't receive OTP?<a href="#!" className="text-orange"> Regenerate OTP</a></p>
                            </div>
                        )}
                        <div className="d-flex gap-3 pt-4">
                            <button
                                type="submit"
                                className={`btn btn-primary btn-sm fs-13 ${otpGenerated ? "d-none" : ""}`}
                            >
                                Generate OTP
                            </button>

                            {otpGenerated && (
                                <ConnectSuccessModal />
                            )}

                            <button
                                type="button"
                                onClick={closeConnectToGstModal}
                                className="btn btn-secondary"
                            >
                                Back
                            </button>
                        </div>

                    </div>

                </Form>
            </Modal>
        </>
    );
};
export default ConnectToGstModal;
