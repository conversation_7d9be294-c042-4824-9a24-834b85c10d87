import { useContext, useEffect, useState } from "react";
import Modal from "react-bootstrap/Modal";
import { useDispatch, useSelector } from "react-redux";
import { StateContext } from "../../../context/StateContext";
import Close from "../../../assets/images/svg/close";
import AddItemMasterCustomFieldModal from "./AddItemMasterCustomField";
import { deleteItemMasterCustomField, fetchItemMasterCustomFieldList, getItemMasterCustomField, updateItemCustomFieldStatus } from "../../../store/configuration/configurationSlice";
import WarningModal from "../../common/WarningModal";
import { replaceIdsWithLabels } from "../../../shared/calculation";
import CustomFieldCalculation from "./CustomFieldCalculation";
import { Form, OverlayTrigger, Tooltip } from "react-bootstrap";
import ReactSelect from "../../../components/ui/ReactSelect";
import useDropdownOption from "../../../shared/dropdownList";
import { updateItemConfiguration } from "../../../store/item/itemSlice";
import { errorToast } from "../../../store/actions/toastAction";
import { toastType } from "../../../constants";

const CustomfieldItemModel = ({ handleClose, id, formik, transaction_type }) => {
    const dispatch = useDispatch();
    const {item} = useSelector(state => state);
    const {
        isCustomFieldItemModel,
        setIsCustomFieldItemModel,
        isCustomFieldModel,
        openCustomFieldModel,
        closeCustomFieldModel,
        isEditModel,
        setIsEditModel,
        customFieldItemMaster,
        setCustomFieldItemMaster,
        customItemConfigurationList,
        setCustomItemConfigurationList,
        setBackendFormula,
        setDisplayFormula,
    } = useContext(StateContext);
    const { unitOfMeasurement } = useDropdownOption();
    const [isDeleteModal, setIsDeleteModal] = useState(false);
    const [isCustomFieldCalculationQty, setIsCustomFieldCalculationQty] = useState(false)
    const [selectedCustomFieldFormula, setSelectedCustomFieldFormula] = useState("");
    const [itemUnitData, setItemUnitData] = useState({
        primary_uom_id: "",
        primary_unit_of_measurement_name: "",
        secondary_uom_id: "",
        secondary_unit_of_measurement_name: "",
        conversion_rate: null,
    });

    const handleOpenDeleteModal = item => {
        setIsDeleteModal(true);
        setCustomFieldItemMaster(item);
    };

    const handleOpenCustomFieldQtyCalculation = item => {
        setIsCustomFieldCalculationQty(true);
        setSelectedCustomFieldFormula(item);
        setCustomFieldItemMaster(item);
        setBackendFormula(item?.default_formula?.formula);
        const DisplayFormula = replaceIdsWithLabels(
            item?.default_formula?.formula,
            customItemConfigurationList
        );
        setDisplayFormula(DisplayFormula ?? "");
    };

    useEffect(() => {
        setItemUnitData({
            primary_uom_id: item?.itemConfigurationData?.primary_uom_id,
            primary_unit_of_measurement_name: item?.itemConfigurationData?.primary_uom?.full_name,
            secondary_uom_id: item?.itemConfigurationData?.secondary_uom_id,
            secondary_unit_of_measurement_name:
                item?.itemConfigurationData?.secondary_uom?.full_name,
            conversion_rate: item?.itemConfigurationData?.conversion_rate,
        });
    }, [item?.itemConfigurationData]);

     const changePrimarydetail = e => {
            if (itemUnitData?.secondary_uom_id !== null && itemUnitData?.secondary_uom_id == e?.value) {
                return dispatch(
                    errorToast({
                        text: "Secondary and Primary Unit can't be same",
                        type: toastType.ERROR,
                    })
                );
            } else {
                setItemUnitData({
                    ...itemUnitData,
                    primary_uom_id: e?.value,
                    primary_unit_of_measurement_name: e?.label,
                });
            }

        };
        const changeSecondarydetail = e => {
            if (itemUnitData?.primary_uom_id !== null && itemUnitData?.primary_uom_id == e?.value) {
                return dispatch(
                    errorToast({
                        text: "Secondary and Primary Unit can't be same",
                        type: toastType.ERROR,
                    })
                );
            } else {
                setItemUnitData({
                    ...itemUnitData,
                    secondary_uom_id: e.value,
                    secondary_unit_of_measurement_name: e.label,
                });
            }
        };

        const handleConversionRateChange = (e) => {
            setItemUnitData({
                ...itemUnitData,
                conversion_rate: e.target.value,
            });
        };

        const handleItemUnitSave = async () => {
            const params = {
                primary_uom_id: itemUnitData?.primary_uom_id,
                secondary_uom_id: itemUnitData?.secondary_uom_id,
                conversion_rate: itemUnitData?.conversion_rate
            }
            if(itemUnitData?.primary_uom_id && itemUnitData?.secondary_uom_id){
                if (itemUnitData?.primary_uom_id == itemUnitData?.secondary_uom_id) {
                    return dispatch(
                        errorToast({
                            text: "Secondary and Primary Unit can't be same",
                            type: toastType.ERROR,
                        })
                    );
                }
            }
            await dispatch(updateItemConfiguration(params));
            handleClose();
            if(!formik.values.id){
            if(itemUnitData?.primary_uom_id){
                formik.setFieldValue("primary_unit_of_measurement", itemUnitData?.primary_uom_id),
                formik.setFieldValue("primary_unit_of_measurement_name", itemUnitData?.primary_unit_of_measurement_name);
            }
            if(itemUnitData?.secondary_uom_id){
                formik.setFieldValue("secondary_unit_of_measurement", itemUnitData?.secondary_uom_id);
                formik.setFieldValue("secondary_unit_of_measurement_name", itemUnitData?.secondary_unit_of_measurement_name);
            }
            if(itemUnitData?.conversion_rate){
                formik.setFieldValue("conversion_rate", itemUnitData?.conversion_rate);
            }
        }

        };

    const handleOpenAddModel = () => {
        setIsEditModel(false);
        openCustomFieldModel();
        dispatch(fetchItemMasterCustomFieldList());
        setCustomFieldItemMaster(null);
    };

    const handleOpenEditModel = async (item) => {
        openCustomFieldModel();
        setIsEditModel(true);
        const customField = await dispatch(getItemMasterCustomField(item?.id));
        setCustomFieldItemMaster(customField);
        dispatch(fetchItemMasterCustomFieldList());
    };

    const handleCloseCalculationModel = () => {
        setIsCustomFieldCalculationQty(false);
        setSelectedCustomFieldFormula(null);
        // setCustomFieldItemMaster(null);
        // setBackendFormula("");
        // setDisplayFormula("");
    }

    const onUpdateCustomField = (type, data) => {
        const response = {
            custom_field_id: data.id,
            status: type
        }
        dispatch(updateItemCustomFieldStatus(response));
        const customField = customItemConfigurationList?.map(item => {
            if (item.id === data.id) {
                return {
                    ...item,
                    status: type,
                    local_status: item?.enable_for_all ? type : false,
                };
            }
            return item;
        });
        // customItemConfigurationList
        setCustomItemConfigurationList(customField);
        formik.setFieldValue(
            "custom_fields",
            customField?.filter(item => item?.status).map(item => item?.id)
        );
    };
    const SortableItem = ({ key, id, item }) => {
        return (
            <div>
                <div
                    key={key}
                    className="d-flex justify-content-between align-items-center gap-3 w-100"
                >
                    <div className="d-flex align-items-center justify-content-between gap-3 w-100">
                        <p className={`mb-0 h5 fw-medium`}>{item?.label_name}</p>
                        <div className="d-flex justify-content-end align-items-center">
                            {item?.label_name === "Quantity" ? (
                                <button
                                    type="button"
                                    className="bg-transparent border-0 p-0 ps-2 pe-2 focus-shadow"
                                    onClick={() => handleOpenCustomFieldQtyCalculation(item)}
                                >
                                    <OverlayTrigger
                                        placement="top"
                                        overlay={<Tooltip id={`tooltip`}>{replaceIdsWithLabels(item?.default_formula?.formula, customItemConfigurationList)}</Tooltip>}
                                    >
                                    <i className="fa-solid fs-4 fa-calculator mx-auto my-3 text-black cursor-pointer ps-1"></i>
                                    </OverlayTrigger>
                                </button>
                            ) : (
                                ""
                            )}
                            {item?.label_name !== "Quantity" ? (
                                <>
                                    <button
                                        type="button"
                                        className="bg-transparent border-0 p-0 ps-2 pe-2 focus-shadow"
                                        onClick={e => handleOpenEditModel(item)}
                                    >
                                        <i className="fas fs-4 fa-edit text-primary mx-auto my-3"></i>
                                    </button>
                                    <button
                                        type="button"
                                        className="bg-transparent border-0 p-0 ps-2 pe-2 focus-shadow"
                                        onClick={() => handleOpenDeleteModal(item)}
                                    >
                                        <i className="fas fs-4 fa-trash-alt text-danger mx-auto my-3"></i>
                                    </button>
                                    <div
                                        className="form-check ms-4 form-switch d-flex gap-2 my-3"
                                        style={{
                                            paddingLeft: "0",
                                        }}
                                    >
                                        <input
                                            className="form-check-input"
                                            style={{
                                                float: "right",
                                                marginLeft: "0",
                                            }}
                                            type="checkbox"
                                            checked={item?.status || false}
                                            onChange={e => onUpdateCustomField(e.target.checked, item)}
                                        />
                                    </div>
                                </>
                            ) : (
                                ""
                            )}
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    const handleCloseCustomFieldModel = () => {
        closeCustomFieldModel();
        setCustomFieldItemMaster(null);
        setIsDeleteModal(false)
        // setIsCustomFieldItemModel(false);
    };

   const handleDeleteCustomHeaderField = () => {
       dispatch(deleteItemMasterCustomField(customFieldItemMaster?.id, handleCloseCustomFieldModel, id, setCustomItemConfigurationList));
       setIsCustomFieldCalculationQty(false);
       setSelectedCustomFieldFormula(null);
       setBackendFormula("");
       setDisplayFormula("");
   };

    return (
        <>
            <Modal
                show={isCustomFieldItemModel}
                onHide={handleClose}
                className="rearrange-item-modal"
                centered
            >
                <div className="modal-dialog-centered modal-xl">
                    <div className="modal-content">
                        <div className="modal-header py-3">
                            <h5 className="modal-title">Item Configuration</h5>
                            <button
                                type="button"
                                className="btn btn-icon btn-sm btn-active-light-primary ms-2 mb-2"
                                onClick={handleClose}
                            >
                                <Close />
                            </button>
                        </div>
                        <div className="model-body desc-main-box">
                            <div className="d-flex align-items-center justify-content-between mt-3">
                                <h3 className="mb-0">Custom Field</h3>
                                <button
                                    className="btn btn-primary btn-sm mb-2"
                                    type="button"
                                    onClick={handleOpenAddModel}
                                    style={{ padding: "6px 14px 6px 10px" }}
                                >
                                    ADD <span className="font-semibold fs-4">+</span>
                                </button>
                                <AddItemMasterCustomFieldModal
                                    show={isCustomFieldModel}
                                    handleClose={handleCloseCustomFieldModel}
                                    id={id}
                                    transaction_type={transaction_type}
                                />
                            </div>
                            <div className="desc-box mb-4">
                                {customItemConfigurationList?.length > 0 ? (
                                    customItemConfigurationList.map((item, index) => (
                                        <SortableItem key={index} id={item?.id} item={item} />
                                    ))
                                ) : (
                                    <div className="text-center">No custom field found</div>
                                )}
                            </div>
                            <div className="desc-box mb-5">
                                <Form.Group>
                                    <div className="input-group flex-nowrap form-group-select mb-4">
                                        <div className="position-relative h-40 w-100  focus-shadow">
                                            <ReactSelect
                                                name="primary_unit_of_measurement"
                                                // required={true}
                                                value={itemUnitData?.primary_uom_id}
                                                options={unitOfMeasurement}
                                                onChange={changePrimarydetail}
                                                radius={true}
                                                islabel={true}
                                                placeholder="Default Primary Unit of Measurement"
                                                defaultLabel="Select Primary Unit"
                                                position={customItemConfigurationList?.length < 7 ? "bottom" : "top"}
                                            />
                                        </div>
                                    </div>
                                </Form.Group>

                                <Form.Group>
                                    <div className="input-group flex-nowrap form-group-select mb-4">
                                        <div className="position-relative h-40 w-100 focus-shadow">
                                            <ReactSelect
                                                name="secondaryUOM"
                                                value={itemUnitData?.secondary_uom_id}
                                                onChange={changeSecondarydetail}
                                                options={unitOfMeasurement}
                                                radius={true}
                                                islabel={true}
                                                placeholder="Default Secondary Unit of Measurement"
                                                defaultLabel="Select Secondary Unit"
                                                position="top"
                                            />
                                        </div>
                                    </div>
                                </Form.Group>

                                <div className=" w-100 flex flex-col gap-2 ">
                                    <label className="form-label fs-6 fw-medium text-gray-900 mb-0">
                                        Conversion Rate
                                    </label>
                                    <div className="d-flex gap-2 align-items-center w-100">
                                        {itemUnitData?.primary_uom_id && (
                                            <p className="mb-0">
                                                1 ({itemUnitData.primary_unit_of_measurement_name})
                                                =
                                            </p>
                                        )}
                                        <input
                                            type="text"
                                            name="conversion_rate"
                                            placeholder="Conversion Rate"
                                            value={itemUnitData?.conversion_rate}
                                            onChange={handleConversionRateChange}
                                            className="form-control w-25 h-40px"
                                        />
                                        {itemUnitData?.secondary_uom_id && (
                                            <p className="mb-0">
                                                ({itemUnitData.secondary_unit_of_measurement_name})
                                            </p>
                                        )}
                                    </div>
                                </div>
                            </div>
                        <div className="d-flex gap-4 rounded-0">
                            <button
                                onClick={handleItemUnitSave}
                                type="button"
                                className="btn btn-primary"
                            >
                                Save
                            </button>
                        </div>
                        </div>
                    </div>
                </div>
            </Modal>
            {isDeleteModal && (
                <WarningModal
                    show={isDeleteModal}
                    title="Delete!"
                    message="Are you sure want to delete this custom field?"
                    showCancelButton
                    showConfirmButton
                    confirmText="Yes, Delete"
                    cancelText="No, Cancel"
                    handleClose={() => setIsDeleteModal(false)}
                    handleSubmit={handleDeleteCustomHeaderField}
                />
            )}
            {isCustomFieldCalculationQty && (
                <CustomFieldCalculation
                    show={isCustomFieldCalculationQty}
                    handleCloseModel={handleCloseCalculationModel}
                    isEditModel={isEditModel}
                    selectedCustomFieldFormula={selectedCustomFieldFormula}
                    type="qty"
                    is_default_configuration={true}
                />
            )}
        </>
    );
};

export default CustomfieldItemModel;
