import React, { useContext, useEffect, useRef, useState } from "react";
import Button from "react-bootstrap/Button";
import Modal from "react-bootstrap/Modal";
import { Form, OverlayTrigger, Tooltip } from "react-bootstrap";
import { useDispatch } from "react-redux";
import ReactSelect from "../../../components/ui/ReactSelect";
import { StateContext } from "../../../context/StateContext";
import { FormInput } from "../../../components/ui/Input";
import useDropdownOption from "../../../shared/dropdownList";
import Close from "../../../assets/images/svg/close";
import AddItemMasterCustomFieldDropdown from "./ItemMasterCustomFieldDropdown";
import { addItemMasterCustomField, updateItemMasterCustomField } from "../../../store/configuration/configurationSlice";
import CustomFieldDate from "../../../components/ui/CustomFieldDate";
import moment from "moment";
import { formattedDate, replaceIdsWithLabels } from "../../../shared/calculation";
import { WARRANTY_FIELD_TYPE } from "../../../constants";

const AddItemMasterCustomFieldModal = ({
    show,
    handleClose,
    id,
    transaction_type
}) => {
    const dispatch = useDispatch();
    const labelRef = useRef(null);
    const { itemMasterCustomFieldList, TransactionTypeList } = useDropdownOption();
    const [fieldName, setFieldName] = useState(null);
    const [fieldType, setFieldType] = useState(null);
    const [changeWarrantyType, setChangeWarrantyType] = useState(null)
    const [valueType, setValueType] = useState(1);
    const [isOpenInPopup, setIsOpenInPopup] = useState(true);
    const [inputType, setInputType] = useState(null);
    const [isWarranty, setIsWarranty] = useState(false)
    const [enableForAllItems, setEnableForAllItems] = useState(false);
    const [isCustomFieldDropdown, setIsCustomFieldDropdown] = useState(false);
    const [transactionData, setTransactionData] = useState(
        TransactionTypeList.map(t => ({ type: t.value, is_checked: false, is_show_in_print: false }))
    );
    const {
        isEditModel,
        setIsEditModel,
        customFieldItemMaster,
        setCustomFieldItemMaster,
        setIsCustomFieldCalculation,
        displayFormula,
        setDisplayFormula,
        backendFormula,
        setBackendFormula,
        storeAddedVariableId,
        setStoreAddedVariableId,
        customItemConfigurationList
    } = useContext(StateContext);
    const filteredCustomItemConfigurationList = customItemConfigurationList?.filter(item => item?.input_type === "number")
    const [customSelectOptions, setCustomSelectOptions] = useState(
        Array(2).fill({
            value: "",
            id: "",
            can_delete: false,
        })
    );

    useEffect(() => {
        if (labelRef.current) {
            labelRef.current.focus();
        }
    }, [show]);

    useEffect(() => {
        if (isEditModel && customFieldItemMaster) {
            setFieldName(customFieldItemMaster.label_name);
            setFieldType(customFieldItemMaster.custom_field_type);
            setIsWarranty(customFieldItemMaster.custom_field_type == WARRANTY_FIELD_TYPE);
            setChangeWarrantyType(customFieldItemMaster.field_type || 1);
            setIsOpenInPopup(customFieldItemMaster.open_in_popup);
            setBackendFormula(customFieldItemMaster?.default_formula?.formula);
            setValueType(customFieldItemMaster.default_formula ? 2 : 1);
            setInputType(customFieldItemMaster.input_type);
            setEnableForAllItems(customFieldItemMaster.enable_for_all);
            setStoreAddedVariableId(Array.isArray(customFieldItemMaster?.default_formula?.used_cf_ids_for_formula) ? customFieldItemMaster.default_formula.used_cf_ids_for_formula.map(item => item)?.join(",") : customFieldItemMaster?.default_formula?.used_cf_ids_for_formula || "");
            setTransactionData(
                TransactionTypeList.map(t => {
                    const transaction = customFieldItemMaster?.types?.find(
                        item => item?.transaction_type === t?.value
                    );
                    return {
                        type: t?.value,
                        is_checked: transaction ? true : false,
                        is_show_in_print: transaction?.is_show_in_print
                    }
                })
            );
            setCustomSelectOptions(
                customFieldItemMaster.options?.map(option => ({
                    value: option.option_label,
                    id: option.id,
                    can_delete: option.can_delete || false,
                })) || Array(2).fill({
                    value: "",
                    id: "",
                    can_delete: false,
                })
            )
            const getDisplayFormula = () => {
                if (customFieldItemMaster?.default_formula?.formula) {
                    return replaceIdsWithLabels(
                        customFieldItemMaster.default_formula.formula,
                        filteredCustomItemConfigurationList
                    );
                }
                if (customFieldItemMaster?.default_value) {
                    return customFieldItemMaster.input_type === "number"
                        ? parseFloat(customFieldItemMaster.default_value)
                        : customFieldItemMaster.default_value;
                }
                return "";
            };
            const DisplayFormula = getDisplayFormula();
            setDisplayFormula(DisplayFormula ?? "");
        }
    }, [show, customFieldItemMaster, isEditModel]);

    const handleCloseModel = () => {
        handleClose();
        setIsEditModel(false);
        setFieldName("");
        setFieldType("");
        setCustomFieldItemMaster(null);
        setTransactionData(
            TransactionTypeList.map(t => ({ type: t.value, is_checked: false, is_show_in_print: false }))
        );
        setIsOpenInPopup(true);
        setValueType(1);
        setDisplayFormula(null);
        setBackendFormula(null);
        setEnableForAllItems(false);
        setCustomSelectOptions(Array(2).fill({
            value: "",
            id: "",
            can_delete: false,
        }));
        setIsCustomFieldDropdown(false);
    };

    const handleAddNewOption = () => {
        setIsCustomFieldDropdown(true);
    };

    const handleAddCustomHeader = e => {
        e.preventDefault();
        const selectedTransactions = transactionData
        ?.filter(item => item.is_checked || item.is_show_in_print)
        ?.map(item => ({
            type: item.type,
            is_show_in_print: item.is_show_in_print ? 1 : 0
        }));
        const customField = {
            label_name: fieldName,
            input_type: inputType,
            custom_field_type: fieldType,
            open_in_popup: isOpenInPopup,
            types: selectedTransactions,
            enable_for_all: enableForAllItems ? 1 : 0,
            field_type: changeWarrantyType,
            ...(valueType == 1 ? {default_value: displayFormula} : backendFormula
            ? {
                default_formula: backendFormula,
                used_cf_ids_for_formula: storeAddedVariableId,
            }
            : {}),
            ...(inputType === "select" ? {options: customSelectOptions} : {})
        };
        if(customFieldItemMaster?.id) {
            dispatch(updateItemMasterCustomField(customFieldItemMaster?.id, customField, handleCloseModel, id, transaction_type));
        }else{
            dispatch(addItemMasterCustomField(customField, handleCloseModel, id, transaction_type));
        }
    };

    const handleChangeCustomFieldType = e => {
        const { value } = e;
        setFieldType(value);
        setIsOpenInPopup(e.open_in_popup);
        setInputType(e.input_type);
        setValueType(e.input_type == "number" ? valueType : 1);
        setIsWarranty(e.label == "Warranty");
        setChangeWarrantyType(1)
        setDisplayFormula(null);
        setBackendFormula(null);
        if(!fieldType){
            setTransactionData(
                TransactionTypeList.map(t => ({ type: t.value, is_checked: false, is_show_in_print: false }))
            );
        }
    };

const handleTransactionChange = (index, key) => {
    setTransactionData(prev =>
        prev.map((item, i) => {
            if (i !== index) return item;

            const updatedItem = {
                ...item,
                [key]: !item[key],
            };

            // If is_checked is toggled off, also turn off print
            if (key === "is_checked" && !updatedItem[key]) {
                updatedItem.is_show_in_print = false;
            }

            return updatedItem;
        })
    );
};


const handleSelectAll = (key, value) => {
    setTransactionData(prev =>
        prev.map(item => {
            const updatedItem = { ...item };

            if (key === "is_show_in_print") {
                // In popup mode, update all
                if (isOpenInPopup) {
                    updatedItem[key] = value;
                } else {
                    // Only update print if transaction is checked
                    if (item.is_checked) {
                        updatedItem[key] = value;
                    }
                }
            } else if (key === "is_checked") {
                updatedItem[key] = value;
                // Reset print when unchecking
                if (!value) {
                    updatedItem.is_show_in_print = false;
                }
            }

            return updatedItem;
        })
    );
};



    const allTransactionChecked = transactionData?.every(item => item?.is_checked);
    const allPrintChecked =
        transactionData?.filter(item => item?.is_checked)?.length > 0 &&
        transactionData?.filter(item => item?.is_checked)?.every(item => item?.is_show_in_print);

    const allInventoryPrintChecked =
        transactionData?.every(item => item?.is_show_in_print);

    const handleOpenCustomFieldCalculation = () => {
        setIsCustomFieldCalculation(true);
    }

    const handleChangeValueType = (value) => {
        setValueType(value);
        setDisplayFormula(null);
        setBackendFormula(null);
    }

    const handleChangeDate = (date) =>{
        const isValidDate = date && !isNaN(new Date(date).getTime());
        const formattedDateValue = isValidDate ? inputType == "datetime" ? moment(new Date(date)).format("DD-MM-YYYY HH:mm") : formattedDate(new Date(date)) : "";
        setDisplayFormula(formattedDateValue);
    }

    const handleChangeDefaultValue = (e) => {
        const { value } = e.target;
        if(inputType == "number" && value?.length < 13){
            setDisplayFormula(value);
        }else if (inputType !== "number") {
            setDisplayFormula(value);
        }
    }

    return (
        <>
            <Modal show={show} onHide={handleCloseModel} size="md" centered backdrop={true}>
                <div className="modal-header py-3">
                    <h5 className="modal-title">
                        {isEditModel ? "Edit Custom field" : "Add Custom field"}
                    </h5>
                    <button
                        type="button"
                        className="btn btn-icon btn-sm btn-active-light-primary ms-2"
                        onClick={handleCloseModel}
                    >
                        <Close />
                    </button>
                </div>
                <Modal.Body className="m-2 mt-1">
                    <form>
                        <div className="mb-4">
                            <Form.Group className="position-relative form-floating-group">
                                <FormInput
                                    ref={labelRef}
                                    className="floating-label-input capitalize"
                                    type="text"
                                    placeholder=""
                                    required
                                    value={fieldName}
                                    onChange={e => setFieldName(e.target.value)}
                                />
                                <Form.Label className="required">Label Name</Form.Label>
                            </Form.Group>
                        </div>
                        <div className="mb-4">
                            <Form.Group>
                                <div className="input-group flex-nowrap">
                                    <div className="position-relative h-40px w-100 focus-shadow">
                                        <ReactSelect
                                            placeholder="Field Type"
                                            options={itemMasterCustomFieldList}
                                            value={fieldType}
                                            onChange={handleChangeCustomFieldType}
                                            required
                                        />
                                    </div>
                                </div>
                            </Form.Group>
                        </div>
                        {!isOpenInPopup ? <>{inputType !== "select" ? (
                            <div className="d-flex align-items-center mt-0 mb-4 gap-3">
                                <div className="form-check">
                                    <label
                                        className="form-label mb-0 text-gray-900"
                                        htmlFor="itemDefaultValue"
                                    >
                                        Set Default Value
                                    </label>
                                    <input
                                        className="form-check-input item-type"
                                        id="itemDefaultValue"
                                        type="radio"
                                        value="1"
                                        checked={valueType == 1}
                                        onChange={() => handleChangeValueType(1)}
                                    />
                                </div>
                                {inputType === "number" ? (
                                    <div className="form-check">
                                        <label
                                            className="form-label mb-0 text-gray-900"
                                            htmlFor="itemsetFormula"
                                        >
                                            Set Formula{" "}
                                        </label>
                                        <input
                                            className="form-check-input item-type"
                                            id="itemsetFormula"
                                            name="item_type_2"
                                            type="radio"
                                            checked={valueType == 2}
                                            onChange={() => handleChangeValueType(2)}
                                        />
                                    </div>
                                ) : (
                                    ""
                                )}
                            </div>
                        ) : (
                            ""
                        )}
                        {inputType === "select" ? (
                            <div className="d-flex justify-content-between">
                                {customSelectOptions?.length <= 10 ? (
                                    <>
                                        <p>
                                            <b>Options:</b>{" "}
                                            {customSelectOptions?.length > 0 &&
                                                customSelectOptions
                                                    .filter(item => item.value?.trim()) // Remove blank or whitespace-only values
                                                    .map(item => item.value)
                                                    .join(", ")}
                                        </p>
                                        <div className="mb-4">
                                            <button
                                                className="btn btn-primary btn-sm"
                                                type="button"
                                                onClick={handleAddNewOption}
                                                style={{ padding: "3px 14px 6px 10px" }}
                                            >
                                                ADD
                                                <span className="font-semibold fs-4">+</span>
                                            </button>
                                        </div>
                                    </>
                                ) : (
                                    ""
                                )}
                            </div>
                        ) : (
                            ""
                        )}
                        <div className="mb-4">
                            {inputType == "number" || inputType == "text" ? (
                                <>
                                {isWarranty ?
                                    <Form.Group className="position-relative form-floating-group">
                                                        <FormInput
                                                            className="floating-label-input-2 h-40"
                                                            type="number"
                                                            step="0.01"
                                                            placeholder=""
                                                            onClick={e => {
                                                                e.target.select();
                                                            }}
                                                            value={displayFormula ?? ''}
                                                            onChange={e =>
                                                                setDisplayFormula(e.target.value)
                                                            }
                                                        />
                                                        <Form.Label>Warranty</Form.Label>
                                                            <div className="w-fit-content bg-light position-absolute credit-dropdown-select focus-shadow input-group d-block">
                                                                <ReactSelect
                                                                    defaultValue={1}
                                                                    options={[
                                                                        {
                                                                            label: "Month",
                                                                            value: 1,
                                                                        },
                                                                        {
                                                                            label: "Year",
                                                                            value: 2,
                                                                        },
                                                                    ]}
                                                                    value={changeWarrantyType}
                                                                    onChange={e =>
                                                                        setChangeWarrantyType(e.value)
                                                                    }
                                                                    placeholder=""
                                                                    isCreatable={false}
                                                                    showborder={false}
                                                                    showbg={true}
                                                                    height="38px"
                                                                />
                                                            </div>
                                                    </Form.Group>
                                : !isOpenInPopup && valueType ?
                                <Form.Group className="position-relative form-floating-group calculator-input">
                                    <FormInput
                                        className="floating-label-input capitalize"
                                        type={valueType == 2 ? "text" : inputType}
                                        placeholder=""
                                        value={displayFormula ?? ''}
                                        onChange={handleChangeDefaultValue}
                                        disabled={valueType == 2}
                                    />
                                    {valueType == 2 ? (
                                        <OverlayTrigger
                                            placement="top"
                                            overlay={<Tooltip id={`tooltip`}>{displayFormula}</Tooltip>}
                                            >
                                        <i
                                            className="fa-solid fs-4 fa-calculator mx-auto my-3 text-black cursor-pointer"
                                            onClick={handleOpenCustomFieldCalculation}
                                        ></i>
                                        </OverlayTrigger>
                                    ) : (
                                        ""
                                    )}
                                    <Form.Label>
                                        {valueType == 1 ? "Default Value" : "Set Formula"}
                                    </Form.Label>
                                </Form.Group>
                                : ""}
                                </>
                            ) : (
                                ""
                            )}
                            {inputType == "date" || inputType == "datetime" ? (
                                <Form.Group className="position-relative form-floating-group">
                                    <CustomFieldDate
                                        value={displayFormula}
                                        onChange={handleChangeDate}
                                        placeholder={"Default Value"}
                                        input_type={inputType}
                                        static_show={true}
                                    />
                                </Form.Group>
                            ) : (
                                ""
                            )}
                        </div>
                        </>
                        : ""}
                        <div className="mb-4">
                            <Form.Group>
                                <div>
                                    <Form.Check
                                        type="checkbox"
                                        label="Enable for all items"
                                        value={enableForAllItems}
                                        checked={enableForAllItems}
                                        onChange={e => setEnableForAllItems(!enableForAllItems)}
                                    />
                                </div>
                            </Form.Group>
                        </div>
                        <div className="custome-field-table-modal mb-5 overflow-auto">
                            <table className="w-100">
                                <thead>
                                    <tr>
                                        <th>Transaction Type</th>
                                        {!isOpenInPopup ? (
                                            <th>
                                                <div className="d-flex justify-content-center align-items-center">
                                                    <div className="me-3">Transaction</div>
                                                    <Form.Group>
                                                        <Form.Check
                                                            type="checkbox"
                                                            label=""
                                                            checked={allTransactionChecked}
                                                            onChange={e =>
                                                                handleSelectAll(
                                                                    "is_checked",
                                                                    e.target.checked
                                                                )
                                                            }
                                                            className="custom-checkbox-orange"
                                                        />
                                                    </Form.Group>
                                                </div>
                                            </th>
                                        ) : (
                                            ""
                                        )}
                                        <th>
                                            <div className="d-flex justify-content-center align-items-center">
                                                <div className="me-3">Print</div>
                                                <Form.Group>
                                                    <Form.Check
                                                        type="checkbox"
                                                        label=""
                                                        checked={isOpenInPopup ? allInventoryPrintChecked : allPrintChecked}
                                                        onChange={e =>
                                                            handleSelectAll(
                                                                "is_show_in_print",
                                                                e.target.checked
                                                            )
                                                        }
                                                        className="custom-checkbox-orange"
                                                    />
                                                </Form.Group>
                                            </div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {TransactionTypeList.map((item, index) => (
                                        <tr key={index}>
                                            <td>{item.name}</td>
                                            {!isOpenInPopup ? (
                                                <td>
                                                    <Form.Group className="d-flex justify-content-center align-items-center">
                                                        <Form.Check
                                                            type="checkbox"
                                                            label=""
                                                            checked={
                                                                transactionData[index].is_checked
                                                            }
                                                            onChange={() =>
                                                                handleTransactionChange(
                                                                    index,
                                                                    "is_checked"
                                                                )
                                                            }
                                                        />
                                                    </Form.Group>
                                                </td>
                                            ) : (
                                                ""
                                            )}
                                            <td>
                                                <Form.Group className="d-flex justify-content-center align-items-center">
                                                    <Form.Check
                                                        type="checkbox"
                                                        label=""
                                                        checked={
                                                            transactionData[index].is_show_in_print
                                                        }
                                                        onChange={() =>
                                                            handleTransactionChange(
                                                                index,
                                                                "is_show_in_print"
                                                            )
                                                        }
                                                        disabled={!isOpenInPopup && !transactionData[index].is_checked}
                                                    />
                                                </Form.Group>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                        <div className="d-flex gap-3">
                            <Button variant="primary" onClick={handleAddCustomHeader} type="button">
                                Save
                            </Button>
                            <Button variant="secondary" onClick={handleCloseModel} type="button">
                                Close
                            </Button>
                        </div>
                    </form>
                </Modal.Body>
            </Modal>
            {isCustomFieldDropdown ? (
                <AddItemMasterCustomFieldDropdown
                    show={isCustomFieldDropdown}
                    close={() => setIsCustomFieldDropdown(false)}
                    customSelectOptions={customSelectOptions}
                    setCustomSelectOptions={setCustomSelectOptions}
                />
            ) : (
                ""
            )}
        </>
    );
};

export default AddItemMasterCustomFieldModal;
