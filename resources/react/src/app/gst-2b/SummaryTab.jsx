import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchGstr2bSummary } from "../../store/gstr-2b/Gstr2bSlice";

const labelToParticularsMap = {
    b2b: {
        books: "Total Invoices As Per Books",
        portal: "Total Invoices As Per GSTR-2B",
        matched: "Matched Invoices",
        mismatched: "Mis-Match Invoice",
        missing_in_books: "Invoices Not in Books",
        missing_in_portal: "Invoices Not In GSTR-2B"
    },
    cdnr: {
        books: "Total Cr. / Dr.  Note As Per Books",
        portal: "Total Cr. / Dr.  Note As Per GSTR-2B",
        matched: "Matched Dr. / Cr. Note",
        mismatched: "Mis-Match Cr. / Dr. Note",
        missing_in_books: "Cr. / Dr.  Not in Books",
        missing_in_portal: "Cr. / Dr.  Not In GSTR-2B"
    }
};

const transformSummaryDataSeparately = (data) => {
    const b2bSummary = [];
    const cdnrSummary = [];

    Object.entries(data.b2b || {}).forEach(([label, values]) => {
        b2bSummary.push({
            Particulars: labelToParticularsMap.b2b[label] || label,
            label,
            ...values
        });
    });

    Object.entries(data.cdnr || {}).forEach(([label, values]) => {
        cdnrSummary.push({
            Particulars: labelToParticularsMap.cdnr[label] || label,
            label,
            ...values
        });
    });

    return { b2bSummary, cdnrSummary };
};



const SummaryTab = ({ activeTab }) => {
    if (activeTab !== "summary") return;
    const [summaryB2BData, setSummaryB2BData] = useState([]);
    const [summaryCnDnData, setSummaryCnDnData] = useState([]);
    const { gstr2bSummary } = useSelector(state => state.gstr2b);

    useEffect(() => {
        if (Object.keys(gstr2bSummary)?.length > 0) {
            const { b2bSummary, cdnrSummary } = transformSummaryDataSeparately(gstr2bSummary?.response);

            setSummaryB2BData(b2bSummary);
            setSummaryCnDnData(cdnrSummary);
        }
    }, [gstr2bSummary]);

    return (
        <>
            <div className="content-wrapper py-6 px-lg-10 px-sm-8 px-6">
                <div className="mb-8">
                    <h3 className="mb-3">B2B Invoices Reconciliation Summary</h3>
                    <div className="summary-invoice-table overflow-auto scroll-color">
                        <table className="table text-nowrap mb-0 w-100">
                            <thead>
                                <tr>
                                    <th>Particulars</th>
                                    <th className="text-end">No. of Invoices</th>
                                    <th className="text-end">Taxable Amount</th>
                                    <th className="text-end">IGST</th>
                                    <th className="text-end">CGST</th>
                                    <th className="text-end">SGST/UTSGT</th>
                                    <th className="text-end">CESS</th>
                                    <th className="text-end">Total tax</th>
                                    <th className="text-end">Invoice Amount</th>
                                </tr>
                            </thead>
                            {summaryB2BData.length > 0 && <tbody>
                                {summaryB2BData.map((row, index) => (
                                    <tr key={index}>
                                        <td>{row.Particulars}</td>
                                        <td className="text-end">
                                            <div className="text-decoration-underline text-primary">
                                                {row.invoices}
                                            </div>
                                        </td>
                                        <td className="text-end">{row.taxable_value.toFixed(2)}</td>
                                        <td className="text-end">{row.total_igst.toFixed(2)}</td>
                                        <td className="text-end">{row.total_cgst.toFixed(2)}</td>
                                        <td className="text-end">{row.total_sgst.toFixed(2)}</td>
                                        <td className="text-end">{row.total_cess.toFixed(2)}</td>
                                        <td className="text-end">{row.total_tax.toFixed(2)}</td>
                                        <td className="text-end">{row.total_invoice_value.toFixed(2)}</td>
                                    </tr>
                                ))}
                            </tbody>
                            }
                        </table>
                    </div>
                </div>
                <div className="mb-4">
                    <h3 className="mb-3">Credit Note/ Debit Note Reconciliation Summary</h3>
                    <div className="summary-invoice-table overflow-auto scroll-color">
                        <table className="table text-nowrap mb-0 w-100">
                            <thead>
                                <tr>
                                    <th>Particulars</th>
                                    <th className="text-end">No. of Invoices</th>
                                    <th className="text-end">Taxable Amount</th>
                                    <th className="text-end">IGST</th>
                                    <th className="text-end">CGST</th>
                                    <th className="text-end">SGST/UTSGT</th>
                                    <th className="text-end">CESS</th>
                                    <th className="text-end">Total tax</th>
                                    <th className="text-end">Invoice Amount</th>
                                </tr>
                            </thead>
                            {summaryCnDnData?.length > 0 && <tbody>
                                {summaryCnDnData.map((row, index) => (
                                    <tr key={index}>
                                        <td>{row.Particulars}</td>
                                        <td className="text-end">
                                            <div className="text-decoration-underline text-primary">
                                                {row.invoices}
                                            </div>
                                        </td>
                                        <td className="text-end">{row.taxable_value.toFixed(2)}</td>
                                        <td className="text-end">{row.total_igst.toFixed(2)}</td>
                                        <td className="text-end">{row.total_cgst.toFixed(2)}</td>
                                        <td className="text-end">{row.total_sgst.toFixed(2)}</td>
                                        <td className="text-end">{row.total_cess.toFixed(2)}</td>
                                        <td className="text-end">{row.total_tax.toFixed(2)}</td>
                                        <td className="text-end">{row.total_invoice_value.toFixed(2)}</td>
                                    </tr>
                                ))}
                            </tbody>
                            }
                        </table>
                    </div>
                </div>
            </div>
        </>
    );
};

export default SummaryTab;
