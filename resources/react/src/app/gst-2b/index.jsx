
import moment from "moment";
import React, { useEffect, useState } from "react";
import { Form, Tab, Tabs } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import MonthPicker from "../../components/ui/MonthPicker";
import QuarterlyPicker from "../../components/ui/QuarterlyPicker";
import ReactSelect from "../../components/ui/ReactSelect";
import CustomHelmet from "../../shared/helmet";
import { fetchCompanyDetails } from "../../store/company/companySlice";
import { fetchGstr2bData, fetchGstr2bDetails, fetchGstr2bSummary } from "../../store/gstr-2b/Gstr2bSlice";
import ConnectToGstModal from "../modal/Gst2b/ConnectToGstModal";
import ReconciliationTable from "./ReconciliationTable";
import SummaryTab from "./SummaryTab";
import Toast from "../../components/ui/Toast";

const monthRangeOptions = [
    { label: "Monthly", value: 1 },
    { label: "Quarterly", value: 2 },
    { label: "Yearly", value: 3 },
];

const getCurrentQuarter = (month) => {
    if (month >= 3 && month <= 5) return 1;
    if (month >= 6 && month <= 8) return 2;
    if (month >= 9 && month <= 11) return 3;
    return 4;
};

const today = new Date();
const currentMonth = today.getMonth();
const currentYear = today.getFullYear();
const currentQuarter = getCurrentQuarter(currentMonth);

// const getQuarterDateRange = (quarter, year) => {
//     let startMonth, endMonth, endDay;

//     switch (quarter) {
//         case 1: // Apr – Jun
//             startMonth = 3; // April
//             endMonth = 5;   // June
//             endDay = 30;
//             break;
//         case 2: // Jul – Sep
//             startMonth = 6; // July
//             endMonth = 8;   // September
//             endDay = 30;
//             break;
//         case 3: // Oct – Dec
//             startMonth = 9;  // October
//             endMonth = 11;   // December
//             endDay = 31;
//             break;
//         case 4: // Jan – Mar
//             startMonth = 0;  // January
//             endMonth = 2;    // March
//             endDay = new Date(year, 3, 0).getDate(); // last day of March
//             break;
//         default:
//             throw new Error("Invalid quarter");
//     }

//     const startDate = new Date(year, startMonth, 1);
//     const endDate = new Date(year, endMonth, endDay);

//     return { startDate, endDate };
// };

const getQuarterDateRange = (quarter, year) => {
    let startMonth, endMonth, endDay;
    let startYear = year;
    let endYear = year;

    switch (quarter) {
        case 1: // Q1: Apr – Jun
            startMonth = 3; // April
            endMonth = 5;   // June
            endDay = 30;
            break;
        case 2: // Q2: Jul – Sep
            startMonth = 6; // July
            endMonth = 8;   // September
            endDay = 30;
            break;
        case 3: // Q3: Oct – Dec
            startMonth = 9;  // October
            endMonth = 11;   // December
            endDay = 31;
            break;
        case 4: // Q4: Jan – Mar (belongs to next calendar year)
            startMonth = 0;  // January
            endMonth = 2;    // March
            startYear = year + 1;
            endYear = year + 1;
            endDay = new Date(endYear, 3, 0).getDate(); // Last day of March
            break;
        default:
            throw new Error("Invalid quarter");
    }

    const startDate = new Date(startYear, startMonth, 1);
    const endDate = new Date(endYear, endMonth, endDay);

    return { startDate, endDate };
};


const Gstr2b = () => {
    const [activeTab, setActiveTab] = useState("summary");
    const [fetched, setFetched] = useState(false);
    const [isLogin, setIsLogin] = useState(false);
    const [showLoadingModal, setShowLoadingModal] = useState(false);
    const [monthFilter, setMonthFilter] = useState({
        monthRange: 1,
        selectedMonth: "",
    });
    const [selectedQuarter, setSelectedQuarter] = useState({
        quarter: currentQuarter,
        year: currentQuarter === 4 && currentMonth < 3 ? currentYear + 1 : currentYear,
    });
    const [selectedMonths, setSelectedMonths] = useState(null);
    const [isSummaryApi, setIsSummaryApi] = useState(true);
    const [searchParams] = useSearchParams();
    const sectionType = searchParams.get('section');
    const dispatch = useDispatch();
    const { company } = useSelector(state => state.company);
    const { gstr2bSummary } = useSelector(state => state.gstr2b);

    useEffect(() => {
        dispatch(fetchCompanyDetails());
    }, []);

    useEffect(() => {
        if (sectionType === "gst-2b-reconciliation") {
            setActiveTab("reconciliation");
        }
    }, [sectionType]);

    useEffect(() => {
        const storedMonth = JSON.parse(localStorage.getItem("selectedMonth"));
        if (storedMonth) {
            const startDate = moment(storedMonth?.startDate, "YYYY-MM-DD", true).isValid() ? storedMonth?.startDate : moment(storedMonth?.startDate, "DD/MM/YYYY").format("YYYY-MM-DD");
            const endDate = moment(storedMonth?.endDate, "YYYY-MM-DD", true).isValid() ? storedMonth?.endDate : moment(storedMonth?.endDate, "DD/MM/YYYY").format("YYYY-MM-DD");

            const params = {
                start_date: startDate,
                end_date: endDate,
            };


             dispatch(fetchGstr2bDetails(params));
             setMonthFilter(prev => ({
                ...prev,
                monthRange: storedMonth?.monthRange,
                selectedMonth: {
                    startDate: startDate,
                    endDate: endDate,
                }
            }));
            setSelectedQuarter({quarter: storedMonth?.quarter, year: storedMonth?.year});
            setFetched(true);
             setTimeout(() => {
                localStorage.removeItem("selectedMonth");
             }, 2000);
        }
    }, [])


    useEffect(() => {
        const rawStart = monthFilter?.selectedMonth?.startDate;
        const rawEnd = monthFilter?.selectedMonth?.endDate;

        const parsedStart = moment(rawStart, "YYYY-MM-DD", true).isValid()
            ? moment(rawStart, "YYYY-MM-DD")
            : moment(rawStart, "DD/MM/YYYY", true).isValid()
                ? moment(rawStart, "DD/MM/YYYY")
                : null;

        const parsedEnd = moment(rawEnd, "YYYY-MM-DD", true).isValid()
            ? moment(rawEnd, "YYYY-MM-DD")
            : moment(rawEnd, "DD/MM/YYYY", true).isValid()
                ? moment(rawEnd, "DD/MM/YYYY")
                : null;

        if (parsedStart && parsedEnd && isSummaryApi) {
            const startDate = parsedStart.format("YYYY-MM-DD");
            const endDate = parsedEnd.format("YYYY-MM-DD");

            const params = {
                start_date: startDate,
                end_date: endDate,
            };

            dispatch(fetchGstr2bSummary(params));
            setIsSummaryApi(false);
        }
    }, [monthFilter]);

    const handleFetch = async () => {
        setShowLoadingModal(true);

        const startDate = moment(monthFilter?.selectedMonth?.startDate, "YYYY-MM-DD", true).isValid() ? monthFilter?.selectedMonth?.startDate : moment(monthFilter?.selectedMonth?.startDate, "DD/MM/YYYY").format("YYYY-MM-DD");
        const endDate = moment(monthFilter?.selectedMonth?.endDate, "YYYY-MM-DD", true).isValid() ? monthFilter?.selectedMonth?.endDate : moment(monthFilter?.selectedMonth?.endDate, "DD/MM/YYYY").format("YYYY-MM-DD");

        const params = {
            start_date: startDate,
            end_date: endDate,
        };
        const response = await dispatch(fetchGstr2bData(params, setShowLoadingModal));

        if (response?.success) {
            await dispatch(fetchGstr2bDetails(params));
            await dispatch(fetchGstr2bSummary(params));
            setShowLoadingModal(false);
            setFetched(true);
        }
    };

    const handleMonthChange = (selectedOptions) => {
        if (selectedOptions.value === 1) {
            setMonthFilter(prev => ({
                ...prev,
                monthRange: selectedOptions.value
            }));
        } else if (selectedOptions.value === 2) {
            const { startDate, endDate } = getQuarterDateRange(selectedQuarter?.quarter, selectedQuarter?.year);
            const formattedStartDate = startDate.toLocaleDateString("en-GB");
            const formattedEndDate = endDate.toLocaleDateString("en-GB");
            setMonthFilter(prev => ({
                ...prev,
                monthRange: selectedOptions.value,
                selectedMonth: {
                    startDate: formattedStartDate,
                    endDate: formattedEndDate
                }
            }));
        } else {
            setMonthFilter(prev => ({
                ...prev,
                monthRange: selectedOptions.value,
                selectedMonth: {
                    startDate: company?.currentFinancialYear?.yearStartDate,
                    endDate: company?.currentFinancialYear?.yearEndDate
                }
            }));
        }
    };

    const handleQuarterChange = (selectedQuarter) => {
        setSelectedQuarter(selectedQuarter);
        const { quarter, year } = selectedQuarter;
        const { startDate, endDate } = getQuarterDateRange(quarter, year);
        const formattedStartDate = startDate.toLocaleDateString("en-GB");
        const formattedEndDate = endDate.toLocaleDateString("en-GB");
        setMonthFilter(prev => ({
            ...prev,
            selectedMonth: {
                startDate: formattedStartDate,
                endDate: formattedEndDate
            }
        }));

    };

    const handleMonthOnChange = (data) => {
        setSelectedMonths(data.startDate);
        const formattedStartDate = data.startDate.toLocaleDateString("en-GB");
        const formattedEndDate = data.endDate.toLocaleDateString("en-GB");
        setMonthFilter(prev => ({
            ...prev,
            selectedMonth: {
                startDate: formattedStartDate,
                endDate: formattedEndDate
            }
        }));
    };

    const startDateInHeader = moment(monthFilter?.selectedMonth?.startDate, "DD/MM/YYYY", true).isValid() ? monthFilter?.selectedMonth?.startDate : moment(monthFilter?.selectedMonth?.startDate, "YYYY-MM-DD").format("DD/MM/YYYY");
    const endDateInHeader = moment(monthFilter?.selectedMonth?.endDate, "DD/MM/YYYY", true).isValid() ? monthFilter?.selectedMonth?.endDate : moment(monthFilter?.selectedMonth?.endDate, "YYYY-MM-DD").format("DD/MM/YYYY");

    return (
        <>
            <CustomHelmet title={"Settings"} />
            <div>
                <div className="text-center mb-3">
                        <h3 className="fw-6 mb-1">GSTR-2B</h3>
                        <h4 className=" fw-6 mb-1 text-black">{company?.legal_name} <span className="text-primary fs-14">(GSTIN: {company?.company_tax?.gstin})</span></h4>
                        <h5 className="fw-6 mb-0 text-black">{startDateInHeader} - {endDateInHeader}</h5>
                </div>
                <div className="d-flex align-items-start justify-content-between flex-wrap gap-4 gap-xl-2 mb-10px">
                    <div className="form-control w-auto">
                        Last Data Imported: {gstr2bSummary?.updated_at && moment(gstr2bSummary?.updated_at, "YYYY-MM-DD HH:mm:ss", true).isValid() ? moment(gstr2bSummary?.updated_at, "YYYY-MM-DD HH:mm:ss").format("DD MMM YYYY hh:mm A") : moment().format("DD MMM YYYY hh:mm A")}
                    </div>
                    <div className="d-flex align-items-center gap-3 flex-wrap justify-content-end ms-auto">
                        <Form.Group>
                            <div className="input-group flex-nowrap">
                                <div className=" position-relative h-40px focus-shadow min-w-120px gstr2b-month-input">
                                    <ReactSelect
                                        placeholder="Monthly"
                                        defaultLabel=""
                                        options={monthRangeOptions}
                                        value={monthFilter?.monthRange}
                                        onChange={handleMonthChange}
                                        islabel={true}
                                        className="h-40px focus-shadow w-100"
                                    />
                                </div>
                            </div>
                        </Form.Group>
                        <div className="custome-monthpicker">
                            {monthFilter?.monthRange === 1 ? <MonthPicker
                                value={selectedMonths}
                                onChange={handleMonthOnChange}
                                minDate={company?.currentFinancialYear?.yearStartDate}
                                maxDate={company?.currentFinancialYear?.yearEndDate}
                            /> : monthFilter?.monthRange === 2 ? <QuarterlyPicker
                                value={selectedQuarter}
                                onChange={handleQuarterChange}
                                minDate={company?.currentFinancialYear?.yearStartDate}
                                maxDate={company?.currentFinancialYear?.yearEndDate}
                            /> : null}

                        </div>

                        {
                            isLogin ? (
                                <div className="d-flex align-items-center gap-3">
                                    <button type="button" className="btn btn-orange btn-sm fs-13" onClick={handleFetch}>
                                        GSTR-2B Data Fetched
                                    </button>
                                    <a href="#!">
                                        <i className="far fa-file-excel shortcut-sale-icon-excel-export text-gray fs-1 text-success"></i>
                                    </a>
                                </div>
                            ) : (
                                <ConnectToGstModal setIsLogin={setIsLogin} />
                            )
                        }
                    </div>
                </div>
                <div className="position-relative">
                    {fetched && (
                        <div className="fetch-data-btn">
                            <button
                                type="button"
                                className="btn btn-primary btn-sm fs-13"
                                onClick={handleFetch}
                            >
                                Re-Fetch GSTR-2B Data
                            </button>
                        </div>
                    )}
                    <Tabs
                        activeKey={activeTab}
                        onSelect={(k) => setActiveTab(k)}
                        className="print-settings-tab flex-nowrap ps-4"
                    >
                        <Tab eventKey="summary" title="Summary">
                            <SummaryTab activeTab={activeTab} />
                        </Tab>
                        <Tab eventKey="reconciliation" title="Reconciliation">
                            <ReconciliationTable activeTab={activeTab} fetched={fetched} setFetched={setFetched} handleFetch={handleFetch} showLoadingModal={showLoadingModal} setShowLoadingModal={setShowLoadingModal} isLogin={isLogin} setIsLogin={setIsLogin} monthFilter={monthFilter} selectedQuarter={selectedQuarter} />
                        </Tab>
                    </Tabs>
                </div>
            </div>
            <Toast />
        </>
    );
};
export default Gstr2b;
