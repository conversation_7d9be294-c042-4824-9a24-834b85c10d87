import React, { useState } from 'react'
import ConnectToGstModal from '../modal/Gst2b/ConnectToGstModal';
import { Modal } from 'react-bootstrap';
import fetchingData from "../../assets/images/fetching-data.gif";
import reconciliation from "../../assets/images/reconciliation-img.png";
import { useSelector } from 'react-redux';

const searchableColumns = [
    "gstin", "name", "books_doc", "books_doc_no", "books_doc_date",
    "books_taxable_value", "books_igst", "books_cgst", "books_sgst", "books_cess",
    "portal_doc_no", "portal_doc_date", "portal_taxable_value", "portal_igst",
    "portal_cgst", "portal_sgst", "portal_cess", "difference_taxable_value",
    "difference_igst", "difference_cgst", "difference_sgst", "difference_cess"
];


const ReconciliationTable = ({ activeTab, fetched, setFetched, handleFetch, showLoadingModal, setShowLoadingModal, isLogin, setIsLogin, monthFilter, selectedQuarter }) => {

    if (activeTab !== "reconciliation") return;
    const [filterStatus, setFilterStatus] = useState("all");
    const [searchTerms, setSearchTerms] = useState({});
    const { gstr2bData } = useSelector(state => state.gstr2b);

    const handleSearchChange = (e, column) => {
        const value = e.target.value;
        setSearchTerms(prev => ({
            ...prev,
            [column]: value,
        }));
    };

    const filteredData = gstr2bData.filter(row => {
        if (filterStatus !== "all" && row.action !== filterStatus) return false;

        for (const key in searchTerms) {
            const searchValue = searchTerms[key]?.toLowerCase();
            const rowValue = String(row[key] ?? "").toLowerCase();
            if (searchValue && !rowValue.includes(searchValue)) {
                return false;
            }
        }
        return true;
    });

    const handleEditClick = (row) => {
        console.log(row, "  row", monthFilter?.selectedMonth);
        localStorage.setItem("selectedMonth", JSON.stringify({
            ...monthFilter.selectedMonth,
            monthRange: monthFilter?.monthRange,
            quarter: selectedQuarter.quarter,
            year: selectedQuarter.year
        }));
    };

    return (
        <>
            {!fetched ? (
                <div className="content-wrapper reconciliation-content-wrapper">
                    <div className="d-flex flex-column align-items-center justify-content-center h-100">
                        <img src={reconciliation} alt="image" className="img-fluid" />
                        <h3 className="text-center mb-3">Let’s get started with reconciliation</h3>
                        <p className="text-center text-gray-700 mb-0 fs-14">To reconcile your GSTR-2B data, please fetch it from the GST portal.</p>
                        {!isLogin && <p className="text-center text-gray-700 mb-0 fs-14"> Click below to connect and start comparing your books.</p>}
                        {!isLogin && <div className="mt-5">
                            <ConnectToGstModal setIsLogin={setIsLogin} />
                        </div>}
                        {isLogin && <button
                            type="button"
                            className="btn btn-primary btn-sm fs-13 mt-3"
                            onClick={handleFetch}
                        >
                            Fetch GSTR-2B Data
                        </button>}
                    </div>
                </div>
            ) : (
                <div>
                    <div className="content-wrapper reconciliation-content-wrapper py-6 px-0">
                        <div className="reconciliation-invoice-table overflow-auto scroll-color">
                            <table className="table  mb-0 w-100">
                                <thead>
                                    <tr>
                                        <th></th>
                                        <th className="text-center" colSpan={3}>Vendor Details</th>
                                        <th className="text-center" colSpan={7}>As Per Books of Accounts</th>
                                        <th className="text-center" colSpan={7}>As Per GSTR-2B</th>
                                        <th className="text-center" colSpan={5}>Difference</th>
                                        <th className="text-center" colSpan={2}></th>

                                    </tr>
                                    <tr>
                                        <th></th>
                                        <th className="text-center">GSTIN</th>
                                        <th className="min-w-72px">Supplier Name</th>
                                        <th className="min-w-72px">Doc. Type</th>
                                        <th className="min-w-72px">Doc. No.</th>
                                        <th className="min-w-72px">Date</th>
                                        <th className="min-w-72px">Taxable Amount</th>
                                        <th className="text-end min-w-72px">IGST</th>
                                        <th className="text-end min-w-72px">CGST</th>
                                        <th className="text-end min-w-72px">SGST</th>
                                        <th className="text-end min-w-72px">CESS</th>
                                        <th className="min-w-72px">Doc. No.</th>
                                        <th className="min-w-72px">Date</th>
                                        <th className="min-w-72px">Taxable Amount</th>
                                        <th className="text-end min-w-72px">IGST</th>
                                        <th className="text-end min-w-72px">CGST</th>
                                        <th className="text-end min-w-72px">SGST</th>
                                        <th className="text-end min-w-72px">CESS</th>
                                        <th className="min-w-72px">Taxable Amount</th>
                                        <th className="text-end min-w-72px">IGST</th>
                                        <th className="text-end min-w-72px">CGST</th>
                                        <th className="text-end min-w-72px">SGST</th>
                                        <th className="text-end min-w-72px">CESS</th>
                                        <th className="text-center min-w-72px">Status</th>
                                        <th className="text-center">Action</th>
                                    </tr>

                                </thead>

                                <tbody>
                                    <tr className="search-header">
                                        <td></td>
                                        {searchableColumns.map((col, index) => (
                                            <td key={index} className="text-end">
                                                <input
                                                    type="text"
                                                    placeholder="Search"
                                                    className="form-control search search-box border-0"
                                                    value={searchTerms[col] || ""}
                                                    onChange={(e) => handleSearchChange(e, col)}
                                                    autoComplete="off"
                                                />
                                            </td>
                                        ))}
                                        <td className="text-end"></td>
                                        <td className="text-end"></td>

                                    </tr>
                                    {filteredData.map((row, index) => {
                                        const isPending = row.action === "pending";
                                        const isMatched = row.action === "matched";
                                        const isMissingInGST = row.action === "missingInGST";
                                        const isMissingInBook = row.action === "missingInBook";

                                        const rowStyle = {
                                            backgroundColor: row?.color
                                        };
                                        return (
                                            <tr key={index} style={rowStyle}>
                                                <td>
                                                    <input
                                                        type="checkbox"
                                                        className="form-check-input"
                                                        style={{ width: "20px", height: "20px" }}
                                                        name="select_all"
                                                    />
                                                </td>
                                                <td>{row.gstin}</td>
                                                <td>{row.name}</td>
                                                <td>{row.books_doc}</td>
                                                <td className="text-decoration-underline text-primary">{row.books_doc_no}</td>
                                                <td className={` ${isPending && (row.books_doc_no !== row.portal_doc_no) ? "reconciliationUnmatchDoc" : ""} "text-center"`}>{row.books_doc_date}</td>
                                                <td className="text-end">{row.books_taxable_value}</td>
                                                <td className="text-end">{row.books_igst}</td>
                                                <td className="text-end">{row.books_cgst}</td>
                                                <td className="text-end">{row.books_sgst}</td>
                                                <td className="text-end">{row.books_cess}</td>
                                                <td>{row.portal_doc_no}</td>
                                                <td className={` ${isPending && (row.books_doc_no !== row.portal_doc_no) ? "reconciliationUnmatchDoc" : ""} "text-center"`}>{row.portal_doc_date}</td>
                                                <td className="text-end">{row.portal_taxable_value}</td>
                                                <td className="text-end">{row.portal_igst}</td>
                                                <td className="text-end">{row.portal_cgst}</td>
                                                <td className="text-end">{row.portal_sgst}</td>
                                                <td className="text-end">{row.portal_cess}</td>
                                                <td className="text-end">{row.difference_taxable_value}</td>
                                                <td className="text-end">{row.difference_igst}</td>
                                                <td className="text-end">{row.difference_cgst}</td>
                                                <td className="text-end">{row.difference_sgst}</td>
                                                <td className="text-end">{row.difference_cess}</td>
                                                <td className="text-center">
                                                    {isPending ? (
                                                        <span title="Partially Matched"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" viewBox="0 0 16 15" fill="none">
                                                            <path d="M8.6632 0.398881C8.4037 -0.0916191 7.5967 -0.0916191 7.3372 0.398881L0.587204 13.1489C0.52667 13.2632 0.496727 13.3912 0.500284 13.5205C0.50384 13.6498 0.540774 13.776 0.607499 13.8868C0.674224 13.9976 0.768471 14.0893 0.881087 14.1529C0.993703 14.2165 1.12086 14.2499 1.2502 14.2499H14.7502C14.8795 14.2501 15.0067 14.2169 15.1194 14.1534C15.2321 14.0898 15.3263 13.9982 15.393 13.8874C15.4597 13.7766 15.4966 13.6504 15.5 13.5211C15.5034 13.3918 15.4732 13.2638 15.4125 13.1496L8.6632 0.398881ZM8.7502 11.9999H7.2502V10.4999H8.7502V11.9999ZM7.2502 8.99988V5.24988H8.7502L8.75095 8.99988H7.2502Z" fill="#E6A000" />
                                                        </svg></span>
                                                    ) : isMatched ? (
                                                        <span title="Matched"><svg xmlns="http://www.w3.org/2000/svg" width="15" height="12" viewBox="0 0 15 12" fill="none">
                                                            <path d="M4.75027 11.8149L0.0927734 7.15741L2.21527 5.03491L4.75027 7.57741L12.1603 0.159912L14.2828 2.28241L4.75027 11.8149Z" fill="#009951" />
                                                        </svg></span>
                                                    ) : isMissingInGST ? (
                                                        <span title="Missing in GST"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" viewBox="0 0 16 15" fill="none">
                                                            <path fillRule="evenodd" clipRule="evenodd" d="M8 15C3.85775 15 0.5 11.6423 0.5 7.5C0.5 3.35775 3.85775 0 8 0C12.1423 0 15.5 3.35775 15.5 7.5C15.5 11.6423 12.1423 15 8 15ZM8 13.5C9.5913 13.5 11.1174 12.8679 12.2426 11.7426C13.3679 10.6174 14 9.0913 14 7.5C14 5.9087 13.3679 4.38258 12.2426 3.25736C11.1174 2.13214 9.5913 1.5 8 1.5C6.4087 1.5 4.88258 2.13214 3.75736 3.25736C2.63214 4.38258 2 5.9087 2 7.5C2 9.0913 2.63214 10.6174 3.75736 11.7426C4.88258 12.8679 6.4087 13.5 8 13.5ZM7.25 10.5H8.75V12H7.25V10.5ZM7.25 9.006C7.25 9.006 8.75 9 8.75 9.006C8.75 8.2545 11 7.5 11 6C11 4.3425 9.67025 3 8.00675 3C7.61222 2.99911 7.22138 3.07605 6.85663 3.22642C6.49187 3.37679 6.16036 3.59763 5.88106 3.87629C5.60177 4.15495 5.38019 4.48597 5.22901 4.85039C5.07782 5.21481 5 5.60547 5 6H6.5C6.5 5.175 7.175 4.5 8 4.5C8.825 4.5 9.5 5.175 9.5 6C9.5 6.675 7.25 7.77525 7.25 9.006Z" fill="#181C32" />
                                                        </svg></span>
                                                    ) : isMissingInBook ? (
                                                        <span title="Missing in Book"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" viewBox="0 0 16 15" fill="none">
                                                            <path fillRule="evenodd" clipRule="evenodd" d="M7.99999 0C12.1415 0 15.5 3.35848 15.5 7.49999C15.5 11.6415 12.1415 15 7.99999 15C3.85848 15 0.5 11.6415 0.5 7.49999C0.5 3.35848 3.85848 0 7.99999 0ZM9.71976 4.71973L7.99999 6.43954L6.28025 4.7198L5.21976 5.78028L6.9395 7.49999L5.21976 9.21976L6.28025 10.2803L7.99999 8.56051L9.71976 10.2803L10.7803 9.21976L9.06051 7.49999L10.7803 5.78025L9.71976 4.71973Z" fill="#C00F0C" />
                                                        </svg></span>
                                                    ) : (
                                                        row.status
                                                    )}
                                                </td>
                                                <td className="text-center">
                                                    {isPending ? (
                                                        <div className="d-flex gap-1 justify-content-center align-items-center" onClick={(row) => handleEditClick(row)} >
                                                            <a
                                                                href={`${window.location.origin}/company/${row.transaction_type === "purchase" ? "purchases" : row.transaction_type === "purchaseReturn" ? "purchase-returns" : row.transaction_type === "expenseDebitNote" ? "expense-debit-notes" : row.transaction_type}/${row.transaction_id}/edit?section=gst-2b`}>
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
                                                                    <path d="M6.29076 2.06105H3.94251C3.00312 2.06105 2.53342 2.06105 2.17462 2.24387C1.85902 2.40468 1.60242 2.66128 1.44161 2.97688C1.25879 3.33568 1.25879 3.80538 1.25879 4.74477V9.44128C1.25879 10.3807 1.25879 10.8504 1.44161 11.2092C1.60242 11.5248 1.85902 11.7814 2.17462 11.9422C2.53342 12.125 3.00312 12.125 3.94251 12.125H8.63902C9.57841 12.125 10.0481 12.125 10.4069 11.9422C10.7225 11.7814 10.9791 11.5248 11.1399 11.2092C11.3227 10.8504 11.3227 10.3807 11.3227 9.44128V7.09303M4.61342 8.77035H5.54968C5.82318 8.77035 5.95993 8.77035 6.08863 8.73945C6.20273 8.71206 6.3118 8.66688 6.41185 8.60557C6.5247 8.53642 6.6214 8.43972 6.81479 8.24632L12.1614 2.89971C12.6246 2.43653 12.6246 1.68557 12.1614 1.22239C11.6982 0.759205 10.9473 0.759205 10.4841 1.22238L5.13746 6.569C4.94406 6.76239 4.84736 6.85909 4.77821 6.97194C4.7169 7.07199 4.67171 7.18107 4.64432 7.29516C4.61342 7.42386 4.61342 7.56061 4.61342 7.83412V8.77035Z" stroke="#4F158C" stroke-width="1.5" strokeLinecap="round" stroke-linejoin="round" />
                                                                </svg>
                                                            </a>
                                                            <a href="#!">
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
                                                                    <path d="M7 0.25C3.5625 0.25 0.75 3.0625 0.75 6.5C0.75 9.9375 3.5625 12.75 7 12.75C10.4375 12.75 13.25 9.9375 13.25 6.5C13.25 3.0625 10.4375 0.25 7 0.25ZM5.75 9.625L2.625 6.5L3.50625 5.61875L5.75 7.85625L10.4937 3.1125L11.375 4L5.75 9.625Z" fill="#009951" />
                                                                </svg>
                                                            </a>
                                                        </div>
                                                    ) : isMatched ? (
                                                        <div></div>
                                                    ) : isMissingInGST ? (
                                                        <a href="#!">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 12 12" fill="none">
                                                                <path d="M11.1466 1.55387C11.4166 0.806992 10.6928 0.0832424 9.94597 0.353867L0.817846 3.65512C0.0684712 3.92637 -0.022154 4.94887 0.667221 5.34824L3.58097 7.03512L6.18285 4.43324C6.30072 4.31939 6.4586 4.2564 6.62247 4.25782C6.78634 4.25925 6.9431 4.32498 7.05898 4.44086C7.17486 4.55674 7.24059 4.71349 7.24202 4.87737C7.24344 5.04124 7.18044 5.19912 7.0666 5.31699L4.46472 7.91887L6.15222 10.8326C6.55097 11.522 7.57347 11.4307 7.84472 10.682L11.1466 1.55387Z" fill="black" />
                                                            </svg>
                                                        </a>
                                                    ) : isMissingInBook ? (
                                                        <a href={`${window.location.origin}/company/${row.transaction_type === "purchase" ? "purchases" : row.transaction_type === "purchaseReturn" ? "purchase-returns" : row.transaction_type === "expenseDebitNote" ? "expense-debit-notes" : row.transaction_type}/create?section=gst-2b&transaction_id=${row.transaction_id}`}>
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="15" viewBox="0 0 14 15" fill="none">
                                                                <path d="M7 0.9375C5.26605 0.958539 3.60905 1.6567 2.38287 2.88287C1.1567 4.10905 0.458539 5.76605 0.4375 7.5C0.458539 9.23395 1.1567 10.8909 2.38287 12.1171C3.60905 13.3433 5.26605 14.0415 7 14.0625C8.73395 14.0415 10.3909 13.3433 11.6171 12.1171C12.8433 10.8909 13.5415 9.23395 13.5625 7.5C13.5415 5.76605 12.8433 4.10905 11.6171 2.88287C10.3909 1.6567 8.73395 0.958539 7 0.9375ZM10.75 7.96875H7.46875V11.25H6.53125V7.96875H3.25V7.03125H6.53125V3.75H7.46875V7.03125H10.75V7.96875Z" fill="#4F158C" />
                                                            </svg>
                                                        </a>
                                                    ) : (
                                                        row.action
                                                    )}
                                                </td>
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div className="button-section d-flex gap-4 mt-6 flex-wrap">
                        <button className="all-btn btn d-flex justify-content-center align-items-center" onClick={() => setFilterStatus("all")}>
                            All
                        </button>
                        <button className="matched-btn d-flex justify-content-center align-items-center gap-2 btn" onClick={() => setFilterStatus("matched")}>
                            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="12" viewBox="0 0 15 12" fill="none">
                                <path d="M4.75027 11.8149L0.0927734 7.15741L2.21527 5.03491L4.75027 7.57741L12.1603 0.159912L14.2828 2.28241L4.75027 11.8149Z" fill="#009951" />
                            </svg>  Matched
                        </button>
                        <button className="pending-btn d-flex justify-content-center align-items-center gap-2 btn" onClick={() => setFilterStatus("pending")}>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" viewBox="0 0 16 15" fill="none">
                                <path d="M8.6632 0.398881C8.4037 -0.0916191 7.5967 -0.0916191 7.3372 0.398881L0.587204 13.1489C0.52667 13.2632 0.496727 13.3912 0.500284 13.5205C0.50384 13.6498 0.540774 13.776 0.607499 13.8868C0.674224 13.9976 0.768471 14.0893 0.881087 14.1529C0.993703 14.2165 1.12086 14.2499 1.2502 14.2499H14.7502C14.8795 14.2501 15.0067 14.2169 15.1194 14.1534C15.2321 14.0898 15.3263 13.9982 15.393 13.8874C15.4597 13.7766 15.4966 13.6504 15.5 13.5211C15.5034 13.3918 15.4732 13.2638 15.4125 13.1496L8.6632 0.398881ZM8.7502 11.9999H7.2502V10.4999H8.7502V11.9999ZM7.2502 8.99988V5.24988H8.7502L8.75095 8.99988H7.2502Z" fill="#E6A000" />
                            </svg> Partially Matched
                        </button>
                        <button className="missing-in-book-btn d-flex justify-content-center align-items-center gap-2 btn" onClick={() => setFilterStatus("missingInBook")}>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" viewBox="0 0 16 15" fill="none">
                                <path fillRule="evenodd" clipRule="evenodd" d="M7.99999 0C12.1415 0 15.5 3.35848 15.5 7.49999C15.5 11.6415 12.1415 15 7.99999 15C3.85848 15 0.5 11.6415 0.5 7.49999C0.5 3.35848 3.85848 0 7.99999 0ZM9.71976 4.71973L7.99999 6.43954L6.28025 4.7198L5.21976 5.78028L6.9395 7.49999L5.21976 9.21976L6.28025 10.2803L7.99999 8.56051L9.71976 10.2803L10.7803 9.21976L9.06051 7.49999L10.7803 5.78025L9.71976 4.71973Z" fill="#C00F0C" />
                            </svg> Missing in Books
                        </button>
                        <button className="missing-in-gst-btn d-flex justify-content-center align-items-center gap-2 btn" onClick={() => setFilterStatus("missingInGST")}>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" viewBox="0 0 16 15" fill="none">
                                <path fillRule="evenodd" clipRule="evenodd" d="M8 15C3.85775 15 0.5 11.6423 0.5 7.5C0.5 3.35775 3.85775 0 8 0C12.1423 0 15.5 3.35775 15.5 7.5C15.5 11.6423 12.1423 15 8 15ZM8 13.5C9.5913 13.5 11.1174 12.8679 12.2426 11.7426C13.3679 10.6174 14 9.0913 14 7.5C14 5.9087 13.3679 4.38258 12.2426 3.25736C11.1174 2.13214 9.5913 1.5 8 1.5C6.4087 1.5 4.88258 2.13214 3.75736 3.25736C2.63214 4.38258 2 5.9087 2 7.5C2 9.0913 2.63214 10.6174 3.75736 11.7426C4.88258 12.8679 6.4087 13.5 8 13.5ZM7.25 10.5H8.75V12H7.25V10.5ZM7.25 9.006C7.25 9.006 8.75 9 8.75 9.006C8.75 8.2545 11 7.5 11 6C11 4.3425 9.67025 3 8.00675 3C7.61222 2.99911 7.22138 3.07605 6.85663 3.22642C6.49187 3.37679 6.16036 3.59763 5.88106 3.87629C5.60177 4.15495 5.38019 4.48597 5.22901 4.85039C5.07782 5.21481 5 5.60547 5 6H6.5C6.5 5.175 7.175 4.5 8 4.5C8.825 4.5 9.5 5.175 9.5 6C9.5 6.675 7.25 7.77525 7.25 9.006Z" fill="#181C32" />
                            </svg> Missing in GSTN
                        </button>
                    </div>
                </div>
            )}
            <Modal show={showLoadingModal} centered
                size="sm">

                <div className="modal-header bg-white justify-content-end  pt-3 pb-0 px-5 border-0">
                    <button
                        className="btn close-button p-0"
                        onClick={() => setShowLoadingModal(false)}
                        type="button"
                    >
                        &times;
                    </button>
                </div>
                <Modal.Body className="text-center py-5">
                    <div >
                        <img src={fetchingData} alt="image" className="img-fluid" style={{ transform: "rotate(180deg)" }} />
                    </div>
                    <h4 className="text-primary mb-0 mt-2">Fetching GSTR-2B data</h4>
                </Modal.Body>
            </Modal>
        </>
    )
}

export default ReconciliationTable
