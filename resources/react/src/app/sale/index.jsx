import moment from "moment";
import { useContext, useEffect, useRef, useState } from "react";
import { Container } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import Youtube from "../../assets/images/svg/youtube";
import Toast from "../../components/ui/Toast";
import {
    apiBaseURL,
    CALCULATION_ON_TYPE,
    ITEM_STATUS,
    SHIPPING_ADDRESS_TYPE_LIST,
    TABLE_HEADER_TYPE,
    toastType,
    TRANSACTION_TYPE,
    transactionTypeMap,
} from "../../constants";
import { StateContext } from "../../context/StateContext";
import {
    calculateAdditionalCharges,
    calculateAdditionalClassification,
    calculateAddLessCharges,
    calculateClassification,
    calculateTotal,
    calculateTotals,
    customToFixed,
    formattedDate,
    gstCalculate,
    RoundOffMethod,
    RoundOffMethodForTds,
    setGSTCalculationType,
} from "../../shared/calculation";
import useDropdownOption from "../../shared/dropdownList";
import CustomHelmet from "../../shared/helmet";
import Loader from "../../shared/loader";
import {
    areAllProductsExemptOrNA,
    calculateGSTFlags,
    checkAllAdditionalChargesNaAndExempt,
    convertToFormData,
    customFieldOptimization,
    fetchOriInvoiceData,
    findGstRate,
    prepareItemSaleData,
    prepareItemSaleDataFromEstimate,
    prepareItemsData,
    prepareLedgerData,
    preparePurchaseToSale,
    prepareSaveAndNewData,
    unitOptionWithKey
} from "../../shared/prepareData";
import {
    checkPathName,
    correctClassificationNatureType,
    getEditLedgerFromList,
} from "../../shared/sharedFunction";
import { useTransactionShortcuts } from "../../shared/shortcut-keys";
import { errorToast } from "../../store/actions/toastAction";
import { getAdvancePayment } from "../../store/advance-payment/advancePaymentSlice";
import { fetchBrokerList } from "../../store/broker/brokerSlice";
import { fetchClassificationList } from "../../store/classification/classificationSlice";
import { fetchCompanyDetails, fetchUserPermission } from "../../store/company/companySlice";
import {
    fetchBankList,
    fetchConfigurationList,
    rearrangeItemList,
    resetConfiguration,
    updateSingleConfiguration,
} from "../../store/configuration/configurationSlice";
import { fetchDispatchAddressList } from "../../store/dispatchAddress/dispatchAddressSlice";
import { fetchInvoiceNumber } from "../../store/invoice/invoiceSlice";
import { fetchItemList, fetchPriceListOfItems, } from "../../store/item/itemSlice";
import {
    fetchAdditionalLedgerList,
    fetchAddlessLedgerList,
    fetchItemLedgerDetail,
    fetchPartyDetail,
    fetchPartyList,
    fetchPaymentLedgerList,
    fetchPaymentModeList,
} from "../../store/ledger/ledgerSlice";
import { fetchPrevNextUrl } from "../../store/prev-next/prev-nextSlice";
import { fetchTcsList, fetchTdsList } from "../../store/rate/rateSlice";
import {
    addSale,
    checkCreditLimit,
    createEInvoice,
    deleteSale,
    getSaleDetailByEstimateChallan,
    updateSale,
} from "../../store/sale/saleSlice";
import { fetchShippingAddressList } from "../../store/shippingAddress/shippingAddressSlice";
import { fetchTransportList } from "../../store/transport/transportSlice";
import Error404Page from "../common/404";
import SaleInvoiceDetail from "../common/InvoiceDetail";
import SaleItems from "../common/Items";
import SaleReturnInvoiceDetail from "../common/Return-InvoiceDetail";
import WarningModal from "../common/WarningModal";
import ConfigurationModal from "../modal/Configuration/ConfigurationModal";
import DeleteWarningModal from "../modal/DeleteWarningModal";

const SalesTransaction = ({
    id,
    singleSale,
    isDuplicate,
    isSaleReturnDuplicate = false,
    singleDeliveryChallan,
    challanId,
    estimateId,
    singleEstimateQuote,
    isSaleToSaleReturn = false,
    saleId,
    PurchaseToSale,
    purchaseToSaleId
}) => {
    const TRANSACTION_PATHS = {
        RETURN: 'return',
        CREATE_SALE_RETURN: 'create-sale-return',
        EDIT: 'edit'
    };
    const saleCreateOrReturn = window.location.pathname.includes(TRANSACTION_PATHS.RETURN);
    const createSaleToSaleReturn = window.location.pathname.includes(TRANSACTION_PATHS.CREATE_SALE_RETURN);
    const saleShippingAddressType = PurchaseToSale ? SHIPPING_ADDRESS_TYPE_LIST.PURCHASE : createSaleToSaleReturn ? SHIPPING_ADDRESS_TYPE_LIST.SALE : singleEstimateQuote ? SHIPPING_ADDRESS_TYPE_LIST.INCOME_ESTIMATE_QUOTE : challanId ? SHIPPING_ADDRESS_TYPE_LIST.DELIVERY_CHALLAN : SHIPPING_ADDRESS_TYPE_LIST.SALE
    const saleReturnShippingAddressType = createSaleToSaleReturn ? SHIPPING_ADDRESS_TYPE_LIST.SALE : SHIPPING_ADDRESS_TYPE_LIST.SALE_RETURN
    const dispatch = useDispatch();
    const invoiceRef = useRef(null);
    const formRef = useRef(null);

    const {
        broker,
        company,
        invoice,
        ledger,
        table,
        dispatchAddress,
        configuration,
        sale,
        estimate,
        deliveryChallan,
        prevNext,
    } = useSelector(selector => selector);

    const fetchSaleDetail = sale?.getSaleById?.length !== 0 && sale?.getSaleById;

    const configurationList = configuration?.configuration;

    const url = window.location.origin;
    const saleEdit = window.location.pathname.includes(TRANSACTION_PATHS.EDIT);
    const isSaleReturn = window.location.pathname.includes("sale-returns") || window.location.pathname.includes("create-sale-return") ;
    const { roundOffOption, gstOptions, classificationOptions, tableHeaderList, accountingTableHeader } = useDropdownOption();
    const {
        items,
        setItems,
        accountingItems,
        setAccountingItems,
        gstValue,
        setGstValue,
        invoiceDetail,
        setInvoiceDetail,
        partyAddress,
        setPartyAddress,
        ewayBillDetail,
        setEwayBillDetail,
        otherDetail,
        setOtherDetail,
        gstCalculation,
        setGstCalculation,
        paymentLedgerDetail,
        setPaymentLedgerDetail,
        classification,
        setClassification,
        additionalGst,
        brokerDetail,
        setBrokerDetail,
        transporterDetail,
        setTransporterDetail,
        localDispatchAddress,
        setLocalDispatchAddress,
        gstQuote,
        setGstQuote,
        setIsConfirmed,
        additionalCharges,
        setAdditionalCharges,
        tcsRate,
        setTcsRate,
        addLessChanges,
        setAddLessChanges,
        cessValue,
        selectedAddress,
        sameAsBill,
        setSameAsBill,
        itemType,
        setItemType,
        setChangeTax,
        grandTotal,
        setGrandTotal,
        mainGrandTotal,
        setMainGrandTotal,
        setCessValue,
        isEditCalculation,
        setIsEditCalculation,
        finalAmount,
        setFinalAmount,
        taxableValue,
        setTaxableValue,
        setConfigurationURL,
        setConfigurationHeaderList,
        setConfigurationTableList,
        setConfigurationFooterList,
        setConfigurationModalName,
        isIGSTCalculation,
        setIsIGSTCalculation,
        setIsSGSTCalculation,
        isSGSTCalculation,
        loader,
        setLoader,
        setAdditionalGst,
        isTcsAmountChange,
        updateParty_quotesOptions,
        setUpdateParty_quotesOptions,
        changeTax,
        dispatchAddressId,
        setDispatchAddressId,
        setInvoiceValue,
        deleteTransaction,
        openDeleteTransactionModel,
        closeDeleteTransactionModel,
        classificationType,
        showDeleteWarningModel,
        setShowDeleteWarningModel,
        isCheckGstType,
        selectedAdvancePayment,
        setSelectedAdvancePayment,
        setShowPaymentTable,
        isDisable,
        setIsDisable,
        customFieldListTransaction,
        setCustomHeaderListTransaction,
        setHasUnsavedChanges,
        isFieldsChanges,
        setisFieldsChanges,
        isBackButtonClick,
        setIsBackButtonClick,
        setUnsavedBackUrl,
        setIsChangePartyId,
    } = useContext(StateContext);

    const [isShowGstValue, setIsShowGstValue] = useState(false);
    const [tcsValue, setTCSValue] = useState(0);
    const [tcsRateValue, setTCSRateValue] = useState(0);
    const [shippingValue, setShippingValue] = useState(0);
    const [packingCharge, setPackingCharge] = useState(0);
    const [updatedTableHeader, setUpdatedTableHeader] = useState([]);
    const [showEInvoiceModal, setShowEInvoiceModal] = useState({
        open: false,
        id: null,
    });
    const [submitTypeLocal, setSubmitTypeLocal] = useState("");
    const [creditLimitModal, setCreditLimitModal] = useState({
        open: false,
        action: null,
    });
    const [submitButtonType, setSubmitButtonType] = useState("");

    const getCustomFieldTransactionType = Object.keys(transactionTypeMap).find(path =>
          checkPathName(path)
    ) ? transactionTypeMap[Object.keys(transactionTypeMap).find(path => checkPathName(path))] : "";

    useEffect(() => {
        if (!additionalCharges.terms_and_conditions?.length > 0 && !id) {
            setAdditionalCharges(prev => ({
                ...prev,
                terms_and_conditions: invoice?.invoiceNumber?.term_and_condition,
            }));
        }
        if (!id) {
            setAdditionalCharges(prev => ({
                ...prev,
                bank_id: configurationList?.document_prefix?.bank_id
            }));
        }
    }, [invoice, configurationList?.document_prefix?.bank_id]);

    useEffect(() => {
        dispatch(resetConfiguration());
    }, []);

    useEffect(() => {
        setConfigurationHeaderList(
            [
                {
                    name: "Dispatch Details",
                    key: "is_enabled_dispatch_details",
                    value: configurationList?.header?.is_enabled_dispatch_details,
                    position: 0,
                },
                {
                    name: "Mobile Number",
                    key: "is_enabled_phone_number",
                    value: configurationList?.header?.is_enabled_phone_number,
                    position: 1,
                },
                !saleCreateOrReturn && {
                    name: "Estimates/Quotes Details",
                    key: "is_enabled_estimate_quote",
                    value: configurationList?.header?.is_enabled_estimate_quote,
                    position: 3,
                },
                !saleCreateOrReturn && {
                    name: "Delivery Challan Details",
                    key: "is_enabled_delivery_challan",
                    value: configurationList?.header?.is_enabled_delivery_challan,
                    position: 4,
                },
                !saleCreateOrReturn &&
                    company?.company?.is_gst_applicable && {
                        name: "E-Way Bill Details",
                        key: "is_enabled_eway_details",
                        value: configurationList?.header?.is_enabled_eway_details,
                        position: 7,
                    },
                {
                    name: "Credit Period Details",
                    key: "is_enabled_credit_period_details",
                    value: configurationList?.header?.is_enabled_credit_period_details,
                    position: 9,
                },
                {
                    name: "PO Details",
                    key: "is_enabled_po_details_of_buyer",
                    value: configurationList?.header?.is_enabled_po_details_of_buyer,
                    position: 8,
                },
            ].filter(Boolean)
        );

        setConfigurationTableList(
            [
                company?.company?.is_gst_applicable && {
                    name: "Change GST Details",
                    key: "is_change_gst_details",
                    value: configurationList?.header?.is_change_gst_details,
                    position: 1,
                },
                {
                    name: "Additional Ledger Description",
                    key: "is_additional_ledger_description",
                    value: configurationList?.item_table_configuration
                        ?.is_additional_ledger_description,
                    position: 3,
                },
                !saleCreateOrReturn && {
                    name: "Warn On Negative Stock",
                    key: "warn_on_negative_stock",
                    value: configurationList?.item_table_configuration?.warn_on_negative_stock,
                    position: 8,
                },
                {
                    name: "Discount 2",
                    key: "is_enabled_discount_2",
                    value: configurationList?.item_table_configuration?.is_enabled_discount_2,
                    position: 7,
                },
                {
                    name: "MRP",
                    key: "is_enabled_mrp",
                    value: configurationList?.item_table_configuration?.is_enabled_mrp,
                    position: 6,
                },
            ].filter(Boolean)
        );

        setConfigurationFooterList([
            {
                name: "TCS",
                key: "is_enabled_tcs_details",
                value: configurationList?.footer?.is_enabled_tcs_details,
                position: 1,
            },
            {
                name: "TDS",
                key: "is_enabled_tds_details",
                value: configurationList?.footer?.is_enabled_tds_details,
                position: 2,
            },
            {
                name: "Payment Details",
                key: "is_enabled_payment_details",
                value: configurationList?.footer?.is_enabled_payment_details,
                position: 7,
            },
            {
                name: "Terms & Conditions",
                key: "is_enabled_terms_and_conditions",
                value: configurationList?.footer?.is_enabled_terms_and_conditions,
                position: 4,
            },
        ].filter(Boolean));
    }, [configurationList, company]);

    useEffect(() => {
        document.getElementById("showName").innerHTML = isDuplicate
            ? "Add Sale Duplicate"
            : isSaleReturnDuplicate
            ? "Add Sale Return Duplicate"
            : saleCreateOrReturn
            ? saleEdit
                ? "Edit Sale Return"
                : "Add Sale Return"
            : saleEdit
            ? "Edit Sale"
            : "Add Sale";
        if (!saleEdit && !isDuplicate && !isSaleReturnDuplicate) {
            setIsEditCalculation(true);
        }
        setConfigurationModalName(
            saleCreateOrReturn ? "Sale Return Configuration" : "Sale Configuration"
        );
    }, [saleCreateOrReturn]);

    useEffect(() => {
        const newSalesData = JSON.parse(localStorage.getItem('saveAndNewData'));
        if (newSalesData) {
            setIsChangePartyId(true);
            dispatch(fetchPartyDetail(newSalesData?.customer_ledger_id));
            dispatch(fetchShippingAddressList(parseFloat(newSalesData?.customer_ledger_id),saleCreateOrReturn ? saleReturnShippingAddressType : saleShippingAddressType, (id || estimateId || challanId)));
            if (newSalesData?.customer_ledger_id) {
                dispatch(fetchPartyList({ ids: [newSalesData?.customer_ledger_id] }));
            } else {
                dispatch(fetchPartyList());
            }

            const newData = newSalesData;
            prepareSaveAndNewData(newData, setGstQuote, setPartyAddress, additionalCharges, setAdditionalCharges, setItemType);
        }
      }, []);

    useEffect(() => {
        if (singleSale) {
            dispatch(fetchPartyDetail(singleSale?.customer_ledger_id));
            if (singleSale?.customer_ledger_id) {
                dispatch(fetchPartyList({ ids: [singleSale?.customer_ledger_id] }));
            } else {
                dispatch(fetchPartyList());
            }
            const ids = [];
            const item_ledger_id = [];
            const tcs_type = 1;
            const tds_type = 2;
            const is_call_payment_ledger = true;

            if (singleSale?.sale_items?.length > 0) {
                singleSale?.sale_items?.forEach(item => {
                    ids.push(item.item_id);
                    item_ledger_id.push(item.ledger_id);
                });
            } else if (singleSale?.sale_return_items?.length > 0) {
                singleSale?.sale_return_items?.forEach(item => {
                    ids.push(item.item_id);
                    item_ledger_id.push(item.income_ledger_id);
                });
            } else if(singleSale?.sale_ledgers?.length > 0) {
                singleSale?.sale_ledgers.forEach(item => {
                    item_ledger_id.push(item.ledger_id);
                });
            }

            if (ids.length > 0) {
                dispatch(fetchItemList({ ids }));
            } else {
                dispatch(fetchItemList());
            }

            getEditLedgerFromList({dispatch, item_ledger_id, tcs_type, tds_type, is_call_payment_ledger, singleTransaction: singleSale});
            if (singleSale?.advance_payment?.length > 0) {
                setShowPaymentTable(true);
                dispatch(
                    getAdvancePayment(
                        singleSale?.customer_ledger_id,
                        saleCreateOrReturn ? "sale-return" : "sale",
                        id
                    )
                );
            }
            prepareItemSaleData({
                singleSale,
                id,
                setStateFunctions: {
                    invoiceDetail,
                    setInvoiceDetail,
                    setCessValue,
                    setGstQuote,
                    setItemType,
                    setClassification,
                    setItems,
                    setAccountingItems,
                    setAddLessChanges,
                    setAdditionalCharges,
                    setPartyAddress,
                    gstCalculation,
                    setGstCalculation,
                    setBrokerDetail,
                    setTransporterDetail,
                    setEwayBillDetail,
                    setOtherDetail,
                    setGstValue,
                    setTcsRate,
                    setPaymentLedgerDetail,
                    setGrandTotal,
                    setMainGrandTotal,
                    setChangeTax,
                    addLessChanges,
                    additionalCharges,
                    setFinalAmount,
                    setTaxableValue,
                    setIsIGSTCalculation,
                    setIsSGSTCalculation,
                    setUpdateParty_quotesOptions,
                    setDispatchAddressId,
                    setSelectedAdvancePayment,
                    setCustomHeaderListTransaction,
                    setSameAsBill,
                    configurationList,
                    saleEdit
                },
                dispatch,
                saleCreateOrReturn: isSaleToSaleReturn ? false : saleCreateOrReturn,
                tableHeaderList,
                classificationOptions,
                accountingTableHeader,
                isDuplicate,
                isSaleReturnDuplicate,
                isSaleToSaleReturn,
                gstOptions,
            });
            setTimeout(() => {
                setLoader(false);
            }, 1000);
        }
    }, [singleSale, classificationOptions]);

    useEffect(() => {
        if (singleEstimateQuote) {
            const estimateToSaleDetail = singleEstimateQuote?.estimateQuoteTransaction
            dispatch(fetchPartyDetail(estimateToSaleDetail?.party_ledger_id));
            if (estimateToSaleDetail?.party_ledger_id) {
                dispatch(
                    fetchPartyList({
                        ids: [estimateToSaleDetail?.party_ledger_id],
                    })
                );
            } else {
                dispatch(fetchPartyList());
            }
            const ids = [];
            const item_ledger_id = [];
            const tcs_type = 1;
            const tds_type = 2;
            const is_call_payment_ledger = true;

            if (singleEstimateQuote?.allTransactionItems) {
                singleEstimateQuote?.allTransactionItems?.forEach(item => {
                    ids.push(item.item_id);
                    item_ledger_id.push(item.ledger_id);
                });
            }

            if (ids.length > 0) {
                dispatch(fetchItemList({ ids }));
            } else {
                dispatch(fetchItemList());
            }
            getEditLedgerFromList({dispatch, item_ledger_id, tcs_type, tds_type, is_call_payment_ledger, singleTransaction: singleEstimateQuote?.estimateQuoteTransaction});
            prepareItemSaleDataFromEstimate({
                singleEstimateQuote,
                estimateToSaleDetail,
                id,
                setStateFunctions: {
                    invoiceDetail,
                    setInvoiceDetail,
                    setCessValue,
                    setGstQuote,
                    setItemType,
                    setClassification,
                    setItems,
                    setAccountingItems,
                    setAddLessChanges,
                    setAdditionalCharges,
                    setPartyAddress,
                    gstCalculation,
                    setGstCalculation,
                    setBrokerDetail,
                    setTransporterDetail,
                    setEwayBillDetail,
                    setOtherDetail,
                    setGstValue,
                    setTcsRate,
                    setPaymentLedgerDetail,
                    setGrandTotal,
                    setMainGrandTotal,
                    setChangeTax,
                    addLessChanges,
                    additionalCharges,
                    setFinalAmount,
                    setTaxableValue,
                    setIsIGSTCalculation,
                    setIsSGSTCalculation,
                    setUpdateParty_quotesOptions,
                    gstQuote,
                    setCustomHeaderListTransaction,
                    setSameAsBill,
                    configurationList,
                },
                dispatch,
                saleCreateOrReturn,
                tableHeaderList,
                classificationOptions,
                accountingTableHeader,
                gstOptions,
                estimateId,
                invoice,
                company
            });
        }
    }, [singleEstimateQuote, classificationOptions, company?.company]);

    useEffect(() => {
        if (fetchSaleDetail) {
            const ids = [];
            const item_ledger_id = [];
            const tcs_type = 1;
            const tds_type = 2;
            const is_call_payment_ledger = true;

            if (fetchSaleDetail?.sale_items) {
                fetchSaleDetail?.sale_items?.forEach(item => {
                    ids.push(item.item_id);
                    item_ledger_id.push(item.ledger_id);
                });
            }

            if (ids.length > 0) {
                dispatch(fetchItemList({ ids }));
            }
            getEditLedgerFromList({dispatch, item_ledger_id, tcs_type, tds_type, is_call_payment_ledger, singleTransaction: fetchSaleDetail});
            fetchOriInvoiceData({
                fetchSaleDetail,
                saleCreateOrReturn,
                setStateFunctions: {
                    invoiceDetail,
                    setInvoiceDetail,
                    gstQuote,
                    setGstQuote,
                    setItemType,
                    setClassification,
                    setItems,
                    setAccountingItems,
                    setAddLessChanges,
                    setAdditionalCharges,
                    setPartyAddress,
                    setGstCalculation,
                    setBrokerDetail,
                    setTransporterDetail,
                    setEwayBillDetail,
                    setOtherDetail,
                    setGstValue,
                    setTcsRate,
                    setPaymentLedgerDetail,
                    setGrandTotal,
                    setMainGrandTotal,
                    setChangeTax,
                    setFinalAmount,
                    setIsIGSTCalculation,
                    setIsSGSTCalculation,
                    setTaxableValue,
                    setCessValue,
                    setCustomHeaderListTransaction,
                    setSameAsBill,
                    saleEdit
                },
                dispatch,
                tableHeaderList,
                accountingTableHeader,
                addLessChanges,
                additionalCharges,
                classificationOptions,
            });
        }
    }, [fetchSaleDetail, classificationOptions]);

    useEffect(() => {
        if (singleDeliveryChallan) {
            const deliveryChallanToSale = singleDeliveryChallan?.deliveryChallanTransaction
            dispatch(fetchPartyDetail(deliveryChallanToSale?.party_ledger_id));
            let ids = [];
            const item_ledger_id = [];
            const tcs_type = 1;
            const tds_type = 2;
            const is_call_payment_ledger = true;
            if (singleDeliveryChallan?.allTransactionItems) {
                singleDeliveryChallan?.allTransactionItems.map(item => {
                    ids.push(item.item_id);
                });
            }
            if (ids.length > 0) {
                    dispatch(fetchItemList({ ids }));
            } else {
                    dispatch(fetchItemList());
            }

            getEditLedgerFromList({dispatch, item_ledger_id, tcs_type, tds_type, is_call_payment_ledger, singleTransaction: deliveryChallanToSale});
            const billingAddress = deliveryChallanToSale?.billing_address;
            const shippingAddress = deliveryChallanToSale?.shipping_address;
            setPartyAddress({
                billingAddress: {
                    address_name: shippingAddress?.address_name,
                    address_1: billingAddress?.address_1,
                    address_2: billingAddress?.address_2,
                    country_id: billingAddress?.country_id,
                    state_id: billingAddress?.state_id,
                    city_id: billingAddress?.city_id,
                    state_name: billingAddress?.state_name,
                    city_name: billingAddress?.city_name,
                    pin_code: billingAddress?.pin_code,
                },
                shippingAddress: {
                    address_name: shippingAddress?.address_name,
                    address_1: shippingAddress?.address_1,
                    address_2: shippingAddress?.address_2,
                    country_id: shippingAddress?.country_id,
                    state_id: shippingAddress?.state_id,
                    city_id: shippingAddress?.city_id,
                    state_name: shippingAddress?.state_name,
                    city_name: shippingAddress?.city_name,
                    pin_code: shippingAddress?.pin_code,
                    shipping_name: deliveryChallanToSale?.shipping_name,
                    shipping_gstin: deliveryChallanToSale?.shipping_gstin,
                    shipping_address_id: deliveryChallanToSale?.shipping_address_id,
                },
            });

            setGstQuote({
                gstin: deliveryChallanToSale?.gstin,
                party_ledger_id: deliveryChallanToSale?.party_ledger_id,
                quotes_id: [
                    {
                        value: Number(challanId),
                        label: deliveryChallanToSale?.challan_number,
                    },
                ],
            });
            setSameAsBill(deliveryChallanToSale?.same_as_billing);
            setBrokerDetail({
                broker_id: deliveryChallanToSale?.broker_id,
                broker_percentage: deliveryChallanToSale?.brokerage,
                brokerage_on_value: deliveryChallanToSale?.brokerage_on_value_type,
            });

            setTransporterDetail({
                transport_id: deliveryChallanToSale?.transport_id,
                transporter_document_number: deliveryChallanToSale?.transporter_document_number,
                transporter_document_date: deliveryChallanToSale?.transporter_document_date
                    ? formattedDate(new Date(deliveryChallanToSale?.transporter_document_date))
                    : "",
                transporter_vehicle_number: deliveryChallanToSale?.transporter_vehicle_number,
            });

            setOtherDetail({
                po_number: deliveryChallanToSale?.po_no,
                date: deliveryChallanToSale?.po_date
                    ? formattedDate(new Date(deliveryChallanToSale?.po_date))
                    : "",
                creditPeriod: deliveryChallanToSale?.credit_period,
                creditPeriodType: deliveryChallanToSale?.credit_period_type || 1,
            });

            setAdditionalCharges({
                        ...additionalCharges,
                        note: deliveryChallanToSale?.narration,
                        terms_and_conditions: deliveryChallanToSale?.term_and_condition,
                        additional_detail:
                            !deliveryChallanToSale?.additional_charges ||
                            deliveryChallanToSale?.additional_charges?.length == 0
                                ? [
                                      {
                                          ac_ledger_id: null,
                                          ac_type: 1,
                                          ac_value: "",
                                          ac_gst_rate_id: {
                                              label: "",
                                              value: 0,
                                              rate: 0,
                                          },
                                          ac_total: 0,
                                      },
                                  ]
                                : deliveryChallanToSale?.additional_charges?.map(charge => {
                                      return {
                                          ac_ledger_id: charge?.ledger_id,
                                          ac_type: charge?.charge_type,
                                          ac_value: charge?.value,
                                          ac_gst_rate_id: {
                                              value: charge?.gst_rate_id,
                                              rate: Number(charge?.gst_percentage),
                                              label: `${charge?.gst_percentage}%`,
                                          },
                                          ac_total: customToFixed(charge?.total_without_tax, 2),
                                      };
                                  }),
            });
            setAddLessChanges(
                deliveryChallanToSale?.add_less?.length == 0
                ? addLessChanges
                : deliveryChallanToSale?.add_less?.map(item => {
                      return {
                          al_ledger_id: item.ledger_id,
                          al_is_show_in_print: item.is_show_in_print,
                          al_type: item.type,
                          al_value:
                              item.type == 2 ? parseFloat(item.value) : parseFloat(item.total),
                          al_total: parseFloat(item.total),
                      };
                  })
            );
            setTcsRate({
                tcs_amount: deliveryChallanToSale?.tcs_tax_id ? customToFixed(deliveryChallanToSale?.tcs_amount, 2) : '',
                tcs_tax_id: deliveryChallanToSale?.tcs_tax_id,
                tcs_rate: deliveryChallanToSale?.tcs_tax_id ? deliveryChallanToSale?.tcs_rate : '',
                tcs_calculated_on: deliveryChallanToSale?.tcs_calculated?.calculated_on,
            });
            if (ids.length > 0) {
                dispatch(fetchItemList({ ids }));
                if (deliveryChallanToSale?.invoice_type == 1) {
                    dispatch(
                        fetchPriceListOfItems(
                            ids,
                            deliveryChallanToSale?.party_ledger_id,
                            null,
                            setIsConfirmed,
                            getCustomFieldTransactionType
                        )
                    );
                }
            }

            setCustomHeaderListTransaction(deliveryChallanToSale?.custom_values)

            const processTransactionItems = transactionItems => {
                return transactionItems.map((transactionItem, index) => {
                    const itemModel = transactionItem.items?.model || {};
                    const gstTax = transactionItem?.gst_id || {};
                    const unitOptions = transactionItem?.unitOfArray || {};
                    const unitOptionDetail = unitOptionWithKey(unitOptions);

                    const baseData = {
                        quantity:
                            customToFixed(
                                transactionItem?.quantity,
                                itemModel?.decimal_places ?? 2
                            ) || 0,
                        description: itemModel?.description || "",
                        gstRate: itemModel?.gst_rate || 0,
                        gst_id: gstTax,
                        gst: itemModel?.gst_tax?.tax_rate,
                        hsn_code: deliveryChallanToSale?.invoice_type == 1 ? itemModel?.hsn_code : transactionItem?.hsn_code,
                        cessRate: itemModel?.gst_cess_rate || 0,
                        sellingPriceWithoutGst: deliveryChallanToSale?.invoice_type == 1 ? itemModel?.selling_price_without_gst : transactionItem?.rpu_without_gst || 0,
                        sellingPriceWithGst: deliveryChallanToSale?.invoice_type == 1 ? itemModel?.selling_price_with_gst : transactionItem?.rpu_with_gst || 0,
                        discountType: itemModel?.discount_type || 1,
                        discountValue: itemModel?.discount_value || 0,
                        discountType_2: itemModel?.discount_type_2 || 1,
                        discountValue_2: itemModel?.discount_value_2 || 0,
                        unitOfMeasurement: itemModel?.unit_of_measurement || "",
                        additional_description: transactionItem?.additional_description || "",
                        selectedLedger: itemModel?.income_ledger_id || null,
                        mrp: itemModel?.mrp || 0,
                        updatedRateWithoutGst: (deliveryChallanToSale?.invoice_type == 1 ? itemModel?.selling_price_without_gst : transactionItem?.rpu_without_gst || 0)?.toFixed(
                            itemModel?.decimal_places_for_rate || 2
                        ),
                        itemUnitOption: unitOptionDetail,
                        with_tax: changeTax ? 1 : 0,
                        custom_fields: customFieldOptimization(transactionItem?.custom_fields),
                        is_status: ITEM_STATUS.IN_ACTIVE,
                    };

                    const totalData = calculateTotal(
                        baseData,
                        false,
                        changeTax,
                        itemType === "accounting"
                    );
                    return {
                        id: transactionItem?.id || null,
                        selectedItem: transactionItem?.item_id || null,
                        multiQuantity: [1, 0, 0, 0],
                        updatedRateWithGst:
                            itemModel?.sale_price_type === 1
                                ? baseData.sellingPriceWithGst
                                : baseData.sellingPriceWithoutGst?.toFixed(2),
                        updatedRateWithoutGst: baseData.sellingPriceWithoutGst?.toFixed(
                            itemModel?.decimal_places_for_rate || 2
                        ),
                        ...transactionItem,
                        ...baseData,
                        ...totalData,
                        sgstValue: totalData?.sgst,
                        cgstValue: totalData?.sgst,
                        igstValue: totalData?.sgst * 2,
                        cessValue: totalData?.cess,
                        selectedUnit: itemModel?.unit_of_measurement || null,
                        rateWithGst: baseData.sellingPriceWithGst,
                        rateWithoutGst: baseData.sellingPriceWithoutGst,
                        isShowDelete: true,
                    };
                });
            };

            const calculateTotals = items => {
                return items.reduce(
                    (acc, item) => {
                        acc.itemTotal += item.total || 0;
                        acc.sgstValue += item?.sgstValue || 0;
                        acc.cgstValue += item.cgstValue || 0;
                        acc.igstValue += item.igstValue || 0;
                        acc.cessTotal += item.cessValue || 0;
                        return acc;
                    },
                    {
                        itemTotal: 0,
                        sgstValue: 0,
                        cgstValue: 0,
                        igstValue: 0,
                        cessTotal: 0,
                    }
                );
            };
            const itemsList = processTransactionItems(
                singleDeliveryChallan?.allTransactionItems || []
            );
            const nature_type = singleDeliveryChallan?.allTransactionItems &&
                      singleDeliveryChallan?.allTransactionItems[0]?.classification_nature_type?.id;
            const nature_name = classificationOptions.find(item => item.value == nature_type);
            setClassification({
                classification_nature: nature_type,
                classification_nature_name: nature_name?.label,
                rcm_applicable:
                    singleDeliveryChallan?.allTransactionItems &&
                      singleDeliveryChallan?.allTransactionItems[0]?.classification_is_rcm_applicable == 1
                            ? true
                            : false
            });
            setGSTCalculationType(
                {
                    classification_nature_name: nature_name?.label,
                },
                setIsIGSTCalculation,
                setIsSGSTCalculation
            );
            const { itemTotal, cessTotal } = calculateTotals(itemsList);
            setGrandTotal(itemTotal);
            setTaxableValue(itemTotal);
            setCessValue(parseFloat(cessTotal));
            if(itemsList?.length > 0) {
                setItems(itemsList);
            }
        }
    }, [singleDeliveryChallan, classificationOptions]);

    useEffect(() => {
            if (PurchaseToSale) {
                dispatch(fetchPartyList());
                const ids = [];
                const item_ledger_id = [];
                const tcs_type = 1;
                const tds_type = 2;
                const is_call_payment_ledger = true;

                if (PurchaseToSale?.purchase_transaction_items) {
                    PurchaseToSale?.purchase_transaction_items?.forEach(item => {
                        ids.push(item.item_id);
                        item_ledger_id.push(item.ledger_id);
                    });
                }

                if (ids.length > 0) {
                    dispatch(fetchItemList({ ids }));
                } else {
                    dispatch(fetchItemList());
                }

                getEditLedgerFromList({dispatch, item_ledger_id, tcs_type, tds_type, is_call_payment_ledger, singleTransaction: PurchaseToSale});

                if (PurchaseToSale?.advance_payment?.length > 0) {
                    setShowPaymentTable(true);
                    dispatch(getAdvancePayment(PurchaseToSale?.supplier_ledger_id, "purchase", purchaseToSaleId));
                }

                preparePurchaseToSale(PurchaseToSale, {
                    purchaseToSaleId,
                    setStateFunctions: {
                        gstQuote,
                        setGstQuote,
                        setItemType,
                        setClassification,
                        setItems,
                        setAccountingItems,
                        setAddLessChanges,
                        setAdditionalCharges,
                        setPartyAddress,
                        setGstCalculation,
                        setBrokerDetail,
                        setTransporterDetail,
                        setEwayBillDetail,
                        setOtherDetail,
                        setGstValue,
                        setTcsRate,
                        setPaymentLedgerDetail,
                        setGrandTotal,
                        setMainGrandTotal,
                        setChangeTax,
                        setFinalAmount,
                        setIsIGSTCalculation,
                        setIsSGSTCalculation,
                        setTaxableValue,
                        setCessValue,
                        setSelectedAdvancePayment,
                        setCustomHeaderListTransaction,
                        setSameAsBill
                    },
                    dispatch,
                    tableHeaderList,
                    accountingTableHeader,
                    addLessChanges,
                    additionalCharges,
                    classificationOptions,
                    isDuplicate
                });
            }
        }, [PurchaseToSale, classificationOptions]);

    useEffect(() => {
        setLoader(true);
        setConfigurationURL(
            saleCreateOrReturn
                ? apiBaseURL.SALE_RETURN_CONFIGURATION
                : apiBaseURL.SALE_CONFIGURATION
        );
        dispatch(
            fetchConfigurationList(
                saleCreateOrReturn
                    ? apiBaseURL.SALE_RETURN_CONFIGURATION
                    : apiBaseURL.SALE_CONFIGURATION
            )
        );
        dispatch(fetchCompanyDetails());
        dispatch(fetchPartyDetail(""));
        if (!saleEdit) {
            dispatch(fetchPartyList());
        }
        dispatch(fetchUserPermission());
        dispatch(getSaleDetailByEstimateChallan());
        dispatch(fetchInvoiceNumber(saleCreateOrReturn));
        dispatch(fetchDispatchAddressList());
        setTimeout(() => {
            dispatch(fetchClassificationList());
            dispatch(fetchBrokerList());
            dispatch(fetchTransportList());
            dispatch(fetchPrevNextUrl(saleCreateOrReturn ? { type: "5", id: id } : { type: "1", id: id }));
            if (!saleEdit) {
                dispatch(fetchBankList());
                dispatch(fetchItemList());
                dispatch(fetchItemLedgerDetail());
                dispatch(fetchPaymentLedgerList());
                dispatch(fetchTcsList({id:1}));
                dispatch(fetchTdsList({id:2}));
                dispatch(fetchAddlessLedgerList());
                dispatch(fetchAdditionalLedgerList());
            }
            dispatch(fetchPaymentModeList(isSaleReturn ? 2 : 1));
        }, 1500);
    }, [dispatch]);

    const onSuccessInvoice = () => {
        if (submitTypeLocal === "duplicate" || submitTypeLocal === "saveAndNew") {
            window.location.href = `${window.location.origin}/company/${
                saleCreateOrReturn ? "sale-returns/create" : "sales/create"
            }`;
        } else {
            window.location.href = `${window.location.origin}/company/${
                saleCreateOrReturn ? "sale-returns" : "sales"
            }`;
        }
    };

    const handleEInvoice = () => {
        setShowEInvoiceModal({
            open: false,
            id: showEInvoiceModal.id,
        });
        setLoader(true);
        dispatch(
            createEInvoice(
                showEInvoiceModal.id,
                saleCreateOrReturn ? "Sale Return" : "Sale",
                setLoader,
                onSuccessInvoice,
                setIsDisable
            )
        );
        setIsDisable(false);
    };

    useEffect(() => {
        if (
            !configurationList?.header?.is_change_gst_details &&
            // !classification?.classification_nature_name &&
            localDispatchAddress &&
            company?.company?.is_gst_applicable &&
            ((!id && !saleId && !estimateId && !isDuplicate && !isSaleReturnDuplicate) || isCheckGstType)
        ) {
            if (
                partyAddress?.billingAddress?.state_id ===
                localDispatchAddress[selectedAddress]?.state_id
            ) {
                setIsSGSTCalculation(true);
                setIsIGSTCalculation(false);
            } else {
                setIsSGSTCalculation(false);
                setIsIGSTCalculation(true);
            }
        }
    }, [
        ledger?.partyDetail?.billingAddress?.state_id,
        localDispatchAddress,
        gstQuote.original_inv_no,
        configurationList?.header?.is_change_gst_details,
        isCheckGstType,
        partyAddress?.billingAddress,
    ]);

    useEffect(() => {
        setLocalDispatchAddress(dispatchAddress?.dispatchAddress);
    }, [dispatchAddress?.dispatchAddress]);

    // -51.7864
    useEffect(() => {
        if (isEditCalculation) {
            const { itemTotal, cessTotal } = calculateTotals(
                itemType === "accounting" ? accountingItems : items,
                classificationType
            );
            setGrandTotal(itemTotal);
            const addition_charges = calculateAdditionalCharges(
                additionalCharges,
                itemTotal,
                isIGSTCalculation
            );
            setTaxableValue(parseFloat(addition_charges?.addition_charges) + itemTotal);
            // setCessValue(cessTotal);
            const newInvoiceValue = isIGSTCalculation
                ? parseFloat(taxableValue || 0) +
                  parseFloat(cessValue || 0) +
                  parseFloat(gstValue?.igstValue || 0) +
                  parseFloat(additionalGst || 0)
                : parseFloat(taxableValue || 0) +
                  parseFloat(cessValue || 0) +
                  parseFloat(gstValue?.sgstValue || 0) +
                  parseFloat(additionalGst || 0) +
                  parseFloat(additionalGst || 0) +
                  parseFloat(gstValue?.cgstValue || 0);
            setInvoiceValue(newInvoiceValue);
            if (taxableValue > 0 && tcsRate?.tcs_rate && !isTcsAmountChange) {
                const tcsRateAmount =
                    tcsRate.tcs_calculated_on == CALCULATION_ON_TYPE.INVOICE_VALUE
                        ? parseFloat(newInvoiceValue * ((tcsRate?.tcs_rate || 0) / 100))
                        : parseFloat(taxableValue * ((tcsRate?.tcs_rate || 0) / 100));
                setTcsRate({
                    ...tcsRate,
                    tcs_amount: parseFloat(tcsRateAmount || 0).toFixed(2),
                });
            }
            const cgst =
                !classification.rcm_applicable && !isIGSTCalculation && isSGSTCalculation
                    ? customToFixed(
                          parseFloat(gstValue?.cgstValue || 0) + parseFloat(additionalGst || 0),
                          2
                      )
                    : 0;
            const sgst =
                !classification.rcm_applicable && !isIGSTCalculation && isSGSTCalculation
                    ? customToFixed(
                          parseFloat(gstValue?.sgstValue) + parseFloat(additionalGst || 0),
                          2
                      )
                    : 0;
            const igst =
                !classification.rcm_applicable && isIGSTCalculation
                    ? customToFixed(
                          parseFloat(gstValue?.igstValue) + parseFloat(additionalGst || 0),
                          2
                      )
                    : 0;
            let totalWithoutAddLess =
                parseFloat(itemTotal) +
                parseFloat(addition_charges?.addition_charges || 0) +
                parseFloat(
                    configurationList?.footer?.is_enabled_tcs_details
                        ? tcsRate.tcs_tax_id
                            ? isNaN(tcsRate.tcs_amount)
                                ? 0
                                : tcsRate.tcs_amount
                            : 0 || 0
                        : 0
                ) +
                // (parseFloat(totalAddLessAmount) || 0) +
                parseFloat(company?.company?.is_gst_applicable ? cessValue || 0 : 0) +
                parseFloat(company?.company?.is_gst_applicable ? cgst : 0) +
                parseFloat(company?.company?.is_gst_applicable ? sgst : 0) +
                parseFloat(company?.company?.is_gst_applicable ? igst : 0);

            totalWithoutAddLess = parseFloat(customToFixed(totalWithoutAddLess, 2));

            const updatedAddLessCharges = calculateAddLessCharges(
                addLessChanges,
                totalWithoutAddLess
            );
            const totalAddLessAmount = updatedAddLessCharges?.reduce((sum, change) => {
                if (change.al_type === 1) {
                    return sum + (parseFloat(change.al_value) || 0);
                } else if (change.al_type === 2) {
                    return sum + (parseFloat(change.al_total) || 0);
                }
                return sum;
            }, 0);
            let tempMainTotal = 0;
            if (!classification.rcm_applicable && isIGSTCalculation && classification.classification_nature_name !== "Export Sales Taxable" && classification.classification_nature_name !== "Sales to SEZ Taxable") {
                tempMainTotal = parseFloat(totalWithoutAddLess + (totalAddLessAmount || 0));
            }else{
                tempMainTotal = parseFloat(totalWithoutAddLess + (totalAddLessAmount || 0) - igst);
            }
            setMainGrandTotal(parseFloat(tempMainTotal));
            if (!configurationList?.header?.is_change_gst_details) {
                const dispatch_address = localDispatchAddress[selectedAddress];
                if(isCheckGstType){
                    if (partyAddress.billingAddress.state_id == dispatch_address?.state_id) {
                        classification.classification_nature_name = "Intrastate Sales Taxable";
                    } else {
                        classification.classification_nature_name = "Interstate Sales Taxable";
                    }
                }
            }
            const gstData = calculateAdditionalClassification(classification, addition_charges?.acTotal);
            setAdditionalGst(gstData);
        }
    }, [
        taxableValue,
        tcsRate.tcs_amount,
        grandTotal,
        mainGrandTotal,
        cessValue,
        items,
        addLessChanges,
        gstValue,
        additionalGst,
        additionalCharges,
        configurationList?.footer?.round_off_method,
        configurationList?.footer?.is_enabled_tcs_details,
        configurationList?.header?.is_change_gst_details
    ]);

    const calculateTotalInSale = () => {
        const { grandFinalAmount, roundOffAmount } = RoundOffMethod(
            mainGrandTotal,
            (gstQuote?.quotes_id?.length == 1 || gstQuote.original_inv_no || saleId || id) && !isCheckGstType && gstCalculation?.round_off_method ? gstCalculation?.round_off_method : configurationList?.footer?.round_off_method
        );
        setGstCalculation({
            ...gstCalculation,
            is_gst_enabled: company?.company?.is_gst_applicable,
            round_of_amount: roundOffAmount,
        });
        setFinalAmount(grandFinalAmount);
    };

    useEffect(() => {
        if (isEditCalculation) {
            calculateTotalInSale();
        }
    }, [mainGrandTotal, configurationList?.footer?.round_off_method, company, additionalCharges, isCheckGstType]);

    useEffect(() => {
        const updateData = (updateKey, value) => {
            dispatch(
                updateSingleConfiguration("sale-configuration", {
                    [updateKey]: value,
                })
            );
        };

        if (challanId) {
            if (!configurationList?.header?.is_enabled_delivery_challan) {
                updateData("is_enabled_delivery_challan", 1);
                updateData("is_enabled_estimate_quote", 0);
            }
        } else if (estimateId) {
            if (!configurationList?.header?.is_enabled_estimate_quote) {
                updateData("is_enabled_estimate_quote", 1);
                updateData("is_enabled_delivery_challan", 0);
            }
        }
    }, [challanId, estimateId]);

    useEffect(() => {
        if (isEditCalculation) {
            const newInvoiceValue = isIGSTCalculation
                ? parseFloat(taxableValue || 0) +
                  parseFloat(cessValue || 0) +
                  parseFloat(gstValue?.igstValue || 0)
                : parseFloat(taxableValue || 0) +
                  parseFloat(cessValue || 0) +
                  parseFloat(gstValue?.sgstValue || 0) +
                  parseFloat(gstValue?.cgstValue || 0);
            setInvoiceValue(newInvoiceValue);
            if (tcsRate?.tcs_rate) {
                setTcsRate(prevRate => ({
                    ...prevRate,
                    tcs_amount:
                        tcsRate.tcs_calculated_on == CALCULATION_ON_TYPE.INVOICE_VALUE
                            ? parseFloat(
                                  newInvoiceValue * ((tcsRate?.tcs_rate || 0) / 100)
                              ).toFixed(2)
                            : parseFloat(taxableValue * ((tcsRate?.tcs_rate || 0) / 100)).toFixed(
                                  2
                              ),
                }));
            }
            if (paymentLedgerDetail.tds_rate) {
                setPaymentLedgerDetail({
                    ...paymentLedgerDetail,
                    tds_amount: RoundOffMethodForTds(taxableValue * (paymentLedgerDetail?.tds_rate / 100), paymentLedgerDetail?.rounding_type),
                });
            }
        }
    }, [taxableValue]);

    useEffect(() => {
        if (
            !configurationList?.header?.is_change_gst_details &&
            company?.company?.is_gst_applicable && isCheckGstType
        ) {
            setClassification({
                rcm_applicable: false,
                classification_nature_name: "",
                classification_nature: 0,
            });
            if (
                partyAddress?.billingAddress?.state_id ===
                localDispatchAddress[selectedAddress]?.state_id
            ) {
                setIsSGSTCalculation(true);
                setIsIGSTCalculation(false);
            } else {
                setIsSGSTCalculation(false);
                setIsIGSTCalculation(true);
            }
        }
    }, [configurationList?.header?.is_change_gst_details]);

    useEffect(() => {
        let updatedHeaders = table?.tableHeader || [];
        if (
            configurationList?.item_table_configuration?.is_enabled_discount_2 === false ||
            configurationList?.item_table_configuration?.is_enabled_discount_2 == 0
        ) {
            updatedHeaders = updatedHeaders?.filter(
                item => item.header !== TABLE_HEADER_TYPE.DISCOUNT_2
            );
        }
        if (configurationList?.item_table_configuration?.is_enabled_mrp === false) {
            updatedHeaders = updatedHeaders?.filter(item => item.header !== TABLE_HEADER_TYPE.MRP);
        }
        if (configurationList?.item_table_configuration?.is_enable_free_quantity === false) {
            updatedHeaders = updatedHeaders?.filter(item => item.header !== TABLE_HEADER_TYPE.FREE_QUANTITY);
        }
        if (!configurationList?.item_table_configuration?.is_enabled_hsn_code) {
            updatedHeaders = updatedHeaders?.filter(item => item.header !== TABLE_HEADER_TYPE.HSN_SAC);
        }
        const filteredColumns = updatedHeaders.filter(col => col?.is_enabled !== false);
        setUpdatedTableHeader(filteredColumns);
    }, [table?.tableHeader, configurationList]);

    useEffect(() => {
        if (
            !id &&
            !isDuplicate &&
            company?.company?.currentFinancialYear &&
            invoice?.invoiceNumber?.invoice_number
        ) {
            const maxDate = moment(company?.company?.currentFinancialYear?.yearEndDate).format(
                "YYYY-MM-DD"
            );
            let newDate = "";
            const date1 = new Date();
            const date2 = new Date(maxDate);

            if (date1 < date2) {
                newDate = moment(date1).format("DD-MM-YYYY");
            } else {
                newDate = moment(date2).format("DD-MM-YYYY");
            }
            setInvoiceDetail({
                ...invoiceDetail,
                invoice_date: newDate,
                challan_date: newDate,
                invoice_number: invoice?.invoiceNumber?.invoice_number,
            });
            setChangeTax(invoice?.invoiceNumber?.with_tax ? 1 : 0);
        }
        if(PurchaseToSale){
            const maxDate = moment(company?.company?.currentFinancialYear?.yearEndDate).format(
                "YYYY-MM-DD"
            );
            let newDate = "";
            const date1 = new Date();
            const date2 = new Date(maxDate);

            if (date1 < date2) {
                newDate = moment(date1).format("DD-MM-YYYY");
            } else {
                newDate = moment(date2).format("DD-MM-YYYY");
            }
            setInvoiceDetail({
                ...invoiceDetail,
                invoice_date: newDate,
                challan_date: newDate,
                invoice_number: invoice?.invoiceNumber?.invoice_number,
            });
            setChangeTax(invoice?.invoiceNumber?.with_tax ? 1 : 0);
        }
    }, [invoice, company?.company?.currentFinancialYear, PurchaseToSale]);

    useEffect(() => {
        setTimeout(() => {
            if(configurationList){
                setLoader(false);
            }
        },300)
    },[configurationList])

    useEffect(() => {
        if(challanId){
            dispatch(rearrangeItemList(TRANSACTION_TYPE.SALE, 1));
            setItemType("item");
        }else if(invoice?.invoiceNumber?.invoice_type){
            dispatch(rearrangeItemList(saleCreateOrReturn ? TRANSACTION_TYPE.SALE_RETURN : TRANSACTION_TYPE.SALE, invoice?.invoiceNumber?.invoice_type == 2 ? 1 : 2));
            setItemType(invoice?.invoiceNumber?.invoice_type == 2 ? "item" : "accounting");
        }
    }, [invoice])

    useEffect(() => {
        const payment_detail = paymentLedgerDetail?.payment_detail?.map(payment => {
            const paymentDate = invoiceDetail.invoice_date;
            return {
                ...payment,
                pd_date: payment.is_show_invoice_date ? paymentDate : payment.pd_date,
            };
        });
        setPaymentLedgerDetail({
            ...paymentLedgerDetail,
            payment_detail: payment_detail,
        });
    }, [invoiceDetail]);

    useEffect(() => {
        if (configurationList?.header?.is_change_gst_details && !singleDeliveryChallan) {
            const gstData = calculateClassification(
                classification,
                itemType === "item"
                    ? findGstRate(items, gstOptions)
                    : findGstRate(accountingItems, gstOptions)
            );

            if (isEditCalculation) {
                setGstValue({
                    cgstValue: gstData.totalCgstTax !== NaN ? gstData?.totalCgstTax : 0.0,
                    sgstValue: gstData?.totalSgstTax || 0,
                    igstValue: gstData?.totalIgstTax || 0,
                });
            }
        } else {
            const matchState =
                ledger?.partyDetail?.billingAddress?.state_id ===
                localDispatchAddress[selectedAddress]?.state_id;
            const gstData = gstCalculate(
                itemType === "item"
                    ? findGstRate(items, gstOptions)
                    : findGstRate(accountingItems, gstOptions),
                grandTotal,
                matchState
            );
            if (isEditCalculation) {
                setGstValue({
                    cgstValue:
                        gstData.totalCgstTax !== NaN ? parseFloat(gstData.totalCgstTax) : 0.0,
                    sgstValue: parseFloat(gstData.totalSgstTax) || 0,
                    igstValue: parseFloat(gstData.totalIgstTax) || 0,
                });
            }
        }
    }, [
        classification,
        grandTotal,
        company,
        ledger?.partyDetail,
        accountingItems,
        items,
        additionalCharges,
        addLessChanges,
        tcsRate.tcs_amount,
        configurationList?.footer?.round_off_method,
    ]);

    useEffect(() => {
        if (ledger?.partyDetail?.billingAddress?.state_id) {
            const GstType =
                localDispatchAddress[selectedAddress]?.state_id ==
                localDispatchAddress[selectedAddress]?.state_id
                    ? true
                    : false;
            setIsShowGstValue(GstType);
        }
    }, [company, ledger?.partyDetail]);

    useEffect(() => {
        if (gstQuote?.quotes_id?.length > 0 && !id) {
            const matchedQuotes = updateParty_quotesOptions?.filter(item =>
                gstQuote?.quotes_id?.includes(item?.value?.toString())
            );
            setGstQuote({
                ...gstQuote,
                original_inv_date: fetchSaleDetail?.date
                    ? formattedDate(fetchSaleDetail?.date)
                    : "",
                quotes_id: matchedQuotes,
            });
        }
    }, [updateParty_quotesOptions, fetchSaleDetail]);

    useTransactionShortcuts(formRef);

    const handleSubmit = async (e, isConfirmed = false) => {
        const submitType = e.nativeEvent.submitter.value;
        const submit_button = submitType === "save" ? 1 : submitType == "saveAndNew" ? 2 : 3;

        if (submitType == "saveAndNew") {
            setSubmitButtonType("saveAndNew")
        }

        const submit_button_type = submitType == "saveAndNew" ? "saveAndNew" : "";
        const buttonType = submit_button_type ? submit_button_type : submitButtonType || ""

        e.preventDefault();
        const dispatch_address = localDispatchAddress[selectedAddress];

        const isFirstDetailInvalid = detail => {
            return (
                (detail.ac_ledger_id === null && !detail?.ac_value) ||
                detail.ac_value === "" ||
                (detail.ac_value == NaN && detail.ac_gst_rate_id === null) ||
                (detail.ac_gst_rate_id?.value === null && detail.ac_total === 0)
            );
        };

        const {
            itemList: item,
            is_na,
            hasNegativeTotal,
        } = prepareItemsData(
            items,
            gstCalculation,
            setGstCalculation,
            company,
            gstOptions,
            configurationList,
            changeTax
        );
        const {
            ledgerList: ledger,
            is_na: is_na_ledger,
            hasNegativeLedgerTotal,
        } = prepareLedgerData(
            accountingItems,
            gstCalculation,
            setGstCalculation,
            gstOptions,
            configurationList,
            changeTax
        );

        const { isExempt, isNa } = areAllProductsExemptOrNA(
            itemType === "accounting" ? accountingItems : items
        );
        const { isAdditionalChargesExempt, isAdditionalChargesNa } =
            checkAllAdditionalChargesNaAndExempt(additionalCharges);

        const { is_cgst_sgst_igst_calculated, is_gst_na } = calculateGSTFlags(
            company,
            isNa,
            isExempt,
            isAdditionalChargesNa,
            isAdditionalChargesExempt
        );

        if (submitType) {
            setSubmitTypeLocal(submitType === "saveAndNew" ? "duplicate" : submitType);
            setIsDisable(true);
            if (
                company?.company?.is_gst_applicable &&
                configurationList?.header?.is_change_gst_details &&
                !classification.classification_nature_name
            ) {
                setIsDisable(false);
                return dispatch(
                    errorToast({
                        text: "Classification nature type is required.",
                        type: toastType.ERROR,
                    })
                );
            }

            if (hasNegativeTotal) {
                setIsDisable(false);
                return dispatch(
                    errorToast({
                        text: "Negative Item Amount Not Accepted.",
                        type: toastType.ERROR,
                    })
                );
            }

            if (hasNegativeLedgerTotal) {
                setIsDisable(false);
                return dispatch(
                    errorToast({
                        text: "Negative Item Amount Not Accepted.",
                        type: toastType.ERROR,
                    })
                );
            }

            if (otherDetail.credit_limit && !isConfirmed && !saleCreateOrReturn) {
                const paidAmount = paymentLedgerDetail?.payment_detail.reduce((total, item) => {
                    const amount = parseFloat(item.pd_amount);
                    return total + (isNaN(amount) ? 0 : amount);
                }, 0);

                const data = {
                    customer_ledger_id: gstQuote.party_ledger_id,
                    grand_total: customToFixed(finalAmount, 2),
                    amount_paid_with_transaction: paidAmount,
                };

                try {
                    const creditLimitData = await checkCreditLimit(data);
                    if (creditLimitData?.isCustomerCreditLimit) {
                        setCreditLimitModal({
                            open: true,
                            action: creditLimitData?.customerLimitAction,
                        });
                        return;
                    }
                } catch (error) {
                    console.error("Error checking credit limit:", error);
                }
            }

            //(is_na || is_na_ledger) && isAdditionalChargeNa

            const submitData = {
                invoice_number: invoiceDetail.invoice_number,
                date: invoiceDetail.invoice_date,
                dispatch_address_id: configurationList?.header?.is_enabled_dispatch_details
                    ? dispatchAddressId
                    : null,
                customer_ledger_id: gstQuote.party_ledger_id,
                ...(company?.company?.is_gst_applicable && {
                    gstin: gstQuote.gstin,
                }),
                ...(saleCreateOrReturn && {
                    original_inv_no: gstQuote.original_inv_no,
                    original_inv_date: gstQuote.original_inv_date,
                }),
                region_iso: gstQuote?.mobile?.region_iso,
                region_code: gstQuote?.mobile?.region_code,
                ...(configurationList?.header?.is_enabled_phone_number && {
                    party_phone_number: gstQuote?.mobile?.party_phone_number,
                }),
                billing_address: {
                    address_1: partyAddress.billingAddress.address_1,
                    address_2: partyAddress.billingAddress.address_2,
                    country_id: partyAddress.billingAddress.country_id,
                    state_id: partyAddress.billingAddress.state_id,
                    city_id: partyAddress.billingAddress.city_id,
                    pin_code: partyAddress.billingAddress.pin_code,
                },
                shipping_gstin: partyAddress?.shippingAddress?.shipping_gstin,
                shipping_name: partyAddress?.shippingAddress?.shipping_name,
                address_name: partyAddress?.shippingAddress?.address_name,
                party_name_same_as_address_name: partyAddress?.shippingAddress
                    ?.party_name_same_as_address_name
                    ? 1
                    : 0,
                ...(partyAddress?.shippingAddress?.shipping_address_id
                    ? { shipping_address_id: partyAddress?.shippingAddress?.shipping_address_id }
                    : {
                          shipping_address: configurationList?.header?.is_enabled_shipping_address
                              ? {
                                    address_name: partyAddress.shippingAddress?.address_name,
                                    shipping_gstin: partyAddress.shippingAddress?.shipping_gstin,
                                    shipping_name: partyAddress.shippingAddress?.shipping_name,
                                    address_1: partyAddress?.shippingAddress?.address_1,
                                    address_2: partyAddress?.shippingAddress?.address_2,
                                    country_id: partyAddress?.shippingAddress?.country_id,
                                    state_id: partyAddress?.shippingAddress?.state_id,
                                    city_id: partyAddress?.shippingAddress?.city_id,
                                    pin_code: partyAddress?.shippingAddress?.pin_code,
                                    party_name_same_as_address_name: partyAddress?.shippingAddress
                                        ?.party_name_same_as_address_name
                                        ? 1
                                        : 0,
                                }
                              : null,
                      }),
                broker_details:
                    configurationList?.header?.is_enabled_broker_details && brokerDetail?.broker_id
                        ? {
                              broker_id: parseFloat(brokerDetail.broker_id),
                              brokerage_for_sale: brokerDetail.broker_percentage,
                              brokerage_on_value_type: brokerDetail.brokerage_on_value,
                          }
                        : null,
                transport_details: configurationList?.header?.is_enabled_transport_details
                    ? {
                          transport_id: transporterDetail.transport_id,
                          transporter_document_number:
                              transporterDetail.transporter_document_number,
                          transporter_document_date: transporterDetail?.transporter_document_date,
                          transporter_vehicle_number: transporterDetail.transporter_vehicle_number,
                      }
                    : null,
                eway_bill_details: configurationList?.header?.is_enabled_eway_details
                    ? {
                          eway_bill_number: ewayBillDetail.eway_bill_number,
                          eway_bill_date: ewayBillDetail.eway_bill_date,
                      }
                    : null,
                other_details: {
                    po_no: otherDetail.po_number,
                    po_date: otherDetail.date,
                    credit_period: otherDetail.creditPeriod,
                    credit_period_type: otherDetail.creditPeriod
                        ? otherDetail.creditPeriodType
                        : null,
                },
                same_as_billing: sameAsBill ? 1 : 0,
                ...(!saleCreateOrReturn ? { sales_item_type: itemType === "item" ? 2 : 1 } : null),
                ...(saleCreateOrReturn
                    ? { sale_return_item_type: itemType === "item" ? 2 : 1 }
                    : null),
                ...(itemType === "item" && { items: item }),
                ...(itemType === "accounting" && { ledgers: ledger }),
                main_classification_nature_type: correctClassificationNatureType(
                    isCheckGstType,
                    classification.classification_nature_name,
                    configurationList?.header?.is_change_gst_details,
                    partyAddress.billingAddress.state_id,
                    dispatch_address?.state_id,
                    isNa,
                    isExempt,
                    isAdditionalChargesNa,
                    isAdditionalChargesExempt
                ),
                is_rcm_applicable: classification.rcm_applicable ? 1 : 0,
                narration: configurationList?.footer?.is_enable_narration
                    ? additionalCharges?.note || null
                    : null,
                term_and_condition: configurationList?.footer?.is_enabled_terms_and_conditions
                    ? additionalCharges?.terms_and_conditions
                    : null,
                ...(configurationList?.footer?.is_enabled_bank_details && {bank_id: additionalCharges?.bank_id}),
                ...(additionalCharges?.additional_detail?.length === 1 &&
                isFirstDetailInvalid(additionalCharges?.additional_detail[0])
                    ? { additional_charges: " " }
                    : {
                          additional_charges: additionalCharges?.additional_detail?.map(detail => ({
                              ...detail,
                              ...(company?.company?.is_gst_applicable && {
                                  ac_gst_rate_id: detail.ac_gst_rate_id?.value || null,
                              }),
                              // add_less_total: detail?.ac_total,
                              ac_total_without_tax:
                                  detail?.ac_type == 2 ? detail?.ac_total : detail.ac_value,
                          })),
                      }),
                taxable_value: customToFixed(parseFloat(taxableValue), 2),
                total: grandTotal,
                ...(company?.company?.is_gst_applicable && {
                    cgst: classification.rcm_applicable
                        ? 0
                        : customToFixed(
                              !isIGSTCalculation && isSGSTCalculation
                                  ? !isEditCalculation
                                      ? parseFloat(gstValue?.cgstValue)
                                      : parseFloat(gstValue?.cgstValue) +
                                        parseFloat(additionalGst || 0)
                                  : 0,
                              2
                          ) || 0,
                }),
                ...(company?.company?.is_gst_applicable && {
                    sgst: classification.rcm_applicable
                        ? 0
                        : customToFixed(
                              !isIGSTCalculation && isSGSTCalculation
                                  ? !isEditCalculation
                                      ? parseFloat(gstValue?.sgstValue)
                                      : parseFloat(gstValue?.sgstValue) +
                                        parseFloat(additionalGst || 0)
                                  : 0,
                              2
                          ) || 0,
                }),
                ...(company?.company?.is_gst_applicable && {
                    igst: classification.rcm_applicable
                        ? 0
                        : customToFixed(
                              isIGSTCalculation
                                  ? !isEditCalculation
                                      ? parseFloat(gstValue?.igstValue)
                                      : parseFloat(gstValue?.igstValue) +
                                        parseFloat(additionalGst || 0)
                                  : 0,
                              2
                          ),
                }),
                ...(configurationList?.footer?.is_enabled_tcs_details
                    ? { tcs_details: tcsRate.tcs_tax_id ? tcsRate : [] }
                    : {}),
                ...(addLessChanges && addLessChanges[0]?.al_ledger_id
                    ? {
                          add_less: addLessChanges,
                      }
                    : { add_less: " " }),
                grand_total: customToFixed(finalAmount, 2),
                gross_value: customToFixed(grandTotal, 2),
                ...(configurationList?.footer?.is_enabled_tds_details
                    ? {
                          tds_details: {
                              tds_tax_id: paymentLedgerDetail.tds_tax_id,
                              tds_rate: paymentLedgerDetail.tds_rate,
                              tds_amount: paymentLedgerDetail.tds_amount,
                          },
                      }
                    : {}),
                ...(paymentLedgerDetail.payment_detail[0]?.pd_ledger_id
                    ? {
                          payment_details: paymentLedgerDetail.payment_detail,
                      }
                    : {
                          payment_details: " ",
                      }),
                cess: customToFixed(cessValue, 2),
                round_off_amount: customToFixed(gstCalculation.round_of_amount, 2),
                is_gst_enabled: !company?.company?.is_gst_applicable
                    ? 0
                    : gstCalculation.is_gst_enabled || 1,
                rounding_amount: customToFixed(gstCalculation.round_of_amount, 2),
                is_cgst_sgst_igst_calculated,
                is_gst_na,
                is_round_off_not_changed: gstCalculation.is_round_off_not_changed ?? 1,
                submit_button_value: submit_button,
                advance_payment: selectedAdvancePayment,
                // custom_fields: customFieldListTransaction?.flatMap((customField) =>
                //     customField.value  ? [{ ...(customField?.id ? {id: customField?.id} : {}), custom_field_id: customField.custom_field_id, value: customField.value }] : []
                // ),
                custom_fields: customFieldListTransaction?.flatMap(customField =>
                    customField.value &&
                    (customField.is_enabled === undefined || customField.is_enabled)
                        ? [
                              {
                                  ...(customField?.id ? { id: customField.id } : {}),
                                  custom_field_id: customField.custom_field_id,
                                  value: customField.value,
                              },
                          ]
                        : []
                ),
                round_off_method:
                    (gstQuote?.quotes_id?.length == 1 || gstQuote.original_inv_no || saleId || id || isDuplicate || isSaleReturnDuplicate) &&
                    !isCheckGstType &&
                    gstCalculation?.round_off_method
                        ? gstCalculation?.round_off_method
                        : configurationList?.footer?.round_off_method,
            };

            const formData = convertToFormData(submitData);
            {
                !configurationList?.header?.is_enabled_delivery_challan &&
                    gstQuote?.quotes_id?.length > 0 &&
                    !saleCreateOrReturn &&
                    !isSaleReturnDuplicate &&
                    formData.append(
                        "estimate_quote_no",
                        gstQuote.quotes_id && gstQuote.quotes_id.map(quote => quote?.value)
                    );
            }
            {
                configurationList?.header?.is_enabled_delivery_challan &&
                    gstQuote?.quotes_id?.length > 0 &&
                    !saleCreateOrReturn &&
                    !isSaleReturnDuplicate &&
                    formData.append(
                        "delivery_challan_no",
                        gstQuote.quotes_id && gstQuote.quotes_id.map(quote => quote?.value)
                    );
            }
            if (additionalCharges?.upload_document) {
                additionalCharges.upload_document.forEach((doc, index) => {
                    if (doc.file) {
                        formData.append(
                            saleCreateOrReturn
                                ? `sale_return_document[${index}]`
                                : `sale_document[${index}]`,
                            doc.file ? doc.file : " "
                        );
                    }
                });
            }

            if (id && !isDuplicate && !isSaleReturnDuplicate && !PurchaseToSale) {
                dispatch(
                    updateSale(
                        id,
                        formData,
                        submitType,
                        saleCreateOrReturn,
                        submit_button,
                        setShowEInvoiceModal,
                        setIsDisable
                    )
                );
            } else {
                dispatch(
                    addSale(
                        formData,
                        submitType === "saveAndNew" ? "duplicate" : submitType,
                        saleCreateOrReturn,
                        submit_button,
                        setShowEInvoiceModal,
                        setIsDisable,
                        submitData,
                        buttonType
                    )
                );
            }
            setisFieldsChanges(false);
            setHasUnsavedChanges(false);
        }
    };

    const handleDeleteTransaction = () => {
        if (id) {
            openDeleteTransactionModel();
        }
    };

    const handleConfirmDelete = () => {
        if (id) {
            dispatch(deleteSale(id, saleCreateOrReturn, setShowDeleteWarningModel));
            closeDeleteTransactionModel();
        }
    };

    useEffect(() => {
        if (broker?.getBrokerDetailsById?.id) {
            setBrokerDetail({
                ...brokerDetail,
                broker_percentage: broker?.getBrokerDetailsById?.brokerage_for_sale || "",
                brokerage_on_value: broker?.getBrokerDetailsById?.brokerage_for_sale_type || "",
            });
        }
    }, [broker.getBrokerDetailsById]);

    const salesUrl = saleCreateOrReturn
        ? prevNext?.fetchPrevNextUrl?.nextBillId
            ? "/company/sale-returns"
            : "/company/sale-returns"
        : "/company/sales";

    const salePrevUrl = `${url}${"/company/sales/"}${
        prevNext?.fetchPrevNextUrl?.previousBillId + "/edit"
    }`;
    const saleNextUrl = `${url}${salesUrl}/${
        prevNext?.fetchPrevNextUrl?.nextBillId && !isDuplicate
            ? prevNext.fetchPrevNextUrl.nextBillId + "/edit"
            : "create"
    }`;
    const saleReturnPrevUrl = `${url}${"/company/sale-returns/"}${
        prevNext?.fetchPrevNextUrl?.previousBillId + "/edit"
    }`;
    const saleReturnNextUrl = `${url}${salesUrl}/${
        prevNext?.fetchPrevNextUrl?.nextBillId
            ? prevNext.fetchPrevNextUrl.nextBillId + "/edit"
            : "create"
    }`;

    const handleInput = (e) => {
        setisFieldsChanges(true);
    };

    const handleBack = () => {
        if(isFieldsChanges) {
            setHasUnsavedChanges(true);
        };
    };

    const handleBeforeUnload = (e) => {
        if (isFieldsChanges && isBackButtonClick) {
            e.preventDefault();
            e.returnValue = '';
        }
    };

    useEffect(() => {
        if (isBackButtonClick) {
                window.addEventListener('beforeunload', handleBeforeUnload);
            return () => {
                window.removeEventListener('beforeunload', handleBeforeUnload);
            };
        };
    }, [isFieldsChanges, isBackButtonClick]);

    return (
        <>
        <CustomHelmet title={isDuplicate
            ? "Add Sale Duplicate"
            : isSaleReturnDuplicate
            ? "Add Sale Return Duplicate"
            : saleCreateOrReturn
            ? saleEdit
                ? "Edit Sale Return"
                : "Add Sale Return"
            : saleEdit
            ? "Edit Sale"
            : "Add Sale"} />
            {loader ? (
                <Loader />
            ) : (sale.status === 404 || estimate.status === 404 || deliveryChallan.status === 404) && !window.location.pathname.includes("/create") ? (
                <Error404Page />
            ) : (
                <div className="mt-12" defaultActiveKey={"edit"} id="uncontrolled-tab-example">
                    <form ref={formRef} onSubmit={handleSubmit} onInput={handleInput}>
                        <Container fluid className="p-0">
                            <div className="content-wrapper-invoice py-6 px-lg-10 px-sm-8 px-6">
                                {saleCreateOrReturn ? (
                                    <SaleReturnInvoiceDetail
                                        tableHeaderList={tableHeaderList}
                                        accountingTableHeader={accountingTableHeader}
                                        id={id}
                                        isShowEstimateOrChallan
                                        invoiceRef={invoiceRef}
                                        ledgerModalName={"Add Customer"}
                                        ledgerModalEditName={"Update Customer"}
                                        estimateId={estimateId}
                                        saleId={saleId}
                                        shipping_address_type={saleReturnShippingAddressType}
                                        isDuplicate={isDuplicate || isSaleReturnDuplicate}
                                    />
                                ) : (
                                    <SaleInvoiceDetail
                                        tableHeaderList={tableHeaderList}
                                        accountingTableHeader={accountingTableHeader}
                                        isShowInvoiceDetails={true}
                                        isShowEstimateOrChallan
                                        id={id}
                                        invoiceRef={invoiceRef}
                                        ledgerModalName={"Add Customer"}
                                        ledgerModalEditName={"Update Customer"}
                                        isDuplicate={isDuplicate || isSaleReturnDuplicate}
                                        shipping_address_type={saleShippingAddressType}
                                    />
                                )}
                            </div>
                            <div className="custom-nav-tabs nav-tabs d-flex">
                                <a
                                    href={ !isFieldsChanges ? saleCreateOrReturn ? saleReturnPrevUrl : salePrevUrl : undefined}
                                    onClick={(e) => {
                                        if (isFieldsChanges) {
                                            e.preventDefault();
                                            setUnsavedBackUrl(saleCreateOrReturn ? saleReturnPrevUrl : salePrevUrl);
                                            setIsBackButtonClick(false);
                                            handleBack();
                                        }
                                    }}
                                    className={`arrow-btn prev-btn d-flex justify-content-center ${
                                        !prevNext?.fetchPrevNextUrl?.previousBillId && "disabled"
                                    } align-items-center cursor_pointer`}
                                >
                                    <i className="fa-solid fa-angle-left"></i>
                                </a>
                                <a
                                    href={ !isFieldsChanges ? saleCreateOrReturn ? saleReturnNextUrl : saleNextUrl : undefined}
                                    onClick={(e) => {
                                        if (isFieldsChanges) {
                                            e.preventDefault();
                                            setUnsavedBackUrl(saleCreateOrReturn ? saleReturnNextUrl : saleNextUrl);
                                            setIsBackButtonClick(false);
                                            handleBack();
                                        }
                                    }}
                                    className={`arrow-btn next-btn d-flex justify-content-center align-items-center  ${
                                        !prevNext?.fetchPrevNextUrl?.nextBillId && !id && "disabled"
                                    } me-3 cursor_pointer`}
                                >
                                    <i className="fa-solid fa-angle-right"></i>
                                </a>
                                {!saleCreateOrReturn && (
                                    <>
                                        <div className="nav-item">
                                            <div
                                                className="nav-link cursor-pointer"
                                                onClick={() =>
                                                    window.open(
                                                        "https://www.youtube.com/watch?v=UxN8HT6Hr2Q"
                                                    )
                                                }
                                            >
                                                <Youtube />
                                            </div>
                                        </div>
                                    </>
                                )}
                                <ConfigurationModal roundOffOption={roundOffOption} />
                            </div>
                        </Container>
                        <SaleItems
                            items={itemType === "item" ? items : accountingItems}
                            setItems={itemType === "item" ? setItems : setAccountingItems}
                            setOnlyItems={setItems}
                            setOnlyAccounting={setAccountingItems}
                            grandTotal={grandTotal}
                            setGrandTotal={setGrandTotal}
                            mainGrandTotal={mainGrandTotal}
                            shippingValue={shippingValue}
                            setShippingValue={setShippingValue}
                            tcsValue={tcsValue}
                            setTCSValue={setTCSValue}
                            tcsRateValue={tcsRateValue}
                            setTCSRateValue={setTCSRateValue}
                            packingCharge={packingCharge}
                            setPackingCharge={setPackingCharge}
                            tableHeader={updatedTableHeader}
                            tableHeaderList={tableHeaderList}
                            accountingTableHeader={accountingTableHeader}
                            isShowGstValue={isShowGstValue}
                            taxableValue={taxableValue}
                            setFinalAmount={setFinalAmount}
                            finalAmount={finalAmount}
                            isShowPaymentDetails={true}
                            showCreditLimit={!saleCreateOrReturn}
                            settlePaymentType={saleCreateOrReturn ? "sale-return" : "sale"}
                            shipping_address_type={saleShippingAddressType}
                        />
                        <Container fluid className="p-0 mt-10 fixed-bottom-section">
                            <div className="d-flex flex-wrap gap-sm-4 gap-3 fixed-buttons px-lg-10 px-sm-8 px-6">
                                <button
                                    type="submit"
                                    name="submitType"
                                    value="save"
                                    className="btn btn-primary"
                                    disabled={isDisable}
                                >
                                    Save
                                </button>
                                {!id && (
                                    <button
                                        type="submit"
                                        name="submitType"
                                        value="saveAndNew"
                                        className="btn btn-primary"
                                        disabled={isDisable}
                                    >
                                        Save & New
                                    </button>
                                )}
                                <button
                                    type="submit"
                                    name="submitType"
                                    value="saveAndPrint"
                                    className="btn btn-primary"
                                    disabled={isDisable}
                                >
                                    Save & Print
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        if(isFieldsChanges) {
                                            setUnsavedBackUrl(`${window.location.origin}${salesUrl}`);
                                            setIsBackButtonClick(false);
                                            handleBack();
                                        } else {
                                            (window.location.href = `${window.location.origin}${salesUrl}`)
                                        };
                                    }
                                    }
                                >
                                    Back
                                </button>
                                {id && (
                                    <button
                                        onClick={handleDeleteTransaction}
                                        className="btn btn-danger"
                                    >
                                        Delete
                                    </button>
                                )}
                            </div>
                        </Container>
                    </form>
                </div>
            )}
            <Toast />
            <WarningModal
                show={showEInvoiceModal.open}
                handleClose={() => {
                    setShowEInvoiceModal({ open: false, id: null });
                    if (submitTypeLocal === "duplicate" || submitTypeLocal === "saveAndNew") {
                        window.location.href = `${window.location.origin}/company/${
                            saleCreateOrReturn ? "sale-returns/create" : "sales/create"
                        }`;
                        setIsDisable(false);
                    } else {
                        window.location.href = `${window.location.origin}/company/${
                            saleCreateOrReturn ? "sale-returns" : "sales"
                        }`;
                        setIsDisable(false);
                    }
                }}
                handleSubmit={handleEInvoice}
                message="Do you want to generate E-Invoice?"
                confirmText="Yes"
                cancelText="No"
                confirmClass="success-btn"
                showCancelButton
                showConfirmButton
            />

            {creditLimitModal.action == 2 ? (
                <WarningModal
                    show={creditLimitModal.open}
                    handleClose={() => {
                        setCreditLimitModal({ open: false, id: null });
                        setIsDisable(false);
                    }}
                     handleSubmit={() => {
                        setCreditLimitModal({ open: false, id: null });
                        setIsDisable(false);
                    }}
                    message={
                        "The total customer invoice amount is more than Credit Limit Amount. So invoice is not generating?"
                    }
                    confirmText={"Ok"}
                    confirmClass="btn-danger"
                    showCancelButton={false}
                    showConfirmButton
                    isWarning={false}
                    modalTitle={"Blocked!"}
                />
             ) : (
                ""
            )}

            {deleteTransaction && (
                <WarningModal
                    show={deleteTransaction}
                    title="Delete!"
                    message="Are you sure want to delete this transaction?"
                    showCancelButton
                    showConfirmButton
                    confirmText="Yes, Delete"
                    cancelText="No, Cancel"
                    handleClose={closeDeleteTransactionModel}
                    handleSubmit={() => handleConfirmDelete()}
                />
            )}

            {creditLimitModal.action === 1 && (
                <WarningModal
                    show={creditLimitModal.open}
                    handleClose={() => {
                        setCreditLimitModal({ open: false, id: null });
                        setIsDisable(false);
                    }}
                    handleSubmit={() => {
                        setCreditLimitModal(prev => ({ ...prev, open: false }));
                        setTimeout(() => {
                            handleSubmit(
                                {
                                    nativeEvent: {
                                        submitter: { value: submitTypeLocal },
                                    },
                                    preventDefault: () => {},
                                },
                                true
                            );
                        }, 0);
                    }}
                    message={
                        "The total customer invoice amount is more than Credit Limit Amount. Are you sure to continue this?"
                    }
                    confirmText={"Yes, Continue"}
                    cancelText={"No, Cancel"}
                    confirmClass="btn-danger"
                    showCancelButton
                    showConfirmButton
                    isWarning
                />
            )}
            {showDeleteWarningModel && (
                <DeleteWarningModal
                    show={showDeleteWarningModel?.show}
                    handleClose={() => setShowDeleteWarningModel({ show: false })}
                    transactions={showDeleteWarningModel?.transactions}
                />
            )}
        </>
    );
};

export default SalesTransaction;
