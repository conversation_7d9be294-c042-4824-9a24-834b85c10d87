import React, { memo, useCallback, useContext, useEffect, useRef, useState } from "react";
import { Col, Container, Form, Row } from "react-bootstrap";
import Accordion from "react-bootstrap/Accordion";
import { useDispatch, useSelector } from "react-redux";
import { FormInput } from "../../components/ui/Input";
import ReactSelect from "../../components/ui/ReactSelect";
import { CALCULATION_ON_TYPE, ITEM_STATUS, LedgerType, TABLE_HEADER_TYPE, toastType, TRANSACTION_TYPE, transactionTypeMap } from "../../constants";
import { StateContext } from "../../context/StateContext";
import {
    calculateAdditionalCharges,
    calculateAddLessCharges,
    calculateTotal,
    calculateTotals,
    customToFixed,
    formattedDate,
    getCalculatedQuantity,
    RoundOffMethod,
    RoundOffMethodForTds,
    setGSTCalculationType,
    updateCustomFieldCalculation,
} from "../../shared/calculation";
import useDropdownOption from "../../shared/dropdownList";
import { negativeStock, rearrangeItemList } from "../../store/configuration/configurationSlice";
import {
    addItem,
    fetchItemList,
    fetchItemListForScan,
    fetchPriceListOfItems,
    getSingleItemById,
} from "../../store/item/itemSlice";
import {
    addLedger,
    fetchLedgerById,
    fetchLedgerGroupList,
    getLedgerGroupDetail,
    getLedgerModelDetail,
    getTcsTdsGroupList,
} from "../../store/ledger/ledgerSlice";
import { fetchTdsTcsRate } from "../../store/rate/rateSlice";
import ClassificationModal from "../modal/Classification/ClassificationModal";
import AddConsolidatingModal from "../modal/Item/ConsolidatingModal";
import AddItemModal from "../modal/Item/ItemModal";
import NegativeStockModal from "../modal/Item/NegativeStockModel";
import AddLedgerModal from "../modal/Ledger/LedgerModal";
import Item from "./Item";
import ShippingCard from "./ShippingCard";
import { getSaleDetailByEstimateChallan } from "../../store/sale/saleSlice";
import WarningModal from "./WarningModal";
import { convertToFormData, customFieldOptimization, prepareTransactionsBills, unitOption, unitOptionWithKey } from "../../shared/prepareData";
import { errorToast } from "../../store/actions/toastAction";
import DatePickerPaymentDetails from "../../components/ui/DatePickerPaymentDetails";
import PaymentTable from "../../components/ui/PaymentTable";
import { useParams } from "react-router-dom";
import { getAdvancePayment } from "../../store/advance-payment/advancePaymentSlice";
import { addItemOnTheFly, checkPathName, getEditLedgerFromList } from "../../shared/sharedFunction";
import PlusCircle from "../../assets/images/svg/PlusCircle";
import DynamicDescriptionModal from "./DynamicDescriptionModal";
import PaymentModeModal from "../modal/PaymentMode/PaymentModeModal";
import CustomFieldBatchModal from "../modal/Item/CustomFieldBatchModal";
import { editPaymentModeData } from "../../store/payment-mode/paymentModeSlice";

const optionStyles = {
    option: (provided, state) => ({
        ...provided,
        fontSize: "12px",
        backgroundColor: state.isSelected ? "#4F158C" : state.isFocused ? "#f7efff" : "white",
        color: state.isSelected ? "white" : state.isFocused ? "#4F158C" : "#181C32",
        padding: "3px 6px",
        cursor: "pointer",
    }),
    menu: provided => ({ ...provided, width: "101px", right: "0" }),
    singleValue: provided => ({
        ...provided,
        fontSize: "12px",
        cursor: "pointer",
    }),
};

const SaleItems = memo(
    ({
        items,
        setItems,
        shippingValue,
        tcsValue,
        setTCSValue,
        tcsRateValue,
        setTCSRateValue,
        packingCharge,
        tableHeader,
        isShowGstValue,
        taxableValue,
        setFinalAmount,
        finalAmount,
        isPurchase = false,
        showCreditLimit = false,
        setOnlyItems,
        setOnlyAccounting,
        isOcr = false,
        settlePaymentType,
        shipping_address_type,
        not_exists,
        notExistItems,
        isDeliveryChallan,
        isRecurring = false,
    }) => {
        const dispatch = useDispatch();
        const isExpenseCredit = window.location.pathname.includes("expense-credit-notes");
        const isExpenseDebit = window.location.pathname.includes("expense-debit-notes");
        const { configuration, item, sale, company, ledger, advancePayment } = useSelector(state => state);
        const saleConfiguration = configuration?.configuration;
        const {
            ledgerOptions,
            ledgerDefaultOptions,
            brokerOptions,
            ledgerDetailOptions,
            ledgerEntityType,
            ledgerGroupType,
            paymentLedgerOptions,
            itemOption,
            itemDefaultOption,
            additionalOptions,
            gstOptions,
            classificationOptions,
            itemUnitOption,
            paymentModeOptions,
            tdsOptions,
            discountOption,
            unitOfMeasurement,
            expenseLedgers
        } = useDropdownOption();
        const {
            isItemsLedgerModel,
            openItemsLedgerModel,
            closeItemsLedgerModel,
            isItemModel,
            openItemModel,
            closeItemModel,
            isClassificationModel,
            openClassificationModel,
            closeClassificationModel,
            isOpenQuantityModel,
            openQuantityModel,
            closeQuantityModel,
            isNegativeStock,
            openIsNegativeStockModel,
            closeIsNegativeStockModel,
            paymentLedgerDetail,
            setPaymentLedgerDetail,
            cessValue,
            setCessValue,
            classification,
            classificationType,
            setClassification,
            changeTax,
            setChangeTax,
            itemType,
            setItemType,
            mainGrandTotal,
            grandTotal,
            setGrandTotal,
            additionalCharges,
            setTaxableValue,
            setAdditionalGst,
            setIsEditCalculation,
            isEditCalculation,
            setCheckGroupLedgerType,
            setGstQuote,
            gstQuote,
            quantityId,
            setQuantityId,
            setGstValue,
            isIGSTCalculation,
            isSGSTCalculation,
            gstValue,
            invoiceDetail,
            purchaseInvoice,
            showItemPriceChangedModal,
            setShowItemPriceChangedModal,
            changeItemPrice,
            setChangeItemPrice,
            isConfirmed,
            setIsConfirmed,
            setMainGrandTotal,
            addLessChanges,
            setAddLessChanges,
            setAdditionalCharges,
            additionalGst,
            tcsRate,
            setSameAsBill,
            brokerDetail,
            setBrokerDetail,
            transporterDetail,
            setTransporterDetail,
            otherDetail,
            setOtherDetail,
            setIsIGSTCalculation,
            setIsSGSTCalculation,
            gstCalculation,
            setGstCalculation,
            itemRef,
            ledgerRef,
            unitRef,
            quantityRef,
            totalRef,
            mrpRef,
            amountRef,
            priceWithGstRef,
            discountTypeRef,
            discountTypeRef2,
            discountValueRef,
            discountValueRef2,
            gstRef,
            setInvoiceValue,
            invoiceValue,
            setTcsRate,
            localDispatchAddress,
            selectedAddress,
            itemOnFly,
            setItemOnFly,
            defaultItem,
            defaultAccountingItem,
            setIsCheckGstType,
            isCheckGstType,
            showPaymentTable,
            setShowPaymentTable,
            selectedAdvancePayment,
            setSelectedAdvancePayment,
            isChangeParty,
            setIsChangeParty,
            userPermission,
            setIsDescription,
            setNotExistItems,
            handleCloseModal,
            setIsDisable,
            setOcrResponse,
            setisFieldsChanges,
            setCustomHeaderListTransaction,
            isDescription,
            openLedgerModel,
            setNotExistParty,
            setPartyTitle,
            closeLedgerModel,
            itemIndex,
            setItemIndex,
            recurringInvoiceDetails,
            isManageCustomItemMaster,
            setIsManageCustomItemMaster,
            setPartyAddress
        } = useContext(StateContext);
        const lineItem = useRef(null);
        const {id} = useParams();
        const [modelName, setModelName] = useState("");
        const [ledgerModelName, setLedgerModelName] = useState("");
        const [isRenderItem, setIsRenderItem] = useState(false);
        const [updatedId, setUpdatedId] = useState(0);
        const [isRcsApplicable, setIsRcsApplicable] = useState(false);
        const [isChangeType, setIsChangeType] = useState(false);
        const [changeLedgerName, setChangeLedgerName] = useState("");
        const [currentAction, setCurrentAction] = useState("");
        const [isPaymentDetailsOpen, setIsPaymentDetailsOpen] = useState("0");
        const [isChangeTds, setIsChangeTds] = useState(false);
        const [scan, setScan] = useState([]);
        const [createNewItem, setCreateNewItem] = useState({
            name:"",
            index: null
        });
        const [isPaymentMode, setIsPaymentMode] = useState(false);
        const [paymentModeId, setPaymentModeId] = useState(null);

        const getCustomFieldTransactionType = Object.keys(transactionTypeMap).find(path =>
            checkPathName(path)
        ) ? transactionTypeMap[Object.keys(transactionTypeMap).find(path => checkPathName(path))] : "";

        const matchPathname = (route) =>{
            return window.location.pathname.includes(route);
        }

        const isShowAdvancePayment = matchPathname("create-sale-return") || matchPathname("sale-returns") || matchPathname("income-credit-notes") ||  matchPathname("income-credit-notes-create") || matchPathname("expense-debit-notes") || matchPathname("expense-debit-notes-create") || matchPathname("create-purchase-return") || matchPathname('purchase-returns-create') || matchPathname('purchase-returns');

        useEffect(() => {
            if(createNewItem?.name){
                addItemOnTheFly(dispatch, itemOnFly, createNewItem?.name, createNewItem?.index, items, setItems, setItemIndex);
            }
        },[createNewItem])

        const totalQuantity = items.reduce(
            (acc, item) => parseFloat(acc) + parseFloat(item.quantity) || 0.0,
            0
        );

        const maxDecimalPlaces = Math.max(
            ...items
                .filter(item => item?.quantity?.toString()?.includes("."))
                .map(item => item.quantity.toString().split(".")[1]?.length || 0),
            0
        );

        const handleKeydown = e => {
            // Check if the forms exist
            const saleFormExists = items;
            if (!saleFormExists) return;

            // Check if the focus is on the button
            const isFocus = document.activeElement.classList.contains("barcode-print-focus-button");
            if (!isFocus) return;

            // Handle Enter key
            if (e.which === 10 || e.which === 13) {
                e.preventDefault();
            }

            // Handle scan logic
            if (scan.length > 0 && e.timeStamp - scan[scan.length - 1].timeStamp > 10) {
                setScan([]);
            }

            if (e.key === "Enter" && scan.length > 0) {
                const scannedString = scan.reduce(
                    (scannedString, entry) => scannedString + entry.key,
                    ""
                );
                setScan([]);
                document.dispatchEvent(new CustomEvent("scanBarcode", { detail: scannedString }));
                return;
            }

            if (e.key !== "Shift") {
                const data = {
                    key: e.key,
                    timeStamp: e.timeStamp,
                    timeStampDiff:
                        scan.length > 0 ? e.timeStamp - scan[scan.length - 1].timeStamp : 0,
                };
                setScan(prevScan => [...prevScan, data]);
            }
        };

        const handleScanBarcode = async e => {
            const itemSku = e.detail;
            let itemOptionDetail = [...itemOption];
            let search = itemSku;
            dispatch(fetchItemList({ search }));
            const updatedData = await dispatch(fetchItemListForScan({ search }, itemOptionDetail));
            try {
                const itemList = [...items];
                const lastItemIndex = itemList?.length - 1;
                if (!itemSku) return;
                updatedData.forEach((item, index) => {
                    if (item.sku == itemSku) {
                        dispatch(
                            fetchPriceListOfItems(
                                [item.value],
                                gstQuote?.party_ledger_id,
                                null,
                                setIsConfirmed
                            )
                        );
                    }
                });
                const isSkuExists = itemList.some(item => item?.sku === itemSku);
                const matchedSkuIndex = itemList.findIndex(item => item?.sku === itemSku);
                const matchedSku = updatedData.find(item => item?.sku === itemSku);
                if (!matchedSku) {
                    return dispatch(
                        errorToast({
                            text: "Item with barcode not found.",
                            type: toastType.ERROR,
                        })
                    );
                }

                if (itemList[lastItemIndex]?.selectedItem && isSkuExists) {
                    itemList[matchedSkuIndex].quantity = parseFloat(itemList[matchedSkuIndex].quantity) + 1;
                    setUpdatedId(matchedSkuIndex);
                } else if (itemList[lastItemIndex]?.selectedItem && !isSkuExists) {
                    setUpdatedId(lastItemIndex + 1);
                } else if (!itemList[lastItemIndex]?.selectedItem) {
                    setUpdatedId(lastItemIndex);
                } else {
                    setUpdatedId(lastItemIndex + 1);
                }
                if (
                    saleConfiguration?.header?.is_change_gst_details &&
                    !classification?.classification_nature_name
                ) {
                    setTimeout(() => {
                        openClassificationModel();
                    }, 1000);
                }
                setIsEditCalculation(true);
                const focusButton = document?.querySelector(".barcode-print-focus-button");
                if (focusButton) focusButton.focus();
            } catch (error) {
                console.error("Error fetching item:", error);
            }
        };

        useEffect(() => {
            // Add event listeners
            document.addEventListener("keydown", handleKeydown);
            document.addEventListener("scanBarcode", handleScanBarcode);

            // Clean up event listeners on unmount
            return () => {
                document.removeEventListener("keydown", handleKeydown);
                document.removeEventListener("scanBarcode", handleScanBarcode);
            };
        }, [scan, items]);

        useEffect(() => {
            if (!isChangeType) {
                if(isCheckGstType){
                    setIsEditCalculation(true);
                }
                let itemsList = [...items];
                if (changeItemPrice && isConfirmed) {
                    const processItems = (itemsList, receivedItems, updatedId) => {
                        if (receivedItems?.length === 1) {
                            // Update only the item at updatedId
                            const singleItem = receivedItems[0];
                            if (singleItem?.item_master) {
                                const model = singleItem?.item_master?.model;

                                const unitDetail = singleItem?.unit_of_array;
                                const unitOptionDetail = unitOption(unitDetail);
                                setIsRcsApplicable(model?.is_rcm_applicable);
                                const custom_fields = items[updatedId]?.id === updatedId+1 ? updateCustomFieldCalculation(singleItem?.custom_fields_item_values) : itemsList[updatedId]?.custom_fields?.length > 0 ? updateCustomFieldCalculation(itemsList[updatedId]?.custom_fields) : updateCustomFieldCalculation(singleItem?.custom_fields_item_values)

                                const mergedCustomFields = updateCustomFieldCalculation(custom_fields)

                                const quantityFromCustomField = getCalculatedQuantity(mergedCustomFields);
                                const finalQuantity =
                                    quantityFromCustomField != null
                                        ? parseFloat(quantityFromCustomField || 1)?.toFixed(
                                              model?.decimal_places ?? 2
                                          )
                                        : parseFloat(itemsList[updatedId]?.quantity || 1).toFixed(
                                              model?.decimal_places ?? 2
                                          );
                                const calculatedData = {
                                    ...itemsList[updatedId],
                                    quantity: finalQuantity || 1,
                                    updatedRateWithoutGst: !isChangeParty
                                        ? isPurchase
                                            ? changeTax
                                                ? parseFloat(
                                                      model?.purchase_price_with_gst || 0
                                                  )?.toFixed(model?.decimal_places_for_rate ?? 2)
                                                : parseFloat(
                                                      model?.purchase_price_without_gst || 0
                                                  )?.toFixed(model?.decimal_places_for_rate ?? 2)
                                            : changeTax
                                            ? parseFloat(
                                                  model?.selling_price_with_gst || 0
                                              )?.toFixed(model?.decimal_places_for_rate ?? 2)
                                            : parseFloat(
                                                  model?.selling_price_without_gst || 0
                                              )?.toFixed(model?.decimal_places_for_rate ?? 2)
                                        : isChangeParty && model?.change_price_list
                                        ? isPurchase
                                            ? changeTax
                                                ? parseFloat(
                                                      model?.purchase_price_with_gst || 0
                                                  )?.toFixed(model?.decimal_places_for_rate ?? 2)
                                                : parseFloat(
                                                      model?.purchase_price_without_gst || 0
                                                  )?.toFixed(model?.decimal_places_for_rate ?? 2)
                                            : changeTax
                                            ? parseFloat(
                                                  model?.selling_price_with_gst || 0
                                              )?.toFixed(model?.decimal_places_for_rate ?? 2)
                                            : parseFloat(
                                                  model?.selling_price_without_gst || 0
                                              )?.toFixed(model?.decimal_places_for_rate ?? 2)
                                        : itemsList[updatedId]?.updatedRateWithoutGst,
                                    discountType: isPurchase
                                        ? model?.purchase_discount_type ?? 1
                                        : model?.discount_type ?? 1,
                                    discountValue: isPurchase
                                        ? model?.purchase_discount_value
                                        : model?.discount_value,
                                    discountType_2: 1,
                                    discountValue_2: null,
                                    gst_copy: model?.gst_tax?.tax_rate,
                                    decimal_places: model?.decimal_places || 2,
                                    decimal_places_for_rate: model?.decimal_places_for_rate || 2,
                                    gst: model?.gst_tax?.tax_rate,
                                    gst_id: model?.gst_tax?.id,
                                    cessRate: model?.gst_gst_cess_rate?.rate,
                                    total: isPurchase
                                        ? model?.purchase_price_without_gst
                                        : model?.selling_price_without_gst,
                                };

                                const calculatedTotal = calculateTotal(
                                    calculatedData,
                                    false,
                                    changeTax,
                                    itemType === "accounting"
                                );
                                itemsList[updatedId] = {
                                    ...itemsList[updatedId],
                                    id: itemsList[updatedId]?.id,
                                    selectedItem: singleItem?.item_master?.id,
                                    multiQuantity: [1, 0, 0, 0],
                                    additional_description: model?.description || "",
                                    selectedLedger: isPurchase
                                        ? model?.expense_ledger_id
                                        : model?.income_ledger_id,
                                    mrp: model?.mrp || 0,
                                    quantity: finalQuantity || 1,
                                    free_quantity: model?.free_quantity,
                                    rateWithGst: !isChangeParty
                                        ? isPurchase
                                            ? model?.purchase_price_with_gst
                                            : model?.selling_price_with_gst
                                        : model?.change_price_list
                                        ? isPurchase
                                            ? model?.purchase_price_with_gst
                                            : model?.selling_price_with_gst
                                        : itemsList[updatedId]?.rateWithGst,
                                    rateWithoutGst: !isChangeParty
                                        ? isPurchase
                                            ? model?.purchase_price_without_gst
                                            : model?.selling_price_without_gst
                                        : model?.change_price_list
                                        ? isPurchase
                                            ? model?.purchase_price_without_gst
                                            : model?.selling_price_without_gst
                                        : itemsList[updatedId]?.rateWithoutGst,
                                    updatedRateWithGst: !isChangeParty
                                        ? isPurchase
                                            ? model?.purchase_price_with_gst
                                            : model?.selling_price_with_gst || 0
                                        : model?.change_price_list
                                        ? isPurchase
                                            ? model?.purchase_price_with_gst
                                                : model?.selling_price_with_gst || 0
                                            : itemsList[updatedId]?.updatedRateWithGst,
                                    updatedRateWithoutGst: !isChangeParty ? isPurchase
                                        ? changeTax ? parseFloat(
                                            model?.purchase_price_with_gst || 0
                                        )?.toFixed(model?.decimal_places_for_rate ?? 2) : parseFloat(
                                            model?.purchase_price_without_gst || 0
                                        )?.toFixed(model?.decimal_places_for_rate ?? 2)
                                        : changeTax ? parseFloat(
                                            model?.selling_price_with_gst || 0
                                        )?.toFixed(model?.decimal_places_for_rate ?? 2) : parseFloat(
                                            model?.selling_price_without_gst || 0
                                        )?.toFixed(model?.decimal_places_for_rate ?? 2) :
                                        isChangeParty && model?.change_price_list ? isPurchase
                                            ? changeTax ? parseFloat(
                                                model?.purchase_price_with_gst || 0
                                            )?.toFixed(model?.decimal_places_for_rate ?? 2) :
                                                parseFloat(
                                                    model?.purchase_price_without_gst || 0
                                                )?.toFixed(model?.decimal_places_for_rate ?? 2)
                                            : changeTax ? parseFloat(
                                                model?.selling_price_with_gst || 0
                                            )?.toFixed(model?.decimal_places_for_rate ?? 2) : parseFloat(
                                                model?.selling_price_without_gst || 0
                                            )?.toFixed(model?.decimal_places_for_rate ?? 2) :
                                            itemsList[updatedId]?.updatedRateWithoutGst,
                                    gst_id: model?.gst_tax?.id || 0,
                                    gst: model?.gst_tax?.tax_rate || 0,
                                    gst_copy: model?.gst_tax?.tax_rate || 0,
                                    discountType: calculatedData.discountType || 1,
                                    discountValue: calculatedData.discountValue,
                                    discountType_2: calculatedData.discount_type_2 || 1,
                                    discountValue_2: calculatedData.discount_value_2,
                                    total: calculatedTotal.total || 0,
                                    cessRate: model?.gst_gst_cess_rate?.rate || 0,
                                    cessValue: calculatedTotal.cess || 0,
                                    selectedUnit: model?.unit_of_measurement?.id || null,
                                    itemUnitOption: unitOptionDetail,
                                    conversationRate: model?.conversion_rate || null,
                                    secondaryUnitOfMeasurement:
                                        model?.secondary_unit_of_measurement || null,
                                    decimal_places: model?.decimal_places || 2,
                                    decimal_places_for_rate: model?.decimal_places_for_rate || 2,
                                    sku: singleItem?.item_master?.sku,
                                    hsn_code: model?.hsn_sac_code,
                                    sgstValue: calculatedTotal.sgst,
                                    cgstValue: calculatedTotal.sgst,
                                    isShowDelete: true,
                                    itemType: singleItem?.item_master?.item_type,
                                    custom_fields: custom_fields,
                                    is_status: ITEM_STATUS.IN_ACTIVE,
                                };
                            }
                        } else if (receivedItems?.length > 1) {
                            // Merge multiple items into the list
                            const mergeItems = (existingItems, receivedItems) => {
                                const updatedItems = [...existingItems];

                                receivedItems.forEach(receivedItem => {
                                    const matchingIndex = updatedItems.findIndex(
                                        item => item.selectedItem === receivedItem?.item_master?.id
                                    );

                                    if (matchingIndex > -1) {
                                        const model = receivedItem?.item_master?.model;
                                        const unitDetail = receivedItem?.unitOfArray;
                                        const unitOptionDetail = unitOptionWithKey(unitDetail);
                                        setIsRcsApplicable(model?.is_rcm_applicable);

                                        const calculatedData = {
                                            ...updatedItems[matchingIndex],
                                            updatedRateWithoutGst: !isChangeParty
                                            ? isPurchase
                                            ? changeTax ? parseFloat(
                                                model?.purchase_price_with_gst || 0
                                            )?.toFixed(model?.decimal_places_for_rate ?? 2) : parseFloat(
                                                model?.purchase_price_without_gst || 0
                                            )?.toFixed(model?.decimal_places_for_rate ?? 2)
                                            : changeTax ? parseFloat(
                                                model?.selling_price_with_gst || 0
                                            )?.toFixed(model?.decimal_places_for_rate ?? 2) : parseFloat(
                                                model?.selling_price_without_gst || 0
                                            )?.toFixed(model?.decimal_places_for_rate ?? 2) :
                                            isChangeParty && model?.change_price_list ? isPurchase
                                                ? changeTax ? parseFloat(
                                                    model?.purchase_price_with_gst || 0
                                                )?.toFixed(model?.decimal_places_for_rate ?? 2) :
                                                    parseFloat(
                                                        model?.purchase_price_without_gst || 0
                                                    )?.toFixed(model?.decimal_places_for_rate ?? 2)
                                                : changeTax ? parseFloat(
                                                    model?.selling_price_with_gst || 0
                                                )?.toFixed(model?.decimal_places_for_rate ?? 2) : parseFloat(
                                                    model?.selling_price_without_gst || 0
                                                )?.toFixed(model?.decimal_places_for_rate ?? 2) :
                                                updatedItems[matchingIndex]?.updatedRateWithoutGst,
                                            discountType: isPurchase
                                                ? model?.purchase_discount_type ?? 1
                                                : model?.discount_type ?? 1,
                                            discountValue: isPurchase
                                                ? model?.purchase_discount_value
                                                : model?.discount_value,
                                            gst_copy: model?.gst_tax?.tax_rate,
                                            decimal_places: model?.decimal_places || 2,
                                            decimal_places_for_rate:
                                                model?.decimal_places_for_rate || 2,
                                            gst: model?.gst_tax?.tax_rate,
                                            gst_id: model?.gst_tax?.id,
                                            cessRate: model?.gst_gst_cess_rate?.rate,
                                            total: isPurchase
                                                ? model?.purchase_price_without_gst
                                                : model?.selling_price_without_gst,
                                        };

                                        const calculatedTotal = calculateTotal(
                                            calculatedData,
                                            false,
                                            changeTax,
                                            itemType === "accounting"
                                        );

                                        updatedItems[matchingIndex] = {
                                            ...updatedItems[matchingIndex],
                                            id: updatedItems[matchingIndex]?.id,
                                            multiQuantity: [1, 0, 0, 0],
                                            additional_description: updatedItems[matchingIndex]?.additional_description ? updatedItems[matchingIndex]?.additional_description : model?.description || "",
                                            selectedLedger: isPurchase
                                                ? model?.expense_ledger_id
                                                : model?.income_ledger_id,
                                            mrp: model?.mrp || 0,
                                            quantity: updatedItems[matchingIndex]?.quantity > 0 ? parseFloat(updatedItems[matchingIndex]?.quantity || 1)?.toFixed(model?.decimal_places ?? 2) : parseFloat(1).toFixed(model?.decimal_places ?? 2),
                                            free_quantity: model?.free_quantity,
                                            rateWithGst: !isChangeParty
                                                ? isPurchase
                                                    ? model?.purchase_price_with_gst
                                                    : model?.selling_price_with_gst
                                                : model?.change_price_list
                                                    ? isPurchase
                                                        ? model?.purchase_price_with_gst
                                                        : model?.selling_price_with_gst
                                                    : updatedItems[matchingIndex]?.rateWithGst,
                                            rateWithoutGst: !isChangeParty
                                                ? isPurchase
                                                    ? model?.purchase_price_without_gst
                                                    : model?.selling_price_without_gst
                                                : model?.change_price_list
                                                    ? isPurchase
                                                        ? model?.purchase_price_without_gst
                                                        : model?.selling_price_without_gst
                                                    : updatedItems[matchingIndex]?.rateWithoutGst,
                                            updatedRateWithGst: !isChangeParty
                                                ? isPurchase
                                                    ? model?.purchase_price_with_gst
                                                    : model?.selling_price_with_gst || 0
                                                : model?.change_price_list
                                                    ? isPurchase
                                                        ? model?.purchase_price_with_gst
                                                        : model?.selling_price_with_gst || 0
                                                    : updatedItems[matchingIndex]?.updatedRateWithGst,
                                            updatedRateWithoutGst: !isChangeParty ? isPurchase
                                                ? changeTax ? parseFloat(
                                                    model?.purchase_price_with_gst || 0
                                                )?.toFixed(model?.decimal_places_for_rate ?? 2) : parseFloat(
                                                    model?.purchase_price_without_gst || 0
                                                )?.toFixed(model?.decimal_places_for_rate ?? 2)
                                                : changeTax ? parseFloat(
                                                    model?.selling_price_with_gst || 0
                                                )?.toFixed(model?.decimal_places_for_rate ?? 2) : parseFloat(
                                                    model?.selling_price_without_gst || 0
                                                )?.toFixed(model?.decimal_places_for_rate ?? 2) :
                                                isChangeParty && model?.change_price_list ? isPurchase
                                                    ? changeTax ? parseFloat(
                                                        model?.purchase_price_with_gst || 0
                                                    )?.toFixed(model?.decimal_places_for_rate ?? 2) :
                                                        parseFloat(
                                                            model?.purchase_price_without_gst || 0
                                                        )?.toFixed(model?.decimal_places_for_rate ?? 2)
                                                    : changeTax ? parseFloat(
                                                        model?.selling_price_with_gst || 0
                                                    )?.toFixed(model?.decimal_places_for_rate ?? 2) : parseFloat(
                                                        model?.selling_price_without_gst || 0
                                                    )?.toFixed(model?.decimal_places_for_rate ?? 2) :
                                                    updatedItems[matchingIndex]?.updatedRateWithoutGst,
                                            gst_id: model?.gst_tax?.id || 0,
                                            gst: model?.gst_tax?.tax_rate || 0,
                                            gst_copy: model?.gst_tax?.tax_rate || 0,
                                            discountType: calculatedData.discountType,
                                            discountValue: calculatedData.discountValue,
                                            discountType_2: calculatedData.discount_type_2 || 1,
                                            discountValue_2: calculatedData.discount_value_2,
                                            total: calculatedTotal.total || 0,
                                            cessRate: model?.gst_gst_cess_rate?.rate || 0,
                                            cessValue: calculatedTotal.cess || 0,
                                            selectedUnit: model?.unit_of_measurement?.id || null,
                                            itemUnitOption: unitOptionDetail,
                                            conversationRate: model?.conversion_rate || null,
                                            secondaryUnitOfMeasurement:
                                                model?.secondary_unit_of_measurement || null,
                                            decimal_places: model?.decimal_places || 2,
                                            decimal_places_for_rate:
                                                model?.decimal_places_for_rate || 2,
                                            hsn_code: model?.hsn_sac_code,
                                            sku: receivedItem?.item_master?.sku,
                                            sgstValue: calculatedTotal.sgst,
                                            cgstValue: calculatedTotal.sgst,
                                            isShowDelete: true,
                                            itemType: receivedItem?.item_master?.item_type,
                                            // custom_fields: items[updatedId]?.id === updatedId+1 ? updateCustomFieldCalculation(singleItem?.custom_fields_item_values) : itemsList[updatedId]?.custom_fields?.length > 0 ? updateCustomFieldCalculation(itemsList[updatedId]?.custom_fields) : updateCustomFieldCalculation(singleItem?.custom_fields_item_values),
                                            is_status: ITEM_STATUS.IN_ACTIVE,
                                        };
                                    }
                                });

                                return updatedItems;
                            };

                            return mergeItems(itemsList, receivedItems);
                        }

                        return itemsList;
                    };

                    // Process items based on received data
                    itemsList = processItems([...items], item?.getSingleItemById, updatedId);
                }
                setItems(itemsList);

                if (isEditCalculation) {
                    const { cessTotal } = calculateTotals(
                        itemsList,
                        classificationType
                    );
                    if(isCheckGstType){ //cessTotal > 0 : remove cess calculation
                        setCessValue(cessTotal);
                    }
                }

                setTimeout(() => {
                    setIsConfirmed(false);
                    // dispatch(getSingleItemById(""));
                    setIsChangeParty(false);
                }, 1800);
                setIsRenderItem(false);
            }
        }, [
            item?.getSingleItemById,
            setItems,
            isRenderItem,
            updatedId,
            changeTax,
            isOpenQuantityModel,
            isConfirmed,
            changeItemPrice,
            saleConfiguration?.item_table_configuration?.is_enabled_discount_2,
            isNegativeStock,
            isIGSTCalculation,
        ]);

        useEffect(() => {
            if (!saleConfiguration?.item_table_configuration?.is_enabled_discount_2 && isCheckGstType) {
                let itemsList = [...items];
                itemsList = itemsList.map(item => {
                    if (item.discountValue_2 != 0) {
                        item.discountValue_2 = 0;
                        const calculatedTotal = calculateTotal(
                            item,
                            false,
                            changeTax,
                            itemType === "accounting"
                        );
                        item.total = calculatedTotal.total || 0;
                    }
                    return item;
                });

                const { itemTotal, gstTotal, cessTotal } = calculateTotals(
                    itemsList,
                    classificationType
                );

                setGrandTotal(itemTotal);

                const addition_charges = calculateAdditionalCharges(
                    additionalCharges,
                    grandTotal,
                    isIGSTCalculation
                );

                const gstTotalWithAmount = isIGSTCalculation
                    ? parseFloat(gstValue?.igstValue || 0) + parseFloat(additionalGst || 0)
                    : parseFloat(gstValue?.sgstValue || 0) +
                    parseFloat(additionalGst || 0) +
                    parseFloat(gstValue?.cgstValue) +
                    parseFloat(additionalGst || 0);
                const total =
                    parseFloat(company?.company?.is_gst_applicable ? gstTotalWithAmount : 0) +
                    parseFloat(taxableValue) +
                    parseFloat(company?.company?.is_gst_applicable ? cessValue : 0) +
                    parseFloat(saleConfiguration?.footer?.is_enabled_tcs_details ? tcsRate?.tcs_amount || 0 : 0);

                setTaxableValue(parseFloat(addition_charges?.addition_charges) + itemTotal);
                const addLessCharges = calculateAddLessCharges(addLessChanges, total);
                setAddLessChanges(addLessCharges);
                const acTotal = additionalCharges?.additional_detail?.reduce((total, item) => {
                    if (item.ac_type === 1) {
                        total += item.ac_total - item?.ac_value;
                    } else if (item?.ac_type == 2 && item?.ac_gst_rate_id) {
                        total += item?.ac_total * (item?.ac_gst_rate_id?.rate / 100);
                    }
                    return total;
                }, 0);

                setAdditionalGst(acTotal);
                if(isCheckGstType){
                    setCessValue(parseFloat(cessTotal));
                }
                setItems(itemsList);
            }
        }, [saleConfiguration?.item_table_configuration?.is_enabled_discount_2]);

        useEffect(() => {
            if (
                sale?.getSaleDetailByEstimateChallan &&
                sale?.getSaleDetailByEstimateChallan?.length !== 0 &&
                saleConfiguration?.header?.is_enabled_estimate_quote
            ) {
                setIsCheckGstType(true);
                const itemsList = [...sale?.getSaleDetailByEstimateChallan?.allTransactionItems];
                const invoiceDetail =
                    sale?.getSaleDetailByEstimateChallan?.estimateQuoteTransaction;

                const ids = [];
                const item_ledger_id = [];

                if (itemsList) {
                    itemsList?.forEach(item => {
                        ids.push(item.item_id);
                        item_ledger_id.push(item.ledger_id);
                    });
                }

                if (
                    sale?.getSaleDetailByEstimateChallan?.estimateQuoteTransaction?.invoice_type == 1
                ) {
                    // dispatch(getTableHeaderDetail(accountingTableHeader));
                    dispatch(rearrangeItemList(TRANSACTION_TYPE.SALE, 2));
                    setItemType("accounting");
                    setOnlyItems(defaultItem)
                } else {
                    setItemType("item");
                    setOnlyAccounting(defaultAccountingItem)
                    // dispatch(getTableHeaderDetail(tableHeaderList));
                    dispatch(rearrangeItemList(TRANSACTION_TYPE.SALE, 1));
                }

                if (ids.length > 0) {
                    dispatch(fetchItemList({ ids }));
                } else {
                    dispatch(fetchItemList());
                }
                getEditLedgerFromList({dispatch, item_ledger_id, singleTransaction: invoiceDetail});
                setCustomHeaderListTransaction(invoiceDetail?.custom_values)
                setSameAsBill(invoiceDetail?.same_as_billing);
                setBrokerDetail({
                    ...brokerDetail,
                    broker_id: invoiceDetail?.broker_id,
                    broker_percentage: invoiceDetail?.brokerage,
                    brokerage_on_value: invoiceDetail?.brokerage_on_value_type,
                });

                setTransporterDetail({
                    ...transporterDetail,
                    transport_id: invoiceDetail?.transport_id,
                    transporter_document_date: invoiceDetail?.transporter_document_date
                        ? formattedDate(invoiceDetail?.transporter_document_date)
                        : null,
                    transporter_document_number: invoiceDetail?.transporter_document_number,
                    transporter_vehicle_number: invoiceDetail?.transporter_vehicle_number,
                });

                setOtherDetail({
                    ...otherDetail,
                    po_number: invoiceDetail?.po_no,
                    date: invoiceDetail?.po_date ? formattedDate(invoiceDetail?.po_date) : null,
                    creditPeriod: invoiceDetail?.credit_period,
                    creditPeriodType: invoiceDetail?.credit_period_type ?? 1,
                });

                const updatedAddLessChanges =
                    invoiceDetail?.add_less?.length == 0
                        ? addLessChanges
                        : invoiceDetail?.add_less?.map(item => {
                            return {
                                al_ledger_id: item.ledger_id,
                                al_is_show_in_print: item.is_show_in_print,
                                al_type: item.type,
                                al_value:
                                    item.type == 2
                                        ? parseFloat(item.value)
                                        : parseFloat(item.total),
                                al_total: parseFloat(item.total),
                            };
                        });
                setAddLessChanges(updatedAddLessChanges);

                const updatedAdditionalCharges =
                    !invoiceDetail?.additional_charges ||
                        invoiceDetail?.additional_charges?.length == 0
                        ? additionalCharges.additional_detail
                        : invoiceDetail?.additional_charges?.map(charge => {
                            return {
                                ac_ledger_id: charge?.ledger_id,
                                ac_type: charge?.charge_type,
                                ac_value: charge?.value,
                                ac_gst_rate_id: {
                                    value: charge?.gst_rate_id,
                                    rate: Number(charge?.gst_percentage),
                                    label: `${charge?.gst_percentage}%`,
                                },
                                ac_total: charge?.total_without_tax,
                            };
                        });
                setAdditionalCharges({
                    upload_document: invoiceDetail?.media?.map(media => {
                        return { original_url: media?.original_url, id: media?.id };
                    }),
                    note: invoiceDetail?.narration,
                    terms_and_conditions: invoiceDetail?.term_and_condition,
                    additional_detail: updatedAdditionalCharges,
                });

                const updatedItemsList =
                    itemsList &&
                    itemsList.map((item, index) => {
                        // setChangeTax(item?.with_tax);
                        const unitOptionDetail = unitOptionWithKey(item?.unitOfArray);

                        const calculatedData = {
                            ...item,
                            updatedRateWithGst: item?.rpu_without_gst?.toFixed(2),
                            rateWithoutGst: item?.rpu_without_gst?.toFixed(
                                item?.decimal_places_for_rate || 2
                            ),
                            updatedRateWithoutGst: item?.rpu_without_gst?.toFixed(
                                item?.decimal_places_for_rate || 2
                            ),
                            updatedAmountWithGst: item?.rpu_without_gst?.toFixed(2),
                            amountWithoutGst: item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                            discountType: item?.discount_type,
                            discountValue: item?.discount_value,
                            discountType_2: item?.discount_type_2 || 1,
                            discountValue_2: item?.discount_value_2,
                            gst_copy: item?.gst_tax_percentage,
                            gst: item?.gst_tax_percentage,
                            gst_id: item?.gst_id,
                            cessRate: item?.cess_rate,
                            // total: item?.total,
                            // updatedTotal: item?.total,
                            quantity: item?.quantity,
                            selectedUnit: item?.unit_id,
                            // sgstValue: item?.classification_cgst_tax,
                            // cgstValue: item?.classification_cgst_tax,
                            // cessValue: item?.classification_cess_tax,
                            isShowDelete: true,
                        };

                        const calculatedTotal = calculateTotal(
                            calculatedData,
                            false,
                            changeTax,
                            itemType === "accounting"
                        );

                        // Return the updated item
                        return {
                            ...item,
                            transaction_item_id: item?.id,
                            selectedItem: item?.item_id,
                            total: calculatedTotal.total,
                            updatedTotal: calculatedTotal.total,
                            sgstValue: calculatedTotal.sgst,
                            cgstValue: calculatedTotal.sgst,
                            igstValue: calculatedTotal.igst,
                            cessValue: customToFixed(calculatedTotal.cess, 2),
                            // sgstValue: item?.classification_cgst_tax,
                            // cgstValue: item?.classification_cgst_tax,
                            // igstValue: item?.classification_igst_tax,
                            // cessValue: item?.classification_cess_tax,
                            // updatedTotal: item?.total,
                            // total: item?.total,
                            multiQuantity: [1, 0, 0, 0],
                            selectedUnit: item?.unit_id,
                            selectedLedger: item?.ledger_id,
                            gst_id: item?.gst_id,
                            gst: item?.gst_tax_percentage,
                            rateWithGst: parseFloat(item?.rpu_without_gst),
                            updatedRateWithGst: parseFloat(item?.rpu_without_gst?.toFixed(2)),
                            rateWithoutGst: parseFloat(
                                item?.rpu_without_gst?.toFixed(
                                    item?.decimal_places_for_rate || 2
                                )
                            ),
                            updatedRateWithoutGst: parseFloat(
                                item?.rpu_without_gst?.toFixed(
                                    item?.decimal_places_for_rate || 2
                                )
                            ),
                            amountWithoutGst: parseFloat(item?.rpu_without_gst),
                            updatedAmountWithoutGst: parseFloat(item?.rpu_without_gst),
                            quantity: item?.quantity,
                            free_quantity: item?.free_quantity,
                            mrp: item?.mrp || 0,
                            hsn_code: item?.hsn_code,
                            cessRate: item?.cess_rate,
                            gst_copy: item?.gst_tax_percentage,
                            with_tax: changeTax ? 1 : 0,
                            rpu: parseFloat(item?.rpu_without_gst),
                            additional_description: item?.additional_description || "",
                            itemUnitOption: unitOptionDetail,
                            conversationRate: item?.conversion_rate ?? 1,
                            item_decimal_places: item?.item_decimal_places,
                            item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                            secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                            decimal_places: item?.decimal_places ?? 2,
                            decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                            discountType: item?.discount_type,
                            discountValue: item?.discount_value,
                            discountType_2: item?.discount_type_2 || 1,
                            discountValue_2: item?.discount_value_2,
                            isShowDelete: true,
                            custom_fields: customFieldOptimization(item?.custom_fields),
                            is_status: ITEM_STATUS.ACTIVE,
                        };
                    });
                const nature_type =
                    itemsList?.length > 0 && itemsList[0]?.classification_nature_type?.id;

                const nature_name = classificationOptions.find(item => item.value == nature_type);

                setClassification({
                    ...classification,
                    classification_nature_name: nature_name?.label,
                    classification_nature: nature_type,
                    rcm_applicable: itemsList[0]?.classification_is_rcm_applicable ? true : false,
                });

                let calculateIGST = false;
                let calculateSGST = false;

                if (!saleConfiguration?.header?.is_change_gst_details) {
                    if (
                        ledger?.partyDetail?.billingAddress?.state_id ===
                        localDispatchAddress[selectedAddress]?.state_id
                    ) {
                        // setIsSGSTCalculation(true);
                        // setIsIGSTCalculation(false);
                        calculateSGST = true;
                        calculateIGST = false;
                    } else {
                        // setIsSGSTCalculation(false);
                        // setIsIGSTCalculation(true);
                        calculateSGST = false;
                        calculateIGST = true;
                    }
                } else {
                    const gstCalculationType = setGSTCalculationType(
                        {
                            classification_nature_name: nature_name?.label,
                        },
                        setIsIGSTCalculation,
                        setIsSGSTCalculation,
                        company?.company?.is_gst_applicable,
                        itemsList
                    );
                    calculateIGST = gstCalculationType?.calculateIGST;
                    calculateSGST = gstCalculationType?.calculateSGST;
                }

                const { itemTotal, gstTotal, totalIgst, cessTotal } = calculateTotals(
                    updatedItemsList,
                    classificationType
                );

                setGrandTotal(itemTotal);

                const addition_charges = calculateAdditionalCharges(
                    { additional_detail: updatedAdditionalCharges },
                    itemTotal,
                    calculateIGST
                );

                let additionalGstCost = 0;

                if (calculateSGST) {
                    additionalGstCost = parseFloat(addition_charges?.acTotal) * 2;
                } else {
                    additionalGstCost = parseFloat(addition_charges?.acTotal);
                }

                const mainTotalBeforeAddLess = parseFloat(addition_charges.addition_charges) +
                    parseFloat(itemTotal) +
                    parseFloat(company?.company?.is_gst_applicable ? customToFixed(additionalGstCost, 2) : 0) +
                    parseFloat(company?.company?.is_gst_applicable ? cessTotal : 0) +
                    parseFloat(company?.company?.is_gst_applicable && calculateSGST ? gstTotal : 0) +
                    parseFloat(company?.company?.is_gst_applicable && calculateSGST ? gstTotal : 0) +
                    parseFloat(company?.company?.is_gst_applicable && calculateIGST ? totalIgst : 0) +
                    parseFloat(saleConfiguration?.footer?.is_enabled_tcs_details ? invoiceDetail?.tcs_amount || 0 : 0);

                const calculatedAddLessCharges = calculateAddLessCharges(updatedAddLessChanges, mainTotalBeforeAddLess);
                const totalAddLessChanges = calculatedAddLessCharges?.reduce((total, item) => {
                    if (item.al_type == 1) {
                        total += parseFloat(item.al_value);
                    } else if (item?.al_type == 2) {
                        total += parseFloat(item?.al_total);
                    }
                    return total;
                }, 0);

                setGstValue({
                    ...gstValue,
                    sgstValue: parseFloat(gstTotal) + parseFloat(addition_charges?.acTotal),
                    cgstValue: parseFloat(gstTotal) + parseFloat(addition_charges?.acTotal),
                    igstValue: parseFloat(totalIgst) + parseFloat(addition_charges?.acTotal),
                });

                setPaymentLedgerDetail({
                    payment_detail: [
                        {
                            pd_ledger_id: null,
                            pd_date: formattedDate(),
                            pd_amount: "",
                            pd_mode: null,
                            pd_reference_number: "",
                            is_show_invoice_date: true,
                        },
                    ],
                    tds_amount: invoiceDetail?.tds_tax_id ? invoiceDetail?.tds_amount : "",
                    tds_rate: invoiceDetail?.tds_tax_id ? invoiceDetail?.tds_rate : "",
                    tds_tax_id: invoiceDetail?.tds_tax_id,
                    rounding_type: invoiceDetail?.tds_rounding_method || 1
                });

                setTaxableValue(parseFloat(addition_charges.addition_charges) + itemTotal);

                setTcsRate({
                    tcs_amount: invoiceDetail?.tcs_tax_id ? invoiceDetail?.tcs_amount : '',
                    tcs_tax_id: invoiceDetail?.tcs_tax_id,
                    tcs_rate: invoiceDetail?.tcs_tax_id ? invoiceDetail?.tcs_rate : '',
                    tcs_calculated_on: invoiceDetail?.tcs_calculated?.calculated_on,
                });

                setAdditionalGst(customToFixed(addition_charges?.acTotal, 2));
                setCessValue(parseFloat(customToFixed(cessTotal, 2)));
                if (updatedItemsList?.length > 0) {
                    setItems(updatedItemsList);
                }else{
                    setItems(defaultItem)
                }

                const grandTotalWithoutRoundOff =  parseFloat(addition_charges.addition_charges) +
                    parseFloat(itemTotal) +
                    parseFloat(company?.company?.is_gst_applicable ? customToFixed(additionalGstCost, 2) : 0) +
                    parseFloat(company?.company?.is_gst_applicable ? cessTotal : 0) +
                    parseFloat(company?.company?.is_gst_applicable && calculateSGST ? gstTotal : 0) +
                    parseFloat(company?.company?.is_gst_applicable && calculateSGST ? gstTotal : 0) +
                    parseFloat(company?.company?.is_gst_applicable && calculateIGST ? totalIgst : 0) +
                    parseFloat(saleConfiguration?.footer?.is_enabled_tcs_details ? invoiceDetail?.tcs_amount || 0 : 0) +
                    parseFloat(totalAddLessChanges)

                setMainGrandTotal(grandTotalWithoutRoundOff);

                const calculateRoundOff = RoundOffMethod(
                    grandTotalWithoutRoundOff,
                    saleConfiguration?.footer?.round_off_method)

                setGstCalculation({
                    ...gstCalculation,
                    round_of_amount: parseFloat(calculateRoundOff?.roundOffAmount),
                    ...(invoiceDetail?.round_off_method ? {round_off_method: Number(invoiceDetail?.round_off_method)} : {})
                });

                setFinalAmount(calculateRoundOff?.grandFinalAmount);
                // setTimeout(() => {
                //     dispatch(getSaleDetailByEstimateChallan(""));
                // }, 2000);
                setIsRenderItem(false);
            }
        }, [setItems, sale?.getSaleDetailByEstimateChallan, changeTax]);

        console.log(items, "itemskf")

        useEffect(() => {
            if (
                sale?.getSaleDetailByEstimateChallan &&
                sale?.getSaleDetailByEstimateChallan?.length !== 0 &&
                saleConfiguration?.header?.is_enabled_delivery_challan
            ) {
                const itemsData = [...sale?.getSaleDetailByEstimateChallan?.allTransactionItems];
                setItemType("item");
                // dispatch(getTableHeaderDetail(tableHeader));
                dispatch(rearrangeItemList(TRANSACTION_TYPE.SALE, 1));
                let ids = [];
                const item_ledger_id = [];
                if (itemsData) {
                    itemsData?.forEach(item => {
                        ids.push(item.item_id);
                        item_ledger_id.push(item.ledger_id);
                    });
                }
                const invoiceDetail =
                    sale?.getSaleDetailByEstimateChallan?.deliveryChallanTransaction;
                getEditLedgerFromList({dispatch, item_ledger_id, singleTransaction: invoiceDetail});

                if (sale?.getSaleDetailByEstimateChallan?.allTransactionItems) {
                    const updatedItemsList =
                        sale?.getSaleDetailByEstimateChallan?.allTransactionItems?.forEach(item => {
                            ids.push(item.item_id);
                        });
                    dispatch(
                        fetchPriceListOfItems(ids, gstQuote?.party_ledger_id, null, setIsConfirmed, getCustomFieldTransactionType)
                    );
                }
                setCustomHeaderListTransaction(invoiceDetail?.custom_values);
                setBrokerDetail({
                    ...brokerDetail,
                    broker_id: invoiceDetail?.broker_id,
                    broker_percentage: invoiceDetail?.brokerage,
                    brokerage_on_value: invoiceDetail?.brokerage_on_value_type,
                });
                setSameAsBill(invoiceDetail?.same_as_billing);
                const billingAddress = invoiceDetail?.billing_address;
                const shippingAddress = invoiceDetail?.shipping_address;
                setPartyAddress({
                billingAddress: {
                    address_name: shippingAddress?.address_name,
                    address_1: billingAddress?.address_1,
                    address_2: billingAddress?.address_2,
                    country_id: billingAddress?.country_id,
                    state_id: billingAddress?.state_id,
                    city_id: billingAddress?.city_id,
                    state_name: billingAddress?.state_name,
                    city_name: billingAddress?.city_name,
                    pin_code: billingAddress?.pin_code,
                },
                shippingAddress: {
                    address_name: shippingAddress?.address_name ?? "",
                    address_1: shippingAddress?.address_1 ?? "",
                    address_2: shippingAddress?.address_2 ?? "",
                    country_id: shippingAddress?.country_id ?? "",
                    state_id: shippingAddress?.state_id ?? "",
                    city_id: shippingAddress?.city_id ?? "",
                    state_name: shippingAddress?.state_name ?? "",
                    city_name: shippingAddress?.city_name ?? "",
                    pin_code: shippingAddress?.pin_code ?? "",
                    shipping_name: invoiceDetail?.shipping_name ?? "",
                    shipping_gstin: invoiceDetail?.shipping_gstin ?? "",
                    shipping_address_id: invoiceDetail?.shipping_address_id ?? "",
                },
                });
                setTransporterDetail({
                    ...transporterDetail,
                    transport_id: invoiceDetail?.transport_id,
                    transporter_document_date: invoiceDetail?.transporter_document_date
                        ? formattedDate(invoiceDetail?.transporter_document_date)
                        : null,
                    transporter_document_number: invoiceDetail?.transporter_document_number,
                    transporter_vehicle_number: invoiceDetail?.transporter_vehicle_number,
                });

                setOtherDetail({
                    ...otherDetail,
                    po_number: invoiceDetail?.po_no,
                    date: invoiceDetail?.po_date ? formattedDate(invoiceDetail?.po_date) : null,
                    creditPeriod: invoiceDetail?.credit_period,
                    creditPeriodType: invoiceDetail?.credit_period_type ?? 1,
                });

                if (ids?.length > 0) {
                    dispatch(fetchItemList({ ids }));
                }

                const processTransactionItems = transactionItems => {
                    return transactionItems.map((transactionItem, index) => {
                        const itemModel = transactionItem.items?.model || {};
                        const gstTax =
                            itemModel?.items?.model?.gst_tax_id ||
                            transactionItem?.gst_tax_id ||
                            {};
                        const unitOptions = transactionItem?.unitOfArray || {};
                        const unitOptionDetail = unitOptionWithKey(unitOptions);

                        const baseData = {
                            quantity: transactionItem?.quantity || 0,
                            description: itemModel?.description || "",
                            gstRate: itemModel?.gst_rate || transactionItem?.gst_rate || 0,
                            gst_id: gstTax || transactionItem?.gst_tax_id,
                            gst: itemModel?.gst_rate || transactionItem?.gst_rate,
                            cessRate: itemModel?.gst_gst_cess_rate?.rate || 0,
                            sellingPriceWithoutGst: changeTax
                                ? itemModel?.selling_price_with_gst || 0
                                : itemModel?.selling_price_without_gst || 0,
                            sellingPriceWithGst: changeTax
                                ? itemModel?.selling_price_with_gst || 0
                                : itemModel?.selling_price_without_gst || 0,
                            discountType: itemModel?.discount_type || 1,
                            discountValue: itemModel?.discount_value,
                            discountType_2: itemModel?.discount_type_2 || 1,
                            discountValue_2: itemModel?.discount_value_2,
                            unitOfMeasurement: itemModel?.unit_of_measurement || "",
                            additional_description: transactionItem?.additional_description || "",
                            selectedLedger: isPurchase
                                ? itemModel?.expense_ledger_id
                                : itemModel?.income_ledger_id || null,
                            mrp: itemModel?.mrp || 0,
                            updatedRateWithoutGst: changeTax
                                ? itemModel?.selling_price_with_gst
                                : itemModel?.selling_price_without_gst || 0,
                            itemUnitOption: unitOptionDetail,
                            with_tax: changeTax ? 1 : 0,
                            is_status: ITEM_STATUS.ACTIVE,
                        };
                        const totalData = calculateTotal(baseData, false, changeTax, false, false);

                        return {
                            id: transactionItem?.id || null,
                            selectedItem: transactionItem?.item_id || null,
                            multiQuantity: [1, 0, 0, 0],
                            updatedRateWithGst:
                                changeTax
                                    ? baseData?.sellingPriceWithGst || 0
                                    : baseData?.sellingPriceWithoutGst?.toFixed(2) || 0,
                            updatedRateWithoutGst: changeTax
                                    ? baseData?.sellingPriceWithGst || 0
                                    : baseData?.sellingPriceWithoutGst?.toFixed(2) || 0,
                            ...transactionItem,
                            ...baseData,
                            ...totalData,
                            sgstValue: totalData.sgst,
                            cgstValue: totalData.sgst,
                            igstValue: totalData.igst,
                            cessValue: totalData.cess,
                            hsn_code: transactionItem?.hsn_code,
                            selectedUnit: itemModel?.unit_of_measurement || null,
                            rateWithGst: changeTax
                                    ? baseData?.sellingPriceWithGst
                                    : baseData?.sellingPriceWithoutGst?.toFixed(2),
                            rateWithoutGst: changeTax
                                    ? baseData?.sellingPriceWithGst
                                    : baseData?.sellingPriceWithoutGst?.toFixed(2),
                            isShowDelete: true,
                            custom_fields: customFieldOptimization(item?.custom_fields),
                            is_status: ITEM_STATUS.ACTIVE,
                        };
                    });
                };

                const calculateTotals = items => {
                    return items.reduce(
                        (acc, item) => {
                            acc.itemTotal += item.total || 0;
                            acc.sgstValue += item?.sgstValue || 0;
                            acc.cgstValue += item.cgstValue || 0;
                            acc.igstValue += item.igstValue || 0;
                            acc.cessTotal += item.cessValue || 0;
                            return acc;
                        },
                        {
                            itemTotal: 0,
                            sgstValue: 0,
                            cgstValue: 0,
                            igstValue: 0,
                            cessTotal: 0,
                        }
                    );
                };

                const updatedAdditionalCharges = !invoiceDetail?.additional_charges ||
                    invoiceDetail?.additional_charges?.length == 0
                        ? [
                              {
                                  ac_ledger_id: null,
                                  ac_type: 1,
                                  ac_value: "",
                                  ac_gst_rate_id: {
                                      label: "",
                                      value: 0,
                                      rate: 0,
                                  },
                                  ac_total: 0,
                              },
                          ]
                        : invoiceDetail?.additional_charges?.map(charge => {
                              return {
                                  ac_ledger_id: charge?.ledger_id,
                                  ac_type: charge?.charge_type,
                                  ac_value: charge?.value,
                                  ac_gst_rate_id: {
                                      value: charge?.gst_rate_id,
                                      rate: Number(charge?.gst_percentage),
                                      label: `${charge?.gst_percentage}%`,
                                  },
                                  ac_total: customToFixed(charge?.total_without_tax, 2),
                              };
                    });

                setAdditionalCharges({
                    note: invoiceDetail
                        ?.narration,
                    terms_and_conditions:
                        invoiceDetail
                            ?.term_and_condition,
                    additional_detail: updatedAdditionalCharges
                });
                const addLessCharges = calculateAddLessCharges(addLessChanges, grandTotal);
                setAddLessChanges(
                    invoiceDetail?.add_less?.length == 0
                        ? addLessCharges
                        : invoiceDetail?.add_less?.map(item => {
                              return {
                                  al_ledger_id: item.ledger_id,
                                  al_is_show_in_print: item.is_show_in_print,
                                  al_type: item.type,
                                  al_value:
                                      item.type == 2
                                          ? parseFloat(item.value)
                                          : parseFloat(item.total),
                                  al_total: parseFloat(item.total),
                                  is_status: ITEM_STATUS.IN_ACTIVE,
                              };
                          })
                );
                const itemsList = processTransactionItems(
                    sale?.getSaleDetailByEstimateChallan?.allTransactionItems || []
                );
                setTimeout(() => {
                const { itemTotal, sgstValue, cgstValue, igstValue, cessTotal } =
                    calculateTotals(itemsList);
                setGrandTotal(itemTotal);

                const addition_charges = calculateAdditionalCharges(
                    { additional_detail: updatedAdditionalCharges },
                    itemTotal,
                    isIGSTCalculation
                );

                const acTotal = updatedAdditionalCharges?.reduce((total, item) => {
                    let gstRate = parseFloat(item?.ac_gst_rate_id?.rate / 2) || 0;
                    if (isIGSTCalculation) {
                        gstRate = parseFloat(item?.ac_gst_rate_id?.rate) || 0;
                    }
                    if (item.ac_type === 1) {
                        total += item?.ac_total - (item?.ac_value == "-" ? 0 : item?.ac_value);
                    } else if (item?.ac_type == 2 && item?.ac_gst_rate_id) {
                        total += item?.ac_total * (gstRate / 100);
                    }
                    return total;
                }, 0);

                setAdditionalCharges({
                    ...addition_charges,
                    additional_detail: addition_charges?.updatedCharges,
                });

                setGstValue({
                    ...gstValue,
                    sgstValue: sgstValue + parseFloat(addition_charges?.acTotal),
                    cgstValue: cgstValue + parseFloat(addition_charges?.acTotal),
                    igstValue: igstValue + parseFloat(addition_charges?.acTotal),
                });

                const gstTotalWithAmount = isIGSTCalculation
                    ? parseFloat(igstValue || 0)
                    : parseFloat(sgstValue || 0) + parseFloat(cgstValue);

                const newInvoiceValue = isIGSTCalculation
                    ? parseFloat(addition_charges?.addition_charges) +
                    parseFloat(itemTotal) +
                    parseFloat(cessTotal || 0) +
                    parseFloat(igstValue || 0) +
                    parseFloat(addition_charges?.acTotal)
                    : parseFloat(addition_charges?.addition_charges) +
                    parseFloat(itemTotal) +
                    parseFloat(cessTotal || 0) +
                    parseFloat(sgstValue || 0) +
                    parseFloat(addition_charges?.acTotal) +
                    parseFloat(addition_charges?.acTotal) +
                    parseFloat(cgstValue || 0);

                setInvoiceValue(newInvoiceValue);

                const updatedTcsAmount =
                    tcsRate.tcs_calculated_on == CALCULATION_ON_TYPE.INVOICE_VALUE
                        ? parseFloat(newInvoiceValue * ((tcsRate?.tcs_rate || 0) / 100)).toFixed(2)
                        : parseFloat(
                              (parseFloat(addition_charges?.addition_charges) +
                                  parseFloat(itemTotal)) *
                                  ((tcsRate?.tcs_rate || 0) / 100)
                          ).toFixed(2);
                setTcsRate({
                    tcs_amount: invoiceDetail?.tcs_tax_id
                        ? customToFixed(invoiceDetail?.tcs_amount, 2)
                        : updatedTcsAmount,
                    tcs_tax_id: invoiceDetail?.tcs_tax_id,
                    tcs_rate: invoiceDetail?.tcs_tax_id ? invoiceDetail?.tcs_rate : "",
                    tcs_calculated_on: invoiceDetail?.tcs_calculated?.calculated_on,
                });

                const total =
                    parseFloat(company?.company?.is_gst_applicable ? gstTotalWithAmount : 0) +
                    parseFloat(addition_charges?.addition_charges) +
                    parseFloat(itemTotal) +
                    parseFloat(company?.company?.is_gst_applicable ? cessTotal : 0) +
                    parseFloat(saleConfiguration?.footer?.is_enabled_tcs_details ? updatedTcsAmount || 0 : 0);

                setTaxableValue(
                    parseFloat(addition_charges?.addition_charges) + parseFloat(itemTotal)
                );

                setAdditionalGst(acTotal);
                setCessValue(parseFloat(customToFixed(cessTotal, 2)));
                setOnlyItems(itemsList);

                let additionalGstTotal = parseFloat(addition_charges?.acTotal);
                if (isSGSTCalculation) {
                    additionalGstTotal = customToFixed(
                        parseFloat(addition_charges?.acTotal) * 2,
                        2
                    );
                }
                const totalAddLessAmount = addLessChanges?.reduce((sum, change) => {
                    if (change.al_type === 1) {
                        return sum + (parseFloat(change.al_value) || 0);
                    } else if (change.al_type === 2) {
                        return sum + (parseFloat(change.al_total) || 0);
                    }
                    return sum;
                }, 0);

                const calculatedGrandTotal =
                    parseFloat(addition_charges?.addition_charges) +
                    parseFloat(itemTotal) +
                    parseFloat(
                        company?.company?.is_gst_applicable
                            ? customToFixed(additionalGstTotal, 2)
                            : 0
                    ) +
                    parseFloat(company?.company?.is_gst_applicable ? cessTotal : 0) +
                   parseFloat(
                            (!classification.rcm_applicable) &&
                                !isIGSTCalculation &&
                                isSGSTCalculation
                                ? company?.company?.is_gst_applicable ? cgstValue : 0 : 0
                             ) +
                    parseFloat(
                            (!classification.rcm_applicable) &&
                                !isIGSTCalculation &&
                                isSGSTCalculation
                                ? company?.company?.is_gst_applicable ? cgstValue : 0 : 0
                        ) +
                    parseFloat(
                            (!classification.rcm_applicable) &&
                                isIGSTCalculation &&
                                !isSGSTCalculation
                                ? company?.company?.is_gst_applicable ? igstValue : 0 : 0
                        ) +
                    // parseFloat(gstCalculation?.round_of_amount || 0) +
                    parseFloat(totalAddLessAmount || 0) +
                    parseFloat(updatedTcsAmount || 0);

                const { grandFinalAmount, roundOffAmount } = RoundOffMethod(
                    calculatedGrandTotal,
                    invoiceDetail?.round_off_method ? Number(invoiceDetail?.round_off_method) : saleConfiguration?.footer?.round_off_method
                );
                setMainGrandTotal(calculatedGrandTotal);
                setFinalAmount(grandFinalAmount);
                setGstCalculation({
                    ...gstCalculation,
                    round_of_amount: roundOffAmount,
                    ...(invoiceDetail?.round_off_method
                        ? { round_off_method: Number(invoiceDetail?.round_off_method) }
                        : {}),
                });
                }, 2000);
            }
        }, [sale?.getSaleDetailByEstimateChallan]);

        useEffect(() => {
            if (isEditCalculation) {
                setItems(prev => {
                    const updatedItems = prev?.map(item => {
                        const updatedItem = {
                            ...item,
                            // ...(isDeliveryChallan ? {} : {discountValue: 0,
                            // discountValue_2: 0,})
                        };
                        const calculatedTotal = calculateTotal(
                            updatedItem,
                            false,
                            changeTax,
                            itemType === "accounting",
                            false,
                            classification.rcm_applicable,
                            isPurchase
                        );
                        return {
                            ...updatedItem,
                            with_tax: changeTax ? 1 : 0,
                            rpu: item?.updatedRateWithGst,
                            total: calculatedTotal.total,
                            cessValue: calculatedTotal.cess,
                        };
                    });
                    const { itemTotal } = calculateTotals(updatedItems, classificationType);
                    const addition_charges = calculateAdditionalCharges(
                        additionalCharges,
                        grandTotal,
                        isIGSTCalculation
                    );
                    setGrandTotal(itemTotal);
                    setTaxableValue(
                        parseFloat(itemTotal) + parseFloat(addition_charges?.addition_charges)
                    );
                    return updatedItems;
                });
            }
        }, [changeTax, isNegativeStock]);

        const handleOpenNegativeStock = () => {
            openIsNegativeStockModel();
        };

        const openNegativeModelHandler = (value, data) => {
            if (
                saleConfiguration?.item_table_configuration?.warn_on_negative_stock &&
                data?.selectedLedger &&
                data.itemType == 1
            ) {
                dispatch(
                    negativeStock(data?.selectedItem, data?.quantity, handleOpenNegativeStock)
                );
            }
        };

        const closeWarningModelHandler = () => {
            closeIsNegativeStockModel();
        };

        const handleItemTypeChange = e => {
            const event = e.target.value;
            const isMakeReturn = matchPathname("sale-returns") ? TRANSACTION_TYPE.SALE_RETURN
                : matchPathname("create-sale-return") ? TRANSACTION_TYPE.SALE_RETURN
                : matchPathname("sales-create") ? TRANSACTION_TYPE.SALE
                : matchPathname("sales") ? TRANSACTION_TYPE.SALE
                : matchPathname("purchase-sale") ? TRANSACTION_TYPE.SALE
                : matchPathname("import-documents/create") ? TRANSACTION_TYPE.PURCHASE
                : matchPathname("income-credit-notes") ? TRANSACTION_TYPE.INCOME_CREDIT_NOTE
                : matchPathname("income-debit-notes") ? TRANSACTION_TYPE.INCOME_DEBIT_NOTE
                : matchPathname("expense-credit-notes") ? TRANSACTION_TYPE.EXPENSE_CREDIT_NOTE
                : matchPathname("expense-debit-notes") ? TRANSACTION_TYPE.EXPENSE_DEBIT_NOTE
                : matchPathname("income-estimate-quote") ? TRANSACTION_TYPE.INCOME_ESTIMATE_QUOTE
                : matchPathname("purchase-order") ? TRANSACTION_TYPE.PURCHASE_ORDER
                : matchPathname('purchase-returns') ? TRANSACTION_TYPE.PURCHASE_RETURN
                : matchPathname('delivery-challan') ? TRANSACTION_TYPE.DELIVERY_CHALLAN
                : matchPathname('purchases') ? TRANSACTION_TYPE.PURCHASE
                : matchPathname('purchase-create') ? TRANSACTION_TYPE.PURCHASE
                : matchPathname('purchases-create') ? TRANSACTION_TYPE.PURCHASE
                : matchPathname('create-purchase-return') ? TRANSACTION_TYPE.PURCHASE
                : matchPathname('recurring-invoices') ? TRANSACTION_TYPE.RECURRING
                : "";
            setItemType(event);
            setIsChangeType(true);
            setIsEditCalculation(true);
            if(event === "accounting" && saleConfiguration?.header?.is_enabled_delivery_challan){
                setGstQuote({
                    ...gstQuote,
                    quotes_id: [],
                });
            }
            dispatch(getSaleDetailByEstimateChallan(""));
            // setFinalAmount(0);
            // setTaxableValue(0);

            setGrandTotal(0);
            if (event === "accounting") {
                dispatch(rearrangeItemList(isMakeReturn, 2));
                setItems([
                    {
                        id: 1,
                        ledger: "",
                        amountWithGst: 0,
                        amountWithoutGst: 0,
                        updatedAmountWithGst: 0,
                        updatedAmountWithoutGst: 0,
                        discountType: 1,
                        discountType_2: 1,
                        discountValue: 0,
                        discountValue_2: 0,
                        gst_id: 0,
                        gst: 0,
                        total: 0,
                        selectedLedger: 0,
                        cgstValue: 0,
                        igstValue: 0,
                        sgstValue: 0,
                        updatedTotal: 0,
                        additional_description: "",
                        isShowDelete: false,
                        with_tax: 0,
                        multiQuantity: [0, 0, 0, 0],
                        quantity: 0,
                        sku: "",
                    },
                ]);
            } else if(event === "without_amount") {
                dispatch(rearrangeItemList(isMakeReturn, 2));
            } else {
                setItems([
                    {
                        id: 1,
                        selectedItem: null,
                        additional_description: "",
                        mrp: 0,
                        selectedLedger: null,
                        quantity: 0,
                        free_quantity: 0,
                        multiQuantity: [0, 0, 0, 0],
                        rateWithGst: 0,
                        updatedRateWithGst: 0,
                        rateWithoutGst: 0,
                        updatedRateWithoutGst: 0,
                        gst_id: 0,
                        gst: 0,
                        gst_copy: 0,
                        discountType: 1,
                        discountValue: 0,
                        discountType_2: 1,
                        discountValue_2: 0,
                        total: 0,
                        stock: 0,
                        selectedUnit: null,
                        cgstValue: 0,
                        igstValue: 0,
                        sgstValue: 0,
                        shippingValue: 0,
                        cessValue: 0,
                        cessRate: 0,
                        with_tax: 0,
                        rpu: 0,
                        itemUnitOption: null,
                        isShowDelete: false,
                        decimal_places: 0,
                        decimal_places_for_rate: 0,
                        sku: "",
                        hsn_code: "",
                    },
                ]);
                dispatch(rearrangeItemList(isMakeReturn, 1));
            }
            const { itemTotal } = calculateTotals([], classificationType);

            const addition_charges = calculateAdditionalCharges(
                additionalCharges,
                0,
                isIGSTCalculation
            );

            setAdditionalGst(customToFixed(addition_charges?.acTotal, 2));
            setTaxableValue(parseFloat(addition_charges?.addition_charges) + itemTotal);
            setGstValue({
                sgstValue: 0,
                cgstValue: 0,
                igstValue: 0,
            });
            setCessValue(parseFloat(0));
            setTimeout(() => {
                setIsChangeType(false);
            }, 1000);
        };


       const handleUpdateItem = useCallback(
           (id, updatedItem, index) => {
               setIsCheckGstType(true);
               setIsEditCalculation(true);
               setUpdatedId(index);
               setIsRenderItem(true);
               setItems(prev =>
                   prev?.map((item, i) =>
                       i === index
                           ? {
                               ...item,
                               ...updatedItem,
                               total: updatedItem.total,
                               custom_fields: updatedItem?.custom_fields?.length > 0 ? updateCustomFieldCalculation(updatedItem?.custom_fields) : [],
                           }
                           : item
                   )
               );
           },
           [setItems, item?.itemTableDetail]
       );

        const addItemRow = useCallback(() => {
            const newItem =
                itemType === "item"
                    ? {
                        id: items.length > 0 ? items[items.length - 1].id + 1 : 1,
                        selectedItem: null,
                        quantity: 0,
                        free_quantity: 0,
                        rateWithGst: 0,
                        updatedRateWithGst: 0,
                        rateWithoutGst: 0,
                        updatedRateWithoutGst: 0,
                        gst: 0,
                        gst_id: 0,
                        units: [],
                        discountType: 1,
                        discountType_2: 1,
                        discountValue: 0,
                        discountValue_2: 0,
                        total: 0,
                        stock: 0,
                        selectedUnit: null,
                        cgstValue: 0,
                        igstValue: 0,
                        sgstValue: 0,
                        shippingValue: 0,
                        cessValue: 0,
                        cessRate: 0,
                        multiQuantity: [0, 0, 0, 0],
                        itemUnitOption: null,
                        isShowDelete: true,
                        with_tax: 0,
                        decimal_places: 0,
                        decimal_places_for_rate: 0,
                        sku: "",
                        hsn_code: "",
                        is_status: ITEM_STATUS.IN_ACTIVE
                    }
                    : itemType === "accounting"
                        ? {
                            id: items.length > 0 ? items[items.length - 1].id + 1 : 1,
                            ledger: "",
                            amountWithGst: 0,
                            amountWithoutGst: 0,
                            discountType: 1,
                            discountType_2: 1,
                            discountValue: 0,
                            discountValue_2: 0,
                            discount: 0,
                            gst: 0,
                            gst_id: 0,
                            total: 0,
                            cgstValue: 0,
                            igstValue: 0,
                            sgstValue: 0,
                            multiQuantity: [0, 0, 0, 0],
                            isShowDelete: true,
                            with_tax: 0,
                            is_status: ITEM_STATUS.IN_ACTIVE
                        }
                        : "";
            setItems(prev => [...prev, newItem]);
            setTimeout(() => {
                lineItem.current.blur();
                if(itemRef?.current){
                    itemRef.current.focus();
                }
            }, 200);
        }, [items, itemType, setItems]);

        const removeItem = useCallback(
            id => {
                setItems(prevItems => {
                    const updatedItems = prevItems.filter((item, index) => index !== id);

                    const { itemTotal, cessTotal } = calculateTotals(updatedItems, classificationType);
                    setGrandTotal(itemTotal);
                    setCessValue(cessTotal);

                    const totalAmount =
                        additionalCharges?.additional_detail?.reduce(
                            (acc, charge) => acc + charge.ac_value,
                            0
                        ) || 0;
                    const addition_charges = calculateAdditionalCharges(
                        additionalCharges,
                        grandTotal,
                        isIGSTCalculation
                    );
                    setTaxableValue(parseFloat(addition_charges?.addition_charges) + itemTotal);

                    setIsEditCalculation(true);
                    setIsChangeType(false);

                    return updatedItems;
                });
            },
            [additionalCharges, items]
        );

        const handleOpenSecond = useCallback(() => {
            // const customFields = item?.getSingleItemById?.[0]?.custom_fields?.filter(field => field?.status);
            // if(customFields?.length == 0) {
                openClassificationModel();
            // }else{
            //     setIsManageCustomItemMaster(true);
            // }
        }, [openClassificationModel, item?.getSingleItemById?.[0]?.custom_fields]);

        const handleAddPaymentLedger = (type, index) => {
            const newItem = {
                pd_ledger_id: "",
                pd_date: isPurchase
                    ? isExpenseCredit || isExpenseDebit
                        ? invoiceDetail.invoice_date
                        : purchaseInvoice.voucher_date ?? invoiceDetail.invoice_date
                    : invoiceDetail.invoice_date,
                pd_amount: "",
                pd_mode: "",
                pd_reference_number: "",
                is_show_invoice_date: true,
            };
            const newPaymentDetail = paymentLedgerDetail?.payment_detail?.filter(
                (_, i) => i !== index
            );
            if (type === "remove") {
                setPaymentLedgerDetail(prev => ({
                    ...prev,
                    payment_detail: newPaymentDetail,
                }));
            } else {
                setPaymentLedgerDetail(prev => ({
                    ...prev,
                    payment_detail: [...prev.payment_detail, newItem],
                }));
            }
        };

        useEffect(() => {
            const tdsAmount = customToFixed(paymentLedgerDetail.tds_amount || 0, 2);
            const totalAdvancePaymentSelected = selectedAdvancePayment?.reduce(
                (acc, item) => acc + item.amount,
                0
            );
            const adjustedFinalAmount = customToFixed(finalAmount, 2) - customToFixed(totalAdvancePaymentSelected || 0, 2) - tdsAmount;

            const totalPdAmount = paymentLedgerDetail.payment_detail.reduce(
                (sum, item) => sum + customToFixed(item.pd_amount || 0, 2),
                0
            );

            setPaymentLedgerDetail(prev => {
                const updatedDetails = prev.payment_detail.map(detail => {
                    return { ...detail, pd_amount: totalPdAmount > adjustedFinalAmount ? adjustedFinalAmount : detail?.pd_amount };
                });

                return {
                    ...prev,
                    payment_detail: updatedDetails,
                };
            });
        }, [finalAmount]);

        const handleChangeTaxStatus = (value) => {
            setIsEditCalculation(true);
            setIsRenderItem(true);
            setChangeTax(value);
            setItems(prev => prev.map(item => ({
                ...item,
                discountValue: 0,
                discountValue_2: 0
            })));
        };

        const openItemsModel = useCallback(
            (id, index) => {
                openItemModel();
                setItemIndex(index);
                if (id) {
                    setModelName("Update Item");
                } else {
                    setModelName("Add Item");
                }
            },
            [openItemModel]
        );

        const openItemLedgerModel = useCallback(
            (id, index) => {
                setItemIndex(index);
                if (id) {
                    setLedgerModelName({ name: "Update Ledger", id: id });
                } else {
                    setLedgerModelName({ name: "Add Ledger", id: "" });
                }
                openItemsLedgerModel();
                setCurrentAction(isPurchase ? "Expense" : "Income");
            },
            [isItemsLedgerModel]
        );

        useEffect(() => {
            const calculatedTCSValue = (grandTotal * tcsRateValue) / 100;
            setTCSValue(parseFloat(customToFixed(calculatedTCSValue, 2)));
        }, [grandTotal, tcsRateValue]);

        const onClickSettlePayment = (showPaymentTable) => {
            const newShowPaymentTable = !showPaymentTable;
            if (newShowPaymentTable) {
                dispatch(getAdvancePayment(gstQuote?.party_ledger_id, settlePaymentType, id));
            }
            setShowPaymentTable(newShowPaymentTable);
            if (!newShowPaymentTable) {
                setSelectedAdvancePayment([]);
            }
        };

        const [transactions, setTransactions] = useState([]);
        useEffect(() => {
            if(advancePayment?.paymentsData?.length !== 0) {
                const prepareTransactions = prepareTransactionsBills(advancePayment?.paymentsData);
                setTransactions(prepareTransactions);
            }
        },[advancePayment])

        const addAllItems = async () => {
            const isChangeGst = false;
            let itemList = [...items];
            const defaultSelectedGroup = item?.itemModelDetail?.defaultSelectedGroup;
            const defaultExpenseOption = expenseLedgers?.find(
                option => option?.label == "Purchase"
            );
            if (items?.length > 0) {
                const processedItems = new Set();
                try {
                    for (let index = 0; index < items.length; index++) {
                        const item = items[index];

                        if (item?.notExistsItems) {

                            if (processedItems.has(item?.selectedItem)) {
                                continue;
                            }
                            const defaultPrimaryUnit = unitOfMeasurement?.filter(
                                unit => unit.label === "PCS-PIECES"
                            );
                            const data = {
                                item_name: item?.selectedItem,
                                group_id: defaultSelectedGroup,
                                item_type: 1,
                                primary_unit_of_measurement: item?.itemUnitOption[0].value,
                                primary_unit_of_measurement_name: item?.itemUnitOption[0].label,
                                secondary_unit_of_measurement: null,
                                secondary_unit_of_measurement_name: "",
                                conversion_rate: 1,
                                decimal_places: 2,
                                sku: null,
                                hsn_code: null,
                                description: null,
                                same_description: "",
                                is_description_same_as_item_name: 0,
                                is_gst_applicable: 0,
                                gst_tax_id: null,
                                hsn_sac_code: "",
                                gst_cess_rate: null,
                                is_rcm_applicable: 0,
                                mrp: "",
                                sale_price: "",
                                sale_price_type: 2,
                                discount_value: "",
                                discount_type: 1,
                                income_ledger_id: "",
                                purchase_price: "",
                                purchase_discount_type: 1,
                                purchase_discount_value: "",
                                purchase_price_type: 2,
                                expense_ledger_id: defaultExpenseOption?.value,
                                decimal_places_for_rate: 2,
                                quantity_unit: defaultPrimaryUnit[0].value,
                                quantity: null,
                                rate: null,
                                opening_balance_type: 2,
                                item_image: null,
                            };

                            const formData = convertToFormData(data);

                            const isNotExistItems = true;
                            const response = await dispatch(
                                addItem(
                                    formData,
                                    handleCloseModal,
                                    items,
                                    setItems,
                                    index,
                                    setItemIndex,
                                    gstQuote,
                                    isPurchase,
                                    openClassificationModel,
                                    false,
                                    changeTax,
                                    isChangeGst,
                                    setIsEditCalculation,
                                    setIsDisable,
                                    isNotExistItems,
                                    setNotExistItems,
                                    company,
                                    setIsChangeType
                                )
                            );
                            const createdId = response?.id;
                            const item_name = response?.item_name;
                            const expenseLedgerId = response?.model?.expense_ledger_id ?? "";
                            const itemUnitOption = response?.units_of_array;
                            const selectedUnit = response?.model?.unit_of_measurement ?? "";
                            if (createdId && itemList[index]) {
                                const updatedItems = itemList.map(it => {
                                    if (
                                        it?.notExistsItems &&
                                        it?.selectedItem === item_name // Match by name
                                    ) {
                                        return {
                                            ...it,
                                            notExistsItems: false,
                                            selectedItem: createdId,
                                            item_name: item_name,
                                            selectedLedger: expenseLedgerId,
                                            selectedUnit:selectedUnit,
                                            itemUnitOption:itemUnitOption
                                        };
                                    }
                                    return it;
                                });
                                setItems(updatedItems);
                                itemList = updatedItems;
                            }
                            processedItems.add(item?.selectedItem);
                        }
                    }

                } catch (error) {
                    console.error("Error in sequential API calls", error);
                }
            }
        };

        const addAllLedger = async () => {
            try {
              const ledgerList = await dispatch(getLedgerModelDetail(LedgerType.ITEM_LEDGER));
              if (!ledgerList) return;

              const findExpenseLedger = Object.keys(ledgerList).find(
                key => ledgerList[key] === "Expense"
              );

              if (!findExpenseLedger) return;

              const expenseLedgerDetail = await dispatch(getLedgerGroupDetail(findExpenseLedger));
              const findExpenseDetail = Object.keys(expenseLedgerDetail?.expenseType || {}).find(
                key => expenseLedgerDetail.expenseType[key] === "Purchase"
              );
              if (!items?.length || !findExpenseDetail) return;

              const processedLedger = new Set();

              for (let index = 0; index < items.length; index++) {
                const item = items[index];
                const ledgerName = item?.selectedLedger;

                if (item?.notExistsLedger && !processedLedger.has(ledgerName)) {
                  const baseExpense = {
                    name: ledgerName,
                    group_id: findExpenseLedger,
                    opening_balance_details: {
                      opening_balance: "",
                      opening_balance_dr_cr: 1,
                      bill_wise: 0,
                    },
                    action: "Expense",
                    expense_type: findExpenseDetail,
                    is_gst_applicable: 0,
                    gst_tax_id: null,
                    gst_cess_rate: null,
                    hsn_sac_code: null,
                    description: null,
                  };

                  await dispatch(
                    addLedger(
                      isPurchase,
                      baseExpense,
                      closeLedgerModel,
                      closeLedgerModel,
                      "",
                      "",
                      index,
                      setItemIndex,
                      items,
                      setItems,
                      addLessChanges,
                      setAddLessChanges,
                      "",
                      "",
                      paymentLedgerDetail,
                      setPaymentLedgerDetail,
                      tcsRate,
                      setTcsRate,
                      changeLedgerName,
                      taxableValue,
                      "",
                      itemType,
                      changeTax,
                      "",
                      setIsDisable
                    )
                  );

                  processedLedger.add(ledgerName);
                }
              }
            } catch (error) {
              console.error("Error adding ledgers:", error);
              throw error;
            }
          };


        const renderCell = item => {
            switch (item.header) {
                case TABLE_HEADER_TYPE.ITEM:
                    return <th className="text-center max-w-100px">Item {isOcr && items?.some(item => item?.notExistsItems) && <span className="item_plus cursor-pointer" onClick={() => addAllItems()} ><PlusCircle /></span>}</th>;
                case TABLE_HEADER_TYPE.LEDGER:
                    return <th className="text-center">Ledger {isOcr && items?.some(item => item?.notExistsLedger) && <span className="item_plus cursor-pointer" onClick={() => addAllLedger()} ><PlusCircle /></span>}</th>;
                case TABLE_HEADER_TYPE.QUANTITY:
                    return <th className="text-center min-w-80px">Quantity</th>;
                case TABLE_HEADER_TYPE.FREE_QUANTITY:
                    return <th className="text-center min-w-80px">Free Quantity</th>;
                case TABLE_HEADER_TYPE.HSN_SAC:
                    return <th className="text-center min-w-80px">HSN/SAC</th>;
                case TABLE_HEADER_TYPE.UOM:
                    return <th className="text-center min-w-100px">UOM</th>;
                case TABLE_HEADER_TYPE.MRP:
                    return <th className="text-center min-w-80px">MRP</th>;
                case TABLE_HEADER_TYPE.UNIT_PRICE:
                    return (
                        <th className="text-center unit-price min-w-120px">
                            <div className="d-flex justify-content-center flex-wrap p-0">
                                Unit Price
                                {company?.company?.is_gst_applicable ? (
                                    <div className="unit-price-dropdown w-100 input-group d-block">
                                        <ReactSelect
                                            defaultValue={1}
                                            options={[
                                                { label: "With Tax", value: 1 },
                                                {
                                                    label: "Without Tax",
                                                    value: 0,
                                                },
                                            ]}
                                            placeholder=""
                                            height="18px"
                                            styles={optionStyles}
                                            onChange={(e) => handleChangeTaxStatus(e.value)}
                                            value={changeTax}
                                            width={"100px"}
                                        />
                                    </div>
                                ) : null}
                            </div>
                        </th>
                    );
                case TABLE_HEADER_TYPE.AMOUNT:
                    return (
                        <th className="text-center unit-price min-w-120px">
                            <div className="d-flex justify-content-center flex-wrap p-0">
                                Amount
                                {company?.company?.is_gst_applicable ? (
                                    <div className="unit-price-dropdown w-100">
                                        <ReactSelect
                                            defaultValue={1}
                                            options={[
                                                { label: "With Tax", value: 1 },
                                                {
                                                    label: "Without Tax",
                                                    value: 0,
                                                },
                                            ]}
                                            placeholder=""
                                            height="18px"
                                            styles={optionStyles}
                                            onChange={(e) => handleChangeTaxStatus(e.value)}
                                            value={changeTax}
                                        />
                                    </div>
                                ) : null}
                            </div>
                        </th>
                    );
                case TABLE_HEADER_TYPE.DISCOUNT_1:
                    return <th className="text-center min-w-110px">Discount 1</th>;
                case TABLE_HEADER_TYPE.DISCOUNT_2:
                    return <th className="text-center min-w-110px">Discount 2</th>;
                case TABLE_HEADER_TYPE.TAX_RATE:
                    return (
                        <th className="text-center min-w-100px" style={{ cssText: "width:120px !important"}}>
                            Tax Rate
                        </th>
                    );
                case "Total":
                    return (
                        <th className="text-center" style={{ minWidth: "90px" }}>
                            {" "}
                            Total
                        </th>
                    );
                default:
                    return (
                        <th key={item.id}
                            className="text-center min-w-100px">
                            {item.header}
                        </th>
                    );
            }
        };

        const openPaymentLedgerModel = (id, index, type) => {
            if (id) {
                dispatch(fetchLedgerById(id));
                setLedgerModelName({ name: "Update Ledger", id: id });
            } else {
                setLedgerModelName({ name: "Add Ledger", id: "" });
            }
            setItemIndex(index);
            setIsChangeTds(true);
            dispatch(fetchLedgerGroupList());
            if (type === "payment") {
                dispatch(getLedgerModelDetail(LedgerType.PAYMENT_LEDGER));
                setCheckGroupLedgerType("payment");
                setCurrentAction("");
            } else {
                dispatch(getTcsTdsGroupList(isPurchase ? 1 : 2, false));
                setCheckGroupLedgerType("tds");
                setChangeLedgerName("tds");
                setCurrentAction(isPurchase ? "Taxes - TDS" : "TDS Receivable");
            }
            openItemsLedgerModel();
        };

        const updatePaymentLedgerOption = (e, name, index, type) => {
            const tempTotal = customToFixed(finalAmount, 2) - customToFixed(paymentLedgerDetail.tds_amount || 0, 2);
            const updatedPaymentDetails = [...paymentLedgerDetail?.payment_detail];
            updatedPaymentDetails[index] = {
                ...updatedPaymentDetails[index],
                [name]: e,
                is_show_invoice_date: false,
            };
            const totalAdvancePaymentelected = selectedAdvancePayment?.reduce(
                (acc, row) => acc + parseFloat(row.received_amount || 0),
                0
            ) || 0;
            if (name === "pd_ledger_id") {
                let totalAllocated = updatedPaymentDetails.reduce((sum, payment, idx) => {
                    return idx !== index ? sum + (payment.pd_amount || 0) : sum;
                }, 0);
                const remainingAmount = tempTotal - totalAllocated;
                updatedPaymentDetails[index].pd_amount = customToFixed(Math.max(remainingAmount - totalAdvancePaymentelected, 0), 2);
                updatedPaymentDetails[index].pd_date = formattedDate();
            }

            setPaymentLedgerDetail({
                ...paymentLedgerDetail,
                payment_detail: updatedPaymentDetails,
            });
        };

        const handleTDSRateChange = (value, type) => {
            let taxable = taxableValue;
            setIsChangeTds(true);
            if (value && type === "tds_tax_id") {
                setPaymentLedgerDetail({
                    ...paymentLedgerDetail,
                    tds_tax_id: value?.value,
                    rounding_type: value?.rounding_type,
                    tds_amount: RoundOffMethodForTds(taxable * (paymentLedgerDetail?.tds_rate / 100), value?.rounding_type),
                });
            } else if (type === "tds_rate") {
                setPaymentLedgerDetail({
                    ...paymentLedgerDetail,
                    tds_rate: value,
                    // tds_amount: customToFixed(parseFloat(taxable * (value / 100)), 2),
                    tds_amount: RoundOffMethodForTds(taxable * (value / 100), paymentLedgerDetail?.rounding_type),
                });
            } else {
                setPaymentLedgerDetail({
                    ...paymentLedgerDetail,
                    tds_tax_id: "",
                    tds_rate: "",
                    tds_amount: "",
                });
            }
            if (type === "tds_tax_id") {
                dispatch(
                    fetchTdsTcsRate(
                        value?.value,
                        gstQuote?.party_ledger_id ?? null,
                        setPaymentLedgerDetail,
                        taxable,
                        grandTotal,
                        false,
                        value?.rounding_type
                    )
                );
            }
        };

        useEffect(() => {
            if (paymentLedgerDetail?.tds_tax_id && isChangeTds) {
                setPaymentLedgerDetail({
                    ...paymentLedgerDetail,
                    // tds_amount: customToFixed(
                    //     parseFloat(taxableValue * (paymentLedgerDetail?.tds_rate / 100)),
                    //     2
                    // ),
                    tds_amount: RoundOffMethodForTds(taxableValue * (paymentLedgerDetail?.tds_rate / 100), paymentLedgerDetail?.rounding_type),
                });
            }
        }, [taxableValue, paymentLedgerDetail?.tds_rate, paymentLedgerDetail?.tds_tax_id]);

        useEffect(() => {
            if (paymentLedgerDetail?.payment_detail[0]?.pd_ledger_id || selectedAdvancePayment?.length > 0) {
                setIsPaymentDetailsOpen("0");
            }
        }, [paymentLedgerDetail]);

        const toggleAccordion = key => {
            const shouldStayOpen =
                paymentLedgerDetail?.payment_detail?.length > 1 ||
                paymentLedgerDetail?.payment_detail?.every(item => item?.pd_amount);
            const hasMissingLedger = paymentLedgerDetail?.payment_detail?.every(item => item?.pd_ledger_id);
            const hasValidAmounts = paymentLedgerDetail.payment_detail.every(
                item => item?.pd_amount !== undefined && item?.pd_amount !== null
            );

            const formElements = document?.querySelectorAll("#payment-details-container input, #payment-details-container select");

            formElements.forEach((element) => {
                if (!element.checkValidity()) {
                    element.reportValidity();
                }
            });

            if (!shouldStayOpen && !hasMissingLedger) {
                setIsPaymentDetailsOpen(prevKey => (prevKey === key ? "" : key));
            } else if (shouldStayOpen && hasMissingLedger && hasValidAmounts) {
                setIsPaymentDetailsOpen(prevKey => (prevKey === key ? "" : key));
            }
        };

        useEffect(() => {
            const tooltipTriggerList = [].slice.call(
                document?.querySelectorAll('[data-bs-toggle="tooltip"]')
            );
            tooltipTriggerList?.forEach(tooltipTriggerEl => {
                new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }, []);

        const handleOpenPaymentModal = async (id, index) => {
            setIsPaymentMode(true);
            setPaymentModeId({id, index});
            if (id) {
                await dispatch(editPaymentModeData(id));
            }
        }

        return (
            <>
                <Container fluid className="p-0 mt-5">
                    <div className="content-wrapper py-6 px-lg-10 px-sm-8 px-6">
                        <div className="card">
                            <div className="items-details-card overflow-x-hidden">
                                <div className="d-flex flex-wrap gap-2 align-items-center justify-content-between mb-2 mx-0">
                                    <div className="">
                                        <h5 className="title-name mb-0 me-4">
                                            {itemType === "item" ? "Item " : "Ledger "}
                                            Details
                                        </h5>
                                    </div>
                                    {isDeliveryChallan ? <div className="ms-auto">
                                        <div className="ms-auto d-flex">
                                            <div
                                                className="d-inline-block"
                                                data-bs-toggle="tooltip"
                                                data-bs-placement="bottom"
                                                title="Click here to use the barcode scanner to select items."
                                            >
                                                <button
                                                    type="button"
                                                    className="btn btn-sm btn-outline-primary barcode-print-focus-button"
                                                    disabled={itemType === "accounting"}
                                                >
                                                    <i className="fa-solid fa-barcode fs-16 text-primary"></i>
                                                </button>
                                            </div>
                                            <div className="form-check mx-2 mt-1 align-content-center">
                                                <label
                                                    className="form-label mb-0 cursor-pointer"
                                                    htmlFor="itemInvoice"
                                                >
                                                    Without Amount
                                                </label>
                                                <input
                                                    className="form-check-input"
                                                    id="itemInvoice"
                                                    checked={itemType === "without_amount"}
                                                    name="sales_item_type"
                                                    type="radio"
                                                    value="without_amount"
                                                    onChange={handleItemTypeChange}
                                                />
                                            </div>
                                            <div className="form-check ms-2 mt-1 align-content-center">
                                                <label
                                                    className="form-label mb-0 cursor-pointer"
                                                    htmlFor="accountingInvoice"
                                                >
                                                    With Amount
                                                </label>
                                                <input
                                                    className="form-check-input"
                                                    id="accountingInvoice"
                                                    name="sales_item_type"
                                                    type="radio"
                                                    value="item"
                                                    checked={itemType === "item"}
                                                    onChange={handleItemTypeChange}
                                                />
                                            </div>
                                        </div>
                                    </div> :
                                    <div className="ms-auto">
                                        <div className="ms-auto d-flex">
                                            <div
                                                className="d-inline-block"
                                                data-bs-toggle="tooltip"
                                                data-bs-placement="bottom"
                                                title="Click here to use the barcode scanner to select items."
                                            >
                                                <button
                                                    type="button"
                                                    className="btn btn-sm btn-outline-primary barcode-print-focus-button"
                                                    disabled={itemType === "accounting"}
                                                >
                                                    <i className="fa-solid fa-barcode fs-16 text-primary"></i>
                                                </button>
                                            </div>
                                            <div className="form-check mx-2 mt-1 align-content-center">
                                                <label
                                                    className="form-label mb-0 cursor-pointer"
                                                    htmlFor="itemInvoice"
                                                >
                                                    Item Invoice
                                                </label>
                                                <input
                                                    className="form-check-input"
                                                    id="itemInvoice"
                                                    checked={itemType === "item"}
                                                    name="sales_item_type"
                                                    type="radio"
                                                    value="item"
                                                    onChange={handleItemTypeChange}
                                                />
                                            </div>
                                            <div className="form-check ms-2 mt-1 align-content-center">
                                                <label
                                                    className="form-label mb-0 cursor-pointer"
                                                    htmlFor="accountingInvoice"
                                                >
                                                    Accounting Invoice
                                                </label>
                                                <input
                                                    className="form-check-input"
                                                    id="accountingInvoice"
                                                    name="sales_item_type"
                                                    type="radio"
                                                    value="accounting"
                                                    checked={itemType === "accounting"}
                                                    onChange={handleItemTypeChange}
                                                />
                                            </div>
                                        </div>
                                    </div>}
                                </div>
                                <div className="item-container overflow-auto">
                                    <table className="table table-bordered sales-table">
                                        <thead  className="table-header">
                                        <tr>
                                            <th>Sr No.</th>
                                            {/* {tableHeader?.length > 0 &&
                                                tableHeader?.map(item => renderCell(item))} */}
                                            {tableHeader?.length > 0 &&
                                                tableHeader?.map((item, index) => (
                                                    <React.Fragment key={item.id || index}>
                                                        {renderCell(item)}
                                                    </React.Fragment>
                                                ))
                                            }
                                            <th
                                                style={{
                                                    maxWidth: "60px",
                                                    width: "60px",
                                                }}
                                            >
                                                {""}
                                            </th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                            {items?.map((item, index) => (
                                                <tr key={item.id}>
                                                    <Item
                                                        ledgerId={gstQuote?.party_ledger_id}
                                                        itemCount={items.length}
                                                        id={item.id}
                                                        index={index}
                                                        item={item}
                                                        onUpdate={handleUpdateItem}
                                                        itemOption={itemOption}
                                                        itemDefaultOption={itemDefaultOption}
                                                        tableHeader={tableHeader}
                                                        itemType={itemType}
                                                        ledgerList={ledgerOptions}
                                                        ledgerDefaultOptions={ledgerDefaultOptions}
                                                        handleOpenSecond={handleOpenSecond}
                                                        setLedgerModel={openItemLedgerModel}
                                                        openItemModel={openItemsModel}
                                                        gstOptions={gstOptions}
                                                        itemUnitOption={itemUnitOption}
                                                        changeTax={changeTax}
                                                        saleConfiguration={saleConfiguration}
                                                        setQuantityId={setQuantityId}
                                                        openQuantityModel={openQuantityModel}
                                                        setIsEditCalculation={setIsEditCalculation}
                                                        setCheckGroupLedgerType={
                                                            setCheckGroupLedgerType
                                                        }
                                                        openIsNegativeStockModel={
                                                            openIsNegativeStockModel
                                                        }
                                                        setIsConfirmed={setIsConfirmed}
                                                        classification={classification}
                                                        isPurchase={isPurchase}
                                                        itemRef={itemRef}
                                                        ledgerRef={ledgerRef}
                                                        unitRef={unitRef}
                                                        quantityRef={quantityRef}
                                                        totalRef={totalRef}
                                                        mrpRef={mrpRef}
                                                        amountRef={amountRef}
                                                        priceWithGstRef={priceWithGstRef}
                                                        discountTypeRef={discountTypeRef}
                                                        discountTypeRef2={discountTypeRef2}
                                                        discountValueRef={discountValueRef}
                                                        discountValueRef2={discountValueRef2}
                                                        gstRef={gstRef}
                                                        setCreateNewItem={setCreateNewItem}
                                                        userPermission={userPermission}
                                                        isRecurring={isRecurring}
                                                        setIsDescription={setIsDescription}
                                                        setNotExistItems={setNotExistItems}
                                                        setOcrResponse={setOcrResponse}
                                                        pageId={id}
                                                        setisFieldsChanges={setisFieldsChanges}
                                                        openLedgerModel={openLedgerModel}
                                                        setNotExistParty={setNotExistParty}
                                                        setPartyTitle={setPartyTitle}
                                                        setItemIndex={setItemIndex}
                                                        recurringInvoiceDetails={recurringInvoiceDetails}
                                                        getCustomFieldTransactionType={getCustomFieldTransactionType}
                                                    />
                                                    <td
                                                        className="pe-0"
                                                        style={{
                                                            maxWidth: "60px",
                                                            width: "60px",
                                                        }}
                                                    >
                                                        <div className="delete-item w-100">
                                                            {/* {item.isShowDelete === true && */}
                                                            {items.length !== 1 ? (
                                                                <button
                                                                    type="button"
                                                                    className="bg-transparent border-0 p-0 ps-3 w-100"
                                                                    data-id="2"
                                                                    onClick={() =>
                                                                        removeItem(index)
                                                                    }
                                                                >
                                                                    <i className="fas fs-2 fa-trash-alt text-danger mx-auto mt-2"></i>
                                                                </button>
                                                            ) : (
                                                                ""
                                                            )}
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                            <tr>
                                                <td style={{ width: "50px" }}>
                                                    {items.length + 1}
                                                </td>
                                                <td>
                                                    <button
                                                        ref={lineItem}
                                                        type="button"
                                                        className="btn-sm btn-icon btn-icon-primary add-item-btn"
                                                        onClick={addItemRow}
                                                    >
                                                        {itemType === "item"
                                                            ? "Line Item"
                                                            : "Ledger"}
                                                    </button>
                                                </td>
                                                {Array.from({
                                                    length:
                                                        tableHeader?.length -
                                                        (company?.company?.is_gst_applicable
                                                            ? 2
                                                            : 3),
                                                })?.map((_, i) => {
                                                    if (
                                                        tableHeader[i + 1]?.header === TABLE_HEADER_TYPE.QUANTITY &&
                                                        itemType === "item"
                                                    ) {
                                                        return (
                                                            <td key={i} className="fs-5 text-end">
                                                                {" "}
                                                                <div className="pe-3">
                                                                    {customToFixed(
                                                                        totalQuantity,
                                                                        maxDecimalPlaces
                                                                    )}
                                                                </div>
                                                            </td>
                                                        );
                                                    }
                                                    if (
                                                        tableHeader[i + 1]?.header ===
                                                        TABLE_HEADER_TYPE.LEDGER &&
                                                        itemType === "item"
                                                    ) {
                                                        return (
                                                            <td key={i} className="fs-5 text-end">
                                                                <div className="pe-2">Total</div>
                                                            </td>
                                                        );
                                                    }
                                                    return <td key={i}></td>;
                                                })}
                                                {company?.company?.is_gst_applicable == 0 ? (
                                                    <td className="fs-5 text-end">
                                                    </td>
                                                ) : ""}
                                                <td className="fs-5 text-end">
                                                    <div className="overflow-auto whitespace-nowrap">
                                                        {company?.company?.currentCurrencySymbol}{" "}
                                                        {grandTotal % 1 === 0
                                                            ? grandTotal
                                                            : customToFixed(grandTotal, 2) == 0
                                                                ? 0.0
                                                                : customToFixed(grandTotal, 2)}
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </Container>
                <Container fluid className="p-0 mt-5">
                    <div className="content-wrapper py-6 px-lg-10 px-sm-8 px-6">
                        <ShippingCard
                            ledgerOptions={ledgerOptions}
                            additionalOptions={additionalOptions}
                            classificationType={classificationType}
                            shippingValue={shippingValue}
                            tcsValue={tcsValue}
                            handleTCSValueChange={e => setTCSValue(parseFloat(e.target.value) || 0)}
                            handleTCSRateChange={e => setTCSRateValue(e.target.value)}
                            tcsRateValue={tcsRateValue}
                            packingCharge={packingCharge}
                            grandTotal={grandTotal}
                            items={items}
                            isShowGstValue={isShowGstValue}
                            taxableValue={taxableValue}
                            mainGrandTotal={mainGrandTotal}
                            setFinalAmount={setFinalAmount}
                            finalAmount={finalAmount}
                            isPurchase={isPurchase}
                            setCurrentAction={setCurrentAction}
                            showCreditLimit={showCreditLimit}
                            isOcr={isOcr}
                            userPermission={userPermission}
                            setItems={setItems}
                        />
                    </div>
                </Container>

                {/* {isShowPaymentDetails && ( */}
                <>
                    {(((company?.company?.is_tds_applicable || !isPurchase) && saleConfiguration?.footer?.is_enabled_tds_details) ||
                        saleConfiguration?.footer?.is_enabled_payment_details) ? (
                        <Container fluid className="p-0 mt-5">
                            <div className="content-wrapper py-6 px-lg-10 px-sm-8 px-6">
                                {(company?.company?.is_tds_applicable || !isPurchase) && saleConfiguration?.footer?.is_enabled_tds_details ? (
                                    <div className="position-relative">
                                        {saleConfiguration?.footer?.is_enabled_tds_details ? (
                                            <>
                                                <Row
                                                    className={`mt-3 mx-0 justify-content-between ${
                                                        saleConfiguration?.footer
                                                            ?.is_enabled_payment_details
                                                            ? "mb-4"
                                                            : ""
                                                        } `}
                                                >
                                                    <Col
                                                        xxl={2}
                                                        lg={3}
                                                        sm={4}
                                                        className="mb-4 form-group-select ps-1"
                                                    >
                                                        <Form.Group>
                                                            <div className="input-group flex-nowrap bg-white">
                                                                <div className={`position-relative h-40px w-100 tcs-input focus-shadow ${userPermission?.add_ledger_master ? 'pe-36px' : ""}`}>
                                                                    <ReactSelect
                                                                        options={tdsOptions}
                                                                        value={
                                                                            paymentLedgerDetail.tds_tax_id ||
                                                                            null
                                                                        }
                                                                        onChange={e => {
                                                                            handleTDSRateChange(
                                                                                e,
                                                                                "tds_tax_id"
                                                                            );
                                                                        }}
                                                                        placeholder="TDS Ledger"
                                                                        defaultLabel="Select TDS Ledger"
                                                                        isCreatable={false}
                                                                        isEdit={userPermission?.edit_ledger_master}
                                                                        handleOpen={() =>
                                                                            openPaymentLedgerModel(
                                                                                paymentLedgerDetail?.tds_tax_id,
                                                                                "",
                                                                                "tds"
                                                                            )
                                                                        }
                                                                        position={saleConfiguration?.footer?.is_enabled_payment_details ? "bottom" : "top"}
                                                                    />
                                                                </div>
                                                                {userPermission?.add_ledger_master ?
                                                                <button
                                                                    className="input-group-text custom-group-text"
                                                                    type="button"
                                                                    onClick={() =>
                                                                        openPaymentLedgerModel(
                                                                            "",
                                                                            "",
                                                                            "tds"
                                                                        )
                                                                    }
                                                                >
                                                                    <i className="fas fa-plus text-gray-900"></i>
                                                                </button>
                                                                : ""}
                                                            </div>
                                                        </Form.Group>
                                                    </Col>
                                                    <Col xxl={2} lg={2} sm={4} className="mb-4">
                                                        <Form.Group className="position-relative form-floating-group">
                                                            <FormInput
                                                                className="floating-label-input"
                                                                step="0.01"
                                                                type="number"
                                                                max={100}
                                                                placeholder=""
                                                                value={paymentLedgerDetail.tds_rate}
                                                                onChange={e =>
                                                                    handleTDSRateChange(
                                                                        e.target.value,
                                                                        "tds_rate"
                                                                    )
                                                                }
                                                            />
                                                            <Form.Label htmlFor="TDS Rate">
                                                                TDS Rate
                                                            </Form.Label>
                                                        </Form.Group>
                                                    </Col>
                                                    <Col xxl={2} lg={2} sm={4} className="mb-4">
                                                        <Form.Group className="position-relative form-floating-group">
                                                            <FormInput
                                                                className="floating-label-input"
                                                                step="0.01"
                                                                type="number"
                                                                placeholder=""
                                                                value={
                                                                    paymentLedgerDetail.tds_amount
                                                                }
                                                                onChange={e =>
                                                                    setPaymentLedgerDetail({
                                                                        ...paymentLedgerDetail,
                                                                        tds_amount: e.target.value,
                                                                    })
                                                                }
                                                            />
                                                            <Form.Label htmlFor="Amount">
                                                                TDS Amount
                                                            </Form.Label>
                                                        </Form.Group>
                                                    </Col>
                                                    <Col
                                                        xxl={2}
                                                        lg={2}
                                                        sm={4}
                                                    ></Col>
                                                    <Col xxl={2} lg={2}></Col>
                                                    <Col
                                                        lg={1}
                                                        sm={4}
                                                    ></Col>
                                                </Row>
                                            </>
                                        ) : null}
                                    </div>
                                ) : null}
                                {saleConfiguration?.footer?.is_enabled_payment_details ? (
                                    <>
                                    <Accordion
                                        defaultActiveKey={isPaymentDetailsOpen}
                                        onSelect={e => toggleAccordion(e)}
                                        activeKey={isPaymentDetailsOpen}
                                    >
                                        <Accordion.Item eventKey="0">
                                                <div className="d-flex ps-1">
                                                    <Accordion.Header>Payment Details</Accordion.Header>
                                                    {isPaymentDetailsOpen && gstQuote?.party_ledger_id && !isShowAdvancePayment ? <div className="d-flex mx-5">
                                                        <h5>Settle</h5>
                                                        <div className="form-check form-switch" style={{ paddingLeft: "0.75rem" }}>
                                                            <input checked={showPaymentTable} onClick={() => onClickSettlePayment(showPaymentTable)} className="form-check-input" type="checkbox" style={{ float: "right", marginLeft: "0px" }} />
                                                        </div>
                                                    </div> : ''}
                                                </div>
                                            <Accordion.Body>
                                                <div className="position-relative overflowX-auto" id="payment-details-container">
                                                    {paymentLedgerDetail?.payment_detail?.map(
                                                        (item, index) => {
                                                        const paymentModeId = paymentModeOptions.find(opt => opt.value == item.pd_mode) || null;
                                                           return (
                                                            <>
                                                                <Row key={index} className="mt-3 mx-0 justify-content-between flex-nowrap">
                                                                    <Col
                                                                        xxl={2}
                                                                        lg={3}
                                                                        sm={4}
                                                                                xs={8}
                                                                        className="mb-3 form-group-select ps-1"
                                                                    >
                                                                        <Form.Group >
                                                                            <div className="input-group flex-nowrap">
                                                                                <div className={`position-relative h-40px w-100 focus-shadow ${userPermission?.add_ledger_master ? 'pe-36px' : ""}`}>
                                                                                    <ReactSelect
                                                                                        options={
                                                                                            paymentLedgerOptions
                                                                                        }
                                                                                        value={
                                                                                            item?.pd_ledger_id ||
                                                                                            null
                                                                                        }
                                                                                        onChange={e =>
                                                                                            updatePaymentLedgerOption(
                                                                                                e.value,
                                                                                                "pd_ledger_id",
                                                                                                index,
                                                                                                "select"
                                                                                            )
                                                                                        }
                                                                                        placeholder={
                                                                                            "Payment Ledger"
                                                                                        }
                                                                                        defaultLabel={
                                                                                            "Select Payment Ledger"
                                                                                        }
                                                                                        islabel={
                                                                                            true
                                                                                        }
                                                                                        className="h-40px"
                                                                                        radius={
                                                                                            true
                                                                                        }
                                                                                        isEdit={
                                                                                            userPermission?.edit_ledger_master
                                                                                        }
                                                                                        handleOpen={() =>
                                                                                            openPaymentLedgerModel(
                                                                                                item?.pd_ledger_id,
                                                                                                "",
                                                                                                "payment"
                                                                                            )
                                                                                        }
                                                                                        position="top"
                                                                                        required={
                                                                                            paymentLedgerDetail
                                                                                                ?.payment_detail
                                                                                                ?.length >
                                                                                                1 ||
                                                                                            item?.pd_amount
                                                                                        }
                                                                                    />
                                                                                </div>
                                                                                {userPermission?.add_ledger_master ?
                                                                                <button
                                                                                    type="button"
                                                                                    className="input-group-text custom-group-text"
                                                                                    onClick={() =>
                                                                                        openPaymentLedgerModel(
                                                                                            "",
                                                                                            index,
                                                                                            "payment"
                                                                                        )
                                                                                    }
                                                                                >
                                                                                    <i className="fas fa-plus text-gray-900"></i>
                                                                                </button>
                                                                                : ""}
                                                                            </div>
                                                                        </Form.Group>
                                                                    </Col>
                                                                    <Col
                                                                        xxl={2}
                                                                        lg={2}
                                                                        sm={4}
                                                                                xs={6}
                                                                        className="mb-3"
                                                                    >
                                                                        <Form.Group >
                                                                            <DatePickerPaymentDetails
                                                                                value={item.pd_date}
                                                                                required={
                                                                                    item?.pd_ledger_id
                                                                                }
                                                                                isPaymentDetails={
                                                                                    true
                                                                                }
                                                                                onChange={e =>
                                                                                    updatePaymentLedgerOption(
                                                                                        formattedDate(
                                                                                            new Date(
                                                                                                e
                                                                                            )
                                                                                        ),
                                                                                        "pd_date",
                                                                                        index,
                                                                                        "date"
                                                                                    )
                                                                                }
                                                                                placeholder="Date"
                                                                                options={true}
                                                                            />
                                                                        </Form.Group>
                                                                    </Col>
                                                                    <Col
                                                                        xxl={2}
                                                                        lg={2}
                                                                        sm={4}
                                                                                xs={6}
                                                                        className="mb-3"
                                                                    >
                                                                        <Form.Group className="position-relative form-floating-group">
                                                                            <FormInput
                                                                                        className="floating-label-input h-40px"
                                                                                step="0.01"
                                                                                type="number"
                                                                                placeholder=""
                                                                                value={
                                                                                    item.pd_amount
                                                                                }
                                                                                required={
                                                                                    item?.pd_ledger_id
                                                                                }
                                                                                onClick={e =>
                                                                                    e.target.select()
                                                                                }
                                                                                onChange={e =>
                                                                                    updatePaymentLedgerOption(
                                                                                        parseFloat(
                                                                                            e.target
                                                                                                .value
                                                                                        ),
                                                                                        "pd_amount",
                                                                                        index,
                                                                                        "input"
                                                                                    )
                                                                                }
                                                                            />
                                                                            <Form.Label
                                                                                className={`whitespace-nowrap ${
                                                                                    item?.pd_ledger_id &&
                                                                                    "required"
                                                                                }`}
                                                                            >
                                                                                Amount
                                                                            </Form.Label>
                                                                        </Form.Group>
                                                                    </Col>
                                                                    <Col
                                                                            xxl={2}
                                                                            lg={2}
                                                                            sm={4}
                                                                            xs={6}
                                                                            className="mb-3"
                                                                        >
                                                                            <Form.Group>
                                                                                <div className="input-group flex-nowrap">
                                                                                    <div className="position-relative h-34px w-100 focus-shadow pe-36px">
                                                                                            <ReactSelect
                                                                                                options={
                                                                                                    paymentModeOptions
                                                                                                }
                                                                                                value={
                                                                                                    paymentModeId?.value || null
                                                                                                }
                                                                                                onChange={e =>
                                                                                                    updatePaymentLedgerOption(
                                                                                                        e.value,
                                                                                                        "pd_mode",
                                                                                                        index,
                                                                                                        "select"
                                                                                                    )
                                                                                                }
                                                                                                placeholder="Select Mode"
                                                                                                defaultLabel="Select Mode"
                                                                                                position="top"
                                                                                                height="34px"
                                                                                                isEdit={true}
                                                                                                handleOpen={(e) => handleOpenPaymentModal(e, index)}
                                                                                            />
                                                                                        </div>
                                                                                        <button
                                                                                            type="button"
                                                                                            className="input-group-text custom-group-text"
                                                                                            onClick={() => handleOpenPaymentModal("", index)}
                                                                                        >
                                                                                            <i className="fas fa-plus text-gray-900"></i>
                                                                                        </button>
                                                                                </div>
                                                                            </Form.Group>
                                                                        </Col>
                                                                    <Col
                                                                        xxl={2}
                                                                        lg={2}
                                                                        sm={4}
                                                                        xs={6}
                                                                        onChange={e =>
                                                                            updatePaymentLedgerOption(
                                                                                e.target.value,
                                                                                "pd_reference_number",
                                                                                index,
                                                                                "date"
                                                                            )
                                                                        }
                                                                        className="mb-3"
                                                                    >
                                                                        <Form.Group className="position-relative form-floating-group">
                                                                            <FormInput
                                                                                        className="floating-label-input h-40px"
                                                                                step="0.01"
                                                                                type="string"
                                                                                placeholder=""
                                                                                value={
                                                                                    paymentLedgerDetail
                                                                                        .payment_detail[
                                                                                        index
                                                                                    ]
                                                                                        .pd_reference_number
                                                                                }
                                                                                onChange={e =>
                                                                                    updatePaymentLedgerOption(
                                                                                        e.target
                                                                                            .value,
                                                                                        "pd_reference_number",
                                                                                        index,
                                                                                        "input"
                                                                                    )
                                                                                }
                                                                            />
                                                                            <Form.Label className="whitespace-nowrap">
                                                                                Reference Number
                                                                            </Form.Label>
                                                                        </Form.Group>
                                                                    </Col>
                                                                    <Col
                                                                        lg={1}
                                                                        sm={1}
                                                                                xs={2}
                                                                        className="mb-3 pe-0 h-40px d-flex justify-content-end"
                                                                    >
                                                                            <div className="d-flex align-items-center position-relative">
                                                                                {paymentLedgerDetail
                                                                                    ?.payment_detail
                                                                                    ?.length > 1 ? (
                                                                                    <button
                                                                                        style={{
                                                                                            marginRight:
                                                                                                "24px",
                                                                                        }}
                                                                                        className="btn btn-transparent p-0"
                                                                                        type="button"
                                                                                        onClick={() =>
                                                                                            handleAddPaymentLedger(
                                                                                                "remove",
                                                                                                index
                                                                                            )
                                                                                        }
                                                                                    >
                                                                                        <i className="fas fa-trash-alt text-danger"></i>
                                                                                    </button>
                                                                                ) : null}
                                                                                {index ===
                                                                                    paymentLedgerDetail
                                                                                        ?.payment_detail
                                                                                        ?.length -
                                                                                    1 &&
                                                                                    paymentLedgerDetail
                                                                                        ?.payment_detail
                                                                                        ?.length < 3 ? (
                                                                                    <button
                                                                                        className="btn btn-transparent p-0 ms-3 position-absolute right-0"
                                                                                        style={{
                                                                                            fontSize:
                                                                                                "20px",
                                                                                        }}
                                                                                        onClick={
                                                                                            handleAddPaymentLedger
                                                                                        }
                                                                                        type="button"
                                                                                    >
                                                                                        +
                                                                                    </button>
                                                                                ) : null}
                                                                            </div>
                                                                        </Col>
                                                                    </Row>
                                                                </>
                                                            )}
                                                        )}
                                                        </div>
                                                </Accordion.Body>
                                            </Accordion.Item>
                                        </Accordion>
                                    {showPaymentTable && !isShowAdvancePayment && <PaymentTable transactions={transactions}/>}
                                    </>
                                ) : null}
                            </div>
                        </Container>
                    ) : null}
                </>
                {/* )} */}
                {isClassificationModel && (
                    <ClassificationModal
                        show={isClassificationModel}
                        handleClose={closeClassificationModel}
                        classificationOptions={classificationOptions}
                        classification={classification}
                        setClassification={setClassification}
                        isRcsApplicable={isRcsApplicable}
                    />
                )}
                {isItemModel && (
                    <AddItemModal
                        show={isItemModel}
                        id={itemOption.length + 1}
                        handleClose={closeItemModel}
                        itemListdata={item}
                        name={modelName}
                        index={itemIndex}
                        setIndex={setItemIndex}
                        items={items}
                        setItems={setItems}
                        setModelName={setModelName}
                        isPurchase={isPurchase}
                        isChangeGst={saleConfiguration?.header?.is_change_gst_details}
                        setIsChangeType={setIsChangeType}
                    />
                )}
                {isItemsLedgerModel && (
                    <AddLedgerModal
                        show={isItemsLedgerModel}
                        handleClose={closeItemsLedgerModel}
                        brokerOptions={brokerOptions}
                        ledgerDetailOptions={ledgerDetailOptions}
                        entityType={ledgerEntityType}
                        ledgerGroupType={ledgerGroupType}
                        id={1}
                        sale={false}
                        name={ledgerModelName}
                        setLedgerModelName={setLedgerModelName}
                        index={itemIndex}
                        changeLedgerName={changeLedgerName}
                        setIndex={setItemIndex}
                        items={items}
                        setItems={setItems}
                        paymentLedgerDetail={paymentLedgerDetail}
                        setPaymentLedgerDetail={setPaymentLedgerDetail}
                        action={currentAction}
                        setTdsRate={setPaymentLedgerDetail}
                        gstQuote={gstQuote}
                        isPurchase={isPurchase}
                        itemType={itemType}
                        changeTax={changeTax}
                        shipping_address_type={shipping_address_type}
                        taxableValue={taxableValue}
                        invoiceValue={invoiceValue}
                        partyLedgerId={gstQuote?.party_ledger_id}
                    />
                )}
                {isOpenQuantityModel && (
                    <AddConsolidatingModal
                        show={isOpenQuantityModel}
                        handleClose={closeQuantityModel}
                        items={items}
                        setItems={setItems}
                        handleSubmit=""
                        quantityId={quantityId}
                        changeTax={changeTax}
                        itemType={itemType}
                        openNegativeModelHandler={openNegativeModelHandler}
                        setIsEditCalculation={setIsEditCalculation}
                    />
                )}
                {isNegativeStock && (
                    <NegativeStockModal
                        show={isNegativeStock}
                        handleClose={closeIsNegativeStockModel}
                        items={items}
                        setItems={setItems}
                        handleSubmit=""
                        quantityId={quantityId}
                        closeWarningModelHandler={closeWarningModelHandler}
                        updatedId={updatedId}
                    />
                )}
                {showItemPriceChangedModal && (
                    <WarningModal
                        show={showItemPriceChangedModal}
                        handleClose={() => {
                            setShowItemPriceChangedModal(false);
                            setChangeItemPrice(false);
                        }}
                        showConfirmButton
                        showCancelButton
                        handleSubmit={() => {
                            setChangeItemPrice(true);
                            setShowItemPriceChangedModal(false);
                            setIsConfirmed(true);
                        }}
                        message="Are you sure want to change the price of this Transaction?"
                        confirmText="Yes, Change"
                        cancelText="No, Cancel"
                        modalTitle="Change !"
                    />
                )}

                {isDescription && <DynamicDescriptionModal
                    isDescription={isDescription}
                    handleDescriptionClose={() => setIsDescription(false)}
                    index={quantityId}
                    items={items}
                    setItems={setItems}
                />}
                {isPaymentMode && <PaymentModeModal isPaymentMode={isPaymentMode} handleClose={() => setIsPaymentMode(false)} paymentModeId={paymentModeId} isPurchase={isPurchase} />}
                {isManageCustomItemMaster && <CustomFieldBatchModal
                    show={isManageCustomItemMaster}
                    handleClose={() => setIsManageCustomItemMaster(false)}
                    openClassificationModel={openClassificationModel}
                />}
            </>
        );
    }
);

export default SaleItems;
