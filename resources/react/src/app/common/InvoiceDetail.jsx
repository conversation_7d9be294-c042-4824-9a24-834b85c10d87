import React, { useCallback, useContext, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { StateContext } from "../../context/StateContext";
import ReactSelect from "../../components/ui/ReactSelect";
import useDropdownOption from "../../shared/dropdownList";
import {
    addLedger,
    fetchLedgerById,
    fetchLedgerGroupList,
    fetchPartyDetail,
    fetchPartyList,
    getLedgerModelDetail,
} from "../../store/ledger/ledgerSlice";
import { ITEM_STATUS, LedgerType, SHIPPING_ADDRESS_TYPE_LIST, transactionTypeMap, USER_PERMISSION } from "../../constants";
import { fetchTransportById } from "../../store/transport/transportSlice";
import {
    fetchBrokerById,
    fetchBrokerDetailsById,
    getBrokerModelDetail,
} from "../../store/broker/brokerSlice";
import { Col, Form, Row } from "react-bootstrap";
import Datepicker from "../../components/ui/DatePicker";
import {
    clearData,
    prepareDispatchData,
} from "../../shared/prepareData";
import { FormInput } from "../../components/ui/Input";
import { challanForSale, estimateForSale, fetchSaleById } from "../../store/sale/saleSlice";
import DispatchFromModal from "../modal/DispatchAddress/DispatchFromModal";
import ShippingFromModal from "../modal/PartyAddress/ShippingFromModal";
import AddBrokerModal from "../modal/Broker/BrokerModal";
import AddTransportModal from "../modal/Transport/TransportModal";
import AddLedgerModal from "../modal/Ledger/LedgerModal";
import ExpenseOtherDetail from "./ExpenseDetail";
import { CheckGstValidate, formattedDate } from "../../shared/calculation";
import EstimateQuoteOtherDetails from "../estimate-quote/EstimateQuoteOtherDetails";
import DispatchDetails from "./DispatchDetails";
import PurchaseOrderOtherDetail from "../purchase-order/PurchaseOrderDetail";
import { fetchShippingAddressList, getShippingAddressList } from "../../store/shippingAddress/shippingAddressSlice";
import PartyAddressModal from "../modal/PartyAddress/PartyAddressModal";
import PurchaseInvoiceDetails from "./PurchaseAndReturnDetail";
import { fetchTdsTcsRate } from "../../store/rate/rateSlice";
import ExpenseCnDnDetail from "../expense-cn-dn/ExpenseCnDnDetail";
import OriginalInvoiceDateDetail from "./OriginalInvoiceDateDetail";
import {
    fetchPurchaseById,
    getPurchaseOrderFromMultipleNumber,
} from "../../store/purchase/purchaseSlice";
import SelectInvoiceModal from "../modal/Item/InvoiceSelection";
import { fetchPartyIdBaseItemList, fetchPriceListOfItems } from "../../store/item/itemSlice";
import PurchaseNumberSelection from "../modal/Item/PurchaseNumberSelection";
import { useParams } from "react-router-dom";
import DatePickerPaymentDetails from "../../components/ui/DatePickerPaymentDetails";
import PhoneInput from "react-phone-input-2";
import CustomField from "./CustomField";
import PlusCircle from "../../assets/images/svg/PlusCircle";
import RecurringInvoiceDetails from "./RecurringInvoiceDetails";
import { useTransactionShortcuts } from "../../shared/shortcut-keys";
import { checkPathName } from "../../shared/sharedFunction";

const SaleInvoiceDetail = ({
    tableHeaderList,
    accountingTableHeader,
    isShowInvoiceDetails = false,
    isShowEstimateInvoice = false,
    isShowPurchaseOrder,
    isShowValidFor = false,
    isShowEstimateOrChallan,
    isShowChallanPageComponent = false,
    isShowChallanPage = false,
    isShowPartyInvoiceNumber = false,
    iShowExpenseDetail,
    isShowPurchaseInvoiceDetails = false,
    isShowPurchaseNumber,
    isShowExpenseCreateOrDebit,
    ExpenseCreateOrDebit,
    id,
    shipping_address_type,
    isPurchase,
    invoiceRef,
    deliveryRef,
    purchaseReturnRef,
    expenseCnDnRef,
    purchaseOrderRef,
    estimateRef,
    ledgerModalName,
    ledgerModalEditName,
    isDuplicate,
    saleInvoiceId,
    isPurchaseToPurchaseReturn,
    purchaseOrderId,
    isOcr,
    isPartyNameExist,
    partyDetails={},
    isRecurring=false
}) => {
    const pathname = window.location.pathname?.includes("sale") ? "datePickerInvoiceDate" : window.location.pathname?.includes("delivery-challan") ? "datePickerChallanDate" : "";
    const { estimateId, challanId, id: otherId } = useParams();
    const {
        invoiceDetail,
        setInvoiceDetail,
        partyAddress,
        ewayBillDetail,
        setEwayBillDetail,
        otherDetail,
        setOtherDetail,
        brokerDetail,
        setBrokerDetail,
        transporterDetail,
        setTransporterDetail,
        localDispatchAddress,
        setLocalDispatchAddress,
        gstQuote,
        setGstQuote,
        selectedAddress,
        setSelectedAddress,
        isDispatchFromModel,
        closeDispatchFromModel,
        openLedgerModel,
        setPartyTitle,
        modalType,
        isDispatchAddressModel,
        openDispatchAddressModel,
        closeDispatchAddressModel,
        setDispatchAddressName,
        sameAsBill,
        setSameAsBill,
        isTransportModel,
        openTransportModel,
        closeTransportModel,
        transportTitle,
        setTransportTitle,
        isBrokerModel,
        openBrokerModel,
        closeBrokerModel,
        setModalType,
        isShippingFromModel,
        openShippingFromModel,
        closeShippingFromModel,
        shippingAddress,
        setShippingAddress,
        selectShippingAddress,
        setSelectShippingAddress,
        setItemType,
        setPartyAddress,
        setTcsRate,
        tcsRate,
        partyTitle,
        isLedgerModel,
        closeLedgerModel,
        dispatchAddressName,
        invoiceNumber,
        setInvoiceNumber,
        setIsCreateParty,
        setIsEditCalculation,
        setIsCheckGstType,
        isIGSTCalculation,
        setCheckGroupLedgerType,
        taxableValue,
        cessValue,
        gstValue,
        GSTError,
        setGSTError,
        isChangedTcs,
        isShowSelectedService,
        openSelectedServiceModel,
        closeSelectedServiceModel,
        items,
        setShowItemPriceChangedModal,
        setIsConfirmed,
        showInvoice,
        setShowInvoice,
        updateParty_quotesOptions,
        updatePurchaseNumberOptions,
        setItems,
        setTaxableValue,
        setMainGrandTotal,
        setFinalAmount,
        setGrandTotal,
        setGstValue,
        gstCalculation,
        setGstCalculation,
        isChangePartyId,
        setIsChangePartyId,
        isFocusedInvoiceNumber,
        setIsFocusedInvoiceNumber,
        isShowSelectedPurchaseNumber,
        openSelectedPurchaseNumberModel,
        closeSelectedPurchaseNumberModel,
        showPurchaseNumber,
        setShowPurchaseNumber,
        setPaymentLedgerDetail,
        setInvoiceValue,
        purchaseInvoice,
        setAdditionalGst,
        setCessValue,
        setAdditionalCharges,
        setIsChangeParty,
        setShowPaymentTable,
        setIsInitialDataLoaded,
        setIsInitialDataLoaded2,
        userPermission,
        setUserPermission,
        setisFieldsChanges,
        setIsShippingAddressModel,
        setNotExistParty,
        setIsChangeShippingAddress,
        saveFeature,
        partyLedgerTrigger,
        setPartyLedgerTrigger
    } = useContext(StateContext);

    const { configuration, sale, transport, ledger, broker, item, shippingAddresses, company } =
        useSelector(selector => selector);

    const {
        partiesOptions,
        brokerOptions,
        transportOptions,
        party_quotesOptions,
        brokerEntityType,
        ledgerDetailOptions,
        ledgerEntityType,
        ledgerGstOption,
        purchaseNumberOptions,
        originalPurchaseInvoiceOption,
        originalInvoiceOption,
        originalDeliveryInvoiceOption,
        brokerageOption
    } = useDropdownOption();

    const dispatch = useDispatch();
    const [isEstimate, setIsEstimate] = useState(1);
    const [typingTimeout, setTypingTimeout] = useState(0);

    const [customer, setCustomer] = useState({
        name: "",
        group_id: "",
        party_details: {
            gstin: null,
            billing_address: {
                address_1: null,
                address_2: null,
                country_id: "",
                state_id: "",
                city_id: null,
                pin_code: null,
            },
            same_as_billing_address: 0,
            shipping_address: {
                shipping_gstin: null,
                shipping_name: null,
                address_1: null,
                address_2: null,
                country_id: "",
                state_id: "",
                city_id: null,
                pin_code: null,
            },
            contact_person_name: null,
            region_iso_1: null,
            region_code_1: null,
            contact_person_phone_1: null,
            region_iso_2: null,
            region_code_2: null,
            contact_person_phone_2: null,
            contact_person_email: null,
        },
        tax_details: [],
        other_details: [],
        opening_balance_details: {
            opening_balance: null,
            opening_balance_dr_cr: null,
        },
        action: isPurchase ? "Supplier" : "Customers",
    });
    const configurationList = configuration?.configuration;

    const getCustomFieldTransactionType = Object.keys(transactionTypeMap).find(path =>
          checkPathName(path)
    ) ? transactionTypeMap[Object.keys(transactionTypeMap).find(path => checkPathName(path))] : "";

    useEffect(() => {
        const user_permission = company?.user_permission;
        const add_broker_master = user_permission?.includes(USER_PERMISSION.ADD_BROKER_MASTER)
        const edit_broker_master = user_permission?.includes(USER_PERMISSION.EDIT_BROKER_MASTER)
        const add_transport_master = user_permission?.includes(USER_PERMISSION.ADD_TRANSPORT_MASTER)
        const edit_transport_master = user_permission?.includes(USER_PERMISSION.EDIT_TRANSPORT_MASTER)
        const add_ledger_master = user_permission?.includes(USER_PERMISSION.ADD_LEDGER)
        const edit_ledger_master = user_permission?.includes(USER_PERMISSION.EDIT_LEDGER)
        const add_new_item_masters = user_permission?.includes(USER_PERMISSION.ADD_ITEM_MASTER)
        const edit_item_masters = user_permission?.includes(USER_PERMISSION.EDIT_ITEM_MASTER)

        setUserPermission({
            ...userPermission,
            add_broker_master,
            edit_broker_master,
            add_transport_master,
            edit_transport_master,
            add_ledger_master,
            edit_ledger_master,
            add_new_item_masters,
            edit_item_masters
        })
    }, [company?.user_permission]);

    useEffect(() => {
        if (
            configurationList?.document_prefix?.method_of_voucher_number == 2 &&
            !id & !estimateId & !challanId
        ) {
            $("#datePickerInvoiceDate").trigger("click");
            $("#datePickerChallanDate").trigger("click");
        }
        if (invoiceRef?.current) {
            invoiceRef.current.focus();
        }
        if (deliveryRef?.current) {
            deliveryRef.current.focus();
        }
        if (expenseCnDnRef?.current) {
            expenseCnDnRef.current.focus();
        }
        if (purchaseReturnRef?.current) {
            purchaseReturnRef.current.focus();
        }
    }, [
        invoiceRef,
        deliveryRef,
        expenseCnDnRef,
        purchaseReturnRef,
        configurationList?.document_prefix?.suffix,
    ]);

    useTransactionShortcuts(pathname);

    useEffect(() => {
        dispatch(getLedgerModelDetail(LedgerType.PARTY_LEDGER));
    }, []);

    useEffect(() => {
        const ledger_group_detail = ledger?.ledgerGroupDetail;
        const matchedGroupName = isPurchase ? "Supplier" : "Customers";
        if (ledger_group_detail) {
            const group_id = Object.keys(ledger_group_detail).find(
                key => ledger_group_detail[key] === matchedGroupName
            );
            setCustomer(prev => ({ ...prev, group_id }));
        }
        if (company?.company) {
            const billing_address = company?.company?.billing_address;
            setCustomer(prev => ({
                ...prev,
                party_details: {
                    ...prev.party_details,
                    billing_address: {
                        ...prev.party_details.billing_address,
                        country_id: billing_address?.country_id,
                        state_id: billing_address?.state_id,
                    },
                    shipping_address: {
                        ...prev.party_details.shipping_address,
                        country_id: billing_address?.country_id,
                        state_id: billing_address?.state_id,
                    },
                },
            }));
        }
    }, [ledger?.ledgerGroupDetail, company?.company?.billing_address]);

    const handleChangeParty = e => {
        setisFieldsChanges(true);
        setPartyLedgerTrigger(prev => prev + 1);
        setShowPaymentTable(false);
        // cooment : when select party to first time 0 index data and after show actual data
        // setSelectShippingAddress(0);
        setGSTError("");
        setIsEditCalculation(true);
        setIsCheckGstType(true);
        if (id) {
            setIsChangePartyId(true);
            setIsChangeParty(true);
        }
        if (e.value) {
            setIsChangePartyId(true);
            setIsChangeParty(true);
        }
        if (e.__isNew__) {
            dispatch(
                addLedger(
                    isPurchase,
                    {
                        ...customer,
                        party_details: {
                            ...customer?.party_details,
                            billing_address: {
                                country_id: company?.company?.billing_address?.country_id,
                                state_id: company?.company?.billing_address?.state_id,
                            },
                            shipping_address: {
                                country_id: company?.company?.billing_address?.country_id,
                                state_id: company?.company?.billing_address?.state_id,
                            },
                        },
                        name: e.label,
                    },
                    null,
                    null,
                    setGstQuote
                )
            );
        } else {
            setGstQuote({
                ...gstQuote,
                party_ledger_id: e.value,
                quotes_id: [],
                gstin:
                    id && !gstQuote.gstin
                        ? e.value == gstQuote.party_ledger_id
                            ? e.details ?? ""
                            : e.details ?? ""
                        : e.details ?? "",
                purchase_number: "",
                original_inv_date: "",
                original_inv_no: "",
            });
        }
        if (e.value === null) {
            dispatch(fetchPartyDetail(""));
            setIsChangePartyId(false);
            setIsChangeParty(false);
            setGstQuote({
                ...gstQuote,
                gstin: "",
                party_ledger_id: "",
                quotes_id: [],
                original_inv_no: "",
                original_inv_date: "",
                valid_for: "",
                valid_for_type: 1,
                purchase_number: "",
                mobile: {
                    region_iso: "in",
                    region_code: "+91",
                    party_phone_number: "+91",
                    phone_input: "+91",
                },
            });
        } else {
            if (e.value && !e.__isNew__) {
                dispatch(fetchPartyDetail(e.value));
                dispatch(fetchPartyIdBaseItemList(e.value))
                const ids = items.map(item => item.selectedItem).filter(Boolean);
                if (ids.length > 0 && !isPurchase && !id) {
                    dispatch(
                        fetchPriceListOfItems(
                            ids,
                            e.value,
                            setShowItemPriceChangedModal,
                            setIsConfirmed,
                            getCustomFieldTransactionType
                        )
                    );
                }
                setInvoiceNumber("");
            }
        }
        setIsCreateParty(e.__isNew__ ? true : false);
        setIsChangeParty(false);
    };

    useEffect(() => {
        if (sameAsBill) {
            setPartyAddress({
                ...partyAddress,
                shippingCopy: {
                    party_name: partyAddress.billingAddress.party_name,
                    gstin: partyAddress.billingAddress.gstin,
                    address_1: partyAddress.billingAddress.address_1,
                    address_2: partyAddress.billingAddress.address_2,
                    country_id: partyAddress.billingAddress.country_id,
                    state_id: partyAddress.billingAddress.state_id,
                    city_id: partyAddress.billingAddress.city_id,
                },
            });
        }
    }, [sameAsBill]);

    useEffect(() => {
        const billing_address = ledger?.partyDetail?.billingAddress;
        const selectshpping = shippingAddresses?.shippingAddresses;
        const party_detail = ledger?.partyDetail?.customerDetail;
        const shipping_address_id = ledger?.partyDetail?.customerDetail?.is_selected_address_id;
        const same_as_billing_address = ledger?.partyDetail?.customerDetail?.same_as_billing_address;
        const findShippingAddressIndex = selectshpping && selectshpping?.findIndex(
            ship => ship.id == shipping_address_id
        );
        const shipping_address =
        selectshpping?.length > 0 && selectshpping[findShippingAddressIndex > -1 ? findShippingAddressIndex : 0];
        const customer_detail = ledger?.partyDetail;
        if(customer_detail){
            setGstQuote({
                ...gstQuote,
                closing_balance: customer_detail?.closing_balance
                    ? customer_detail?.closing_balance
                    : 0,
            });
            if (id && isChangePartyId) {
                setGstQuote({
                    ...gstQuote,
                    gstin: customer_detail?.customerDetail?.gstin ?? "",
                    closing_balance: customer_detail?.closing_balance
                        ? customer_detail?.closing_balance
                        : 0,
                });
            } else if (isChangePartyId) {
                setGstQuote({
                    ...gstQuote,
                    gstin: customer_detail?.customerDetail?.gstin ?? "",
                    closing_balance: customer_detail?.closing_balance
                        ? customer_detail?.closing_balance
                        : 0,
                });
            }
        }
        const billing = {
            address_1: billing_address?.address_1 || "",
            address_2: billing_address?.address_2 || "",
            country_id: billing_address?.country_id || "",
            state_id: billing_address?.state_id || "",
            city_id: billing_address?.city_id || "",
            pin_code: billing_address?.pin_code || "",
            state_name: billing_address?.state_name || "",
            city_name: billing_address?.city_name || "",
        };
        const shipping = {
            same_as_billing_address: party_detail?.same_as_billing_address || "",
            shipping_address_id: party_detail?.same_as_billing_address ? party_detail?.is_selected_address_id : shipping_address?.id || "",
            shipping_gstin: shipping_address?.shipping_gstin || "",
            shipping_name: shipping_address?.shipping_name || "",
            address_1: shipping_address?.address_1 || "",
            address_2: shipping_address?.address_2 || "",
            country_id: shipping_address?.country_id || "",
            state_id: shipping_address?.state_id || "",
            city_id: shipping_address?.city_id || "",
            state_name: shipping_address?.state_name || "",
            city_name: shipping_address?.city_name || "",
            pin_code: shipping_address?.pin_code || "",
        };
        if (id && isChangePartyId) {
            setPartyAddress({
                billingAddress: billing,
                shippingAddress: shipping,
            });
        } else if (!id && isChangePartyId) {
            setPartyAddress({
                billingAddress: billing,
                shippingAddress: shipping,
            });
        }
        if (id && isChangePartyId && customer_detail) {
            setGstQuote({
                ...gstQuote,
                closing_balance: customer_detail?.closing_balance
                    ? customer_detail?.closing_balance
                    : 0,
                mobile: {
                    region_iso:
                        customer_detail?.customerDetail?.region_iso_1 ||
                        customer_detail?.customerDetail?.region_iso_2 ||
                        "in",
                    region_code:
                        customer_detail?.customerDetail?.region_code_1 ||
                        customer_detail?.customerDetail?.region_code_2 ||
                        "+91",
                    party_phone_number:
                        customer_detail?.customerDetail?.phone_1 ||
                        customer_detail?.customerDetail?.phone_2,
                    phone_input:
                        "+" +
                        (customer_detail?.customerDetail?.region_code_1 ||
                            customer_detail?.customerDetail?.region_code_2 ||
                            "91") +
                        (customer_detail?.customerDetail?.phone_1 ||
                            customer_detail?.customerDetail?.phone_2),
                },
            });
        } else if (!id && gstQuote.party_ledger_id && customer_detail) {
            setGstQuote({
                ...gstQuote,
                closing_balance: customer_detail?.closing_balance
                    ? customer_detail?.closing_balance
                    : 0,
                mobile: {
                    region_iso:
                        customer_detail?.customerDetail?.region_iso_1 ||
                        customer_detail?.customerDetail?.region_iso_2 ||
                        "in",
                    region_code:
                        customer_detail?.customerDetail?.region_code_1 ||
                        customer_detail?.customerDetail?.region_code_2 ||
                        "+91",
                    party_phone_number:
                        customer_detail?.customerDetail?.phone_1 ||
                        customer_detail?.customerDetail?.phone_2,
                    phone_input:
                        "+" +
                        (customer_detail?.customerDetail?.region_code_1 ||
                            customer_detail?.customerDetail?.region_code_2 ||
                            "91") +
                        (customer_detail?.customerDetail?.phone_1 ||
                            customer_detail?.customerDetail?.phone_2),
                },
            });
        }

        const isId = id || true;
        if (isId && isChangePartyId) {
            setBrokerDetail({
                ...brokerDetail,
                broker_id: customer_detail?.customerDetail?.broker_master || "",
                broker_percentage: customer_detail?.customerDetail?.brokerage || "",
                brokerage_on_value: customer_detail?.customerDetail?.brokerage_on_value || "",
            });
            setTransporterDetail({
                ...transporterDetail,
                transport_id: customer_detail?.customerDetail?.transporter_id,
            });
            setOtherDetail({
                ...otherDetail,
                credit_limit: customer_detail?.customerDetail?.credit_limit,
                credit_limit_amount: customer_detail?.customerDetail?.credit_limit_amount,
                creditPeriod:
                    parseFloat(customer_detail?.customerDetail?.credit_limit_period) || null,
                creditPeriodType: customer_detail?.customerDetail?.credit_period_type || 1,
            });
            setSameAsBill(same_as_billing_address);
        } else if (!id && estimateId && isChangePartyId) {
            setBrokerDetail({
                ...brokerDetail,
                broker_id: customer_detail?.customerDetail?.broker_master || "",
                broker_percentage: customer_detail?.customerDetail?.brokerage || "",
                brokerage_on_value: customer_detail?.customerDetail?.brokerage_on_value || "",
            });
            setTransporterDetail({
                ...transporterDetail,
                transport_id: customer_detail?.customerDetail?.transporter_id,
            });
            setOtherDetail({
                ...otherDetail,
                credit_limit: customer_detail?.customerDetail?.credit_limit,
                credit_limit_amount: customer_detail?.customerDetail?.credit_limit_amount,
                creditPeriod:
                    parseFloat(customer_detail?.customerDetail?.credit_limit_period) || null,
                creditPeriodType: customer_detail?.customerDetail?.credit_period_type || 1,
            });
            // }
            setIsChangePartyId(false);
            setIsChangeParty(false);
            setSameAsBill(same_as_billing_address);
        } else if (
            !id &&
            !estimateId &&
            !isDuplicate &&
            !saleInvoiceId &&
            !isPurchaseToPurchaseReturn &&
            !purchaseOrderId &&
            !challanId
        ) {

            setBrokerDetail({
                ...brokerDetail,
                broker_id: customer_detail?.customerDetail?.broker_master || "",
                broker_percentage: customer_detail?.customerDetail?.brokerage || "",
                brokerage_on_value: customer_detail?.customerDetail?.brokerage_on_value || "",
            });

            setTransporterDetail({
                ...transporterDetail,
                transport_id: customer_detail?.customerDetail?.transporter_id,
            });

            setOtherDetail({
                ...otherDetail,
                credit_limit: customer_detail?.customerDetail?.credit_limit,
                credit_limit_amount: customer_detail?.customerDetail?.credit_limit_amount,
                creditPeriod:
                    parseFloat(customer_detail?.customerDetail?.credit_limit_period) || null,
                creditPeriodType: customer_detail?.customerDetail?.credit_period_type || 1,
            });

            setIsChangePartyId(false);
            setIsChangeParty(false);
        } else if (id && isChangePartyId) {
            setBrokerDetail({
                ...brokerDetail,
                broker_id: customer_detail?.customerDetail?.broker_master || "",
                broker_percentage: customer_detail?.customerDetail?.brokerage || "",
                brokerage_on_value: customer_detail?.customerDetail?.brokerage_on_value || "",
            });
            setTransporterDetail({
                ...transporterDetail,
                transport_id: customer_detail?.customerDetail?.transporter_id,
            });
            setOtherDetail({
                ...otherDetail,
                credit_limit: customer_detail?.customerDetail?.credit_limit,
                credit_limit_amount: customer_detail?.customerDetail?.credit_limit_amount,
                creditPeriod:
                    parseFloat(customer_detail?.customerDetail?.credit_limit_period) || null,
                creditPeriodType: customer_detail?.customerDetail?.credit_period_type || 1,
            });
            setIsChangePartyId(false);
            setIsChangeParty(false);
            setSameAsBill(same_as_billing_address);
        }
    }, [ledger?.partyDetail, gstQuote?.party_ledger_id]);

    const handleChangeDate = useCallback(
        (date, name) => {
            const isValidDate = date && !isNaN(new Date(date).getTime());
            const formattedDateValue = isValidDate ? formattedDate(new Date(date)) : "";

            if (name === "transport") {
                setTransporterDetail({
                    ...transporterDetail,
                    transporter_document_date: formattedDateValue,
                });
            } else if (name === "invoice") {
                setInvoiceDetail({
                    ...invoiceDetail,
                    invoice_date: formattedDateValue,
                });
                // Added by Ridham For set first payment details Date.
                setPaymentLedgerDetail(prevState => ({
                    ...prevState,
                    payment_detail: prevState.payment_detail.map((detail, index) =>
                        index === 0
                            ? {
                                  ...detail,
                                  pd_date: detail.pd_date
                                      ? detail.pd_date
                                      : isPurchase
                                      ? purchaseInvoice?.voucher_date ?? formattedDateValue
                                      : formattedDateValue,
                              }
                            : detail
                    ),
                }));
            } else if (name === "gstInvoice") {
                setGstQuote({
                    ...gstQuote,
                    original_inv_date: formattedDateValue,
                });
            } else if (name === "eway") {
                setEwayBillDetail({
                    ...ewayBillDetail,
                    eway_bill_date: formattedDateValue,
                });
            } else if (name === "party") {
                setGstQuote({
                    ...gstQuote,
                    original_inv_date: formattedDateValue,
                });
            } else if (name === "other") {
                setOtherDetail({
                    ...otherDetail,
                    date: formattedDateValue,
                });
            } else if (name === "challan") {
                setInvoiceDetail({
                    ...invoiceDetail,
                    challan_date: formattedDateValue,
                });
            }
        },
        [transporterDetail, invoiceDetail, ewayBillDetail, otherDetail]
    );

    const handleOpenShippingAddressModel = () => {
        openShippingFromModel();
        setIsShippingAddressModel(true)
    }

    const handleOpenLedger = useCallback(
        id => {
            dispatch(fetchLedgerGroupList());
            openLedgerModel();
            if (id) {
                setPartyTitle({
                    name: ledgerModalEditName ?? "Update Ledger",
                    id: id,
                });
                dispatch(fetchLedgerById(id));
            } else {
                setPartyTitle({
                    name: ledgerModalName ?? "Add Ledger",
                    id: "",
                });
            }
            dispatch(getLedgerModelDetail(LedgerType.PARTY_LEDGER));
            setCheckGroupLedgerType("party");
            setIsShippingAddressModel(false)
        },
        [modalType]
    );

    const handleOpenPartyAddress = name => {
        openDispatchAddressModel();
        setDispatchAddressName(name);
        if(partyAddress?.billingAddress?.country_id !== 101){
            setIsInitialDataLoaded(false)
            setIsInitialDataLoaded2(false)
        }
    };

    const changeEstimateChallan = (e, type) => {

        setisFieldsChanges(true);

        const selectedInvoice = e
            .filter(item => item.value !== null && item.value !== undefined)
            .map(item => item.value);

        const selectedInvoiceWithLabel = e
            .filter(item => item.value !== null && item.value !== undefined)
            .map(item => item);

        setShowInvoice(true);
        if (type == 1) {
            dispatch(estimateForSale(selectedInvoice, "", "", "", id));
            setIsEstimate(1);
        } else {
            dispatch(challanForSale(selectedInvoice, "", "", "", id));
            setIsEstimate(2);
        }
        setIsEditCalculation(false);
        setGstQuote({
            ...gstQuote,
            quotes_id: selectedInvoiceWithLabel,
        });
    };

    useEffect(() => {
        if (gstQuote?.quotes_id?.length > 1 && showInvoice) {
            openSelectedServiceModel();
        }
    }, [gstQuote.quotes_id]);

    useEffect(() => {
        if (gstQuote?.purchase_number?.length > 1 && showPurchaseNumber) {
            openSelectedPurchaseNumberModel();
        }
    }, [gstQuote.purchase_number]);

    const onChangePurchaseInvoiceNumber = (e, type) => {

        setisFieldsChanges(true);

        setInvoiceNumber(e.value);
        dispatch(fetchShippingAddressList(parseFloat(gstQuote.party_ledger_id), type ? SHIPPING_ADDRESS_TYPE_LIST.SALE : SHIPPING_ADDRESS_TYPE_LIST.PURCHASE, e.value));
        if (e.value) {
            if (type) {
                dispatch(fetchSaleById(e.value));
            } else {
                // dispatch(fetchPurchaseFromInvoiceNumber(e.value));
                dispatch(fetchPurchaseById(e.value));
            }
        } else {
            clearData({
                setStateFunctions: {
                    setOtherDetail,
                    setTransporterDetail,
                    setBrokerDetail,
                    setItems,
                    setTaxableValue,
                    setMainGrandTotal,
                    setFinalAmount,
                    setGrandTotal,
                    setGstValue,
                    gstCalculation,
                    setGstCalculation,
                },
            });
        }
    };

    const handlePhoneChange = (value, country) => {
        const valueStr = value.toString();
        const dialCodeStr = country.dialCode.toString();
        let number = "";
        if (valueStr.startsWith(dialCodeStr)) {
            number = valueStr.slice(dialCodeStr.length);
        }
        setGstQuote({
            ...gstQuote,
            mobile: {
                region_iso: country.countryCode,
                region_code: country.dialCode,
                party_phone_number: number,
                phone_input: value,
            },
        });
    };

    const brokerPercentage = e => {
        if (e.target.value > 100) {
            return;
        }
        setBrokerDetail({
            ...brokerDetail,
            broker_percentage: e.target.value,
        });
    };

    const handleOpenTransportModal = id => {
        openTransportModel();
        if (id !== "") {
            setTransportTitle("edit");
            dispatch(fetchTransportById(id));
        } else {
            setTransportTitle("");
        }
    };

    const CreditType = e => {
        setOtherDetail({ ...otherDetail, creditPeriodType: e.target.value });
    };

    const validForTypeChange = e => {
        setisFieldsChanges(true);
        setGstQuote({ ...gstQuote, valid_for_type: e.target.value });
    };

    const changePurchaseNumber = e => {

        setisFieldsChanges(true);

        const selectedInvoice = e
            .filter(item => item.value !== null && item.value !== undefined)
            .map(item => item.value);

        const selectedInvoiceWithLabel = e
            .filter(item => item.value !== null && item.value !== undefined)
            .map(item => item);
        setShowPurchaseNumber(true);
        if (selectedInvoice.length > 0) {
            setGstQuote({ ...gstQuote, purchase_number: selectedInvoiceWithLabel });
            dispatch(getPurchaseOrderFromMultipleNumber(selectedInvoice, "", "", "", id));
        } else {
            setGstQuote({ ...gstQuote, purchase_number: "" });
            setGstValue({ ...gstValue, sgstValue: 0.0, cgstValue: 0.0, igstValue: 0.0 });
            setAdditionalGst(0);
            setCessValue(0);
            setAdditionalCharges({
                note: "",
                terms_and_conditions: "",
                upload_document: "",
                additional_detail: [
                    {
                        ac_ledger_id: null,
                        ac_type: 1,
                        ac_value: null,
                        ac_gst_rate_id: {
                            label: "",
                            value: 0,
                            rate: 0,
                        },
                        ac_total: 0,
                        is_status: ITEM_STATUS.IN_ACTIVE,
                    },
                ],
            });
            clearData({
                setStateFunctions: {
                    setOtherDetail,
                    setTransporterDetail,
                    setBrokerDetail,
                    setItems,
                    setTaxableValue,
                    setMainGrandTotal,
                    setFinalAmount,
                    setGrandTotal,
                    setGstValue,
                    gstCalculation,
                    setGstCalculation,
                },
            });
        }
    };

    const BrokerModel = id => {
        openBrokerModel();
        dispatch(getBrokerModelDetail());
        if (id !== "") {
            setModalType("edit");
            dispatch(fetchBrokerById(id));
        } else {
            setModalType("");
        }
    };

    const handleBrokerChange = e => {
        if (e.value) {

            setisFieldsChanges(true);
            setBrokerDetail({
                ...brokerDetail,
                broker_id: e.value,
            });
            dispatch(fetchBrokerDetailsById(e.value));
        } else {
            setBrokerDetail({
                ...brokerDetail,
                broker_id: null,
                broker_percentage: "",
                brokerage_on_value: "",
            });
        }
    };

    useEffect(() => {
        if (shippingAddresses?.shippingAddresses?.length > 0) {
            const shippingList = shippingAddresses?.shippingAddresses;
            setShippingAddress(shippingList);
            setIsChangeShippingAddress(prev => prev + 1);
        }
    }, [shippingAddresses?.shippingAddresses]);

    useEffect(() => {
            const shipping_address_id = partyAddress?.shippingAddress?.shipping_address_id;
            const sameAsBilling = partyAddress?.shippingAddress?.same_as_billing_address;

            if (shipping_address_id && !sameAsBilling && shippingAddresses?.shippingAddresses?.length) {
                const addresses = shippingAddresses.shippingAddresses;
                const index = addresses.findIndex(addr => addr.id === shipping_address_id);
                // Set the correct index
                if(index !== -1){
                    setSelectShippingAddress(index > -1 ? index : !saveFeature ? 0 : "");
                }

                // Sync partyAddress state with shipping address
                const shipping_address = addresses[index > -1 ? index : 0];
                if (!sameAsBill && shipping_address && index !== -1) {
                    setPartyAddress(prev => ({
                        ...prev,
                        shippingAddress: {
                            shipping_address_id: shipping_address?.id,
                            address_1: shipping_address?.address_1,
                            address_2: shipping_address?.address_2,
                            country_id: shipping_address?.country_id,
                            state_id: shipping_address?.state_id,
                            city_id: shipping_address?.city_id,
                            pin_code: shipping_address?.pin_code,
                            shipping_gstin: shipping_address?.shipping_gstin || "",
                            shipping_name: shipping_address?.shipping_name || "",
                            same_as_billing_address: false,
                        },
                    }));
                }
            }

            // Same as billing address scenario
            if (sameAsBill) {
                setPartyAddress(prev => ({
                    ...prev,
                    shippingAddress: {
                        ...prev.shippingAddress,
                        address_1: prev.billingAddress?.address_1,
                        address_2: prev.billingAddress?.address_2,
                        country_id: prev.billingAddress?.country_id,
                        state_id: prev.billingAddress?.state_id,
                        city_id: prev.billingAddress?.city_id,
                        pin_code: prev.billingAddress?.pin_code,
                        shipping_gstin: prev.billingAddress?.shipping_gstin || "",
                        shipping_name: prev.billingAddress?.shipping_name || "",
                        same_as_billing_address: true,
                    },
                }));
            }
        }, [
            partyAddress?.shippingAddress?.shipping_address_id,
            shippingAddresses?.shippingAddresses,
            sameAsBill,
        ]);

    useEffect(() => {
        if (gstQuote.party_ledger_id) {
            dispatch(fetchShippingAddressList(parseFloat(gstQuote.party_ledger_id), shipping_address_type, (id || otherId || estimateId || challanId)));
            if (tcsRate?.tcs_tax_id) {
                const newInvoiceValue = isIGSTCalculation
                    ? parseFloat(taxableValue || 0) +
                      parseFloat(cessValue || 0) +
                      parseFloat(gstValue?.igstValue || 0)
                    : parseFloat(taxableValue || 0) +
                      parseFloat(cessValue || 0) +
                      parseFloat(gstValue?.sgstValue || 0) +
                      parseFloat(gstValue?.cgstValue || 0);
                setInvoiceValue(newInvoiceValue);
                if (isChangedTcs) {
                    dispatch(
                        fetchTdsTcsRate(
                            tcsRate?.tcs_tax_id,
                            gstQuote?.party_ledger_id,
                            setTcsRate,
                            taxableValue,
                            newInvoiceValue
                        )
                    );
                }
            }
        }else{
            setTimeout(() => {
                dispatch(getShippingAddressList(""))
            }, 1000);
            setSelectShippingAddress("")
        }
    }, [partyLedgerTrigger, gstQuote?.party_ledger_id]);

    const handleScroll = () => {
        dispatch(fetchPartyList({ skip: partiesOptions.length }));
    };

    const customFilter = searchText => {
        if (!searchText) {
            return;
        }
        if (typingTimeout) {
            clearTimeout(typingTimeout);
        }
        setTypingTimeout(
            setTimeout(() => dispatch(fetchPartyList({ search: searchText })), 500)
        );
    };

    const changeGstNumber = e => {
        const { value } = e.target;
        const upperValue = value.trim().toUpperCase();
        setGstQuote({
            ...gstQuote,
            gstin: upperValue,
        });

        if (upperValue.length === 15 && CheckGstValidate(upperValue)) {
            setGSTError(""); // Clear error on valid GSTIN
        } else if (upperValue.length > 0 && upperValue.length !== 15) {
            setGSTError("GSTIN must be exactly 15 characters long");
        } else if (upperValue.length === 0) {
            setGSTError(""); // Clear error if input is cleared
        } else {
            setGSTError("Please enter a valid GSTIN");
        }
    };

    const changeOriginalInvoice = e => {
        setIsEditCalculation(true);
        setGstQuote({
            ...gstQuote,
            original_inv_no: e.value ?? "",
        });
        if (e.value) {
            dispatch(fetchPurchaseById(e.value));
        } else {
            clearData({
                setStateFunctions: {
                    setOtherDetail,
                    setTransporterDetail,
                    setBrokerDetail,
                    setItems,
                    setTaxableValue,
                    setMainGrandTotal,
                    setFinalAmount,
                    setGrandTotal,
                    setGstValue,
                    gstCalculation,
                    setGstCalculation,
                },
            });
        }
    };

    const createNotExistsParty = useCallback((isPartyNameExist) => {
            dispatch(fetchLedgerGroupList());
            openLedgerModel();
            if (id) {
                setPartyTitle({
                    name: ledgerModalEditName ?? "Update Ledger",
                    id: id,
                });
                dispatch(fetchLedgerById(id));
            } else {
                setPartyTitle({
                    name: ledgerModalName ?? "Add Ledger",
                    id: "",
                });
            }
            setNotExistParty(isPartyNameExist)
            dispatch(getLedgerModelDetail(LedgerType.PARTY_LEDGER));
            setCheckGroupLedgerType("party");
            setIsShippingAddressModel(false)
        },
        [modalType]
    );

    return (
        <div>
            {isOcr ?
            <>
                <h4>Transaction Details</h4>
                <Row className="justify-content-start mb-2 mx-xl-0">
                    <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mt-4 me-xl-8">
                        <ReactSelect
                            defaultValue={1}
                            placeholder="Transaction type"
                            options={[{ label: "Purchase", value: "1" }]}
                        />
                    </Col>
                </Row>
                </>
                 :
            ""}
            {isShowChallanPageComponent ? (
                <Row className="justify-content-start mb-2 mx-xl-0">
                    {id && (
                        <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mt-6  mb-2 me-xl-8">
                            <Form.Group className="position-relative form-floating-group">
                                <FormInput
                                    className="floating-label-input"
                                    type="text"
                                    placeholder=""
                                    value={invoiceDetail?.challan_number}
                                    onChange={e =>
                                        setInvoiceDetail({
                                            ...invoiceDetail,
                                            challan_number: e.target.value,
                                        })
                                    }
                                    ref={deliveryRef}
                                    // disabled={
                                    //     configurationList?.document_prefix
                                    //         ?.method_of_voucher_number == 2
                                    // }
                                />
                                <Form.Label>Challan Number</Form.Label>
                            </Form.Group>
                        </Col>
                    )}
                    {!id &&
                    !configurationList?.document_prefix?.suffix &&
                    !configurationList?.document_prefix?.prefix ? (
                        <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-2 mt-6 me-xl-8">
                            <Form.Group className="position-relative form-floating-group">
                                <Form.Control
                                    ref={deliveryRef}
                                    className="floating-label-input"
                                    type="text"
                                    required
                                    placeholder=""
                                    disabled={
                                        !isShowPurchaseInvoiceDetails &&
                                        configurationList?.document_prefix
                                            ?.method_of_voucher_number == 2
                                    }
                                    value={invoiceDetail?.challan_number}
                                    onChange={e =>
                                        setInvoiceDetail({
                                            ...invoiceDetail,
                                            challan_number: e.target.value,
                                        })
                                    }
                                />
                                <Form.Label className="required">Challan Number</Form.Label>
                            </Form.Group>
                        </Col>
                    ) : (
                        !id && (
                            <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 me-xl-8 mt-6">
                                <div
                                    className={`d-flex form-control  p-0 ${
                                        isFocusedInvoiceNumber ? "shadow" : ""
                                    }`}
                                    style={{ maxHeight: "34px" }}
                                >
                                    {configurationList?.document_prefix?.prefix !== null && (
                                        <div className="start-date bg-light border-right-primary p-2 w-fit-content">
                                            {configurationList?.document_prefix?.prefix}
                                        </div>
                                    )}
                                    <Form.Group className="position-relative w-100 form-floating-group">
                                        <FormInput
                                            ref={deliveryRef}
                                            className="floating-label-input form-control px-2 border-0 shadow-none"
                                            type="text"
                                            required
                                            placeholder=""
                                            value={invoiceDetail?.challan_number}
                                            onChange={e =>
                                                setInvoiceDetail({
                                                    ...invoiceDetail,
                                                    challan_number: e.target.value,
                                                })
                                            }
                                            disabled={
                                                configurationList?.document_prefix
                                                    ?.method_of_voucher_number == 2
                                            }
                                            onFocus={() => setIsFocusedInvoiceNumber(true)}
                                            onBlur={() => setIsFocusedInvoiceNumber(false)}
                                        />
                                        <Form.Label
                                            className="required"
                                            style={{ maxWidth: "calc(100% - 15px)" }}
                                        >
                                            {" "}
                                            Number
                                        </Form.Label>
                                    </Form.Group>
                                    {configurationList?.document_prefix?.suffix && (
                                        <div className="end-date border-left-primary p-2 bg-light w-fit-content">
                                            {configurationList?.document_prefix?.suffix}
                                        </div>
                                    )}
                                </div>
                            </Col>
                        )
                    )}
                    <Col xl={2} md={4} sm={6} className="px-xl-0 mt-6  mb-2 me-xl-8">
                        <Form.Group>
                            <Datepicker
                                value={invoiceDetail?.challan_date}
                                required={true}
                                placeholder={"Challan Date"}
                                onChange={e => handleChangeDate(e, "challan")}
                                options={true}
                                domId={"datePickerChallanDate"}
                            />
                        </Form.Group>
                    </Col>
                </Row>
            ) : (
                ""
            )}

            { isRecurring && (
                <RecurringInvoiceDetails partyDetails={partyDetails} items={items} setItems={setItems} setAdditionalCharges={setAdditionalCharges} />
            )}

            <Row className="justify-content-start mb-2 mx-xl-0">
                {isShowEstimateInvoice ? (
                    <EstimateQuoteOtherDetails
                        configurationList={configurationList}
                        id={id}
                        estimateRef={estimateRef}
                        isFocusedInvoiceNumber={isFocusedInvoiceNumber}
                        setIsFocusedInvoiceNumber={setIsFocusedInvoiceNumber}
                    />
                ) : (
                    ""
                )}
                {isShowPurchaseOrder ? (
                    <PurchaseOrderOtherDetail
                        configurationList={configurationList}
                        id={id}
                        purchaseOrderRef={purchaseOrderRef}
                        isFocusedInvoiceNumber={isFocusedInvoiceNumber}
                        setIsFocusedInvoiceNumber={setIsFocusedInvoiceNumber}
                    />
                ) : (
                    ""
                )}
                {isShowPurchaseInvoiceDetails ? (
                    <PurchaseInvoiceDetails
                        configurationList={configurationList}
                        purchaseReturnRef={purchaseReturnRef}
                        isOcr={isOcr}
                    />
                ) : (
                    ""
                )}
                {isShowExpenseCreateOrDebit ? (
                    <ExpenseCnDnDetail
                        ExpenseCreateOrDebit={ExpenseCreateOrDebit}
                        expenseCnDnRef={expenseCnDnRef}
                        configurationList={configurationList}
                    />
                ) : (
                    ""
                )}
                {isShowInvoiceDetails ? (
                    <>
                        {!isOcr && (
                            <Col sm={12} className="px-xl-0">
                                <h5 className="mb-0">Invoice Details</h5>
                            </Col>
                        )}
                        {id && (
                            <Col
                                xl={2}
                                lg={3}
                                md={4}
                                sm={6}
                                className="px-xl-0 mb-2 mt-4 me-xl-8"
                            >
                                <Form.Group className="position-relative form-floating-group">
                                    <Form.Control
                                        ref={invoiceRef}
                                        className="floating-label-input"
                                        type="text"
                                        required
                                        name="invoice_number"
                                        placeholder=""
                                        // disabled={
                                        //     !isShowPurchaseInvoiceDetails &&
                                        //     configurationList?.document_prefix
                                        //         ?.method_of_voucher_number == 2
                                        // }
                                        value={invoiceDetail.invoice_number}
                                        onChange={e =>
                                            setInvoiceDetail({
                                                ...invoiceDetail,
                                                invoice_number: e.target.value,
                                            })
                                        }
                                    />
                                    <Form.Label className="required">Invoice Number</Form.Label>
                                </Form.Group>
                            </Col>
                        )}
                        {!id &&
                        ((!configurationList?.document_prefix?.suffix &&
                            !configurationList?.document_prefix?.prefix) ||
                            isShowPurchaseNumber) ? (
                            <Col
                                xl={2}
                                lg={3}
                                md={4}
                                sm={6}
                                className="px-xl-0 mb-2 mt-6 me-xl-8"
                            >
                                <Form.Group className="position-relative form-floating-group">
                                    <Form.Control
                                        ref={invoiceRef}
                                        className="floating-label-input border-radius-0"
                                        type="text"
                                        required
                                        name="invoice_number"
                                        placeholder=""
                                        disabled={
                                            !isShowPurchaseInvoiceDetails &&
                                            configurationList?.document_prefix
                                                ?.method_of_voucher_number == 2
                                        }
                                        value={invoiceDetail.invoice_number}
                                        onChange={e =>
                                            setInvoiceDetail({
                                                ...invoiceDetail,
                                                invoice_number: e.target.value,
                                            })
                                        }
                                    />
                                    <Form.Label className="required">Invoice Number</Form.Label>
                                </Form.Group>
                            </Col>
                        ) : (
                            !id && (
                                <Col
                                    xl={2}
                                    lg={3}
                                    md={4}
                                    sm={6}
                                    className="px-xl-0 me-xl-8 mt-6"
                                >
                                    <div
                                        className={`d-flex align-items-center form-control p-0 ${
                                            isFocusedInvoiceNumber ? "shadow border-light" : ""
                                        }`}
                                        style={{ maxHeight: "34px" }}
                                    >
                                        {configurationList?.document_prefix?.prefix !== null && (
                                            <div className="start-date bg-light border-right-primary p-2 w-fit-content">
                                                {configurationList?.document_prefix?.prefix}
                                            </div>
                                        )}
                                        <Form.Group className="position-relative w-100 form-floating-group">
                                            <FormInput
                                                ref={invoiceRef}
                                                className="floating-label-input form-control px-2 border-0 shadow-none"
                                                placeholder=""
                                                type="text"
                                                value={invoiceDetail.invoice_number}
                                                onChange={e =>
                                                    setInvoiceDetail({
                                                        ...invoiceDetail,
                                                        invoice_number: e.target.value,
                                                    })
                                                }
                                                onFocus={() => setIsFocusedInvoiceNumber(true)}
                                                onBlur={() => setIsFocusedInvoiceNumber(false)}
                                                disabled={
                                                    configurationList?.document_prefix
                                                        ?.method_of_voucher_number == 2
                                                }
                                            />
                                            <Form.Label
                                                style={{
                                                    maxWidth: "calc(100% - 20px)",
                                                    top: "5px",
                                                }}
                                            >
                                                Invoice Number
                                                <span
                                                    style={{
                                                        color: "red",
                                                        paddingLeft: "4px",
                                                    }}
                                                >
                                                    *
                                                </span>
                                            </Form.Label>
                                        </Form.Group>
                                        {configurationList?.document_prefix?.suffix && (
                                            <div className="end-date border-left-primary p-2 bg-light w-fit-content">
                                                {configurationList?.document_prefix?.suffix}
                                            </div>
                                        )}
                                    </div>
                                </Col>
                            )
                        )}
                        <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mt-6  mb-2 me-xl-8">
                            <Form.Group>
                                <Datepicker
                                    value={invoiceDetail?.invoice_date}
                                    required={true}
                                    placeholder={"Invoice Date"}
                                    onChange={e => handleChangeDate(e, "invoice")}
                                    options={true}
                                    isInvoiceDetails
                                    isPurchase={isPurchase}
                                    domId={"datePickerInvoiceDate"}
                                />
                            </Form.Group>
                        </Col>
                        <Col xl={2} md={2} sm={6} className="px-xl-0 mb-2"></Col>
                        <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-2"></Col>
                    </>
                ) : (
                    ""
                )}
            </Row>

            {iShowExpenseDetail && <ExpenseOtherDetail iShowExpenseDetail={iShowExpenseDetail} />}
            <DispatchDetails />

           {!isRecurring && (
                <Row className="justify-content-start mx-xl-0">
                    <Col sm={12} className="px-xl-0">
                        <h5 className="mb-3">Party Details</h5>
                    </Col>
                    <Col
                        xl={2}
                        lg={3}
                        md={4}
                        sm={6}
                        className="px-xl-0 mb-4 form-group-select me-xl-8"
                    >
                        <Form.Group>
                            {isPartyNameExist && !gstQuote?.party_ledger_id && <div className="not_exist_item">
                                <span className="not_exist_item_text">{isPartyNameExist}</span>
                                <span className="item_plus cursor-pointer" onClick={() => createNotExistsParty(isPartyNameExist)} ><PlusCircle /></span>

                            </div>
                            }
                            <div className="input-group flex-nowrap">
                                <div className={`position-relative h-40px w-100 focus-shadow ${userPermission?.add_ledger_master ? 'pe-36px' : ""}`}>
                                    <ReactSelect
                                        customLabel="party"
                                        options={partiesOptions}
                                        value={gstQuote.party_ledger_id || null}
                                        onChange={handleChangeParty}
                                        portal={true}
                                        required={true}
                                        islabel={true}
                                        radius={true}
                                        placeholder="Select Party"
                                        defaultLabel="Select Party"
                                        width="400px"
                                        isEdit={userPermission?.edit_ledger_master}
                                        handleOpen={handleOpenLedger}
                                        isCreatable
                                        onMenuScrollToBottom={handleScroll}
                                        customFilter={customFilter}
                                    />
                                </div>
                                {userPermission?.add_ledger_master ?
                                <button
                                    type="button"
                                    onClick={() => handleOpenLedger("")}
                                    className="input-group-text custom-group-text"
                                >
                                    <i className="fas fa-plus text-gray-900"></i>
                                </button>
                                : ""}
                            </div>
                        </Form.Group>
                        {gstQuote?.party_ledger_id && (
                            <span style={{ whiteSpace: "nowrap" }}>
                                Current Balance: {company?.company?.currentCurrencySymbol}
                                {gstQuote?.closing_balance ? gstQuote?.closing_balance : 0}
                            </span>
                        )}
                    </Col>

                    {!isPurchase && configurationList?.header?.is_enabled_phone_number ? (
                        <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                            <PhoneInput
                                country={"in"}
                                value={gstQuote?.mobile?.phone_input || "+91"}
                                onChange={(phone, code) => handlePhoneChange(phone, code)}
                                countryCodeEditable={false}
                                containerClass="w-100"
                                inputClass="w-100 h-40px fw-500 focus-shadow"
                            />
                        </Col>
                    ) : (
                        ""
                    )}
                    {configurationList?.is_gst_applicable ? (
                        <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                            <Form.Group className="position-relative form-floating-group">
                                <FormInput
                                    className="floating-label-input"
                                    type="text"
                                    placeholder=""
                                    value={gstQuote.gstin ?? null}
                                    onChange={changeGstNumber}
                                    minLength="15"
                                    maxLength="15"
                                />
                                <Form.Label>GSTIN</Form.Label>
                            </Form.Group>
                            {GSTError && (
                                <span className="position-absolute text-danger">{GSTError}</span>
                            )}
                        </Col>
                    ) : null}
                    {isShowExpenseCreateOrDebit && (
                        <>
                            <OriginalInvoiceDateDetail
                                originalInvoiceOption={originalPurchaseInvoiceOption}
                                gstQuote={gstQuote}
                                changeOriginalInvoice={changeOriginalInvoice}
                                handleChangeDate={handleChangeDate}
                            />
                        </>
                    )}
                    {isShowPurchaseNumber ? (
                        <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                            <div className="input-group flex-nowrap">
                                <div className="position-relative w-100 focus-shadow delivery-challan focus-shadow">
                                    <ReactSelect
                                        options={
                                            id && updatePurchaseNumberOptions !== null && !isDuplicate
                                                ? updatePurchaseNumberOptions
                                                : purchaseNumberOptions || []
                                        }
                                        placeholder={"Purchase Order Number"}
                                        defaultLabel={"Select Order Number"}
                                        islabel={true}
                                        value={gstQuote.purchase_number || null}
                                        onChange={e => changePurchaseNumber(e)}
                                        className="border-0"
                                        isMulti
                                    />
                                </div>
                            </div>
                        </Col>
                    ) : null}
                    {isShowEstimateOrChallan && (
                        <>
                            {configurationList?.header?.is_enabled_estimate_quote && (
                                <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                                    <div className="input-group flex-nowrap">
                                        <div className="position-relative w-100 focus-shadow delivery-challan">
                                            <ReactSelect
                                                options={
                                                    id &&
                                                    updateParty_quotesOptions !== null &&
                                                    !isDuplicate && configurationList?.header?.is_enabled_estimate_quote
                                                        ? updateParty_quotesOptions
                                                        : party_quotesOptions || []
                                                }
                                                placeholder={"Estimates / Quotes"}
                                                defaultLabel={"Select Estimates / Quotes"}
                                                islabel={true}
                                                value={gstQuote.quotes_id || null}
                                                onChange={e => changeEstimateChallan(e, 1)}
                                                className="border-0"
                                                isMulti={true}
                                            />
                                        </div>
                                    </div>
                                </Col>
                            )}{" "}
                            {configurationList?.header?.is_enabled_delivery_challan && (
                                <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                                    <div className="input-group flex-nowrap">
                                        <div className="position-relative w-100 focus-shadow delivery-challan">
                                            <ReactSelect
                                                options={
                                                    id &&
                                                    updateParty_quotesOptions !== null &&
                                                    !isDuplicate && configurationList?.header?.is_enabled_delivery_challan
                                                        ? updateParty_quotesOptions
                                                        : party_quotesOptions || []
                                                }
                                                // options={party_quotesOptions || []}
                                                placeholder={"Delivery Challan"}
                                                defaultLabel={"Select Delivery Challan"}
                                                islabel={true}
                                                value={gstQuote.quotes_id || null}
                                                onChange={changeEstimateChallan}
                                                className="border-0"
                                                isMulti={true}
                                            />
                                        </div>
                                    </div>
                                </Col>
                            )}
                        </>
                    )}
                    {isShowPartyInvoiceNumber && (
                        <>
                            <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                                <div className="input-group flex-nowrap">
                                    <div className="position-relative h-40px w-100 focus-shadow">
                                        <ReactSelect
                                            options={isShowChallanPage ? originalDeliveryInvoiceOption:
                                                isShowChallanPageComponent
                                                    ? originalInvoiceOption
                                                    : originalPurchaseInvoiceOption || []
                                            }
                                            placeholder={"Invoice Number"}
                                            defaultLabel={"Select Invoice Number"}
                                            islabel={true}
                                            value={invoiceNumber || null}
                                            onChange={e =>
                                                onChangePurchaseInvoiceNumber(
                                                    e,
                                                    isShowChallanPageComponent
                                                )
                                            }
                                            className="h-40px border-0"
                                        />
                                    </div>
                                </div>
                            </Col>
                            <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                                <Form.Group>
                                    <Datepicker
                                        value={gstQuote?.original_inv_date ?? null}
                                        // required={true}
                                        placeholder={"Invoice Date"}
                                        onChange={e => handleChangeDate(e, "gstInvoice")}
                                    />
                                </Form.Group>
                            </Col>
                        </>
                    )}
                    {isShowValidFor && (
                        <>
                            <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                                <Form.Group className="position-relative form-floating-group">
                                    <FormInput
                                        className="floating-label-input"
                                        step="0.01"
                                        type="number"
                                        placeholder=""
                                        value={gstQuote.valid_for}
                                        onChange={e => {
                                            setisFieldsChanges(true);
                                            setGstQuote({
                                                ...gstQuote,
                                                valid_for: e.target.value,
                                            })
                                        }
                                        }
                                    />
                                    <Form.Label>Valid For</Form.Label>
                                </Form.Group>
                            </Col>
                            <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                                <label
                                    htmlFor="validForType"
                                    className="form-label fs-6 fw-bolder text-gray-900 mb-0"
                                >
                                    Valid For Type
                                </label>
                                <div className="d-flex align-items-center mt-3">
                                    <div className="form-check mx-2">
                                        <label className="form-label" htmlFor="validForMonth">
                                            Month
                                        </label>
                                        <input
                                            className="form-check-input"
                                            name="valid_for_type"
                                            type="radio"
                                            value={1}
                                            defaultChecked={
                                                gstQuote.valid_for_type == 1 ||
                                                gstQuote.valid_for_type == null
                                            }
                                            // checked={gstQuote.valid_for_type == 1}
                                            onClick={validForTypeChange}
                                        />
                                    </div>
                                    <div className="form-check mx-2">
                                        <label className="form-label" htmlFor="validForDays">
                                            Day
                                        </label>
                                        <input
                                            className="form-check-input"
                                            name="valid_for_type"
                                            type="radio"
                                            checked={gstQuote.valid_for_type == 2}
                                            value={2}
                                            onClick={validForTypeChange}
                                        />
                                    </div>
                                </div>
                            </Col>
                        </>
                    )}
                </Row>
            )}
           {!isRecurring &&(
                <Row className="justify-content-start mx-xl-0">
                    <Col xl={2} lg={6} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                        <div className="dispatch-address-bg h-100 p-3">
                            <Form.Group>
                                <div>
                                    <div className="d-flex justify-content-between">
                                        <p className="text-primary">Billing Address</p>
                                        <button
                                            type="button"
                                            className="focus-icon-btn p-0"
                                            onClick={() => handleOpenPartyAddress("Billing Address")}
                                        >
                                            <i className="fas fa-edit text-primary cursor-pointer"></i>
                                        </button>
                                    </div>
                                    <div>
                                        <p className="custom-paragraph mb-0">
                                            <>
                                                {partyAddress?.billingAddress?.address_1 &&
                                                    `${partyAddress.billingAddress.address_1}, `}
                                                {partyAddress?.billingAddress?.address_2 &&
                                                    `${partyAddress.billingAddress.address_2}, `}
                                                {partyAddress?.billingAddress?.city_name &&
                                                    `${partyAddress.billingAddress.city_name}, `}
                                                {partyAddress?.billingAddress?.state_name &&
                                                    `${partyAddress.billingAddress.state_name}, `}
                                                {prepareDispatchData(partyAddress?.billingAddress)}
                                                {partyAddress?.billingAddress?.pin_code &&
                                                    ", " + partyAddress?.billingAddress?.pin_code}
                                            </>
                                        </p>
                                    </div>
                                </div>
                            </Form.Group>
                        </div>
                    </Col>
                    <Col xl={2} lg={6} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                        {configurationList?.header?.is_enabled_shipping_address && (
                            <div className="dispatch-address-bg h-100 p-3">
                                <Form.Group>
                                    <div>
                                        <div className="d-flex justify-content-between">
                                            <p className="text-primary">Shipping Address</p>
                                            <button
                                                type="button"
                                                className="focus-icon-btn p-0"
                                                onClick={handleOpenShippingAddressModel}
                                            >
                                                <i className="fas fa-edit text-primary cursor-pointer"></i>
                                            </button>
                                        </div>
                                        <div>
                                            <p className="custom-paragraph mb-3">
                                                {/* //gstQuote?.party_ledger_id */}
                                                {!sameAsBill &&
                                                shippingAddress[selectShippingAddress] ? (
                                                    <>
                                                        {`${
                                                            shippingAddress[selectShippingAddress]
                                                                ?.address_1
                                                                ? shippingAddress[selectShippingAddress]
                                                                    ?.address_1 + ","
                                                                : ""
                                                        } ${
                                                            shippingAddress[selectShippingAddress]
                                                                ?.address_2
                                                                ? shippingAddress[selectShippingAddress]
                                                                    ?.address_2 + ","
                                                                : ""
                                                        }
                                                        ${
                                                            shippingAddress[selectShippingAddress]
                                                                ?.city_name
                                                                ? shippingAddress[selectShippingAddress]
                                                                    ?.city_name + ","
                                                                : ""
                                                        }
                                                        ${
                                                            shippingAddress[selectShippingAddress]
                                                                ?.state_name
                                                                ? shippingAddress[selectShippingAddress]
                                                                    ?.state_name + ","
                                                                : ""
                                                        }
                                                        ${prepareDispatchData(
                                                            shippingAddress[selectShippingAddress]
                                                        )}
                                                        ${
                                                            shippingAddress[selectShippingAddress]
                                                                ?.pin_code
                                                                ? "," +
                                                                shippingAddress[selectShippingAddress]
                                                                    ?.pin_code
                                                                : ""
                                                        }`}
                                                    </>
                                                ) : (
                                                    ""
                                                )}
                                            </p>
                                        </div>
                                        <Form.Check
                                            type="checkbox"
                                            label="Same as Billing Address"
                                            value={sameAsBill}
                                            checked={sameAsBill}
                                            onChange={e => setSameAsBill(e.target.checked)}
                                        />
                                    </div>
                                </Form.Group>
                            </div>
                        )}
                    </Col>
                    <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
                    <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
                    <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
                </Row>
            )}

            {configurationList?.header?.is_enabled_broker_details ? (
                <Row className="justify-content-start align-items-center mx-xl-0">
                    <Col sm={12} className="px-xl-0">
                        <h5 className="mb-3">Broker Details</h5>
                    </Col>
                    <Col
                        xl={2}
                        lg={3}
                        md={4}
                        sm={6}
                        className="px-xl-0 mb-4 form-group-select me-xl-8"
                    >
                        <Form.Group>
                            <div className="input-group flex-nowrap">
                                <div className={`position-relative h-40px w-100 focus-shadow ${userPermission?.add_broker_master ? 'pe-36px' : ""}`}>
                                    <ReactSelect
                                        customLabel="party"
                                        options={brokerOptions || []}
                                        placeholder={"Broker Name"}
                                        defaultLabel={"Select Broker"}
                                        islabel={true}
                                        value={brokerDetail.broker_id || null}
                                        onChange={e => handleBrokerChange(e)}
                                        portal={true}
                                        radius={true}
                                        width="400px"
                                        className="h-40px"
                                        isEdit={userPermission?.edit_broker_master}
                                        handleOpen={BrokerModel}
                                    />
                                </div>
                                {userPermission?.add_broker_master ?
                                <button
                                    type="button"
                                    className="input-group-text custom-group-text"
                                    onClick={() => BrokerModel("")}
                                >
                                    <i className="fas fa-plus text-gray-900"></i>
                                </button>
                                 : ""}
                            </div>
                        </Form.Group>
                    </Col>
                    <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                        <Form.Group>
                            <div className="input-group flex-nowrap">
                                <div className="position-relative h-40px w-100 form-floating-group">
                                    <FormInput
                                        className="position-relative floating-label-input h-100"
                                        type="number"
                                        step="0.01"
                                        min={0}
                                        max={100}
                                        placeholder=""
                                        value={brokerDetail.broker_percentage || ""}
                                        onChange={brokerPercentage}
                                    />
                                    <Form.Label>Brokerage Percentage</Form.Label>
                                </div>
                                <div className="input-group-text custom-group-text">
                                    <i className="fas fa-percentage text-gray-900"></i>
                                </div>
                            </div>
                        </Form.Group>
                    </Col>
                    <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                        <Form.Group>
                            <div className="input-group flex-nowrap">
                                <div className="position-relative h-40px w-100 focus-shadow">
                                    <ReactSelect
                                        name="brokerageValue"
                                        placeholder="Brokerage on Value"
                                        options={brokerageOption}
                                        value={brokerDetail.brokerage_on_value}
                                        onChange={selectedOption => {
                                            setisFieldsChanges(true);
                                            setBrokerDetail({
                                                ...brokerDetail,
                                                brokerage_on_value: selectedOption.value,
                                            })
                                        }
                                        }
                                    />
                                </div>
                            </div>
                        </Form.Group>
                    </Col>
                    <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
                    <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
                </Row>
            ) : (
                ""
            )}

            {configurationList?.header?.is_enabled_transport_details ? (
                <Row className="justify-content-start mx-xl-0">
                    <Col sm={12} className="px-xl-0">
                        <h5 className="mb-3">Transport Details</h5>
                    </Col>
                    <Col
                        xl={2}
                        lg={3}
                        md={4}
                        sm={6}
                        className="px-xl-0 mb-4 form-group-select me-xl-8"
                    >
                        <Form.Group>
                            <div className="input-group flex-nowrap">
                                <div className={`position-relative h-40px w-100 focus-shadow ${userPermission?.add_transport_master ? 'pe-36px' : ""}`}>
                                    <ReactSelect
                                        customLabel="party"
                                        options={transportOptions || []}
                                        placeholder={"Transporter Name"}
                                        defaultLabel={"Select Transporter"}
                                        width="400px"
                                        islabel={true}
                                        value={transporterDetail.transport_id || null}
                                        onChange={e => {
                                            setisFieldsChanges(true);
                                            setTransporterDetail({
                                                ...transporterDetail,
                                                transport_id: e.value,
                                            })
                                            }
                                        }
                                        portal={true}
                                        className="h-40px"
                                        radius={true}
                                        isEdit={userPermission?.edit_transport_master}
                                        handleOpen={handleOpenTransportModal}
                                    />
                                </div>
                                {userPermission?.add_transport_master ?
                                <button
                                    type="button"
                                    className="input-group-text custom-group-text"
                                    onClick={() => handleOpenTransportModal("")}
                                >
                                    <i className="fas fa-plus text-gray-900"></i>
                                </button>
                                : ""}
                            </div>
                        </Form.Group>
                    </Col>
                    <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                        <Form.Group className="position-relative form-floating-group">
                            <Datepicker
                                value={transporterDetail.transporter_document_date || ""}
                                placeholder={"Document Date"}
                                onChange={e => handleChangeDate(e, "transport")}
                                isTransporter={true}
                            />
                        </Form.Group>
                    </Col>
                    <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                        <Form.Group className="position-relative form-floating-group">
                            <FormInput
                                className="floating-label-input"
                                type="text"
                                placeholder=""
                                value={transporterDetail.transporter_document_number || ""}
                                onChange={e =>
                                    setTransporterDetail({
                                        ...transporterDetail,
                                        transporter_document_number: e.target.value,
                                    })
                                }
                            />
                            <Form.Label>Document Number</Form.Label>
                        </Form.Group>
                    </Col>
                    <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                        <Form.Group className="position-relative form-floating-group">
                            <FormInput
                                className="floating-label-input"
                                type="text"
                                placeholder=""
                                value={transporterDetail.transporter_vehicle_number || ""}
                                onChange={e =>
                                    setTransporterDetail({
                                        ...transporterDetail,
                                        transporter_vehicle_number: e.target.value,
                                    })
                                }
                            />
                            <Form.Label>Vehicle Number</Form.Label>
                        </Form.Group>
                    </Col>
                    <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
                </Row>
            ) : (
                ""
            )}

            {configurationList?.header?.is_enabled_eway_details && !isShowPurchaseOrder ? (
                <Row className="justify-content-start mx-xl-0">
                    <Col sm={12} className="px-xl-0">
                        <h5 className="mb-3">E-way Bill Details</h5>
                    </Col>
                    <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                        <Form.Group className="position-relative form-floating-group">
                            <FormInput
                                className="floating-label-input"
                                type="text"
                                placeholder=""
                                value={ewayBillDetail.eway_bill_number || ""}
                                onChange={e =>
                                    setEwayBillDetail({
                                        ...ewayBillDetail,
                                        eway_bill_number: e.target.value,
                                    })
                                }
                            />
                            <Form.Label>E-way Bill Number</Form.Label>
                        </Form.Group>
                    </Col>
                    <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                        <Form.Group>
                            <Datepicker
                                value={ewayBillDetail.eway_bill_date || ""}
                                placeholder="E-way Bill Date"
                                onChange={e => handleChangeDate(e, "eway")}
                            />
                        </Form.Group>
                    </Col>
                    <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
                    <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
                    <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
                </Row>
            ) : (
                ""
            )}
            <Row className="justify-content-start align-items-center mx-xl-0">
                {(configurationList?.header?.is_enabled_credit_period_details ||
                    configurationList?.header?.is_enabled_po_details_of_buyer) &&
                !isShowPurchaseOrder ? (
                    <Col sm={12} className="px-xl-0">
                        <h5 className="mb-3">Other Details</h5>
                    </Col>
                ) : null}
                {configurationList?.header?.is_enabled_po_details_of_buyer ? (
                    <>
                        <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                            <Form.Group className="position-relative form-floating-group">
                                <FormInput
                                    className="floating-label-input"
                                    type="text"
                                    placeholder=""
                                    value={otherDetail.po_number || ""}
                                    onChange={e =>
                                        setOtherDetail({
                                            ...otherDetail,
                                            po_number: e.target.value,
                                        })
                                    }
                                />
                                <Form.Label>PO Number</Form.Label>
                            </Form.Group>
                        </Col>
                        <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                            <Form.Group className="position-relative form-floating-group">
                                <DatePickerPaymentDetails
                                    value={otherDetail.date || ""}
                                    onChange={e => handleChangeDate(e, "other")}
                                    placeholder="PO Date"
                                    isTransporter
                                />
                            </Form.Group>
                        </Col>
                    </>
                ) : null}
                {configurationList?.header?.is_enabled_credit_period_details ? (
                    <>
                        <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                            <Form.Group className="position-relative form-floating-group h-34px">
                                <FormInput
                                    className="floating-label-input h-100"
                                    type="number"
                                    min={0}
                                    placeholder=""
                                    value={otherDetail.creditPeriod || ""}
                                    onChange={e =>
                                        setOtherDetail({
                                            ...otherDetail,
                                            creditPeriod: e.target.value,
                                        })
                                    }
                                />
                                <Form.Label
                                    style={{
                                        maxWidth: "calc(100% - 100px)",
                                    }}
                                >
                                    Credit Period
                                </Form.Label>
                                <div
                                    className="w-fit-content bg-light position-absolute credit-dropdown-select focus-shadow input-group d-block"
                                    style={{ minWidth: "90px" }}
                                >
                                    <ReactSelect
                                        defaultValue={1}
                                        options={[
                                            {
                                                label: "Month",
                                                value: 1,
                                            },
                                            {
                                                label: "Day",
                                                value: 2,
                                            },
                                        ]}
                                        placeholder=""
                                        isCreatable={false}
                                        showborder={false}
                                        showbg={true}
                                        height="32px"
                                        value={otherDetail.creditPeriodType}
                                        onChange={selectedOption => {
                                            CreditType({
                                                target: {
                                                    value: selectedOption.value,
                                                },
                                            });
                                        }}
                                    />
                                </div>
                            </Form.Group>
                        </Col>
                    </>
                ) : null}
                <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
            </Row>

            <CustomField />

            {isDispatchFromModel && (
                <DispatchFromModal
                    show={isDispatchFromModel}
                    handleClose={closeDispatchFromModel}
                    localDispatchAddress={localDispatchAddress}
                    setLocalDispatchAddress={setLocalDispatchAddress}
                    selectedAddress={selectedAddress}
                    setSelectedAddress={setSelectedAddress}
                />
            )}
            {isShippingFromModel && (
                <ShippingFromModal
                    show={isShippingFromModel}
                    handleClose={closeShippingFromModel}
                    localDispatchAddress={shippingAddress}
                    setLocalDispatchAddress={setShippingAddress}
                    selectedAddress={selectShippingAddress}
                    setSelectedAddress={setSelectShippingAddress}
                    isLedgerModel={isLedgerModel}
                    gstQuote={gstQuote}
                    selectShippingAddress={selectShippingAddress}
                    setSelectShippingAddress={setSelectShippingAddress}
                    shipping_address_type={shipping_address_type}
                />
            )}
            {isDispatchAddressModel && (
                <PartyAddressModal
                    show={isDispatchAddressModel}
                    handleClose={closeDispatchAddressModel}
                    name={dispatchAddressName}
                    data={ledger?.partyDetail}
                    partyAddress={partyAddress}
                    setPartyAddress={setPartyAddress}
                    sameAsBill={sameAsBill}
                />
            )}
            {isBrokerModel && (
                <AddBrokerModal
                    show={isBrokerModel}
                    handleClose={closeBrokerModel}
                    setModalType={setModalType}
                    modalType={modalType}
                    broker={broker}
                    entityType={brokerEntityType}
                    setBrokerDetail={setBrokerDetail}
                    isPurchase={isPurchase}
                    isBackdrop
                />
            )}
            {isTransportModel && (
                <AddTransportModal
                    show={isTransportModel}
                    handleClose={closeTransportModel}
                    setModalType={setTransportTitle}
                    modalType={transportTitle}
                    transport={transport}
                    setTransporterDetail={setTransporterDetail}
                    isBackdrop
                />
            )}
            {isShowSelectedService && (
                <SelectInvoiceModal
                    show={isShowSelectedService}
                    handleClose={closeSelectedServiceModel}
                    isEstimate={isEstimate}
                    setItemType={setItemType}
                    accountingTableHeader={accountingTableHeader}
                    tableHeaderList={tableHeaderList}
                    showInvoice={showInvoice}
                    setShowInvoice={setShowInvoice}
                />
            )}
            {isShowSelectedPurchaseNumber && (
                <PurchaseNumberSelection
                    show={isShowSelectedPurchaseNumber}
                    handleClose={closeSelectedPurchaseNumberModel}
                    setItemType={setItemType}
                    accountingTableHeader={accountingTableHeader}
                    tableHeaderList={tableHeaderList}
                    showPurchaseNumber={showPurchaseNumber}
                    setShowPurchaseNumber={setShowPurchaseNumber}
                />
            )}
            {isLedgerModel && (
                <AddLedgerModal
                    show={isLedgerModel}
                    handleClose={closeLedgerModel}
                    brokerOptions={brokerOptions}
                    ledgerDetailOptions={ledgerDetailOptions}
                    entityType={ledgerEntityType}
                    ledgerGstOption={ledgerGstOption}
                    itemLegder={item?.itemDetail}
                    gstQuote={gstQuote}
                    setGstQuote={setGstQuote}
                    idName="party_ledger_id"
                    id={2}
                    sale={true}
                    name={partyTitle}
                    action={isPurchase ? "Supplier" : "Customers"}
                    isPurchase={isPurchase}
                    shipping_address_type={shipping_address_type}
                    items={items}
                />
            )}
        </div>
    );
};

export default SaleInvoiceDetail;
