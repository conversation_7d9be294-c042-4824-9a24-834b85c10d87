import React, { useCallback, useContext, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { StateContext } from "../../context/StateContext";
import ReactSelect from "../../components/ui/ReactSelect";
import useDropdownOption from "../../shared/dropdownList";
import {
    addLedger,
    fetchLedgerById,
    fetchPartyDetail,
    fetchPartyList,
    getLedgerModelDetail,
} from "../../store/ledger/ledgerSlice";
import { LedgerType, SHIPPING_ADDRESS_TYPE_LIST, transactionTypeMap, USER_PERMISSION } from "../../constants";
import { fetchTransportById } from "../../store/transport/transportSlice";
import {
    fetchBrokerById,
    fetchBrokerDetailsById,
    getBrokerModelDetail,
} from "../../store/broker/brokerSlice";
import { Col, Form, Row } from "react-bootstrap";
import Datepicker from "../../components/ui/DatePicker";
import { clearData, prepareDispatchData } from "../../shared/prepareData";
import { FormInput } from "../../components/ui/Input";
import { fetchSaleById } from "../../store/sale/saleSlice";
import DispatchFromModal from "../modal/DispatchAddress/DispatchFromModal";
import ShippingFromModal from "../modal/PartyAddress/ShippingFromModal";
import PartyAddressModal from "../modal/PartyAddress/PartyAddressModal";
import AddBrokerModal from "../modal/Broker/BrokerModal";
import AddTransportModal from "../modal/Transport/TransportModal";
import AddLedgerModal from "../modal/Ledger/LedgerModal";
import { CheckGstValidate, formattedDate } from "../../shared/calculation";
import DispatchDetails from "./DispatchDetails";
import { fetchShippingAddressList } from "../../store/shippingAddress/shippingAddressSlice";
import OriginalInvoiceDateDetail from "./OriginalInvoiceDateDetail";
import { fetchPriceListOfItems, fetchPartyIdBaseItemList } from "../../store/item/itemSlice";
import { fetchGstData } from "../../store/gst/gstSlice";
import DatePickerPaymentDetails from "../../components/ui/DatePickerPaymentDetails";
import PhoneInput from "react-phone-input-2";
import { useParams } from "react-router-dom";
import CustomField from "./CustomField";
import { useTransactionShortcuts } from "../../shared/shortcut-keys";
import { checkPathName } from "../../shared/sharedFunction";

const SaleReturnInvoiceDetail = ({
    id,
    shipping_address_type,
    invoiceRef,
    incomeCnDnRef,
    IncomeCreateOrDebit,
    ledgerModalName,
    ledgerModalEditName,
    isPurchase = false,
    estimateId,
    saleId,
    isDuplicate
}) => {
    const { configuration, item, transport, broker, ledger, shippingAddresses, company } =
        useSelector(selector => selector);
    const pathname = window.location.pathname?.includes("sale") ? "datePickerInvoiceDate" : "datePickerInvoiceDate";
    const {
        invoiceDetail,
        setInvoiceDetail,
        partyAddress,
        setPartyAddress,
        ewayBillDetail,
        setEwayBillDetail,
        otherDetail,
        setOtherDetail,
        brokerDetail,
        setBrokerDetail,
        transporterDetail,
        setTransporterDetail,
        localDispatchAddress,
        gstQuote,
        setGstQuote,
        selectedAddress,
        isDispatchFromModel,
        openDispatchFromModel,
        closeDispatchFromModel,
        isLedgerModel,
        openLedgerModel,
        closeLedgerModel,
        partyTitle,
        setPartyTitle,
        modalType,
        openDispatchAddressModel,
        setDispatchAddressName,
        sameAsBill,
        setSameAsBill,
        isTransportModel,
        openTransportModel,
        closeTransportModel,
        transportTitle,
        setTransportTitle,
        isBrokerModel,
        openBrokerModel,
        closeBrokerModel,
        setModalType,
        isShippingFromModel,
        openShippingFromModel,
        closeShippingFromModel,
        shippingAddress,
        selectShippingAddress,
        dispatchAddressName,
        setLocalDispatchAddress,
        setSelectedAddress,
        setShippingAddress,
        setSelectShippingAddress,
        setIsChangeShippingAddress,
        isDispatchAddressModel,
        closeDispatchAddressModel,
        isCreateParty,
        setIsCreateParty,
        setIsChangeParty,
        setIsEditCalculation,
        setIsCheckGstType,
        setInvoiceNumber,
        setCheckGroupLedgerType,
        items,
        setItems,
        setShowItemPriceChangedModal,
        setIsConfirmed,
        setTaxableValue,
        setMainGrandTotal,
        setFinalAmount,
        setGrandTotal,
        setGstValue,
        gstCalculation,
        setGstCalculation,
        GSTError,
        setGSTError,
        isChangePartyId,
        setIsChangePartyId,
        isFocusedInvoiceNumber,
        setIsFocusedInvoiceNumber,
        setShowPaymentTable,
        setIsInitialDataLoaded,
        setIsInitialDataLoaded2,
        userPermission,
        setUserPermission,
        setisFieldsChanges,
        setIsShippingAddressModel,
        saveFeature,
        partyLedgerTrigger,
        setPartyLedgerTrigger
    } = useContext(StateContext);

    const {
        partiesOptions,
        brokerOptions,
        transportOptions,
        originalInvoiceOption,
        brokerEntityType,
        ledgerDetailOptions,
        ledgerEntityType,
        ledgerGstOption,
        brokerageOption
    } = useDropdownOption();

    const {id: otherId} = useParams()
    const dispatch = useDispatch();
    const [typingTimeout, setTypingTimeout] = useState(0);
    const [customer, setCustomer] = useState({
        name: "",
        group_id: "",
        party_details: {
            gstin: null,
            billing_address: {
                address_1: null,
                address_2: null,
                country_id: "",
                state_id: "",
                city_id: null,
                pin_code: null,
            },
            same_as_billing_address: 0,
            shipping_address: {
                shipping_gstin: null,
                shipping_name: null,
                address_1: null,
                address_2: null,
                country_id: "",
                state_id: "",
                city_id: null,
                pin_code: null,
            },
            contact_person_name: null,
            region_iso_1: null,
            region_code_1: null,
            contact_person_phone_1: null,
            region_iso_2: null,
            region_code_2: null,
            contact_person_phone_2: null,
            contact_person_email: null,
        },
        tax_details: [],
        other_details: [],
        opening_balance_details: {
            opening_balance: null,
            opening_balance_dr_cr: null,
        },
        action: "Customers",
    });
    const configurationList = configuration?.configuration;

    const getCustomFieldTransactionType = Object.keys(transactionTypeMap).find(path =>
          checkPathName(path)
    ) ? transactionTypeMap[Object.keys(transactionTypeMap).find(path => checkPathName(path))] : "";

    useEffect(() => {
        const user_permission = company?.user_permission;
        const add_broker_master = user_permission?.includes(USER_PERMISSION.ADD_BROKER_MASTER)
        const edit_broker_master = user_permission?.includes(USER_PERMISSION.EDIT_BROKER_MASTER)
        const add_transport_master = user_permission?.includes(USER_PERMISSION.ADD_TRANSPORT_MASTER)
        const edit_transport_master = user_permission?.includes(USER_PERMISSION.EDIT_TRANSPORT_MASTER)
        const add_ledger_master = user_permission?.includes(USER_PERMISSION.ADD_LEDGER)
        const edit_ledger_master = user_permission?.includes(USER_PERMISSION.EDIT_LEDGER)
        const add_new_item_masters = user_permission?.includes(USER_PERMISSION.ADD_ITEM_MASTER)
        const edit_item_masters = user_permission?.includes(USER_PERMISSION.EDIT_ITEM_MASTER)

        setUserPermission({
            ...userPermission,
            add_broker_master,
            edit_broker_master,
            add_transport_master,
            edit_transport_master,
            add_ledger_master,
            edit_ledger_master,
            add_new_item_masters,
            edit_item_masters
        })
    }, [company?.user_permission]);

    useEffect(() => {
        if (configurationList?.document_prefix?.method_of_voucher_number == 2 && !id) {
            $("#datePickerInvoiceDate").trigger("click");
        }
        if (invoiceRef?.current) {
            invoiceRef.current.focus();
        }
        if (incomeCnDnRef?.current) {
            incomeCnDnRef.current.focus();
        }
    }, [
        invoiceRef,
        incomeCnDnRef,
        configurationList?.document_prefix?.suffix,
        configurationList?.document_prefix?.method_of_voucher_number,
    ]);

    useTransactionShortcuts(pathname);

    useEffect(() => {
        dispatch(getLedgerModelDetail(LedgerType.PARTY_LEDGER));
        setCheckGroupLedgerType("party");
    }, []);

    useEffect(() => {
        const ledger_group_detail = ledger?.ledgerGroupDetail;
        if (ledger_group_detail) {
            const group_id = Object.keys(ledger_group_detail).find(
                key => ledger_group_detail[key] === "Customers"
            );
            setCustomer(prev => ({ ...prev, group_id }));
        }
        if (company?.company?.addresses) {
            const billing_address =
                company?.company?.addresses[0]?.address_type == 1
                    ? company?.company?.addresses[0]
                    : company?.company?.addresses[1];
            const dispatch_address =
                company?.company?.addresses[0]?.address_type == 2
                    ? company?.company?.addresses[0]
                    : company?.company?.addresses[1];
            setCustomer(prev => ({
                ...prev,
                party_details: {
                    ...prev.party_details,
                    billing_address: {
                        ...prev.party_details.billing_address,
                        country_id: billing_address?.country_id,
                        state_id: billing_address?.state_id,
                    },
                    shipping_address: {
                        ...prev.party_details.shipping_address,
                        country_id: dispatch_address?.country_id,
                        state_id: dispatch_address?.state_id,
                    },
                },
            }));
        }
    }, [ledger?.ledgerGroupDetail, isCreateParty]);

    const handleChangeParty = e => {
        setisFieldsChanges(true);
        setPartyLedgerTrigger(prev => prev + 1);
        setGSTError("");
        setShowPaymentTable(false);
        // coment : when select party to first time 0 index data and after show actual data
        // setSelectShippingAddress(0);
        setIsEditCalculation(true);
        setIsCheckGstType(true);
        if (id) {
            setIsChangePartyId(true);
            setIsChangeParty(true);
        }
        if (e.value) {
            setIsChangePartyId(true);
            setIsChangeParty(true);
        }
        if (e.__isNew__) {
            dispatch(
                addLedger(
                    isPurchase,
                    {
                        ...customer,
                        party_details: {
                            ...customer?.party_details,
                            billing_address: {
                                country_id: company?.company?.billing_address?.country_id,
                                state_id: company?.company?.billing_address?.state_id,
                            },
                            shipping_address: {
                                country_id: company?.company?.billing_address?.country_id,
                                state_id: company?.company?.billing_address?.state_id,
                            },
                        },
                        name: e.label,
                    },
                    null,
                    null,
                    setGstQuote
                )
            );
        } else {
            setGstQuote({
                ...gstQuote,
                party_ledger_id: e.value,
                quotes_id: [],
                gstin:
                    id && !gstQuote.gstin
                        ? e.value == gstQuote.party_ledger_id
                            ? e.details ?? ""
                            : e.details ?? ""
                        : e.details ?? "",
                original_inv_no: "",
                original_inv_date: "",
            });
        }
        if (e.value === null) {
            setIsChangePartyId(false);
            setIsChangeParty(false);
            dispatch(fetchPartyDetail(""));
            setGstQuote({
                ...gstQuote,
                gstin: "",
                party_ledger_id: "",
                quotes_id: [],
                original_inv_no: "",
                original_inv_date: "",
                valid_for: "",
                valid_for_type: 1,
                mobile: {
                    region_iso: "in",
                    region_code: "+91",
                    party_phone_number: "",
                    phone_input: "+91",
                },
            });
        } else {
            if (e.value && !e.__isNew__) {
                dispatch(fetchPartyDetail(e.value));
                dispatch(fetchPartyIdBaseItemList(e.value))
                setInvoiceNumber("");
                const ids = items.map(item => item.selectedItem).filter(Boolean);
                if (ids.length > 0 && !id) {
                    dispatch(
                        fetchPriceListOfItems(
                            ids,
                            e.value,
                            setShowItemPriceChangedModal,
                            setIsConfirmed,
                            getCustomFieldTransactionType
                        )
                    );
                }
            }
        }
        setIsCreateParty(e.__isNew__ ? true : false);
        setIsChangeParty(false);
    };

    const handleChangeDate = useCallback(
        (date, name) => {
            const isValidDate = date && !isNaN(new Date(date).getTime());
            const formatDate = isValidDate ? formattedDate(new Date(date)) : "";

            if (name === "transport") {
                setTransporterDetail({
                    ...transporterDetail,
                    transporter_document_date: formatDate,
                });
            } else if (name === "invoice") {
                setInvoiceDetail({
                    ...invoiceDetail,
                    invoice_date: formatDate,
                });
            } else if (name === "eway") {
                setEwayBillDetail({
                    ...ewayBillDetail,
                    eway_bill_date: formatDate,
                });
            } else if (name === "party") {
                setGstQuote({
                    ...gstQuote,
                    original_inv_date: formatDate,
                });
            } else if (name === "other") {
                setOtherDetail({
                    ...otherDetail,
                    date: formatDate,
                });
            }
        },
        [transporterDetail, invoiceDetail, ewayBillDetail, otherDetail]
    );

    useEffect(() => {
        if (sameAsBill) {
            setPartyAddress({
                ...partyAddress,
                shippingCopy: {
                    party_name: partyAddress.billingAddress.party_name,
                    gstin: partyAddress.billingAddress.gstin,
                    address_1: partyAddress.billingAddress.address_1,
                    address_2: partyAddress.billingAddress.address_2,
                    country_id: partyAddress.billingAddress.country_id,
                    state_id: partyAddress.billingAddress.state_id,
                    city_id: partyAddress.billingAddress.city_id,
                },
            });
        }
    }, [sameAsBill]);

    useEffect(() => {
        const billing_address = ledger?.partyDetail?.billingAddress;
        const selectShipping = shippingAddresses?.shippingAddresses;
        const shipping_address_id = ledger?.partyDetail?.customerDetail?.is_selected_address_id
        const party_detail = ledger?.partyDetail?.customerDetail
        const same_as_billing_address = ledger?.partyDetail?.customerDetail?.same_as_billing_address;
        const findShippingAddressIndex = selectShipping?.findIndex((ship) => ship.id == shipping_address_id)
        const shipping_address = selectShipping[findShippingAddressIndex > -1 ? findShippingAddressIndex : 0];
        const customer_detail = ledger?.partyDetail;
        if(customer_detail){
            setGstQuote({
                ...gstQuote,
                closing_balance: customer_detail?.closing_balance
                    ? customer_detail?.closing_balance
                    : 0,
            });
            if (id && isChangePartyId) {
                setGstQuote({
                    ...gstQuote,
                    gstin: customer_detail?.customerDetail?.gstin ?? "",
                    closing_balance: customer_detail?.closing_balance
                        ? customer_detail?.closing_balance
                        : 0,
                });
            } else if (isChangePartyId) {
                setGstQuote({
                    ...gstQuote,
                    gstin: customer_detail?.customerDetail?.gstin ?? "",
                    closing_balance: customer_detail?.closing_balance
                        ? customer_detail?.closing_balance
                        : 0,
                });
            }
        }
        const billing = {
            address_1: billing_address?.address_1 || "",
            address_2: billing_address?.address_2 || "",
            country_id: billing_address?.country_id || "",
            state_id: billing_address?.state_id || "",
            city_id: billing_address?.city_id || "",
            state_name: billing_address?.state_name || "",
            city_name: billing_address?.city_name || "",
            pin_code: billing_address?.pin_code || "",
        };
        const shipping = {
            same_as_billing_address: party_detail?.same_as_billing_address,
            shipping_address_id: party_detail?.same_as_billing_address ? party_detail?.is_selected_address_id : shipping_address?.id || "",
            shipping_gstin: shipping_address?.shipping_gstin,
            shipping_name: shipping_address?.shipping_name,
            address_1: shipping_address?.address_1 || "",
            address_2: shipping_address?.address_2 || "",
            country_id: shipping_address?.country_id || "",
            state_id: shipping_address?.state_id || "",
            city_id: shipping_address?.city_id || "",
            state_name: shipping_address?.state_name || "",
            city_name: shipping_address?.city_name || "",
            pin_code: shipping_address?.pin_code || "",
        };
        if (id && isChangePartyId) {
            setPartyAddress({
                billingAddress: billing,
                shippingAddress: shipping,
            });
        } else if (!id && isChangePartyId) {
            setPartyAddress({
                billingAddress: billing,
                shippingAddress: shipping,
            });
        }
        if (id && isChangePartyId) {
            setGstQuote({
                ...gstQuote,
                closing_balance: customer_detail?.closing_balance
                    ? customer_detail?.closing_balance
                    : 0,
                mobile: {
                    region_iso:
                        customer_detail?.customerDetail?.region_iso_1 ||
                        customer_detail?.customerDetail?.region_iso_2 ||
                        "in",
                    region_code:
                        customer_detail?.customerDetail?.region_code_1 ||
                        customer_detail?.customerDetail?.region_code_2 ||
                        "+91",
                    party_phone_number:
                        customer_detail?.customerDetail?.phone_1 ||
                        customer_detail?.customerDetail?.phone_2,
                    phone_input:
                        "+" +
                        (customer_detail?.customerDetail?.region_code_1 ||
                            customer_detail?.customerDetail?.region_code_2 ||
                            "91") +
                        (customer_detail?.customerDetail?.phone_1 ||
                            customer_detail?.customerDetail?.phone_2),
                },
            });
        } else if (!id && gstQuote.party_ledger_id) {
            setGstQuote({
                ...gstQuote,
                closing_balance: customer_detail?.closing_balance
                    ? customer_detail?.closing_balance
                    : 0,
                mobile: {
                    region_iso:
                        customer_detail?.customerDetail?.region_iso_1 ||
                        customer_detail?.customerDetail?.region_iso_2 ||
                        "in",
                    region_code:
                        customer_detail?.customerDetail?.region_code_1 ||
                        customer_detail?.customerDetail?.region_code_2 ||
                        "+91",
                    party_phone_number:
                        customer_detail?.customerDetail?.phone_1 ||
                        customer_detail?.customerDetail?.phone_2,
                    phone_input:
                        "+" +
                        (customer_detail?.customerDetail?.region_code_1 ||
                            customer_detail?.customerDetail?.region_code_2 ||
                            "91") +
                        (customer_detail?.customerDetail?.phone_1 ||
                            customer_detail?.customerDetail?.phone_2),
                },
            });
        }
        const isId = id || true;
        if (isId && isChangePartyId) {
            setBrokerDetail({
                ...brokerDetail,
                broker_id: customer_detail?.customerDetail?.broker_master || "",
                broker_percentage: customer_detail?.customerDetail?.brokerage || "",
                brokerage_on_value: customer_detail?.customerDetail?.brokerage_on_value || "",
            });
            setTransporterDetail({
                ...transporterDetail,
                transport_id: customer_detail?.customerDetail?.transporter_id,
                // transporter_document_number:
                //     customer_detail?.customerDetail?.transporter_document_number,
                // transporter_document_date:
                //     customer_detail?.customerDetail?.transporter_document_date ??
                //     transporterDetail?.transporter_document_date,
                // transporter_vehicle_number:
                //     customer_detail?.customerDetail?.transporter_vehicle_number,
            });
            setOtherDetail({
                ...otherDetail,
                credit_limit: customer_detail?.customerDetail?.credit_limit,
                credit_limit_amount: customer_detail?.customerDetail?.credit_limit_amount,
                creditPeriod:
                    parseFloat(customer_detail?.customerDetail?.credit_limit_period) || null,
                creditPeriodType: customer_detail?.customerDetail?.credit_period_type || 1,
            });
            // setIsChangePartyId(false);
            setSameAsBill(same_as_billing_address);
        }else if (!id && estimateId && isChangePartyId) {
            setBrokerDetail({
                ...brokerDetail,
                broker_id: customer_detail?.customerDetail?.broker_master,
                broker_percentage: customer_detail?.customerDetail?.brokerage,
                brokerage_on_value: customer_detail?.customerDetail?.brokerage_on_value,
            });
            setTransporterDetail({
                ...transporterDetail,
                transport_id: customer_detail?.customerDetail?.transporter_id,
            });
            setOtherDetail({
                ...otherDetail,
                credit_limit: customer_detail?.customerDetail?.credit_limit,
                credit_limit_amount: customer_detail?.customerDetail?.credit_limit_amount,
                creditPeriod:
                parseFloat(customer_detail?.customerDetail?.credit_limit_period) || null,
                creditPeriodType: customer_detail?.customerDetail?.credit_period_type || 1,
            });
            setSameAsBill(same_as_billing_address);
        } else if (!id && !estimateId && !saleId && !isDuplicate) {
            setBrokerDetail({
                ...brokerDetail,
                broker_id: customer_detail?.customerDetail?.broker_master,
                broker_percentage: customer_detail?.customerDetail?.brokerage,
                brokerage_on_value: customer_detail?.customerDetail?.brokerage_on_value,
            });
            setTransporterDetail({
                ...transporterDetail,
                transport_id: customer_detail?.customerDetail?.transporter_id,
            });
            setOtherDetail({
                ...otherDetail,
                credit_limit: customer_detail?.customerDetail?.credit_limit,
                credit_limit_amount: customer_detail?.customerDetail?.credit_limit_amount,
                creditPeriod:
                parseFloat(customer_detail?.customerDetail?.credit_limit_period) || null,
                creditPeriodType: customer_detail?.customerDetail?.credit_period_type || 1,
            });
        } else if (id && isChangePartyId) {
            setBrokerDetail({
                ...brokerDetail,
                broker_id: customer_detail?.customerDetail?.broker_master,
                broker_percentage: customer_detail?.customerDetail?.brokerage,
                brokerage_on_value: customer_detail?.customerDetail?.brokerage_on_value,
            });
            setTransporterDetail({
                ...transporterDetail,
                transport_id: customer_detail?.customerDetail?.transporter_id,
                // transporter_document_number:
                //     customer_detail?.customerDetail?.transporter_document_number,
                // transporter_document_date:
                //     customer_detail?.customerDetail?.transporter_document_date ??
                //     transporterDetail?.transporter_document_date,
                // transporter_vehicle_number:
                //     customer_detail?.customerDetail?.transporter_vehicle_number,
            });
            setOtherDetail({
                ...otherDetail,
                credit_limit: customer_detail?.customerDetail?.credit_limit,
                credit_limit_amount: customer_detail?.customerDetail?.credit_limit_amount,
                creditPeriod:
                    parseFloat(customer_detail?.customerDetail?.credit_limit_period) || null,
                creditPeriodType: customer_detail?.customerDetail?.credit_period_type || 1,
            });
            setIsChangePartyId(false);
            setSameAsBill(same_as_billing_address);
        }
        // setTimeout(() => {
        //     dispatch(partyDetail(""));
        // }, 500);
    }, [ledger?.partyDetail, gstQuote?.party_ledger_id]);

    const handleOpenShippingAddressModel = () => {
        openShippingFromModel();
        setIsShippingAddressModel(true)
    }

    const handleOpenLedger = useCallback(
        id => {
            openLedgerModel();
            if (id) {
                setPartyTitle({
                    name: ledgerModalEditName ?? "Update Ledger",
                    id: id,
                });
                dispatch(fetchLedgerById(id));
            } else {
                setPartyTitle({
                    name: ledgerModalName ?? "Add Ledger",
                    id: "",
                });
            }
            dispatch(getLedgerModelDetail(LedgerType.PARTY_LEDGER));
            setCheckGroupLedgerType("party");
            setIsShippingAddressModel(false)
        },
        [modalType]
    );

    const handleOpenPartyAddress = name => {
        openDispatchAddressModel();
        setDispatchAddressName(name);
        if(partyAddress?.billingAddress?.country_id !== 101){
            setIsInitialDataLoaded(false)
            setIsInitialDataLoaded2(false)
        }
    };

    const handleBrokerChange = e => {
        if (e.value) {
            setBrokerDetail({
                ...brokerDetail,
                broker_id: e.value,
            });
            dispatch(fetchBrokerDetailsById(e.value));
            setisFieldsChanges(true);
        } else {
            setBrokerDetail({
                ...brokerDetail,
                broker_id: null,
                broker_percentage: "",
                brokerage_on_value: "",
            });
        }
    };

    const brokerPercentage = e => {
        if (e.target.value > 100) {
            return;
        }
        setBrokerDetail({
            ...brokerDetail,
            broker_percentage: e.target.value,
        });
    };

    const handleOpenTransportModal = id => {
        openTransportModel();
        if (id !== "") {
            setTransportTitle("edit");
            dispatch(fetchTransportById(id));
        } else {
            setTransportTitle("");
        }
    };

    const CreditType = e => {
        setOtherDetail({ ...otherDetail, creditPeriodType: e.target.value });
    };

    const BrokerModel = id => {
        openBrokerModel();
        dispatch(getBrokerModelDetail());
        if (id !== "") {
            setModalType("edit");
            dispatch(fetchBrokerById(id));
        } else {
            setModalType("");
        }
    };

    const changeOriginalInvoice = e => {
        setIsEditCalculation(false);
        setIsCheckGstType(false);
        setGstQuote({
            ...gstQuote,
            original_inv_no: e.value,
        });
        if (e.value) {
            dispatch(fetchSaleById(e.value));
            dispatch(fetchShippingAddressList(gstQuote?.party_ledger_id, SHIPPING_ADDRESS_TYPE_LIST.SALE, e.value));
        } else {
            clearData({
                setStateFunctions: {
                    setOtherDetail,
                    setTransporterDetail,
                    setBrokerDetail,
                    setItems,
                    setTaxableValue,
                    setMainGrandTotal,
                    setFinalAmount,
                    setGrandTotal,
                    setGstValue,
                    gstCalculation,
                    setGstCalculation,
                },
            });
        }
    };

    const handlePhoneChange = (value, country) => {
        const valueStr = value.toString();
        const dialCodeStr = country.dialCode.toString();
        let number = "";
        if (valueStr.startsWith(dialCodeStr)) {
            number = valueStr.slice(dialCodeStr.length);
        }
        setGstQuote({
            ...gstQuote,
            mobile: {
                region_iso: country.countryCode,
                region_code: country.dialCode,
                party_phone_number: number,
                phone_input: value,
            },
        });
    };

    useEffect(() => {
        if (shippingAddresses?.shippingAddresses?.length > 0) {
            const shippingList = shippingAddresses?.shippingAddresses;
            setShippingAddress(shippingList);
            setIsChangeShippingAddress(prev => prev + 1)
        }
    }, [shippingAddresses?.shippingAddresses]);

    useEffect(() => {
        const shipping_address_id = partyAddress?.shippingAddress?.shipping_address_id;
        const sameAsBilling = partyAddress?.shippingAddress?.same_as_billing_address;

        if (shipping_address_id && !sameAsBilling && shippingAddresses?.shippingAddresses?.length) {
            const addresses = shippingAddresses.shippingAddresses;
            const index = addresses.findIndex(addr => addr.id === shipping_address_id);

            // Set the correct index
            if(index !== -1){
                setSelectShippingAddress(index > -1 ? index : !saveFeature ? 0 : "");
            }

            // Sync partyAddress state with shipping address
            const shipping_address = addresses[index > -1 ? index : 0];
            if (!sameAsBill && shipping_address && index !== -1) {
                setPartyAddress(prev => ({
                    ...prev,
                    shippingAddress: {
                        shipping_address_id: shipping_address?.id,
                        address_1: shipping_address?.address_1,
                        address_2: shipping_address?.address_2,
                        country_id: shipping_address?.country_id,
                        state_id: shipping_address?.state_id,
                        city_id: shipping_address?.city_id,
                        pin_code: shipping_address?.pin_code,
                        shipping_gstin: shipping_address?.shipping_gstin || "",
                        shipping_name: shipping_address?.shipping_name || "",
                        same_as_billing_address: false,
                    },
                }));
            }
        }

        // Same as billing address scenario
        if (sameAsBill) {
            setPartyAddress(prev => ({
                ...prev,
                shippingAddress: {
                    ...prev.shippingAddress,
                    address_1: prev.billingAddress?.address_1,
                    address_2: prev.billingAddress?.address_2,
                    country_id: prev.billingAddress?.country_id,
                    state_id: prev.billingAddress?.state_id,
                    city_id: prev.billingAddress?.city_id,
                    pin_code: prev.billingAddress?.pin_code,
                    shipping_gstin: prev.billingAddress?.shipping_gstin || "",
                    shipping_name: prev.billingAddress?.shipping_name || "",
                    same_as_billing_address: true,
                },
            }));
        }
    }, [
        partyAddress?.shippingAddress?.shipping_address_id,
        shippingAddresses?.shippingAddresses,
        sameAsBill,
    ]);

    useEffect(() => {
        if (gstQuote.party_ledger_id) {
            dispatch(fetchShippingAddressList(gstQuote.party_ledger_id, shipping_address_type, (id || otherId)));
        }
    }, [partyLedgerTrigger, gstQuote?.party_ledger_id]);

    const handleScroll = () => {
        dispatch(fetchPartyList({ skip: partiesOptions.length }));
    };

    const customFilter = searchText => {
        if (!searchText) {
            return;
        }
        if (typingTimeout) {
            clearTimeout(typingTimeout);
        }
        setTypingTimeout(
            setTimeout(() => dispatch(fetchPartyList({ search: searchText })), 500)
        );
    };

    const changeGstNumber = e => {
        const { value } = e.target;
        const upperValue = value.trim().toUpperCase();
        setGstQuote({
            ...gstQuote,
            gstin: upperValue,
        });

        if (upperValue.length === 15 && CheckGstValidate(upperValue)) {
            // dispatch(fetchGstData(upperValue));
            setGSTError(""); // Clear error on valid GSTIN
        } else if (upperValue.length > 0 && upperValue.length !== 15) {
            setGSTError("GSTIN must be exactly 15 characters long");
        } else if (upperValue.length === 0) {
            setGSTError(""); // Clear error if input is cleared
        } else {
            setGSTError("Please enter a valid GSTIN");
        }
    };

    return (
        <div>
            <Row className="justify-content-start mb-2 mx-xl-0">
                <Col sm={12} className="px-xl-0">
                    <h5 className="mb-0">Invoice Details</h5>
                </Col>
                {id && (
                    <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-2 mt-6 me-xl-8">
                        <Form.Group className="position-relative form-floating-group">
                            <Form.Control
                                className="floating-label-input"
                                type="text"
                                required
                                name="invoice_number"
                                placeholder=""
                                // disabled={
                                //     configurationList?.document_prefix
                                //         ?.method_of_voucher_number == 2
                                // }
                                value={invoiceDetail.invoice_number}
                                onChange={e =>
                                    setInvoiceDetail({
                                        ...invoiceDetail,
                                        invoice_number: e.target.value,
                                    })
                                }
                                ref={invoiceRef || incomeCnDnRef}
                            />
                            <Form.Label className="required">
                                {/* Credit Note Number */}
                                {IncomeCreateOrDebit
                                    ? "Credit Note Number"
                                    : "Debit Note Number"}
                            </Form.Label>
                        </Form.Group>
                    </Col>
                )}
                {!id &&
                !configurationList?.document_prefix?.suffix &&
                !configurationList?.document_prefix?.prefix ? (
                    <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-2 mt-6 me-xl-8">
                        <Form.Group className="position-relative form-floating-group">
                            <Form.Control
                                className="floating-label-input"
                                type="text"
                                required
                                name="invoice_number"
                                placeholder=""
                                value={invoiceDetail.invoice_number}
                                onChange={e =>
                                    setInvoiceDetail({
                                        ...invoiceDetail,
                                        invoice_number: e.target.value,
                                    })
                                }
                                disabled={
                                    configurationList?.document_prefix?.method_of_voucher_number ==
                                    2
                                }
                                ref={invoiceRef || incomeCnDnRef}
                            />
                            <Form.Label className="required">
                                {/* Credit Note Number */}
                                {IncomeCreateOrDebit
                                    ? "Credit Note Number"
                                    : IncomeCreateOrDebit === false
                                    ? "Debit Note Number"
                                    : "Credit Note Number"}
                            </Form.Label>
                        </Form.Group>
                    </Col>
                ) : (
                    !id && (
                        <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 me-xl-8 mt-6">
                            <div
                                className={`d-flex form-control border-primary p-0 ${
                                    isFocusedInvoiceNumber ? "shadow" : ""
                                }`}
                                style={{ maxHeight: "34px" }}
                            >
                                {configurationList?.document_prefix?.prefix !== null && (
                                    <div className="start-date bg-light border-right-primary p-2 w-fit-content">
                                        {configurationList?.document_prefix?.prefix}
                                    </div>
                                )}
                                <Form.Group className="position-relative w-100 form-floating-group">
                                    <FormInput
                                        className="floating-label-input form-control px-2 border-0 shadow-none"
                                        placeholder=""
                                        type="text"
                                        value={invoiceDetail.invoice_number}
                                        onChange={e =>
                                            setInvoiceDetail({
                                                ...invoiceDetail,
                                                invoice_number: e.target.value,
                                            })
                                        }
                                        disabled={
                                            configurationList?.document_prefix
                                                ?.method_of_voucher_number == 2
                                        }
                                        ref={invoiceRef || incomeCnDnRef}
                                        onFocus={() => setIsFocusedInvoiceNumber(true)}
                                        onBlur={() => setIsFocusedInvoiceNumber(false)}
                                    />
                                    <Form.Label
                                        style={{
                                            maxWidth: "calc(100% - 20px)",
                                            top: "5px",
                                        }}
                                    >
                                        {IncomeCreateOrDebit
                                            ? "Credit Note Number"
                                            : IncomeCreateOrDebit === false
                                            ? "Debit Note Number"
                                            : "Credit Note Number"}
                                        <span
                                            style={{
                                                color: "red",
                                                paddingLeft: "4px",
                                            }}
                                        >
                                            *
                                        </span>
                                    </Form.Label>
                                </Form.Group>
                                {configurationList?.document_prefix?.suffix && (
                                    <div className="end-date border-left-primary p-2 bg-light w-fit-content">
                                        {configurationList?.document_prefix?.suffix}
                                    </div>
                                )}
                            </div>
                        </Col>
                    )
                )}
                <Col xl={2} md={4} sm={6} className="px-xl-0 mt-6  mb-2 me-xl-8">
                    <Form.Group>
                        <Datepicker
                            value={invoiceDetail?.invoice_date}
                            required={true}
                            placeholder={IncomeCreateOrDebit
                                ? "Credit Note Date"
                                : "Debit Note Date"}
                            onChange={e => handleChangeDate(e, "invoice")}
                            options={true}
                            isPurchase={isPurchase}
                            domId={"datePickerInvoiceDate"}
                        />
                    </Form.Group>
                </Col>
                <Col xl={2} md={2} sm={6} className="px-xl-0 mb-2"></Col>
                <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-2"></Col>
            </Row>

            <DispatchDetails />

            <Row className="justify-content-start mx-xl-0">
                <Col sm={12} className="px-xl-0">
                    <h5 className="mb-3">Party Details</h5>
                </Col>
                <Col
                    xl={2}
                    lg={3}
                    md={4}
                    sm={6}
                    className="px-xl-0 mb-4 form-group-select me-xl-8"
                >
                    <Form.Group>
                        <div className="input-group flex-nowrap">
                            <div className={`position-relative h-40px w-100 pe-36px focus-shadow ${userPermission?.add_ledger_master ? 'pe-36px' : ""}`}>
                                <ReactSelect
                                    customLabel="party"
                                    options={partiesOptions}
                                    value={gstQuote.party_ledger_id || null}
                                    onChange={handleChangeParty}
                                    portal={true}
                                    required={true}
                                    islabel={true}
                                    radius={true}
                                    placeholder="Select Party"
                                    defaultLabel="Select Party"
                                    width="400px"
                                    isEdit={userPermission?.edit_ledger_master}
                                    handleOpen={handleOpenLedger}
                                    isCreatable
                                    onMenuScrollToBottom={handleScroll}
                                    customFilter={customFilter}
                                />
                            </div>
                            {userPermission?.add_ledger_master ?
                            <button
                                type="button"
                                onClick={() => handleOpenLedger("")}
                                className="input-group-text custom-group-text"
                            >
                                <i className="fas fa-plus text-gray-900"></i>
                            </button>
                            : ""}
                        </div>
                    </Form.Group>
                    {gstQuote?.party_ledger_id && (
                        <span style={{ whiteSpace: "nowrap" }}>
                            Current Balance: {company?.company?.currentCurrencySymbol}
                            {gstQuote?.closing_balance ?? 0}
                        </span>
                    )}
                </Col>
                {!isPurchase && configurationList?.header?.is_enabled_phone_number ? (
                    <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                        <PhoneInput
                            country={"in"}
                            value={gstQuote?.mobile?.phone_input || "+91"}
                            onChange={(phone, code) => handlePhoneChange(phone, code)}
                            countryCodeEditable={false}
                            containerClass="w-100"
                            inputClass="w-100 h-40px fw-500 focus-shadow"
                        />
                    </Col>
                ) : (
                    ""
                )}
                <OriginalInvoiceDateDetail
                    originalInvoiceOption={originalInvoiceOption}
                    gstQuote={gstQuote}
                    changeOriginalInvoice={changeOriginalInvoice}
                    handleChangeDate={handleChangeDate}
                />
                {configurationList?.is_gst_applicable ? (
                    <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4">
                        <Form.Group className="position-relative form-floating-group">
                            <FormInput
                                className="floating-label-input"
                                type="text"
                                placeholder=""
                                value={gstQuote.gstin}
                                onChange={changeGstNumber}
                                minLength="15"
                                maxLength="15"
                            />
                            <Form.Label>GSTIN</Form.Label>
                        </Form.Group>
                        {GSTError && (
                            <span className="position-absolute text-danger">{GSTError}</span>
                        )}
                    </Col>
                ) : null}
                <Col xl={2} lg={3} md={4} className="px-xl-0 mb-4"></Col>
            </Row>
            <Row className="justify-content-start mx-xl-0">
                <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                    <div className="dispatch-address-bg h-100 p-3">
                        <Form.Group>
                            <div>
                                <div className="d-flex justify-content-between">
                                    <p className="text-primary">Billing Address</p>
                                    <i
                                        className="fas fa-edit text-primary cursor-pointer"
                                        onClick={() => handleOpenPartyAddress("Billing Address")}
                                    ></i>
                                </div>
                                <div>
                                    <p className="custom-paragraph mb-0">
                                        <>
                                            {partyAddress?.billingAddress?.address_1 &&
                                                `${partyAddress.billingAddress.address_1}, `}
                                            {partyAddress?.billingAddress?.address_2 &&
                                                `${partyAddress.billingAddress.address_2}, `}
                                            {partyAddress?.billingAddress?.city_name &&
                                                `${partyAddress.billingAddress.city_name}, `}
                                            {partyAddress?.billingAddress?.state_name &&
                                                `${partyAddress.billingAddress.state_name}, `}
                                            {prepareDispatchData(partyAddress?.billingAddress)}
                                            {partyAddress?.billingAddress?.country_id && ", "}
                                            {partyAddress?.billingAddress?.pin_code}
                                        </>
                                    </p>
                                </div>
                            </div>
                        </Form.Group>
                    </div>
                </Col>
                <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                    {configurationList?.header?.is_enabled_shipping_address ? (
                        <div className="dispatch-address-bg h-100 p-3">
                            <Form.Group>
                                <div>
                                    <div className="d-flex justify-content-between">
                                        <p className="text-primary">Shipping Address</p>
                                        <i
                                            className="fas fa-edit text-primary cursor-pointer"
                                            onClick={handleOpenShippingAddressModel}
                                        ></i>
                                    </div>
                                    <div>
                                        <p className="custom-paragraph mb-3">
                                            {!sameAsBill &&
                                            shippingAddress[selectShippingAddress] &&
                                            gstQuote?.party_ledger_id ? (
                                                <>
                                                    {`${
                                                        shippingAddress[selectShippingAddress]
                                                            ?.address_1
                                                            ? shippingAddress[selectShippingAddress]
                                                                  ?.address_1 + ","
                                                            : ""
                                                    } ${
                                                        shippingAddress[selectShippingAddress]
                                                            ?.address_2
                                                            ? shippingAddress[selectShippingAddress]
                                                                  ?.address_2 + ","
                                                            : ""
                                                    }
                                                    ${
                                                        shippingAddress[selectShippingAddress]
                                                            ?.city_name
                                                            ? shippingAddress[selectShippingAddress]
                                                                  ?.city_name + ","
                                                            : ""
                                                    }
                                                    ${
                                                        shippingAddress[selectShippingAddress]
                                                            ?.state_name
                                                            ? shippingAddress[selectShippingAddress]
                                                                  ?.state_name + ","
                                                            : ""
                                                    } ${prepareDispatchData(
                                                        shippingAddress[selectShippingAddress]
                                                    )} ${
                                                        shippingAddress[selectShippingAddress]
                                                            ?.pin_code
                                                            ? "," +
                                                              shippingAddress[selectShippingAddress]
                                                                  ?.pin_code
                                                            : ""
                                                    }`}
                                                </>
                                            ) : (
                                                ""
                                            )}
                                        </p>
                                    </div>
                                    <Form.Check
                                        type="checkbox"
                                        label="Same as Billing Address"
                                        value={sameAsBill}
                                        checked={sameAsBill}
                                        onChange={e => setSameAsBill(e.target.checked)}
                                    />
                                </div>
                            </Form.Group>
                        </div>
                    ) : null}
                </Col>
                <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
                <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
                <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
            </Row>

            {configurationList?.header?.is_enabled_broker_details ? (
                <Row className="justify-content-start mx-xl-0">
                    <Col sm={12} className="px-xl-0">
                        <h5 className="mb-3">Broker Details</h5>
                    </Col>
                    <Col
                        xl={2}
                        lg={3}
                        md={4}
                        sm={6}
                        className="px-xl-0 mb-4 form-group-select me-xl-8"
                    >
                        <Form.Group>
                            <div className="input-group flex-nowrap">
                                <div className={`position-relative h-40px w-100 pe-36px focus-shadow ${userPermission?.add_broker_master ? 'pe-36px' : ""}`}>
                                    <ReactSelect
                                        customLabel="party"
                                        options={brokerOptions || []}
                                        placeholder={"Broker Name"}
                                        defaultLabel={"Select Broker"}
                                        islabel={true}
                                        value={brokerDetail.broker_id || null}
                                        onChange={handleBrokerChange}
                                        portal={true}
                                        radius={true}
                                        className="h-40px"
                                        isEdit={userPermission?.edit_broker_master}
                                        handleOpen={BrokerModel}
                                    />
                                </div>
                                {userPermission?.add_broker_master ?
                                <button
                                    type="button"
                                    className="input-group-text custom-group-text"
                                    onClick={() => BrokerModel("")}
                                >
                                    <i className="fas fa-plus text-gray-900"></i>
                                </button>
                                : ''}
                            </div>
                        </Form.Group>
                    </Col>
                    <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                        <Form.Group>
                            <div className="input-group flex-nowrap">
                                <div className="position-relative h-40px w-100 form-floating-group">
                                    <FormInput
                                        className="position-relative floating-label-input"
                                        type="number"
                                        step="0.01"
                                        max={100}
                                        placeholder=""
                                        value={brokerDetail.broker_percentage || ""}
                                        onChange={brokerPercentage}
                                    />
                                    <Form.Label>Brokerage Percentage</Form.Label>
                                </div>
                                <button
                                    className="input-group-text custom-group-text"
                                    type="button"
                                >
                                    <i className="fas fa-percentage text-gray-900"></i>
                                </button>
                            </div>
                        </Form.Group>
                    </Col>
                    <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                        <Form.Group>
                            <div className="input-group flex-nowrap">
                                <div className="position-relative h-40px w-100 focus-shadow">
                                    <ReactSelect
                                        name="brokerageValue"
                                        placeholder="Brokerage on Value"
                                        options={brokerageOption}
                                        value={brokerDetail.brokerage_on_value}
                                        onChange={selectedOption => {
                                            setBrokerDetail({
                                                ...brokerDetail,
                                                brokerage_on_value: selectedOption.value,
                                            })
                                            setisFieldsChanges(true);
                                        }
                                        }
                                    />
                                </div>
                            </div>
                        </Form.Group>
                    </Col>
                    <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
                    <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
                </Row>
            ) : null}

            {configurationList?.header?.is_enabled_transport_details ? (
                <Row className="justify-content-start mx-xl-0">
                    <Col sm={12} className="px-xl-0">
                        <h5 className="mb-3">Transport Details</h5>
                    </Col>
                    <Col
                        xl={2}
                        lg={3}
                        md={4}
                        sm={6}
                        className="px-xl-0 mb-4 form-group-select me-xl-8"
                    >
                        <Form.Group>
                            <div className="input-group flex-nowrap">
                                <div className={`position-relative h-40px w-100 pe-36px focus-shadow ${userPermission?.add_transport_master ? 'pe-36px' : ""}`}>
                                    <ReactSelect
                                        customLabel="party"
                                        options={transportOptions || []}
                                        placeholder={"Transporter Name"}
                                        defaultLabel={"Select Transporter"}
                                        islabel={true}
                                        value={transporterDetail.transport_id || null}
                                        onChange={e => {
                                            setTransporterDetail({
                                                ...transporterDetail,
                                                transport_id: e.value,
                                            });
                                            setisFieldsChanges(true);
                                        }
                                        }
                                        portal={true}
                                        className="h-40px"
                                        radius={true}
                                        isEdit={userPermission?.edit_transport_master}
                                        handleOpen={handleOpenTransportModal}
                                    />
                                </div>
                                {userPermission?.add_transport_master ?
                                <button
                                    type="button"
                                    className="input-group-text custom-group-text"
                                    onClick={() => handleOpenTransportModal("")}
                                >
                                    <i className="fas fa-plus text-gray-900"></i>
                                </button>
                                 : ""}
                            </div>
                        </Form.Group>
                    </Col>
                    <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                        <Form.Group className="position-relative form-floating-group">
                            <DatePickerPaymentDetails
                                value={transporterDetail.transporter_document_date}
                                placeholder={"Document Date"}
                                onChange={e => handleChangeDate(e, "transport")}
                                isTransporter
                            />
                        </Form.Group>
                    </Col>
                    <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                        <Form.Group className="position-relative form-floating-group">
                            <FormInput
                                className="floating-label-input"
                                type="text"
                                placeholder=""
                                value={transporterDetail.transporter_document_number || ""}
                                onChange={e =>
                                    setTransporterDetail({
                                        ...transporterDetail,
                                        transporter_document_number: e.target.value,
                                    })
                                }
                            />
                            <Form.Label>Document Number</Form.Label>
                        </Form.Group>
                    </Col>
                    <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                        <Form.Group className="position-relative form-floating-group">
                            <FormInput
                                className="floating-label-input"
                                type="text"
                                placeholder=""
                                value={transporterDetail.transporter_vehicle_number || ""}
                                onChange={e =>
                                    setTransporterDetail({
                                        ...transporterDetail,
                                        transporter_vehicle_number: e.target.value,
                                    })
                                }
                            />
                            <Form.Label>Vehicle Number</Form.Label>
                        </Form.Group>
                    </Col>
                    <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
                </Row>
            ) : null}

            {configurationList?.header?.is_enabled_eway_details ? (
                <Row className="justify-content-start mx-xl-0">
                    <Col sm={12} className="px-xl-0">
                        <h5 className="mb-3">E-way Bill Details</h5>
                    </Col>
                    <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                        <Form.Group className="position-relative form-floating-group">
                            <FormInput
                                className="floating-label-input"
                                type="text"
                                placeholder=""
                                value={ewayBillDetail.eway_bill_number || ""}
                                onChange={e =>
                                    setEwayBillDetail({
                                        ...ewayBillDetail,
                                        eway_bill_number: e.target.value,
                                    })
                                }
                            />
                            <Form.Label>E-way Bill Number</Form.Label>
                        </Form.Group>
                    </Col>
                    <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                        <Form.Group>
                            <Datepicker
                                value={ewayBillDetail.eway_bill_date}
                                placeholder="E-way Bill Date"
                                onChange={e => handleChangeDate(e, "eway")}
                            />
                        </Form.Group>
                    </Col>
                    <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
                    <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
                    <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
                </Row>
            ) : null}

            <Row className="justify-content-start mx-xl-0">
                {configurationList?.header?.is_enabled_credit_period_details ||
                configurationList?.header?.is_enabled_po_details_of_buyer ? (
                    <Col sm={12} className="px-xl-0">
                        <h5 className="mb-3">Other Details</h5>
                    </Col>
                ) : null}
                {configurationList?.header?.is_enabled_po_details_of_buyer ? (
                    <>
                        <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                            <Form.Group className="position-relative form-floating-group">
                                <FormInput
                                    className="floating-label-input"
                                    type="text"
                                    placeholder=""
                                    value={otherDetail.po_number || ""}
                                    onChange={e =>
                                        setOtherDetail({
                                            ...otherDetail,
                                            po_number: e.target.value,
                                        })
                                    }
                                />
                                <Form.Label>PO Number</Form.Label>
                            </Form.Group>
                        </Col>
                        <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                            <Form.Group className="position-relative form-floating-group">
                                <DatePickerPaymentDetails
                                    value={otherDetail?.date}
                                    onChange={e => handleChangeDate(e, "other")}
                                    placeholder="PO Date"
                                    isTransporter
                                />
                            </Form.Group>
                        </Col>
                    </>
                ) : null}
                {configurationList?.header?.is_enabled_credit_period_details ? (
                    <>
                        <Col xl={2} lg={3} md={4} sm={6} className="px-xl-0 mb-4 me-xl-8">
                            <Form.Group className="position-relative form-floating-group h-34px">
                                <FormInput
                                    className="floating-label-input h-100"
                                    type="number"
                                    placeholder=""
                                    min={0}
                                    value={otherDetail.creditPeriod || ""}
                                    onChange={e =>
                                        setOtherDetail({
                                            ...otherDetail,
                                            creditPeriod: e.target.value,
                                        })
                                    }
                                />
                                <Form.Label
                                    style={{
                                        maxWidth: "calc(100% - 100px)",
                                    }}
                                >
                                    Credit Period
                                </Form.Label>
                                {/* {company?.company?.is_gst_applicable ? ( */}
                                <div
                                    className="w-fit-content bg-light position-absolute credit-dropdown-select focus-shadow input-group d-block"
                                    style={{ minWidth: "90px" }}
                                >
                                    <ReactSelect
                                        defaultValue={1}
                                        options={[
                                            {
                                                label: "Month",
                                                value: 1,
                                            },
                                            {
                                                label: "Day",
                                                value: 2,
                                            },
                                        ]}
                                        placeholder=""
                                        isCreatable={false}
                                        showborder={false}
                                        showbg={true}
                                        height="32px"
                                        value={otherDetail.creditPeriodType}
                                        onChange={selectedOption => {
                                            CreditType({
                                                target: {
                                                    value: selectedOption.value,
                                                },
                                            });
                                            setisFieldsChanges(true);
                                        }}
                                    />
                                </div>
                                {/* ) : (
                                    ""
                                )} */}
                            </Form.Group>
                        </Col>
                    </>
                ) : null}
                <Col xl={2} lg={3} md={4} className="px-xl-0 mb-md-4"></Col>
            </Row>
            <CustomField />
            {isDispatchFromModel && (
                <DispatchFromModal
                    show={isDispatchFromModel}
                    handleClose={closeDispatchFromModel}
                    localDispatchAddress={localDispatchAddress}
                    setLocalDispatchAddress={setLocalDispatchAddress}
                    selectedAddress={selectedAddress}
                    setSelectedAddress={setSelectedAddress}
                />
            )}
            {isShippingFromModel && (
                <ShippingFromModal
                    show={isShippingFromModel}
                    handleClose={closeShippingFromModel}
                    localDispatchAddress={shippingAddress}
                    setLocalDispatchAddress={setShippingAddress}
                    selectedAddress={selectShippingAddress}
                    setSelectedAddress={setSelectShippingAddress}
                    setSelectShippingAddress={setSelectShippingAddress}
                    gstQuote={gstQuote}
                    shipping_address_type={shipping_address_type}
                />
            )}
            {isDispatchAddressModel && (
                <PartyAddressModal
                    show={isDispatchAddressModel}
                    handleClose={closeDispatchAddressModel}
                    name={dispatchAddressName}
                    data={ledger?.partyDetail}
                    partyAddress={partyAddress}
                    setPartyAddress={setPartyAddress}
                    sameAsBill={sameAsBill}
                />
            )}
            {isBrokerModel && (
                <AddBrokerModal
                    show={isBrokerModel}
                    handleClose={closeBrokerModel}
                    setModalType={setModalType}
                    modalType={modalType}
                    broker={broker}
                    entityType={brokerEntityType}
                    setBrokerDetail={setBrokerDetail}
                    isPurchase={isPurchase}
                />
            )}
            {isTransportModel && (
                <AddTransportModal
                    show={isTransportModel}
                    handleClose={closeTransportModel}
                    setModalType={setTransportTitle}
                    modalType={transportTitle}
                    transport={transport}
                    setTransporterDetail={setTransporterDetail}
                />
            )}
            {isLedgerModel && (
                <AddLedgerModal
                    show={isLedgerModel}
                    handleClose={closeLedgerModel}
                    brokerOptions={brokerOptions}
                    ledgerDetailOptions={ledgerDetailOptions}
                    entityType={ledgerEntityType}
                    ledgerGstOption={ledgerGstOption}
                    itemLegder={item?.itemDetail}
                    gstQuote={gstQuote}
                    setGstQuote={setGstQuote}
                    idName="party_ledger_id"
                    id={2}
                    sale={true}
                    name={partyTitle}
                    action={"Customers"}
                    shipping_address_type={shipping_address_type}
                />
            )}
        </div>
    );
};

export default SaleReturnInvoiceDetail;
