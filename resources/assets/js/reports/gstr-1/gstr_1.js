const { set } = require("lodash");
const { list } = require("postcss");

document.addEventListener('DOMContentLoaded', loadGstr1Report);

let gstr1ReportDateRangeFilter;

function loadGstr1Report() {
    validateGstr1OTP();
    loadGstR1MonthDatePicker();
    loadGstR1QuarterlyDatePicker();
    let differenceValue = $(this).val();
    $('.difference-amount').each(function () {
        const differenceAmount = parseFloat($(this).text().replace(/,/g, ''));
        let valueDifference = Math.abs(differenceAmount)
        if (valueDifference <= differenceValue) {
            $(this).removeClass('text-danger');
        }else{
            $(this).addClass('text-danger');
        }
    });
    if (!$('#gstr1ReportDateRange').length) {
        return;
    }


    let date = companyFilter.gstr_1_report_date ? companyFilter.gstr_1_report_date.split(' - ') : '';
    // let searchParams = new URLSearchParams(window.location.search);

    // let start = moment(searchParams.get('start_date') ?? moment(date[0]) ?? moment().startOf('month'));
    // let end = moment(searchParams.get('end_date') ?? moment(date[1]) ?? moment().endOf('month'));

    let start = moment(date[0]) ?? moment().startOf("month");
    let end = moment(date[1]) ?? moment().endOf("month");

    function cb(start, end) {
        let startDate = start.format('YYYY-MM-DD');
        let endDate = end.format('YYYY-MM-DD');
        let dateFilter = startDate + ' - ' + endDate;
        updateCompanyFilter('gstr_1_report_date', dateFilter)
        window.livewire.emit('dateFilter', dateFilter)
        $('#gstr1ReportDateRange').
            html(start.format('MMMM D, YYYY') + ' - ' +
                end.format('MMMM D, YYYY'));
    }

    gstr1ReportDateRangeFilter = $('#gstr1ReportDateRange').
        daterangepicker({
            startDate: start,
            endDate: end,
            minDate: currentFinancialYearStartDate,
            maxDate: currentFinancialYearEndDate,
            showDropdowns: true,
            locale: {
                format: 'DD-MM-YYYY',
            },
            ranges: {
                'This FY': [financialYearStartDate(), financialYearEndDate()],
                'Till Date': [tillStartDate(), tillEndDate()],
                'This Month': [
                    moment().startOf('month'),
                    moment().endOf('month')],
                'Last Month': [
                    moment().subtract(1, 'month').startOf('month'),
                    moment().subtract(1, 'month').endOf('month')],
                'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                'Today': [moment(), moment()],
                'Yesterday': [
                    moment().subtract(1, 'days'),
                    moment().subtract(1, 'days')],
            },
        }, cb);

    cb(start, end);

    gstr1ReportDateRangeFilter.on('apply.daterangepicker', function (ev, picker) {
        let startDate = picker.startDate.format('YYYY-MM-DD');
        let endDate = picker.endDate.format('YYYY-MM-DD');
        let date = startDate + ' - ' + endDate;

        updateCompanyFilter('gstr_1_report_date', date);

        // Build route parameters
        let params = {
            start_date: startDate,
            end_date: endDate,
            filter_type: 3
        };

        // Redirect
        location.href = route('company.reports.gstr-1', params);
    });

    gstr1ReportDateRangeFilter.on('cancel.daterangepicker',
        function (ev, picker) {
            let startDate = cancelFYStartDate;
            let endDate = cancelFYEndDate;
            let date = startDate + ' - ' + endDate
            updateCompanyFilter('gstr_1_report_date', date)
            let params = {
                start_date: startDate,
                end_date: endDate,
                filter_type: 3
            };

            location.href = route('company.reports.gstr-1', params);
        });
}

function loadGstR1MonthDatePicker() {
    let comparativeStartMonth = $("#gstR1StartMonth");

    if (!comparativeStartMonth.length) {
        return;
    }

    let financialDate = companyFilter.gstr_1_report_date
        ? companyFilter.gstr_1_report_date.split(" - ")
        : "";

    let financialYear = companyFilter.current_financial_year
    ? companyFilter.current_financial_year.split(" - ")
    : "";
    let start = moment(financialDate[0]) ?? moment().startOf("month");

    let startYear = financialYear[0] ?? financialYearStartDate().split("/")[2];
    let endYear = financialYear[1] ?? financialYearEndDate().split("/")[2];
    let fyStartDate = new Date(startYear, 3, 1); // April 1, 2024 (Month index 3 = April)
    let fyEndDate = new Date(endYear, 2, 31);  // March 31, 2025 (Month index 2 = March)

    $('.gstr1-start-month').val(start.format('MMM-yyyy'));
    comparativeStartMonth.datepicker({
        format: "M-yyyy",
        startView: "months",
        minViewMode: "months",
        autoclose: true,
        startDate: fyStartDate,
        endDate: fyEndDate,
    }).on('changeDate', function (e) {
        let selectedDate = $(this).datepicker('getDate');

        if (selectedDate) {
            let selectedDate = $(this).datepicker('getDate');

            if (selectedDate) {
                let year = selectedDate.getFullYear();
                let month = selectedDate.getMonth() + 1;

                let startDate = new Date(year, month - 1, 2);
                let endDate = new Date(year, month,1);

                let formattedStartDate = startDate.toISOString().slice(0, 10);
                let formattedEndDate = endDate.toISOString().slice(0, 10);
                let dateRange = formattedStartDate + " - " + formattedEndDate;

                updateCompanyFilter('gstr_1_report_date', dateRange)
                let params = {
                    start_date: formattedStartDate,
                    end_date: formattedEndDate,
                    filter_type: 1
                };

                location.href = route('company.reports.gstr-1', params);
            }
        }
    });
}

function loadGstR1QuarterlyDatePicker() {
    let comparativeStartMonth = $("#gstR1QuarterMonth");

    if (!comparativeStartMonth.length) {
        return;
    }
    let financialYear = companyFilter.gstr_1_report_date
        ? companyFilter.gstr_1_report_date.split(" - ")
        : "";
        let start = moment(financialYear[0]) ?? moment().startOf("month");
        let end = moment(financialYear[1]) ?? moment().endOf("month");
    let startYear = financialYear[0] ?? financialYearStartDate().split("/")[2];
    let endYear = financialYear[1] ?? financialYearEndDate().split("/")[2];
    let fyStartDate = new Date(startYear, 3, 1); // April 1, 2024 (Month index 3 = April)
    let fyEndDate = new Date(endYear, 2, 31);  // March 31, 2025 (Month index 2 = March)
    $('.gstr1-quarter-month').html(start.format('M-yyyy') + ' - ' + end.format('M-yyyy'));
    $.fn.datepicker.dates['qtrs'] = {
        days: ["Sunday", "Moonday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
        daysShort: ["Sun", "Moon", "Tue", "Wed", "Thu", "Fri", "Sat"],
        daysMin: ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"],
        months: ["Q1", "Q2", "Q3", "Q4", "", "", "", "", "", "", "", ""],
        monthsShort: ["Jan&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Feb&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Mar", "Apr&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;May&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Jun", "Jul&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Aug&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sep", "Oct&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Nov&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Dec", "", "", "", "", "", "", "", ""],
        today: "Today",
        clear: "Clear",
        format: "mm/dd/yyyy",
        titleFormat: "MM yyyy",
        weekStart: 0
    };

    comparativeStartMonth.datepicker({
        format: "MM yyyy",
        minViewMode: 1,
        autoclose: true,
        language: "qtrs",
        forceParse: false,
        startDate: fyStartDate,
        endDate: fyEndDate,
    }).on("show", function (event) {
        $(".datepicker table tr td span").css("width", "100%");
        $(".month").each(function (index, element) {
            if (index > 3) $(element).hide();
        });

    }).on("changeDate", function (event) {
        let selectedDate = event.date;
        let month = selectedDate.getMonth();
        let year = selectedDate.getFullYear();
        let startMonth, endMonth;

        if (month == 0) {
            startMonth = 3; // April
            endMonth = 5; // June
        } else if (month == 1) {
            startMonth = 6; // July
            endMonth = 8; // September
        } else if (month == 2) {
            startMonth = 9; // October
            endMonth = 11; // December
        } else {
            startMonth = 0; // January
            endMonth = 2; // March
        }

        let startDate = new Date(year, startMonth, 1);
        let endDate = new Date(year, endMonth + 1, 0); // Last day of the end month

        let formattedStartDate = startDate.toISOString().slice(0, 10);
        let formattedEndDate = endDate.toISOString().slice(0, 10);
        let dateRange = formattedStartDate + " - " + formattedEndDate;
        updateCompanyFilter('gstr_1_report_date', dateRange)
        let params = {
            start_date: formattedStartDate,
            end_date: formattedEndDate,
            filter_type: 2
        };

        location.href = route('company.reports.gstr-1', params);
    });
}

listenChange('#gstR1DateRange', function () {
    let type = $(this).val();

    if (type == 1) { // Months
        $('#gstR1StartMonth').removeClass('d-none');
    } else {
        $('#gstR1StartMonth').addClass('d-none');
    }

    if (type == 2) { // Quarters
        $('#gstR1QuarterMonth').removeClass('d-none');
    } else {
        $('#gstR1QuarterMonth').addClass('d-none');
    }

    if (type == 3) {  // Custom
        $('.gstr1-report-daterange').removeClass('d-none');
        $('#gstr1ReportDateRange').click();
    } else {
        $('.gstr1-report-daterange').addClass('d-none');
    }
})

function handleGstr1Filing(formData) {
    $.ajax({
        type: 'POST',
        url: route('company.reports.file-gstr1'),
        data: formData,
        beforeSend: function () {
            startPageLoader();
        },
        success: function (response) {
            let result = response.data;
            if (response.data.otpISInValid) {
                let gstLogin = response.data.gstLogin
                $('#gstLoginUser').val(gstLogin.username);
                $('#gstLoginUser').attr('readonly',true);
                $('#otpForGstr1').attr('required',true);
                $('.gst-otp').removeClass('d-none');
                $("#loginGSTPortalModal").modal("show");
                displaySuccessMessage(response.message);
            }
            if (response.success) {
                displaySuccessMessage(response.message);
                setTimeout(function () {
                    location.reload();
                }, 1000);

            }
        },
        error: function (error) {
            stopPageLoader();
            displayErrorMessage(error.responseJSON.message);
            setTimeout(function () {
                location.reload();
            }, 1000);
        },
        complete: function () {
            stopPageLoader();
            stopLoader();
            screenUnLock();
        },
    });
}


listenClick('#fileGstR1Button', function () {
    $('#verifyGstr1OTPModal').modal('hide');
    let startDate = $(this).attr('data-start-date');
    let endDate = $(this).attr('data-end-date');
    let formData = {
        'start_date': startDate,
        'end_date': endDate,
        'otp': 123456,
    };
    handleGstr1Filing(formData);
});
listenClick('#fileGstR1EVCButton', function () {
    // $("#congratulationsModel").modal("show");
    $("#verifyGstr1OTPModal").modal("show");
})

// listenClick('#fileGstR1EVCButton', function(){
//     let endDate = $('#evcPanForGstr1Report').val();
//     let formData = {
//         'opt': endDate,
//     };
//     $.ajax({
//         type: 'get',
//         url: route('company.reports.file-gstr1-evc'),
//         data: formData,
//         beforeSend: function () {
//             startPageLoader();
//         },
//         success: function (response) {
//             let result = response.data;
//             console.log(result);
//             if (response.data.success) {
//                 $("#verifyGstr1OTPModal").modal("hide");
//                 displaySuccessMessage(response.message);
//             }
//             if(!result.success){
//                 displayErrorMessage(result.message)
//             }
//         },
//         error: function (error) {
//             stopPageLoader();
//             displayErrorMessage(error.responseJSON.message);
//         },
//         complete: function () {
//             stopPageLoader();
//             stopLoader();
//             screenUnLock();
//         },
//     });
// })

listenSubmit('#storEvcPanNumberAndSendOTP', function(e){
    e.preventDefault();
     let panNumber = $('#evcPanForGstr1Report').val();
     let endDate = $('#evcEndDateForGstr1Report').val();
     let formData = {
        'pan': panNumber,
        'endDate' : endDate
    };
        $.ajax({
        type: 'get',
        url: route('company.reports.file-gstr1-evc'),
        data: formData,
        beforeSend: function () {
            startPageLoader();
        },
        success: function (response) {
            if (response.success) {
                $("#verifyGstr1OTPModal").modal("hide");
                $("#verifyGstr1OTPNumberModal").modal("show");
                displaySuccessMessage(response.message);
            }
            if(!response.success){
                displayErrorMessage(result.message)
            }
        },
        error: function (error) {
            stopPageLoader();
            displayErrorMessage(error.responseJSON.message);
        },
        complete: function () {
            stopPageLoader();
            stopLoader();
            screenUnLock();
        },
    });
})

listenSubmit('#saveEVCOtpForGSTR1Report', function(e){
    e.preventDefault();
    let endDate = $('#evcEndDateForGstr1Report').val();
    let formData = {
        'endDate' : endDate,
        'otp': $('#evcOtpForGstr1Report').val(),
    };
    $.ajax({
        type: 'get',
        url: route('company.reports.file-gstr1-evc-otp'),
        data: formData,
        beforeSend: function () {
            startPageLoader();
        },
        success: function (response) {
            let result = response.data;
            if (response.data.success) {
                $("#verifyGstr1OTPNumberModal").modal("hide");
                $('.ack-num').text(result.result.stdClass.ack_num);
                $("#congratulationsModel").modal("show");
                //  location.reload();
                // setTimeout(function () {

                // }, 2000);
                displaySuccessMessage(response.message);
            }
            if(!result.success){
                displayErrorMessage(result.message)
            }
        },
        error: function (error) {
            stopPageLoader();
            displayErrorMessage(error.responseJSON.message);
        },
        complete: function () {
            stopPageLoader();
            stopLoader();
            screenUnLock();
        },
    });
})

listenSubmit('#otpForGstr1Form', function (e) {
    e.preventDefault();
    let startDate = $('#fileGstR1Button').attr('data-start-date');
    let endDate = $('#fileGstR1Button').attr('data-end-date');
    let otp = $('#otpForGstr1').val();

    let formData = {
        'start_date': startDate,
        'end_date': endDate,
        'otp': otp,
    };

    if (!otp.length) {
        displayErrorMessage('Please enter OTP');
        return false;
    }else if (otp.length !== 6) {
        displayErrorMessage('OTP must be 6 digits');
        return false;
    }


    handleGstr1Filing(formData);
})

// listenHiddenBsModal('#verifyGstr1OTPModal', function () {

//     $('#otpForGstr1Form')[0].reset();
// });

function validateGstr1OTP() {
    $('#otpForGstr1').on('input', function (e) {
        let value = $(this).val();
        $(this).val(value.replace(/\D/g, '').slice(0, 6));
    });
}

listenClick('.gst-portal-login', function(){
    let date = companyFilter.gstr_1_report_date ? companyFilter.gstr_1_report_date.split(' - ') : '';

    $('#GstFileingDate').val(date[1]);
    $("#loginGSTPortalModal").appendTo("body").modal("show");
})

listenSubmit('#loginGSTPortalForm', function(e){
    e.preventDefault();
    let form = $(this).serialize();

    loginGstPortalForm(form)
})

listenClick('.connect-gstin-btn', function () {
    let date = companyFilter.gstr_1_report_date ? companyFilter.gstr_1_report_date.split(' - ') : '';
    $('#GstFileingDate').val(date[1]);
    $("#loginGSTPortalModal").appendTo("body").modal("show");
});

function startGstinModalLoader() {
    $('.connect-gstin-submit-btn').addClass('d-none');
    $('.connect-gstin-spinner').removeClass('d-none');
}

function stopGstinModalLoader() {
    $('.connect-gstin-submit-btn').removeClass('d-none');
    $('.connect-gstin-spinner').addClass('d-none');
}

listenChange('#changeFinancialYear', function () {
    let dateFilter = $('#changeFinancialYear option:selected').val();
    window.livewire.emit('financialYear', dateFilter)
})

listenClick('.view-summary',function(){
    let id = $(this).attr('data-id');
    viewGstSummary(route('company.gstr1-view-summary',{id}));
})

listenClick('.view-gstr3b-summary',function(){
    let id = $(this).attr('data-id');
    viewGstSummary(route('company.gstr3b-view-summary',{id}));
})

function viewGstSummary(url) {
    $.ajax({
        url: url,
        type: 'GET',
        beforeSend: function () {
            startPageLoader();
        },
        success: function (response) {
            let result = response.data;
            $('.append-view-summary').empty().append(result);
            $('#gstr1SummaryModal').appendTo("body").modal("show");
            if (result.success) {
                displaySuccessMessage(result.message);
            }
        },
        error: function (error) {
            stopPageLoader();
            displayErrorMessage(error.responseJSON.message);
        },
        complete: function () {
            stopPageLoader();
            stopLoader();
            screenUnLock();
        },
    });
}

listenClick('.resend-otp-for-gst-login',function(){
    let form = $('#loginGSTPortalForm').serialize()
        form = form  + "&resend=1";

        loginGstPortalForm(form)
})

function loginGstPortalForm(form){
    $.ajax({
        type: 'POST',
        url: route('company.gst.portal.login'),
        data: form  + "&resend=1",
        beforeSend: function () {
            startPageLoader();
        },
        success: function (response) {
            if (response.success) {
                displaySuccessMessage(response.message);
                if(response.data.hideModal){
                    $("#loginGSTPortalModal").modal("hide");
                    $('#gstLoginUser').attr('readonly',false);
                    $('#otpForGstr1').attr('required',false);
                    $('.gst-otp').addClass('d-none');
                    $('.send-gst-otp').removeClass('d-none');
                    location.reload();
                }else{
                    $("#loginGSTPortalModal").modal("show");
                    $('#gstLoginUser').attr('readonly',true);
                    $('#otpForGstr1').attr('required',true);
                    $('.gst-otp').removeClass('d-none');
                    $('.send-gst-otp').addClass('d-none');
                }

            } else {
                displayErrorMessage(response.message);
            }
        },
        error: function (error) {
            stopPageLoader();
            displayErrorMessage(error.responseJSON.message);
        },
        complete: function () {
            stopPageLoader();
            stopLoader();
            screenUnLock();
        },
    });
}

listenClick('.fetch-data-gstr',function(){
    // const $link = $(this);
    // const $icon = $link.find('.fa-repeat');
    // if ($link.hasClass('disabled')) return;
    // $icon.addClass('rotate-animation');
    // $link.addClass('disabled').css('pointer-events', 'none').css('opacity', '0.5');
    $.ajax({
        type: 'GET',
        url: route('company.get-gst-data'),
        beforeSend: function () {
            startPageLoader();
        },
        success: function (response) {
            if (response.success) {
                displaySuccessMessage(response.message);
            }
        },
        error: function (error) {
            stopPageLoader();
            $("#loginGSTPortalModal").appendTo("body").modal("show");
            displayErrorMessage(error.responseJSON.message);
        },
        complete: function () {
            stopPageLoader();
            stopLoader();
            screenUnLock();
        },
    })
})

listenKeyup('.allow-difference-gst',function(){
    let differenceValue = $(this).val();
    $('.difference-amount').each(function () {
        const differenceAmount = parseFloat($(this).text().replace(/,/g, ''));
        let valueDifference = Math.abs(differenceAmount)
        if (valueDifference <= differenceValue) {
            $(this).removeClass('text-danger');
        }else{
            $(this).addClass('text-danger');
        }
    });
})
listenClick('#gstr1Reload', function () {
    let startDate = $(this).attr('data-start-date');
    let endDate = $(this).attr('data-end-date');
    handleGstPortalRequest(
        route('company.reports.gstr-1-reload'),
        { start_date: startDate, end_date: endDate },
        'GSTR-1 data reloaded successfully'
    );
});

listenClick('#resetGstData', function () {
    let startDate = $(this).attr('data-start-date');
    let endDate = $(this).attr('data-end-date');
    handleGstPortalRequest(
        route('company.reset-gst-data-for-portal'),
        { start_date: startDate, end_date: endDate },
        'GSTR-1 data reset successfully'
    );
});


function handleGstPortalRequest(url, data, successMessage) {
    $.ajax({
        type: 'POST',
        url: url,
        data: data,
        beforeSend: function () {
            startPageLoader();
        },
        success: function (response) {
            if (response.success) {
                displaySuccessMessage(response.message || successMessage);
                location.reload();
            }
        },
        error: function (error) {
            stopPageLoader();
            displayErrorMessage(error.responseJSON.message);
            if(error.responseJSON.data?.showModal){
                $("#loginGSTPortalModal").modal("show");
                $('#gstLoginUser').val(error.responseJSON.data.data.username);
                $('#gstLoginUser').attr('readonly', true);
                $('#otpForGstr1').attr('required', true);
                $('.gst-otp').removeClass('d-none');
                $('.send-gst-otp').addClass('d-none');
            }
        },
        complete: function () {
            stopPageLoader();
            stopLoader();
            screenUnLock();
        },
    });
}

listenClick('.resend-otp-link',function(e){
    let endDate = $('#evcEndDateForGstr1Report').val();
    let formData = {
        'endDate' : endDate,
    };
    $.ajax({
        type: 'get',
        url: route('company.resend-verification-otp'),
        data: formData,
        beforeSend: function () {
            startPageLoader();
        },
        success: function (response) {
            let result = response.data;
            if (response.data.success) {
                displaySuccessMessage(response.message);
            }
            if(!result.success){
                displayErrorMessage(result.message)
            }
        },
        error: function (error) {
            stopPageLoader();
            displayErrorMessage(error.responseJSON.message);
        },
        complete: function () {
            stopPageLoader();
            stopLoader();
            screenUnLock();
        },
    });
})
$("#congratulationsModel").on('hidden.bs.modal', function () {
    location.reload(); // reload entire page
});
