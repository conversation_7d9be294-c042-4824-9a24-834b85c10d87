document.addEventListener("DOMContentLoaded", createEditClient);

import <PERSON> from "shepherd.js";
let bankContainerId;
let subscriptionType;

function createEditClient() {
    // let currentURL = $(location).attr("pathname");

    // if (currentURL == "/admin/company-list") {
    //     $("#kt_aside_toggle").click();
    // }

    loadDatePickr();
    bankContainerId = 0;
    editSubscriptionList();
    // webIntroduction()
    loadCompanyDatePicker();
    if ($("#billingAndDispatchAddressSame").is(":checked")) {
        $(".dispatch-address-label").addClass("d-none");
    } else {
        $(".dispatch-address-label").removeClass("d-none");
    }
    if ($('#isGstApplicable').is(":checked")) {
        $('.check-gst-number').attr('required', true);
        $('.gstin-label').addClass('required');
        $('.address1').attr('required', true);
        $('.address1-label').addClass('required');
        $('#billingCountryId').attr('required', true);
        $('.country-label').addClass('required');
        $('.state1').attr('required', true);
        $('.state-label').addClass('required');
        $('.city1').attr('required', true);
        $('.city-label').addClass('required');

        $('.dispatchAddress1').attr('required', true);
        $('.dispatch-address1-label').addClass('required');
        $('.dispatchCountry').attr('required', true);
        $('.dispatch-country-label').addClass('required');
        $('.dispatchState').attr('required', true);
        $('.dispatch-state-label').addClass('required');
        $('.dispatchCity').attr('required', true);
        $('.dispatch-city-label').addClass('required');
    }
    if ($('#isTdsApplicable').is(":checked")) {
        $('.tan-label').addClass('required');
        $('.tan-number').attr('required', true);
    }
    if ($(".check-gst-number").hasClass("d-none")) {
        $(".check-gst-number").removeClass("d-none");
    }

    if ($('#userRegRole').val() == 'consultant') {
        $('#consultantTypeField').removeClass('d-none');
    }

    if ($('#bankDetail').hasClass('show')) {
        $('.bank-name').attr('required', true);
        $('.bank-name-label').addClass('required');
    } else {
        $('.bank-name').attr('required', false);
        $('.bank-name-label').removeClass('required');
    }

    // $(".check-gst-number").trigger("keyup");
}
function webIntroduction() {
    let clientUser = $(".client-user").val();

    const tour = new Shepherd.Tour({
        useModalOverlay: true,

        defaultStepOptions: {
            showCancelLink: true,
        },

        scrollTo: { behavior: 'smooth', block: 'center' }
    });

    tour.on("cancel", webIntroductionUpdate);

    if (clientUser != true) {
        tour.addStep({
            id: "a",
            title: "How to setup your company ?",
            text: "Please click on given button to setup your company.",
            classes: "custom-class",
            attachTo: { element: "#editCompanyProfile", on: "bottom" },
            buttons: [
                { text: "Next", action: tour.next },
                { text: "Finish", action: tour.cancel },
            ],
        });
        tour.addStep({
            id: "b",
            title: "Enter into Your Company",
            text: "Please click on given link, and Enter into Your Company.",
            attachTo: { element: "#enterCompany", on: "bottom" },
            buttons: [
                { text: "Next", action: tour.next },
                { text: "Back", action: tour.back },
                { text: "Finish", action: tour.cancel },
            ],
        });
        tour.addStep({
            id: "c",
            title: "Add Users",
            text: "Please click on button, and add user in your Company.",
            attachTo: { element: "#addUserToIntroduction", on: "right" },
            buttons: [
                { text: "Back", action: tour.back },
                { text: "Finish", action: tour.cancel },
            ],
        });
    }

    if (clientUser == true) {
        tour.addStep({
            id: "b",
            title: "Enter into Your Company",
            text: "Please click on given link, and Enter into Your Company.",
            attachTo: { element: "#enterCompany", on: "bottom" },
            buttons: [{ text: "End", action: tour.cancel }],
        });
    }

    let webIntroduction = $(".web-introduction").val();

    if (webIntroduction) {
        tour.start();
    }
}

function webIntroductionUpdate() {
    $.ajax({
        type: "GET",
        url: route("company.update-web-introduction"),
        success: function (result) { },
    });
}

listenChange("#billingAndDispatchAddressSame", async function () {
    if ($(this).is(":checked")) {
        $('#dispatchAddress1').attr('required', false);
        $('#dispatchAddress2').attr('required', false);
        $('#dispatchStateId').attr('required', false);
        $('#dispatchCityId').attr('required', false);
        $("#collapseOne").collapse("hide");
        $(".billing-and-dispatch-address-same-hide").addClass("d-none");
        $(".dispatch-address-label").addClass("d-none");
    } else {
        $("#collapseOne").collapse("show");
        $(".billing-and-dispatch-address-same-hide").removeClass("d-none");
        // $('#dispatchAddress1').attr('required', true);
        // $('#dispatchAddress2').attr('required', true);
        // $('#dispatchStateId').attr('required', true);
        // $('#dispatchCityId').attr('required', true);
        $("#dispatchAddress1").val("");
        $("#dispatchAddress2").val("");
        $("#dispatchPincode").val("");
        $("#dispatchCountryId option").each(function () {
            if (this.value == $("#billingCountryId").val()) {
                $(this).prop("selected", true);
            }
        });
        await Promise.all([
            appendOptionToStateSelect(
                $("#dispatchStateId"),
                $("#billingCountryId").val()
            ),
        ]);
        $("#dispatchStateId").val("").trigger("change");
        $("#dispatchCityId").val("").trigger("change");
        $(".dispatch-address-label").text(' ').trigger("change");
        $(".dispatch-address-label").removeClass("d-none");
        initSelect2();
    }
});

listenClick("#companyCreateEditForm", function () {
    if ($('#isGstApplicable').is(":checked")) {
        if ($('.check-gst-number').val().trim().length === 0) {
            displayErrorMessage('GSTIN field is required.');
            return false;
        }
        if ($('#dispatchStateId').val() == "") {
            displayErrorMessage('Dispatch State is required.');
            return false;
        }
        if ($('#dispatchCityId').val() == "") {
            displayErrorMessage('Dispatch City is required.');
            return false;
        }
    }
    if ($('#isGstApplicable').is(":checked") && $(".check-gst-number").val().trim().length < 15) {
        displayErrorMessage("The GSTIN field must be at least 15 characters.");
        return false;
    }

    var logo = $('.company-logo')[0].files[0];
    if (logo) {
        var img = new Image();
        img.src = URL.createObjectURL(logo);
        img.onload = function () {
            var width = img.width;
            var height = img.height;
            if (width > 3200 || height > 2100) {
                displayErrorMessage('Please upload an image of dimensions less than 3200 x 2100.');
                return false;
            }
        };
    }

    var signature = $('.company-signature')[0].files[0];
    if (signature) {
        var img = new Image();
        img.src = URL.createObjectURL(signature);
        img.onload = function () {
            var width = img.width;
            var height = img.height;

            if (width > 1300 || height > 800) {
                displayErrorMessage('Please upload an image of dimensions less than 1300 x 800.');
                return false;
            }
        };
    }
});

function loadDatePickr() {
    let bookStartDate = $("#bookStartDate")
        .attr({ autocomplete: "off", placeholder: "dd-mm-yyyy" })
        .flatpickr({
            allowInput: true,
            altFormat: "d-m-Y",
            ariaDateFormat: "d-m-Y",
            dateFormat: "d-m-Y",
            // defaultDate: moment().format('DD-MM-YYYY'),
            parseDate: (datestr, format) => {
                formatDate(datestr, bookStartDate);

                return moment(datestr, "DD-MM-YYYY").toDate();
            },
        });

    listenClick('.book-start-date-button', function () {
        bookStartDate.open();
    })

    let financialStartDate = $("#financialStartDate")
        .attr({ autocomplete: "off", placeholder: "dd-mm-yyyy" })
        .flatpickr({
            allowInput: true,
            altFormat: "d-m-Y",
            ariaDateFormat: "d-m-Y",
            dateFormat: "d-m-Y",
            // defaultDate: moment().format('DD-MM-YYYY'),
            parseDate: (datestr, format) => {
                formatDate(datestr, financialStartDate);

                return moment(datestr, "DD-MM-YYYY").toDate();
            },
        });
    let editTrialExpireDate = $("#editTrialExpireDate")
        .attr({ autocomplete: "off", placeholder: "dd-mm-yyyy" })
        .flatpickr({
            allowInput: true,
            altFormat: "d-m-Y",
            ariaDateFormat: "d-m-Y",
            dateFormat: "d-m-Y",
        });
    let dateOfOnboarding = $("#dateOfOnboarding")
        .attr({ autocomplete: "off", placeholder: "dd-mm-yyyy" })
        .flatpickr({
            allowInput: true,
            altFormat: "d-m-Y",
            ariaDateFormat: "d-m-Y",
            dateFormat: "d-m-Y",
            // defaultDate: moment().format('DD-MM-YYYY'),
            parseDate: (datestr, format) => {
                formatDate(datestr, dateOfOnboarding);

                return moment(datestr, "DD-MM-YYYY").toDate();
            },
        });
    if ($("#billingAndDispatchAddressSame").is(":checked")) {
        $("#dispatchAddress1,#dispatchAddress2,#dispatchPincode").prop(
            "readonly",
            true
        );
    }
}

listenChange("#shippingCountryId", function () {
    $.ajax({
        url: route("states.list"),
        type: "get",
        dataType: "json",
        data: { countryId: $(this).val() },
        success: function (data) {
            $("#shippingStateId").empty();
            $("#shippingStateId").select2({
                placeholder: "Select State",
                allowClear: false,
            });
            $("#shippingStateId").append(
                $('<option value=""></option>').text("Select State")
            );
            $.each(data.data, function (v, i) {
                $("#shippingStateId").append(
                    $("<option></option>").attr("value", i).text(v)
                );
            });
        },
    });
});

listenChange("#billingCountryId", function () {
    $.ajax({
        url: route("states.list"),
        type: "get",
        dataType: "json",
        data: { countryId: $(this).val() },
        success: function (data) {
            $("#billingStateId").empty();
            $("#billingStateId").select2({
                placeholder: "Select State",
                allowClear: false,
            });
            $("#billingStateId").append(
                $('<option value=""></option>').text("Select State")
            );
            $.each(data.data, function (v, i) {
                $("#billingStateId").append(
                    $("<option></option>").attr("value", i).text(v)
                );
            });
        },
    });
});

listenChange("#billingStateId", function () {
    $.ajax({
        url: route("cities.list"),
        type: "get",
        dataType: "json",
        data: {
            stateId: $(this).val(),
            country: $("#billingCountryId").val(),
        },
        success: function (data) {
            $("#billingCityId").empty();
            $("#billingCityId").select2({
                placeholder: "Select City",
                allowClear: false,
            });
            $("#billingCityId").append(
                $('<option value=""></option>').text("Select City")
            );
            $.each(data.data, function (i, v) {
                $("#billingCityId").append(
                    $("<option></option>").attr("value", i).text(v)
                );
            });
        },
    });
});

listenChange("#shippingStateId", function () {
    $.ajax({
        url: route("cities.list"),
        type: "get",
        dataType: "json",
        data: {
            stateId: $(this).val(),
            country: $("#shippingCountryId").val(),
        },
        success: function (data) {
            $("#shippingCityId").empty();
            $("#shippingCityId").select2({
                placeholder: "Select City",
                allowClear: false,
            });
            $.each(data.data, function (i, v) {
                $("#shippingCityId").append(
                    $("<option></option>").attr("value", i).text(v)
                );
            });
        },
    });
});

listenChange("#dispatchCountryId", function () {
    $.ajax({
        url: route("states.list"),
        type: "get",
        dataType: "json",
        data: { countryId: $(this).val() },
        success: function (data) {
            $("#dispatchStateId").empty();
            $("#dispatchStateId").select2({
                placeholder: "Select State",
                allowClear: false,
            });
            $("#dispatchStateId").append(
                $('<option value=""></option>').text("Select State")
            );
            $.each(data.data, function (v, i) {
                $("#dispatchStateId").append(
                    $("<option></option>").attr("value", i).text(v)
                );
            });

            if ($("#isEdit").val() && $("#stateId").val()) {
                $("#dispatchStateId")
                    .val($("#dispatchStateId").val())
                    .trigger("change");
            }
        },
    });
});

listenChange("#dispatchStateId", function () {
    $.ajax({
        url: route("cities.list"),
        type: "get",
        dataType: "json",
        data: {
            stateId: $(this).val(),
            country: $("#dispatchCountryId").val(),
        },
        success: function (data) {
            $("#dispatchCityId").empty();
            $("#dispatchCityId").select2({
                placeholder: "Select City",
                allowClear: false,
            });
            $.each(data.data, function (i, v) {
                $("#dispatchCityId").append(
                    $("<option></option>").attr("value", i).text(v)
                );
            });
        },
    });
});

listenChange("#billingAndDispatchAddressSame", async function () {
    if (this.checked) {
        $("#dispatchAddress1").val($("#billingAddress1").val());
        $("#dispatchAddress2").val($("#billingAddress2").val());
        $("#dispatchPincode").val($("#billingPincode").val());

        $("#dispatchCountryId option").each(function () {
            if (this.value == $("#billingCountryId").val()) {
                $(this).prop("selected", true);
            }
        });

        await Promise.all([
            appendOptionToStateSelect(
                $("#dispatchStateId"),
                $("#billingCountryId").val()
            ),
            appendOptionToCitySelect(
                $("#dispatchCityId"),
                $("#billingStateId").val()
            ),
        ]);

        $("#dispatchStateId option").each(function () {
            if (this.value == $("#billingStateId").val()) {
                $(this).prop("selected", true);
            }
        });

        $("#dispatchCityId option").each(function () {
            if (this.value == $("#billingCityId").val()) {
                $(this).prop("selected", true);
            }
        });
        initSelect2();

        $("#dispatchAddress1,#dispatchAddress2,#dispatchPincode").prop(
            "readonly",
            true
        );
        $(".dispatch-address-label").addClass("d-none");
    } else {
        $("#dispatchAddress1,#dispatchAddress2,#dispatchPincode")
            .val(null)
            .prop("readonly", false);
        $("#dispatchStateId,#dispatchCityId").val(null).trigger("change");
        $("#dispatchCountryId").val(101).trigger("change");
        $(".dispatch-address-label").text(' ').trigger("change");
        $(".dispatch-address-label").removeClass("d-none");
    }
});

listenKeyup("#billingAddress1, #billingAddress2, #billingPincode", function () {
    if ($("#billingAndDispatchAddressSame").is(":checked")) {
        $("#dispatchAddress1").val($("#billingAddress1").val());
        $("#dispatchAddress2").val($("#billingAddress2").val());
        $("#dispatchPincode").val($("#billingPincode").val());

        $("#dispatchAddress1,#dispatchAddress2,#dispatchPincode").prop(
            "readonly",
            true
        );
    }
});

listenChange(
    "#billingCountryId,#billingStateId,#billingCityId",
    async function () {
        if ($("#billingAndDispatchAddressSame").is(":checked")) {
            $("#dispatchCountryId option").each(function () {
                if (this.value == $("#billingCountryId").val()) {
                    $(this).prop("selected", true);
                }
            });
            await Promise.all([
                appendOptionToStateSelect(
                    $("#dispatchStateId"),
                    $("#billingCountryId").val()
                ),
                appendOptionToCitySelect(
                    $("#dispatchCityId"),
                    $("#billingStateId").val()
                ),
            ]);

            $("#dispatchStateId option").each(function () {
                if (this.value == $("#billingStateId").val()) {
                    $(this).prop("selected", true);
                }
            });

            $("#dispatchCityId option").each(function () {
                if (this.value == $("#billingCityId").val()) {
                    $(this).prop("selected", true);
                }
            });
            initSelect2();
        }
    }
);

listenChange("#shippingAndDispatchAddressSame", async function () {
    if (this.checked) {
        $("#dispatchAddress1").val($("#shippingAddress1").val());
        $("#dispatchAddress2").val($("#shippingAddress2").val());
        $("#dispatchPincode").val($("#shippingPincode").val());

        $("#dispatchCountryId option").each(function () {
            if (this.value == $("#shippingCountryId").val()) {
                $(this).prop("selected", true);
            }
        });

        await Promise.all([
            appendOptionToStateSelect(
                $("#dispatchStateId"),
                $("#shippingCountryId").val()
            ),
            appendOptionToCitySelect(
                $("#dispatchCityId"),
                $("#shippingStateId").val()
            ),
        ]);

        $("#dispatchStateId option").each(function () {
            if (this.value == $("#shippingStateId").val()) {
                $(this).prop("selected", true);
            }
        });

        $("#dispatchCityId option").each(function () {
            if (this.value == $("#shippingCityId").val()) {
                $(this).prop("selected", true);
            }
        });

        initSelect2();
        $("#dispatchAddress1,#dispatchAddress2,#dispatchPincode").prop(
            "readonly",
            true
        );
    } else {
        $("#dispatchAddress1,#dispatchAddress2,#dispatchPincode")
            .val(null)
            .prop("readonly", false);
        $("#dispatchStateId,#dispatchCityId").val(null).trigger("change");
        $("#dispatchCountryId").val(101).trigger("change");
    }
});

listenChange("#isGstApplicable", function () {
    if (this.checked) {
        $('.check-gst-number').attr('required', true);
        $('.gstin-label').addClass('required');
        $(".gstin-number,.gst-user-id,.gst-password").removeClass("d-none");
        $(".check-gst-number").attr("disabled", false);
        $("#gstUserId").attr("disabled", false);
        $("#gstPass").attr("disabled", false);
        $('.address1').attr('required', true);
        $('.address1-label').addClass('required');
        $('#billingCountryId').attr('required', true);
        $('.country-label').addClass('required');
        $('.state1').attr('required', true);
        $('.state-label').addClass('required');
        $('.city1').attr('required', true);
        $('.city-label').addClass('required');

        $('.dispatchAddress1').attr('required', true);
        $('.dispatch-address1-label').addClass('required');
        $('.dispatchCountry').attr('required', true);
        $('.dispatch-country-label').addClass('required');
        $('.dispatchState').attr('required', true);
        $('.dispatch-state-label').addClass('required');
        $('.dispatchCity').attr('required', true);
        $('.dispatch-city-label').addClass('required');
    } else {
        $('check-gst-number').attr('required', false);
        $('.gstin-label').removeClass('required');
        $(".gstin-number,.gst-user-id,.gst-password").addClass("d-none");
        $(".check-gst-number").attr("disabled", true);
        $("#gstUserId").attr("disabled", true);
        $("#gstPass").attr("disabled", true);
        $('.address1').attr('required', false);
        $('.address1-label').removeClass('required');
        $('#billingCountryId').attr('required', false);
        $('.country-label').removeClass('required');
        $('.state1').attr('required', false);
        $('.state-label').removeClass('required');
        $('.city1').attr('required', false);
        $('.city-label').removeClass('required');

        $('.dispatchAddress1').attr('required', false);
        $('.dispatch-address1-label').removeClass('required');
        $('.dispatchCountry').attr('required', false);
        $('.dispatch-country-label').removeClass('required');
        $('.dispatchState').attr('required', false);
        $('.dispatch-state-label').removeClass('required');
        $('.dispatchCity').attr('required', false);
        $('.dispatch-city-label').removeClass('required');
    }
});

listenChange("#yesMaintainInventory", function () {
    if (this.checked) {
        $("#yesInventoryWithAccount").val(1).prop("checked", true);
    }
});

listenChange("#noMaintainInventory", function () {
    if (this.checked) {
        $("#noInventoryWithAccount").val(0).prop("checked", true);
    }
});

listenClick("#yesInventoryWithAccount", function () {
    if ($("[name=maintain_inventory]:checked").val() == 0) {
        $("#noInventoryWithAccount").val(0).prop("checked", true);
    }
});

listenChange("#isTdsApplicable", function () {
    if (this.checked) {
        $('.tan-label').addClass('required');
        $('.tan-number').attr('required', true);
        $(".tan-number").removeClass("d-none");
    } else {
        $('.tan-label').removeClass('required');
        $('.tan-number').attr('required', false);
        $('.tan-number').val(null);
        $(".tan-number").addClass("d-none");
    }
});

listenClick(".company-delete-btn", function (event) {
    let recordId = $(this).attr("data-id");
    let url = route("franchise.clients.destroy", { client: recordId });
    deleteItem(url, "Client");
});

listenClick(".same-as-legal-name", function () {
    if (this.checked) {
        $(".company-trade-name").val($(".company-legal-name").val());
        $(".company-trade-name").prop("readonly", true);
    } else {
        $(".company-trade-name").prop("readonly", false);
        $(".company-trade-name").val(null);
    }
});

listenSubmit("#editTaxDetailsForm", function (e) {
    e.preventDefault();
    if ($(".gst-number-is-invalid").val() == 0) {
        displayErrorMessage("GST number is Invalid");
        return false;
    }
    let clientId = $("#editCompanyId").val();
    let action = $(this).attr("action");
    if (!action) {
        action = route("franchise.clients.update", { client: clientId });
    }
    $.ajax({
        url: action,
        type: "POST",
        data: new FormData(this),
        processData: false,
        contentType: false,
        success: function (result) {
            if (result) {
                displaySuccessMessage(result.message);
                setTimeout(function () {
                    if (result.data.company_id != null || result.data.companyList != null) {
                        location.href = route('company.set-company', result.data.company_id);
                    } else {
                        location.href = route("company.companies.index");
                    }
                }, 1000);
            }
        },
        error: function (result) {
            displayErrorMessage(result.responseJSON.message);
        },
    });
});

listenSubmit("#editAddressForm", function (e) {
    e.preventDefault();
    let clientId = $("#editCompanyId").val();
    let action = $(this).attr("action");
    if (!action) {
        action = route("franchise.clients.update", { client: clientId });
    }
    $.ajax({
        url: action,
        type: "POST",
        data: $(this).serialize(),
        success: function (result) {
            if (result.success) {
                displaySuccessMessage(result.message);
                $('.nav-tabs a[href="#bankDetails"]').tab("show");
            }
        },
        error: function (result) {
            displayErrorMessage(result.responseJSON.message);
        },
    });
});

listenSubmit("#editOtherDetailsForm", function (e) {
    e.preventDefault();
    let clientId = $("#editCompanyId").val();
    let action = $(this).attr("action");
    if (!action) {
        action = route("franchise.clients.update", { client: clientId });
    }
    $.ajax({
        url: action,
        type: "POST",
        data: new FormData(this),
        processData: false,
        contentType: false,
        success: function (result) {
            if (result) {
                displaySuccessMessage(result.message);
                setTimeout(function () {
                    location.href = route("company.companies.index");
                }, 1000);
            }
        },
        error: function (result) {
            displayErrorMessage(result.responseJSON.message);
        },
    });
});

listenSubmit("#editCompanyBasicDetailForm", function (e) {
    e.preventDefault();
    let clientId = $("#editCompanyId").val();
    let action = $(this).attr("action");
    if (!action) {
        action = route("franchise.clients.update", { client: clientId });
    }
    $.ajax({
        url: action,
        type: "POST",
        data: $(this).serialize(),
        success: function (result) {
            if (result) {
                displaySuccessMessage(result.message);
                $('.nav-tabs a[href="#taxDetails"]').tab("show");
            }
        },
        error: function (result) {
            displayErrorMessage(result.responseJSON.message);
        },
    });
});

listenSubmit("#addCompanyBank", function (e) {
    e.preventDefault();
    let companyId = $("#editCompanyId").val();
    $.ajax({
        url: route("franchise.add-company-bank", { companyId: companyId }),
        type: "POST",
        data: $(this).serialize(),
        success: function (result) {
            displaySuccessMessage(result.message);
            Livewire.emit("refresh");
            $("#addBankModal").modal("hide");
            $("#addCompanyBank")[0].reset();
        },
        error: function (result) {
            displayErrorMessage(result.responseJSON.message);
        },
    });
});

listenClick(".company-bank-delete-btn", function (event) {
    let recordId = $(this).attr("data-id");
    let url = route("franchise.delete-company-bank", { bankId: recordId });
    deleteItem(url, "Bank");
});

listenClick(".edit-company", function () {
    let bankId = $(this).attr("data-id");
    $.ajax({
        url: route("franchise.edit-company-bank", { bankId: bankId }),
        type: "GET",
        success: function (result) {
            if (result.success) {
                $(".bank-id").val(result.data.id);
                $(".bank-name").val(result.data.bank_name);
                $(".bank-account-number").val(result.data.bank_acc_number);
                $(".bank-branch").val(result.data.bank_branch);
                $(".bank-ifsc-code").val(result.data.ifsc_code);
                $("#bankAccountType")
                    .val(result.data.bank_acc_type)
                    .trigger("change");
                $("#updateCompanyBankModel").appendTo("body").modal("show");
            }
        },
        error: function (result) {
            displayErrorMessage(result.responseJSON.message);
        },
    });
});

listenSubmit("#updateCompanyBank", function (e) {
    e.preventDefault();
    let bankId = $(".bank-id").val();
    $.ajax({
        url: route("franchise.update-company-bank", { bankId: bankId }),
        type: "POST",
        data: $(this).serialize(),
        success: function (result) {
            displaySuccessMessage(result.message);
            livewire.emit("refresh");
            $("#updateCompanyBankModel").modal("hide");
            $("#updateCompanyBank")[0].reset();
        },
        error: function (result) {
            displayErrorMessage(result.responseJSON.message);
        },
    });
});

listenChange(".company-pan-number", function () {
    let inputValues = $(this).val();
    if (inputValues.length >= 4) {
        let character = inputValues.substring(3, 4);
        appendEntityType(character);
    }
    if (inputValues.length == 0 || inputValues.length == null) {
        $(".entity-type").children("div").remove();
    }
    if (inputValues.length >= 4) {
        setTimeout(function () {
            let character = inputValues.substring(3, 4);
            if (character) {
                appendEntityType(character);
            }
        }, 1000);
    }
});

listenKeyup(".company-pan-number", function () {
    $(this).val($(this).val().toUpperCase());
    let inputValues = $(this).val();
    let regex = /[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    if (inputValues.length === 10 && regex.test(inputValues)) {
        $(".pan-number-invalid").addClass("d-none");
        return regex.test(inputValues);
    } else {
        $(".pan-number-invalid").removeClass("d-none");
    }

    if (inputValues.length == 0 || inputValues.length == null) {
        $(".pan-number-invalid").addClass("d-none");
        if ($(".customer-cin-number").length) {
            $(".customer-cin-number").addClass("d-none");
        }
        $(".entity-type").children("div").remove();
    }

    if (inputValues.length >= 4) {
        let character = inputValues.substring(3, 4);
        appendEntityType(character);
    }
    setTimeout(function () {
        let character = $(".company-pan-number").val().substring(3, 4);
        if (character) {
            appendEntityType(character);
        }
    }, 2000);
});

listenChange(".pan-number", function () {
    let inputValues = $(this).val();

    if (inputValues.length >= 4) {
        let character = inputValues.substring(3, 4);
        appendEntityType(character);
    }
    if (inputValues.length == 0 || inputValues.length == null) {
        $(".entity-type").children("div").remove();
    }
    if (inputValues.length >= 4) {
        setTimeout(function () {
            let character = inputValues.substring(3, 4);
            if (character) {
                appendEntityType(character);
            }
        }, 1000);
    }
});

listenKeyup(".pan-number", function () {
    $(this).val($(this).val().toUpperCase());
    let inputValues = $(this).val();
    let regex = /[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    if (inputValues.length === 10 && regex.test(inputValues)) {
        $(".pan-number-invalid").addClass("d-none");
        return regex.test(inputValues);
    } else {
        $(".pan-number-invalid").removeClass("d-none");
    }

    if (inputValues.length == 0 || inputValues.length == null) {
        $(".pan-number-invalid").addClass("d-none");
        if ($(".customer-cin-number").length) {
            $(".customer-cin-number").addClass("d-none");
        }
        $(".entity-type").children("div").remove();
    }

    if (inputValues.length >= 4) {
        let character = inputValues.substring(3, 4);
        appendEntityType(character);
    }
    setTimeout(function () {
        let character = $(".company-pan-number").val().substring(3, 4);
        if (character) {
            appendEntityType(character);
        }
    }, 2000);
});


function appendEntityType(character) {
    $.ajax({
        url: route("get-entity-type"),
        type: "get",
        data: { character: character },
        success: function (result) {
            $(".entity-type").children("div").remove();
            let data = result.data;
            $(".select-entity-type").val(data.entityTypes.id).trigger("change");
            // $.each(data.entityTypes, function (key, value) {
            //     $(".entity-type").children("div").remove();
            //     $(".entity-type").append(data.html);
            //     if ($(".customer-cin-number").length) {
            //         if (value.character == "C") {
            //             $(".customer-cin-number").removeClass("d-none");
            //         } else {
            //             $(".empty-cin-number").val("");
            //             $(".customer-cin-number").addClass("d-none");
            //         }
            //     }
            // });
            // //for selected only Private Limited Company
            // $("input[name='entity_type'][value='5']").prop("checked", true);
            // //for selected only Partnership Firm (F)
            // $("input[name='entity_type'][value='3']").prop("checked", true);
        },
        error: function (error) {
            displayErrorMessage(error.responseJSON.message);
        },
    });
}

listenClick(".company-bank-button", function () {
    $("#addCompanyBank")[0].reset();
    $("#addBankModal").appendTo("body").modal("show");
});

listenChange("#subscriptionType", function () {
    subscriptionType = $(this).val();
    subscriptionList(subscriptionType);
});

function subscriptionList(subscriptionType) {
    if (subscriptionType == 1) {
        $(".subscription-type-month").removeClass("d-none");
        $(".subscription-type-week").addClass("d-none");
    } else if (subscriptionType == 2) {
        $(".subscription-type-week").removeClass("d-none");
        $(".subscription-type-month").addClass("d-none");
    } else {
        $(".subscription-type-week").addClass("d-none");
        $(".subscription-type-month").addClass("d-none");
    }
}

function editSubscriptionList() {
    setTimeout(function () {
        subscriptionType = $("#subscriptionType").val();
        subscriptionList(subscriptionType);
    }, 1000);
}

listenSubmit("#editManagerDetailsForm", function (e) {
    e.preventDefault();
    let clientId = $("#editCompanyId").val();

    $.ajax({
        url: route("franchise.clients.update", { client: clientId }),
        type: "POST",
        data: new FormData(this),
        processData: false,
        contentType: false,
        success: function (result) {
            if (result) {
                displaySuccessMessage(result.message);
            }
        },
        error: function (result) {
            displayErrorMessage(result.responseJSON.message);
        },
    });
});

listenChange(".company-logo", function (e) {
    $(".error-for-image-size-logo").text("");
    let files = e.target.files;
    const fsize = files[0].size;
    const file = Math.round(fsize / 1024);

    if (file >= 2048) {
        $(".error-for-image-size-logo").text(
            "File too Big, please select a file less than 2mb."
        );
    }

    $(".hint-for-image-size-logo").text(
        "Preferred Image Max Size:3200px X 2100px and Maximum Image Size of 2MB."
    );
});
listenChange(".company-signature", function (e) {
    $(".error-for-image-size-signature").text("");
    let files = e.target.files;
    const fsize = files[0].size;
    const file = Math.round(fsize / 1024);

    if (file >= 2048) {
        $(".error-for-image-size-signature").text(
            "File too Big, please select a file less than 2mb."
        );
    }

    $(".hint-for-image-size-signature").text(
        "Preferred Image Max Size:1300px X 800px and Maximum Image Size of 2MB."
    );
});

listenChange("#accountIsExists", function () {
    let isChecked = $(this).is(":checked");
    if (isChecked) {
        $(".alreadyAccount").removeClass("d-none");
        $('#accountId').attr('required', true);
        $(".notAlreadyAccount").addClass("d-none");
    } else {
        $(".notAlreadyAccount").removeClass("d-none");
        $('#accountId').attr('required', false);
        $(".alreadyAccount").addClass("d-none");
    }
});

listenSubmit("#addClientForm", function (e) {
    let isChecked = $("#accountIsExists").is(":checked");
    if (isChecked) {
        if (!$("#accountId").val()) {
            displayErrorMessage("Email field is required.");
            return false;
        }
    } else {
        if (!$("#email").val()) {
            displayErrorMessage("Email field is required.");
            return false;
        }
        if (!$("#password").val()) {
            displayErrorMessage("Password field is required.");
            return false;
        }
        if (!$("#passwordConfirmation").val()) {
            displayErrorMessage("Confirm password field is required.");
            return false;
        }
    }
});

listenChange("#companyStatusFilter", function () {
    window.livewire.emit("subscribeStatusFilter", $(this).val());
});
listenChange("#scheduledDemo", function () {
    window.livewire.emit("scheduledDemoFilter", $(this).val());
});
listenChange("#companyFilter", function () {
    window.livewire.emit("companyStatusFilter", $(this).val());
});
listenChange("#companyRegisterFilter", function () {
    window.livewire.emit("registerFilter", $(this).val());
});

listenClick(".edit-trial-expire-date", function () {
    let accountId = $(this).attr("data-id");
    let date = $(this).attr("data-date");
    $("#editTrialExpireAccountId").val(accountId);
    $("#editTrialExpireDate").val(date).trigger("change");
    // $("#editTrialExpireDate").flatpickr().setDate(date, true, "d-m-Y");
    $("#editTrialExpireDateModal").appendTo("body").modal("show");
});

listenSubmit("#editTrialExpireDateForm", function (e) {
    e.preventDefault();
    let accountId = $("#editTrialExpireAccountId").val();
    let date = $("#editTrialExpireDate").val();
    $.ajax({
        type: "POST",
        url: route("admin.edit-trial-expire-date", accountId),
        data: { date: date },
        success: function (result) {
            if (result.success) {
                window.Livewire.emit("refresh");
                displaySuccessMessage(result.message);
                $("#editTrialExpireDateModal").modal("hide");
            }
        },
        error: function (result) {
            displayErrorMessage(result.responseJSON.message);
        },
    });
});

let companyDateRangeFilter;

function loadCompanyDatePicker() {
    if (!$("#companyDateRangeFilter").length) {
        return;
    }
    let start = moment("2021/04/01");
    let end = moment();
    function cb(start, end) {
        var startDate = start.format("YYYY-MM-DD");
        var endDate = end.format("YYYY-MM-DD");
        var dateFilter = startDate + " - " + endDate;
        window.livewire.emit("dateFilter", dateFilter);
        $("#companyDateRangeFilter").html(
            moment(start).format("MMMM D, YYYY") +
            " - " +
            moment(end).format("MMMM D, YYYY")
        );
    }

    companyDateRangeFilter = $("#companyDateRangeFilter").daterangepicker(
        {
            startDate: start,
            endDate: end,
            locale: {
                format: "DD-MM-YYYY",
            },
            ranges: {
                "From beginning Till Date": [moment("2021/04/01"), moment()],
                "This FY": [financialYearStartDate(), moment()],
                "This Year": [moment().startOf("year"), moment().endOf("year")],
                "This Month": [
                    moment().startOf("month"),
                    moment().endOf("month"),
                ],
                "Last Month": [
                    moment().subtract(1, "month").startOf("month"),
                    moment().subtract(1, "month").endOf("month"),
                ],
                Today: [moment(), moment()],
                Yesterday: [
                    moment().subtract(1, "days"),
                    moment().subtract(1, "days"),
                ],
            },
        },
        cb
    );
    cb(start, end);

    $(".cancelBtn").text("Reset");

    companyDateRangeFilter.on("apply.daterangepicker", function (ev, picker) {
        let startDate = picker.startDate.format("YYYY-MM-DD");
        let endDate = picker.endDate.format("YYYY-MM-DD");
        let date = startDate + " - " + endDate;
        window.livewire.emit("dateFilter", date);
    });
}

listenClick(".company-without-franchise-email-filter", function () {
    let companyEmailFilterValue = $(this).val();
    window.livewire.emit("getEmailFilter", companyEmailFilterValue);
});

listenClick(".delete-company", function () {
    let recordId = $(this).attr("data-id");
    let url = route("company.companies.delete", recordId);
    deleteItem(url, "Company");
});

listenSubmit("#companyCreateForm", function (e) {
    e.preventDefault();
    if (
        $("#isGstApplicable").is(":checked") &&
        $(".check-gst-number").val().trim().length === 0
    ) {
        displayErrorMessage("GSTIN field is required.");

        if ($("#dispatchStateId").val() == "") {
            displayErrorMessage("Dispatch State is required.");
            return false;
        }
        if ($("#dispatchCityId").val() == "") {
            displayErrorMessage("Dispatch City is required.");
            return false;
        }

        return false;
    }
    if ($('#isGstApplicable').is(":checked") && $(".check-gst-number").val().trim().length < 15) {
        displayErrorMessage("The GSTIN field must be at least 15 characters.");
        return false;
    }


    this.submit();
});

listenChange('#userRegRole', function () {
    if ($(this).val() == 'consultant') {
        $('#consultantTypeField').removeClass('d-none');
    } else {
        $('#consultantTypeField').addClass('d-none');
    }
});

listenClick(".leave-company-btn-for-consultant", function () {
    let recordId = $(this).attr("data-id");

    Swal.fire({
        title: "Leave Company !",
        text: 'Are you sure want to leave this company ?',
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#D9214E",
        cancelButtonText: "No, Cancel",
        confirmButtonText: "Yes, Leave",
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: route("consultant.leave.company", {
                    company: recordId,
                }),
                type: "DELETE",
                dataType: "json",
                success: function (obj) {
                    Livewire.emit('refresh');
                    Swal.fire({
                        icon: "success",
                        title: "Leaved Company !",
                        confirmButtonColor: "#009ef7",
                        text: "You have left the company.",
                        timer: 2000,
                    });
                },
                error: function (data) {
                    Swal.fire({
                        title: "",
                        text: data.responseJSON.message,
                        confirmButtonColor: "#009ef7",
                        icon: "error",
                        timer: 5000,
                    });
                },
            });
        }
    });
});

listenClick("#addBillingAddressBtn", function (e) {
    e.preventDefault();
    let billingAddress1 = $(".address1").val();
    let billingAddress2 = $(".address2").val();
    let billingCity = $(".city1").val();
    let billingState = $(".state1").val();
    let billingCountry = $("#billingCountryId").val();
    let billingPinCode = $(".pin-code").val();

    if ($("#isGstApplicable").is(":checked") && billingAddress1 == "") {
        displayErrorMessage("Address Line 1 Field is required");
        return false;
    }
    if ($("#isGstApplicable").is(":checked") && billingState == "") {
        displayErrorMessage("State Field is required");
        return false;
    }
    if ($("#isGstApplicable").is(":checked") && billingCity == "") {
        displayErrorMessage("City Field is required");
        return false;
    }
    $.ajax({
        type: "POST",
        url: route("company.address"),
        data: {
            country: billingCountry,
            state: billingState,
            city: billingCity
        },
        dataType: "json",
        success: function (response) {
            if (response.success) {
                var billingAddress = getFullAddress(billingAddress1, billingAddress2, response.data.city_name, response.data.state_name, response.data.country_name, billingPinCode);
                $(".billing-address-label").html(billingAddress);
                $("#billingAddressModal").modal("hide");
            }
        },
    });
});

// listenClick(".billing-address-edit-btn", function(){
//     $('#billingAddressModal').appendTo('body').modal('show');
// })

listenHiddenBsModal("#dispatchAddressModal", function () {
    if ($("#billingAndDispatchAddressSame:checked").length) {
        let billingAddress1 = $(".address1").val();
        let billingAddress2 = $(".address2").val();
        let billingCity = $(".city1").val();
        let billingState = $(".state1").val();
        let billingCountry = $("#billingCountryId").val();
        let billingPinCode = $(".pin-code").val();

        $(".dispatchAddress1").val(billingAddress1);
        $(".dispatchAddress2").val(billingAddress2);
        setTimeout(function () {
            $(".dispatchCountry").val(billingCountry).trigger("change");
        }, 500);
        setTimeout(function () {
            $("#dispatchStateId").val(billingState).trigger("change");
        }, 1000);
        setTimeout(function () {
            $("#dispatchCityId").val(billingCity).trigger("change");
        }, 2000);
        $("#dispatchPincode").val(billingPinCode);
    }
});

listenClick("#addDispatchAddressBtn", function (e) {
    e.preventDefault();
    let dispatchAddress1 = $(".dispatchAddress1").val();
    let dispatchAddress2 = $(".dispatchAddress2").val();
    let dispatchCity = $("#dispatchCityId").val();
    let dispatchState = $("#dispatchStateId").val();
    let dispatchCountry = $(".dispatchCountry").val();
    let dispatchPinCode = $("#dispatchPincode").val();
    if ($("#isGstApplicable").is(":checked") && dispatchAddress1 == "") {
        displayErrorMessage("Address Line 1 Field is required");
        return false;
    }
    if ($("#isGstApplicable").is(":checked") && dispatchState == "") {
        displayErrorMessage("State Field is required");
        return false;
    }
    if ($("#isGstApplicable").is(":checked") && dispatchCity == "") {
        displayErrorMessage("City Field is required");
        return false;
    }
    $.ajax({
        type: "POST",
        url: route("company.address"),
        data: {
            country: dispatchCountry,
            state: dispatchState,
            city: dispatchCity
        },
        dataType: "json",
        success: function (response) {
            if (response.success) {
                if (!$("#billingAndDispatchAddressSame").is(":checked")) {
                    var dispatchAddress = getFullAddress(dispatchAddress1, dispatchAddress2, response.data.city_name, response.data.state_name, response.data.country_name, dispatchPinCode);
                    $(".dispatch-address-label").html(dispatchAddress);
                }
                $("#dispatchAddressModal").modal("hide");
            }
        },
    });
});

listenClick(".create-custom-field-btn", function () {
    $(".custom-field-name").val("").trigger("change");
    $(".custom-field-is-print").prop("checked", true);
    $("#addCustomFieldModal").appendTo("body").modal("show");
});

listenClick("#addCompanyCustomFieldBtn", function (e) {
    e.preventDefault();
    let length = $(".add-custom-field").length;
    if (length > 4) {
        displayErrorMessage("you can not enter custom field more than 5");
        return false;
    }
    let fieldName = $(".custom-field-name").val();
    let isPrint = $(".custom-field-is-print").is(":checked") ? 1 : 0;
    let printBtn = "";
    if (fieldName == "") {
        displayErrorMessage("Name field is required");
        return false;
    }
    if ($(".custom-field-is-print").is(":checked")) {
        printBtn =
            '<a href="javascript:void(0)"><svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16.4999 6.41732V4.76732C16.4999 3.74055 16.4999 3.22717 16.3001 2.835C16.1243 2.49004 15.8439 2.20957 15.4989 2.03381C15.1067 1.83398 14.5933 1.83398 13.5666 1.83398H8.43325C7.40649 1.83398 6.89311 1.83398 6.50094 2.03381C6.15597 2.20957 5.87551 2.49004 5.69974 2.835C5.49992 3.22717 5.49992 3.74055 5.49992 4.76732V6.41732M5.49992 16.5007C4.64744 16.5007 4.22121 16.5007 3.8715 16.4069C2.9225 16.1527 2.18124 15.4114 1.92696 14.4624C1.83325 14.1127 1.83325 13.6865 1.83325 12.834V10.8173C1.83325 9.27717 1.83325 8.5071 2.13298 7.91884C2.39664 7.4014 2.81733 6.9807 3.33478 6.71705C3.92304 6.41732 4.69311 6.41732 6.23325 6.41732H15.7666C17.3067 6.41732 18.0768 6.41732 18.6651 6.71705C19.1825 6.9807 19.6032 7.4014 19.8669 7.91884C20.1666 8.5071 20.1666 9.27717 20.1666 10.8173V12.834C20.1666 13.6865 20.1666 14.1127 20.0729 14.4624C19.8186 15.4114 19.0773 16.1527 18.1283 16.4069C17.7786 16.5007 17.3524 16.5007 16.4999 16.5007M13.7499 9.62565H16.4999M8.43325 20.1673H13.5666C14.5933 20.1673 15.1067 20.1673 15.4989 19.9675C15.8439 19.7917 16.1243 19.5113 16.3001 19.1663C16.4999 18.7741 16.4999 18.2607 16.4999 17.234V15.7673C16.4999 14.7406 16.4999 14.2272 16.3001 13.835C16.1243 13.49 15.8439 13.2096 15.4989 13.0338C15.1067 12.834 14.5933 12.834 13.5666 12.834H8.43325C7.40649 12.834 6.89311 12.834 6.50094 13.0338C6.15597 13.2096 5.87551 13.49 5.69974 13.835C5.49992 14.2272 5.49992 14.7406 5.49992 15.7673V17.234C5.49992 18.2607 5.49992 18.7741 5.69974 19.1663C5.87551 19.5113 6.15597 19.7917 6.50094 19.9675C6.89311 20.1673 7.40649 20.1673 8.43325 20.1673Z" stroke="black" stroke-width="1.83333" stroke-linecap="round" stroke-linejoin="round" /></svg></a>';
    }
    let content = '<tr class="my-4 d-flex justify-content-between align-items-center" id="new-' + length + '" data-id="' + length + '"><td class="gap-3 d-flex align-items-center"><span><svg width="21" height="14" viewBox="0 0 21 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1.875 7H19.125M1.875 1.25H19.125M1.875 12.75H19.125" stroke="#4D4D52" stroke-width="1.91667" stroke-linecap="round" stroke-linejoin="round" /></svg></span><p class="mb-0 h5 add-custom-field" data-print="' + isPrint + '">' + fieldName + '</p></td><td class="gap-2 d-flex align-items-center">' + printBtn + '<a href="javascript:void(0)" class="delete-custom-field-btn" data-field="new-' + length + '"><i class="fas fs-2 fa-trash-alt text-danger ms-3"></i></a></td></tr>';
    $(".custom-field-collection").append(content);
    $("#addCustomFieldModal").modal("hide");
    $("#customFieldModal").modal("show");
});

listenClick("#saveCustomFieldBtn", function (e) {
    e.preventDefault();
    let fieldArray = [];
    $(".show-custom-fields").html("");
    $(".add-custom-field").each(function () {
        fieldArray.push({
            name: $(this).text(),
            is_printable: $(this).attr("data-print") == '1' ? 1 : 0,
        });
    });
    $(".input-field-array").val(JSON.stringify(fieldArray)).trigger("change");
    $.each(fieldArray, function (key, val) {
        $(".show-custom-fields").append(
            '<div class="col-xl-4 col-sm-6"> <div class="p-3 mb-3 bg-white rounded-2"> <p class="mb-0 fs-5 fw-5">' +
            val.name +
            "</p> </div> </div>"
        );
    });
    $("#customFieldModal").modal("hide");
});

listenClick(".delete-custom-field-btn", function (e) {
    e.preventDefault();
    let fieldId = $(this).attr("data-field");
    $("#" + fieldId).remove();
});

$(document).ready(function () {
    var field = document.getElementsByClassName("sortable");
    if (!field || field instanceof HTMLCollection) {
        return;
    }
    Sortable.create(field, {
        cursor: "move",
        placeholder: "sortable-placeholder",
    });
});

function getFullAddress(address1, address2, city, state, country, pinCode) {
    var addressArray = [];
    if (address1 && address1.trim() !== "") {
        addressArray.push(address1);
    }
    if (address2 && address2.trim() !== "") {
        addressArray.push(address2);
    }
    if (city && city.trim() !== "") {
        addressArray.push(city);
    }
    if (state && state.trim() !== "") {
        addressArray.push(state);
    }
    if (country && country.trim() !== "") {
        addressArray.push(country);
    }
    if (pinCode && pinCode.trim() !== "") {
        addressArray.push(pinCode);
    }
    var fullAddress = addressArray.join(", ");
    return fullAddress;
}
listenClick(".bank-details-accordion", function () {
    setTimeout(function () {
        if ($('#bankDetail').hasClass('show')) {
            $('.bank-name').attr('required', true);
            $('.bank-name-label').addClass('required');
        } else {
            $('.bank-name').attr('required', false);
            $('.bank-name-label').removeClass('required');
        }
    }, 500)
})

listenClick("#planLimitErrorModalBtn", function () {
    $('#planLimitErrorModal').modal('show');
});

$(window).on('load', function () {
    var isPlanLimitError = $('#planLimitErrorModalCheck').val();
    if (isPlanLimitError == 1) {
        $('#planLimitErrorExceedModal').modal('show');
    }
});
