<?php

use App\Http\Controllers\AgeingReportController;
use App\Http\Controllers\API\v2\Gstr2BReconciliation\Gstr2BReconciliationAPIController;
use App\Http\Controllers\AuditTrailController;
use App\Http\Controllers\AutoReminderSettingController;
use App\Http\Controllers\BalanceSheetReportController;
use App\Http\Controllers\BillWiseProfitReportController;
use App\Http\Controllers\BrokerMasterController;
use App\Http\Controllers\BrokerReportController;
use App\Http\Controllers\CashFlowStatementReportController;
use App\Http\Controllers\CCAvenueController;
use App\Http\Controllers\CessRateController;
use App\Http\Controllers\ChequeConfigurationController;
use App\Http\Controllers\ColumnSelectorFieldController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\CompanyDashboardController;
use App\Http\Controllers\CompanyFilterController;
use App\Http\Controllers\CompanyGroupController;
use App\Http\Controllers\CompanySettingController;
use App\Http\Controllers\CompanyShortcutKeyController;
use App\Http\Controllers\CompanySupportController;
use App\Http\Controllers\CompanyTeamManagementController;
use App\Http\Controllers\ConfigurationController;
use App\Http\Controllers\CustomerSummaryReportController;
use App\Http\Controllers\DailyOutstandingReportSettingController;
use App\Http\Controllers\DailyReportSettingController;
use App\Http\Controllers\DayBookReportController;
use App\Http\Controllers\DeliveryChallanTransactionController;
use App\Http\Controllers\EInvoiceController;
use App\Http\Controllers\EstimateQuoteTitleController;
use App\Http\Controllers\EWayBillController;
use App\Http\Controllers\ExpenseCreditNoteTransactionController;
use App\Http\Controllers\ExpenseDebitNoteTransactionController;
use App\Http\Controllers\ExpenseTransactionController;
use App\Http\Controllers\FinancialYearOpeningBalanceController;
use App\Http\Controllers\FreezeTransactionController;
use App\Http\Controllers\GlobalSearchController;
use App\Http\Controllers\GstDashboardController;
use App\Http\Controllers\Gstr1ReportController;
use App\Http\Controllers\Gstr3BDetailedController;
use App\Http\Controllers\Gstr3BSummeryController;
use App\Http\Controllers\GstrLoginController;
use App\Http\Controllers\HsnSummaryInwardReportController;
use App\Http\Controllers\HsnSummaryReportController;
use App\Http\Controllers\IncomeCreditNoteTransactionController;
use App\Http\Controllers\IncomeDebitNoteTransactionController;
use App\Http\Controllers\IncomeEstimateQuoteTransactionController;
use App\Http\Controllers\IncomeTransactionController;
use App\Http\Controllers\InputTaxRegisterReportController;
use App\Http\Controllers\InvoiceLinkController;
use App\Http\Controllers\ItemMasterController;
use App\Http\Controllers\ItemMasterGroupController;
use App\Http\Controllers\ItemStockController;
use App\Http\Controllers\ItemWiseDetailsReportController;
use App\Http\Controllers\ItemWiseProfitReportController;
use App\Http\Controllers\JournalTransactionController;
use App\Http\Controllers\LedgerController;
use App\Http\Controllers\LedgerReportController;
use App\Http\Controllers\NotificationTemplateController;
use App\Http\Controllers\NotificationToSendController;
use App\Http\Controllers\OutputTaxRegisterReportController;
use App\Http\Controllers\OutstandingReportController;
use App\Http\Controllers\PartyWiseSalesPurchaseReportController;
use App\Http\Controllers\PaymentModeController;
use App\Http\Controllers\PaymentTransactionController;
use App\Http\Controllers\PriceListController;
use App\Http\Controllers\PurchaseOrderTitleController;
use App\Http\Controllers\PurchaseOrderTransctionController;
use App\Http\Controllers\PurchaseReportController;
use App\Http\Controllers\PurchaseReturnTransactionController;
use App\Http\Controllers\PurchaseTransactionController;
use App\Http\Controllers\Python\PurchaseOcrResultController;
use App\Http\Controllers\RazorpayController;
use App\Http\Controllers\ReceiptTransactionController;
use App\Http\Controllers\SaleReportController;
use App\Http\Controllers\SalesReturnTransactionController;
use App\Http\Controllers\SaleTransactionController;
use App\Http\Controllers\StockReportController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\SupplierSummaryReportController;
use App\Http\Controllers\TcsStatementController;
use App\Http\Controllers\TdsStatementReportController;
use App\Http\Controllers\ThirdPartyApiKeyController;
use App\Http\Controllers\TradingProfitLossReportController;
use App\Http\Controllers\TransactionJournalController;
use App\Http\Controllers\TransactionPaymentController;
use App\Http\Controllers\TransactionReceiptController;
use App\Http\Controllers\TransportMasterController;
use App\Http\Controllers\TrashController;
use App\Http\Controllers\TrialBalanceController;
use App\Http\Controllers\TutorialController;
use App\Http\Controllers\UnitOfMeasurementController;
use App\Http\Controllers\Whatsapp\WhatsappSettingController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Impersonate Logout
Route::get('/users/impersonate-logout', [CompanyController::class, 'userImpersonateLogout'])->name('impersonate.userLogout');
Route::get('company/get-gst-information-api/{gstNumber}', [CompanyController::class, 'getGstInformation'])
    ->name('company.get-gst-information-api');

Route::get('invoice/{type}/{id}', [InvoiceLinkController::class, 'getInvoice'])->name('invoice-link');
Route::get('download-attachment/{pushNotification}/download', [NotificationToSendController::class, 'downloadAttachment'])->name('push-notification-download-attachment');

Route::get('delete-company', [CompanyController::class, 'deleteYourCompany'])->name('delete-company');
Route::get('current-company-delete', [CompanyController::class, 'currentCompanyDelete'])->name('current-company-delete');

Route::get('reset-password-link', [CompanyController::class, 'resetLink'])->name('reset-link');
Route::middleware('auth', 'role:client_admin|client_user|franchises_admin|franchises_junior_account_executive|franchises_senior_account_executive|franchises_manager|franchises_senior_manager|consultant|consultant_user', 'checkPlanIsExpire', 'checkCompanyAndUserLimit')->group(function () {
    Route::get('company/companies', [CompanyController::class, 'companyList'])->name('company.companies.index')->withoutMiddleware('checkCompanyAndUserLimit');
    Route::get('company/companies/create', [CompanyController::class, 'createCompany'])->name('company.companies.create');
    Route::get('company/companies/update-web-introduction', [CompanyController::class, 'updateWebIntroduction'])->name('company.update-web-introduction');
    Route::post('company/companies/store', [CompanyController::class, 'storeCompany'])->name('company.companies.store');
    Route::post('company/address', [CompanyController::class, 'getCompanyAddress'])->name('company.address');

    Route::get('company/daily-report-setting', [DailyReportSettingController::class, 'index'])->name('company.daily-report-setting.index')->middleware('checkCompanyIsUnderFranchise');
    Route::post('company/get-phone-number-field', [DailyReportSettingController::class, 'getPhoneNumberField'])->name('company.get-phone-number-field');
    Route::post('company/phone-number-for-daily-report/store', [DailyReportSettingController::class, 'storePhoneNumber'])->name('company.phone-number-for-daily-report.store');
    Route::post('company/adjust-time-for-daily-report/store', [DailyReportSettingController::class, 'storeAdjustTime'])->name('company.adjust-time-for-daily-report.store');
    Route::get('company/adjust-time-for-daily-report/get', [DailyReportSettingController::class, 'getAdjustTime'])->name('company.get-adjust-time-for-daily-report');
    Route::delete('company/phone-number-for-daily-report/destroy', [DailyReportSettingController::class, 'deletePhoneNumber'])->name('company.phone-number-for-daily-report.destroy');

    Route::get('company/daily-outstanding-report-setting', [DailyOutstandingReportSettingController::class, 'index'])->name('company.daily-outstanding-report-setting.index')->middleware('checkCompanyIsUnderFranchise');
    Route::post('company/adjust-time-for-daily-outstanding-report/store', [DailyOutstandingReportSettingController::class, 'storeAdjustTime'])->name('company.adjust-time-for-daily-outstanding-report.store');
    Route::get('company/adjust-time-for-daily-outstanding-report/get', [DailyOutstandingReportSettingController::class, 'getAdjustTime'])->name('company.get-adjust-time-for-daily-outstanding-report');
    Route::post('company/phone-number-for-daily-outstanding-report/store', [DailyOutstandingReportSettingController::class, 'storePhoneNumber'])->name('company.phone-number-for-daily-outstanding-report.store');
    Route::delete('company/phone-number-for-daily-outstanding-report/destroy', [DailyOutstandingReportSettingController::class, 'deletePhoneNumber'])->name('company.phone-number-for-daily-outstanding-report.destroy');

    Route::get('company/users', [CompanyTeamManagementController::class, 'usersList'])
        ->name('company.users.index')->middleware('role:client_admin|consultant')->withoutMiddleware('checkCompanyAndUserLimit');
    Route::get('company/users/create', [CompanyTeamManagementController::class, 'createUser'])
        ->name('company.users.create')->middleware('role:client_admin|consultant');
    Route::post('company/users/store', [CompanyTeamManagementController::class, 'storeUser'])
        ->name('company.users.store')->middleware('role:client_admin|consultant');
    Route::get('company/back-to-company', [CompanyController::class, 'backToCompany'])->name('company.back-to-company');

    Route::get('company/subscriptions', [SubscriptionController::class, 'index'])
        ->name('company.subscriptions.index')->middleware(['role:client_admin', 'checkCompanyIsUnderFranchise'])->withoutMiddleware('checkCompanyAndUserLimit');
    Route::get('company/subscriptions/create', [SubscriptionController::class, 'create'])
        ->name('company.subscriptions.create')->middleware('role:client_admin')->withoutMiddleware(['checkPlanIsExpire', 'checkCompanyAndUserLimit']);
    Route::get('company/subscriptions/{plan}/buy-now', [SubscriptionController::class, 'buyNow'])
        ->name('company.subscriptions.buy-now.index')
        ->middleware('role:client_admin')
        ->withoutMiddleware(['checkPlanIsExpire', 'checkCompanyAndUserLimit']);
    Route::post('company/subscriptions/buy-now/referral-code', [SubscriptionController::class, 'getReferralCode'])
        ->name('company.subscriptions.buy-now.referral-code')->withoutMiddleware('checkCompanyAndUserLimit');
    Route::get('company/subscriptions/{plan}/upgrade', [SubscriptionController::class, 'upgradePlan'])
        ->name('company.subscriptions.upgrade.index')
        ->middleware('role:client_admin')
        ->withoutMiddleware(['checkPlanIsExpire', 'checkCompanyAndUserLimit']);

    Route::get('company/thank-you/{id}', [CCAvenueController::class, 'thankYou'])
        ->name('company.thank-you');
    Route::get('company/additional-pay-success/{id}', [CCAvenueController::class, 'additionalPaySuccess'])
        ->name('company.additional-pay-success');
    Route::get('company/wp-thank-you/{id}/{deviceName?}', [CCAvenueController::class, 'wpThankYou'])
        ->name('company.wp-thank-you');
    // Route::post('company/subscriptions/store', [SubscriptionController::class, 'purchaseSubscription'])
    //     ->name('company.subscriptions.store')->middleware('role:client_admin');
    Route::post('company/subscriptions/store', [CCAvenueController::class, 'purchaseSubscription'])
        ->name('company.subscriptions.store')
        ->middleware('role:client_admin')
        ->withoutMiddleware(['checkPlanIsExpire', 'checkCompanyAndUserLimit']);
    Route::get('company/current-plan', [SubscriptionController::class, 'currentPlan'])
        ->name('company.current-plan')->middleware('role:client_admin')->withoutMiddleware('checkCompanyAndUserLimit');
    Route::get('company/{subscriptions}/download-payment-success-pdf', [SubscriptionController::class, 'downloadPaymentSuccessPdf'])
        ->name('company.download-payment-success-pdf')->middleware('role:client_admin')->withoutMiddleware('checkCompanyAndUserLimit');
    // Payment Success Pdf
    Route::get('company/{transaction}/add-on-transaction-pdf', [SubscriptionController::class, 'downloadAddOnTransactionPdf'])
        ->name('company.download-add-on-transaction-pdf')->middleware('role:client_admin')->withoutMiddleware('checkCompanyAndUserLimit');

    Route::middleware('checkUserHavePermission')->group(function () {
        Route::get('company/companies/{company}/edit', [CompanyController::class, 'editCompany'])->name('company.companies.edit');
        Route::delete('company/companies/{company}/delete', [CompanyController::class, 'deleteCompany'])->name('company.companies.delete')->withoutMiddleware('checkCompanyAndUserLimit');
        Route::post('company/companies/{company}', [CompanyController::class, 'updateCompany'])->name('company.companies.update');
        Route::post('company/add-business-category', [CompanyController::class, 'addBusinessCategory'])->name('company.add-business-category');
        Route::get('company/set-company/{company}', [CompanyController::class, 'setCompany'])->name('company.set-company');

        Route::get('company/users/{user}/edit', [CompanyTeamManagementController::class, 'editUser'])
            ->name('company.users.edit')->middleware('role:client_admin|consultant');
        Route::post('company/users/{user}/update', [CompanyTeamManagementController::class, 'updateUser'])
            ->name('company.users.update')->middleware('role:client_admin|consultant');
        Route::delete('company/users/{user}/destroy', [CompanyTeamManagementController::class, 'deleteUser'])
            ->name('company.users.destroy')->middleware('role:client_admin|consultant')->withoutMiddleware('checkCompanyAndUserLimit');
        Route::delete('company/consultant/{consultant}/destroy', [CompanyTeamManagementController::class, 'deleteConsultant'])
            ->name('company.consultant.destroy')->middleware('role:client_admin');
        Route::delete('company/consultant/{user}/destroy', [CompanyTeamManagementController::class, 'deleteConsultantUser'])
            ->name('company.consultant-user.destroy')->middleware('role:consultant');
    });
});

Route::prefix('company')->name('company.')->middleware('auth', 'role:franchises_admin|franchises_junior_account_executive|franchises_senior_account_executive|franchises_manager|franchises_senior_manager|client_admin|client_user|consultant|consultant_user', 'checkCompanyIsSetInSession', 'checkPlanIsExpire', 'checkCompanyAndUserLimit', 'trClosingBal', 'checkCompanyBusinessCategory')->group(function () {
    Route::controller(CompanyDashboardController::class)->group(function () {
        // Route::get('/dashboard', 'index')->name('dashboard')
        //     ->middleware(['permission:company_view_dashboard', 'userLastUsedAt']);
        Route::get('/dashboard', function () {
            return view('company.react-transaction');
        })->name('dashboard')->middleware(['permission:company_view_dashboard', 'userLastUsedAt']);
        Route::get('/whats-new', function () {
            return view('company.react-transaction');
        })->name('dashboard.whats-new')->middleware(['permission:company_view_dashboard', 'userLastUsedAt']);
        Route::get('chart', 'chatData')->name('dashboard-chart')
            ->middleware('permission:company_view_dashboard');
        Route::get('chart-enable-and-disable', 'chartEnableAndDisable')->name('chart-enable-and-disable');
        Route::get('/get-dashboard-button', 'getDashboardButton')->name('get-dashboard-button');
        Route::delete('/delete-dashboard-button', 'destroy')->name('dashboard-button.destroy');

        Route::get('/top-selling-items', function () {
            return view('company.react-transaction');
        })->name('top-selling-items')->middleware(['permission:company_view_dashboard', 'userLastUsedAt']);
        Route::get('/least-selling-items', function () {
            return view('company.react-transaction');
        })->name('least-selling-items')->middleware(['permission:company_view_dashboard', 'userLastUsedAt']);

    });
    Route::get('/low-stock-report', function () {
        return view('company.react-transaction');
    })->name('low-stock-report');

    Route::controller(CompanyTeamManagementController::class)
        ->middleware('role:franchises_admin|franchises_junior_account_executive|franchises_senior_account_executive|franchises_manager|franchises_senior_manager|client_admin|consultant')
        ->group(function () {
            Route::get('get-consultant-data/{input?}', 'getConsultants')->name('get-consultants');
            // Route::get('get-all-consultant-data/', 'getAllConsultants')->name('get-all-consultants');
            Route::get('company-team-management', 'index')->name('company-team-management.index');
            Route::get('company-team-management/create', 'create')->name('company-team-management.create');
            Route::post('company-team-management', 'store')->name('company-team-management.store');
            Route::post('company-team-management/add-consultant', 'storeConsultant')->name('company-team-management.store-consultant');
            // Route::get('company-team-management/{company_team_management}', 'show')->name('company-team-management.show');
            Route::get('company-team-management/{company_team_management}/edit', 'edit')->name('company-team-management.edit');
            Route::match(['put', 'post'], 'company-team-management/{company_team_management}', 'update')->name('company-team-management.update');
            Route::delete('company-team-management/{company_team_management}', 'destroy')->name('company-team-management.destroy');
        });

    Route::controller(AutoReminderSettingController::class)->group(function () {
        Route::get('auto-payment-reminder', 'index')->name('auto-payment-reminder');
        Route::post('auto-payment-reminder', 'store')->name('store-auto-reminder-setting');
        Route::post('update-auto-reminder', 'update')->name('setting.update-auto-reminder');
        Route::post('change-auto-payment-reminder-status', 'changeAutoPaymentReminderStatus')->name('change-auto-payment-reminder-status');
        Route::post('change-auto-payment-reminder-group', 'changeAutoPaymentReminderGroup')->name('change-auto-payment-reminder-group');
    });

    // OCR
    Route::controller(PurchaseOcrResultController::class)->group(function () {
        Route::get('import-documents', 'index')->name('import-documents.index')->middleware('permission:company_view_import_documents_ocr');
        // Ocr react
        Route::get('import-documents/create/{id}', function () {
            return view('company.react-transaction');
        })->name('import-documents.create')->middleware('permission:company_edit_import_documents_ocr');
        // Ocr react
        Route::post('import-documents/store', 'storePdf')->name('import-documents.store')->middleware('permission:company_add_import_documents_ocr');
        Route::delete('import-documents/destroy/{id}', 'destroy')->name('import-documents.destroy')->middleware('permission:company_delete_import_documents_ocr');
        Route::post('bulk-delete-import-documents', 'bulkDeleteImportDocuments')->name('bulk-delete-import-documents')->middleware('permission:company_delete_import_documents_ocr');
        // Bank OCR
        Route::delete('import-bank-documents/destroy/{id}', 'destroyBankDocument')->name('import-bank-documents.destroy');
        // Bank OCR React
        Route::get('import-bank-documents/create/{id}', function () {
            return view('company.react-transaction');
        })->name('import-bank-documents.create');
    });

    Route::controller(CompanySettingController::class)->group(function () {
        // Route::get('setting/{tabName?}', 'index')->name('setting.index');
        // Route::get('/setting', function () {
        //     return view('company.react-transaction');
        // })->name('setting.index')->middleware(['permission:company_view_general_setting|company_view_ewaybill_and_einvoice_setting|company_view_print_setting|company_view_email_and_whatsapp_configuration|company_view_cheque_printing']);

        Route::get('/general-settings', function () {
            return view('company.react-transaction');
        })->name('general-setting.index')->middleware(['permission:company_view_general_setting']);
        Route::get('/ewaybill-einvoice-settings', function () {
            return view('company.react-transaction');
        })->name('ewaybill-einvoice-setting.index')->middleware(['permission:company_view_ewaybill_and_einvoice_setting']);
        Route::get('/print-settings', function () {
            return view('company.react-transaction');
        })->name('print-setting.index')->middleware(['permission:company_view_print_setting']);
        Route::get('/email-and-whatsapp-configuration', function () {
            return view('company.react-transaction');
        })->name('email-and-whatsapp-configuration-setting.index')->middleware(['permission:company_view_email_and_whatsapp_configuration']);
        Route::get('/cheque-printing', function () {
            return view('company.react-transaction');
        })->name('cheque-printing-setting.index')->middleware(['permission:company_view_cheque_printing']);
        Route::get('preview-invoice-pdf-ui', 'previewInvoicePDFUI')->name('preview-invoice-pdf-ui');
        //        Route::post('mail-configuration',  'mailConfiguration')->name('mail-configuration');
        //        Route::get('set-gmail-credentials', 'setGmailCredentials')->name('set-gmail-credentials');
        //        Route::post('whatsapp-configuration', 'whatsappConfiguration')->name('whatsapp-configuration');
        Route::post('fixed-digit-configuration', 'fixedDigitConfiguration')->name('fixed-digit-configuration');
        Route::post('company-credentials', 'credentials')->name('company-credentials');
        Route::post('company-invoice', 'invoice')->name('company-invoice');
        Route::post('company-change-pdf-format', 'updatePdfFormat')->name('update-pdf-format');
        Route::post('company-change-expense-pdf-format', 'updateExpensePdfFormat')->name('update-expense-pdf-format');
        Route::post('company-estimate-invoice', 'estimateInvoice')->name('company-estimate-invoice');
        Route::post('update-company-delivery-challan-header', 'updateDeliveryChallanHeader')->name('update-delivery-challan-header');
        Route::post('update-company-delivery-challan-print-details', 'updateDeliveryChallanDetails')->name('update-delivery-challan-details');
        Route::post('update-company-delivery-challan-footer', 'updateDeliveryChallanFooter')->name('update-delivery-challan-footer');
        Route::get('alternate-phone', 'getAlternatePhone')->name('get-alternate-phone');
        Route::post('alternate-phone', 'alternatePhone')->name('alternate-phone');
        Route::get('alternate-phone-estimate', 'getAlternatePhoneEstimate')->name('get-alternate-phone-estimate');
        Route::get('alternate-phone-delivery-challan', 'getAlternatePhoneDeliveryChallan')->name('get-alternate-phone-delivery-challan');
        Route::post('alternate-phone-estimate', 'alternatePhoneEstimate')->name('alternate-phone-estimate');
        Route::post('alternate-phone-delivery-challan', 'alternatePhoneDeliveryChallan')->name('alternate-phone-delivery-challan');
        Route::get('alternate-email', 'getAlternateEmail')->name('get-alternate-email');
        Route::get('slogan', 'getSlogan')->name('get-slogan');
        Route::post('slogan', 'updateSlogan')->name('slogan');
        Route::get('alternate-email-estimate', 'getAlternateEmailEstimate')->name('get-alternate-email-estimate');
        Route::get('alternate-email-delivery-challan', 'getAlternateEmailDeliveryChallan')->name('get-alternate-email-delivery-challan');
        Route::post('alternate-email', 'alternateEmail')->name('alternate-email');
        Route::post('alternate-email-estimate', 'alternateEmailEstimate')->name('alternate-email-estimate');
        Route::post('alternate-email-delivery-challan', 'alternateEmailDeliveryChallan')->name('alternate-email-delivery-challan');
        Route::get('logo-modal', 'getLogo')->name('get-logo-modal');
        Route::get('logo-modal-estimate', 'getLogoEstimate')->name('get-logo-modal-estimate');
        Route::get('logo-modal-delivery-challan', 'getLogoDeliveryChallan')->name('get-logo-modal-delivery-challan');
        Route::delete('logo-delete', 'deleteLogo')->name('logo-image-remove.destroy');
        Route::delete('logo-delete-estimate', 'deleteLogoEstimate')->name('logo-image-remove-estimate.destroy');
        Route::delete('logo-delete-delivery-challan', 'deleteLogoDeliveryChallan')->name('logo-image-remove-delivery-challan.destroy');
        Route::delete('signature-delete', 'deleteSignature')->name('signature-image-remove.destroy');
        Route::delete('delivery-challan-signature-delete', 'deleteDeliveryChallanSignature')->name('delivery-challan-signature-image-remove.destroy');
        Route::delete('signature-delete-estimate', 'deleteSignatureEstimate')->name('signature-image-remove-estimate.destroy');
        Route::post('logo-modal', 'changeLogo')->name('change-logo-modal');
        Route::post('logo-modal-estimate', 'changeLogoEstimate')->name('change-logo-modal-estimate');
        Route::post('logo-modal-delivery-challan', 'changeLogoDeliveryChallan')->name('change-logo-modal-delivery-challan');
        Route::get('signature-modal', 'getSignature')->name('get-signature-modal');
        Route::get('signature-modal-estimate', 'getSignatureEstimate')->name('get-signature-modal-estimate');
        Route::get('signature-modal-delivery-challan', 'getSignatureDeliveryChallan')->name('get-delivery-challan-signature');
        Route::post('signature-modal', 'changeSignature')->name('change-signature-modal');
        Route::post('signature-modal-estimate', 'changeSignatureEstimate')->name('change-signature-modal-estimate');
        Route::post('signature-modal-delivery-challan', 'changeSignatureDeliveryChallan')->name('change-signature-modal-delivery-challan');
        Route::post('notification-template-configuration', 'notificationTemplateConfiguration')->name('notification-template-configuration');

        Route::post('print-header-setting-update', 'printHeaderSettingUpdate')->name('print-header-setting-update');
        Route::post('print-expense-header-setting-update', 'printExpenseHeaderSettingUpdate')->name('print-expense-header-setting-update');
        Route::post('print-estimate-header-setting-update', 'printEstimateHeaderSettingUpdate')->name('print-estimate-header-setting-update');
        Route::post('print-details-setting-update', 'printDetailsSettingUpdate')->name('print-details-setting-update');
        Route::post('print-estimate-details-setting-update', 'printEstimateDetailsSettingUpdate')->name('print-estimate-details-setting-update');
        Route::post('print-expense-transaction-details-setting-update', 'printExpenseDetailsSettingUpdate')->name('print-expense-transaction-details-setting-update');
        Route::post('print-footer-setting-update', 'printFooterSettingUpdate')->name('print-footer-setting-update');
        Route::post('print-estimate-footer-setting-update', 'printEstimateFooterSettingUpdate')->name('print-estimate-footer-setting-update');
        Route::post('show-print-setting-modal', 'showPrintSettingModal')->name('show-print-setting-modal');
        Route::post('print-expense-footer-setting-update', 'printExpenseFooterSettingUpdate')->name('print-expense-footer-setting-update');

        Route::post('add-custom-field', 'addCustomField')->name('add-custom-field');
        Route::post('company-change-thermal-print-size', 'updateThermalPrintSize')->name('update-thermal-print-size');

        Route::post('thermal-print-header-setting-update', 'thermalPrintHeaderSettingUpdate')->name('thermal-print-header-setting-update');
        Route::post('thermal-print-details-setting-update', 'thermalPrintDetailsSettingUpdate')->name('theraml-print-details-setting-update');
        Route::post('thermal-print-footer-setting-update', 'thermalPrintFooterSettingUpdate')->name('thermal-print-footer-setting-update');

        Route::post('update-invoice-pdf-ui', 'updateInvoicePDFUI')->name('update-invoice-pdf-ui');
        Route::post('update-direct-print', 'updateDirectPrint')->name('update-direct-print');
        Route::post('update-invoice-landscape-ui', 'updateInvoiceLandscapeUI')->name('update-invoice-landscape-ui');
    });

    Route::controller(WhatsappSettingController::class)->group(function () {
        Route::post('instance-details', 'addWhatsappDevice')->name('add-wp-device');
        Route::post('connected-devices', 'connectWpDevice')->name('store-wp-devices');
        Route::post('instance-device-name/{id}', 'updateInstanceDeviceName')->name('update-instance-device-name');
        Route::get('connect-device-wise-configuration/{instanceId}', 'connectDeviceWiseConfiguration')->name('connect-device-wise-configuration');
        Route::delete('remove-whatsapp-device', 'deleteWhatsappDevice')->name('whatsapp-device.destroy');
        Route::get('get-instance-details/{instnace}', 'getInstanceDetails')->name('instance-details');
        Route::get('setting-purchase-whatsapp-credit', 'purchaseWhatsappCredit')->name('purchase-whatsapp-credit');
    });

    Route::controller(WhatsappSettingController::class)->group(function () {
        Route::post('instance-details', 'addWhatsappDevice')->name('add-wp-device');
        Route::post('connected-devices', 'connectWpDevice')->name('store-wp-devices');
        Route::post('instance-device-name/{id}', 'updateInstanceDeviceName')->name('update-instance-device-name');
        Route::get('connect-device-wise-configuration/{instanceId}', 'connectDeviceWiseConfiguration')->name('connect-device-wise-configuration');
        Route::delete('remove-whatsapp-device', 'deleteWhatsappDevice')->name('whatsapp-device.destroy');
        Route::get('get-instance-details/{instnace}', 'getInstanceDetails')->name('instance-details');
        Route::get('setting-purchase-whatsapp-credit', 'purchaseWhatsappCredit')->name('purchase-whatsapp-credit');
    });

    Route::controller(CCAvenueController::class)->group(function () {
        Route::post('purchase-limit', 'purchaseLimit')->name('purchase-limit');
    });

    //NotificationTemplateController
    Route::controller(NotificationTemplateController::class)->group(function () {
        Route::get('company-email-configuration/{notificationTemplate}/edit', 'emailConfiguration')->name('company-email-configuration.edit');
        Route::post('company-email-configuration/{notificationTemplate}', 'updateEmailConfiguration')->name('company-email-configuration.update');
    });

    Route::get('notifications', [NotificationToSendController::class, 'showAllNotifications'])->name('show-all-notification');
    //CompanyGroupController Route
    Route::controller(CompanyGroupController::class)->group(function () {
        Route::get('groups', 'index')->name('groups.index')
            ->middleware('permission:company_view_groups|company_summery_groups_master');
        Route::get('groups/create', 'create')->name('groups.create')
            ->middleware('permission:company_add_new_groups');
        Route::post('groups/store', 'store')->name('groups.store')
            ->middleware('permission:company_add_new_groups');
        Route::get('groups/{group}/edit', 'edit')->name('groups.edit')
            ->middleware('permission:company_edit_groups');
        Route::put('groups/{group}', 'update')->name('groups.update')
            ->middleware('permission:company_edit_groups');
        Route::delete('groups/{group} ', 'destroy')->name('groups.destroy')
            ->middleware('permission:company_delete_groups');
        Route::put('item-groups/{group}', 'updateItemMasterGroup')->name('item-groups.update');
        Route::post('add-item-master-group', 'addItemMasterGroup')->name('add-item-master-group');
    });
    //item-master-group
    Route::controller(ItemMasterGroupController::class)->group(function () {
        Route::get('item-master-groups', 'index')->name('item-master-groups.index');
        Route::get('item-master-group/{group}/edit', 'ItemMasterGroup')->name('item-master-group');
    });

    Route::get('get-gst-tax-percentage/{gstTaxId}', [SaleTransactionController::class, 'getGstRatePercentage'])
        ->name('get-gst-tax-percentage');
    //Ledger Route
    Route::controller(LedgerController::class)->group(function () {
        Route::get('get-profit-loss-sharing-ratio', 'getProfitLossSharingRatio')->name('get-profit-loss-sharing-ratio');
        Route::get('get-holding-ratio', 'getHoldingRatio')->name('get-holding-ratio');
        Route::get('ledgers', 'index')->name('ledgers.index')
            ->middleware('permission:company_view_ledgers|company_summery_ledgers_master');
        // Route::get('ledgers/create', 'create')->name('ledgers.create')
        //     ->middleware('permission:company_add_new_ledgers');
        Route::get('ledgers/create', function () {
            return view('company.react-transaction');
        })->name('ledgers.create')->middleware('permission:company_add_new_ledgers');
        Route::post('ledgers', 'store')->name('ledgers.store')
            ->middleware('permission:company_add_new_ledgers');
        /*Route::get('ledgers/{ledger}','show')->name('ledgers.show')
            ->middleware('permission:company_view_ledgers');*/

        // Route::get('ledgers/{ledger}/edit/{tabName?}', 'edit')->name('ledgers.edit')
        //     ->middleware('permission:company_edit_ledgers');

        Route::get('ledgers/{ledger}/edit', function () {
            return view('company.react-transaction');
        })->name('ledgers.edit')->middleware('permission:company_edit_ledgers');

        Route::post('ledgers/{ledger}', 'update')->name('ledgers.update')
            ->middleware('permission:company_edit_ledgers');
        Route::delete('ledgers/{ledger} ', 'destroy')->name('ledgers.destroy')
            ->middleware('permission:company_delete_ledgers');
        Route::post('import-ledgers', 'importLedger')->name('import-ledger')
            ->middleware('permission:company_import_export_ledgers');
        Route::get('import-ledgers/progress', 'checkImportProgress')->middleware('permission:company_import_export_ledgers')->name('ledger.import.progress');
        Route::get('export-ledger-error', 'exportLedgerError')->name('export-ledger-error')
            ->middleware('permission:company_import_export_ledgers');
        Route::get('export-ledger/{companyId}', 'exportLedger')->name('export-ledger')
            ->middleware('permission:company_import_export_ledgers');
        Route::get('get-company-group/{groupId}', 'getCompanyGroupName')->name('get-company-group');
        Route::get('{ledgerId}/get-company-group/{groupId}', 'getEditCompanyGroupName')->name('get-edit-company-group');
        Route::post('store-location-of-assets', 'storeLocationOfAssets')->name('ledger.store-location-of-assets');
        Route::prefix('ledgers')->name('ledgers.')->group(function () {
            // for delete customer ledger delete media
            Route::get('{ledgerId}/get-old-ledger-data', 'getOldLedgerData')->name('get-old-ledger-data');
            Route::delete('delete-ledger-media/{mediaId}', 'deleteLedgerMedia')->name('delete-ledger-media');
            //for customer ledger bank details
            Route::post('add-ledger-bank/{ledgerId}/{modelType}', 'addCustomerLedgerBank')->name('add-ledger-bank');
            Route::get('edit-ledger-bank/{bankId}/{modelType}', 'editCustomerLedgerBank')->name('edit-ledger-bank');
            Route::post('update-ledger-bank/{bankId}', 'updateCustomerLedgerBank')->name('update-ledger-bank');
            Route::delete('delete-ledger-bank/{bankId}/{modelType}', 'deleteCustomerLedgerBank')->name('delete-ledger-bank');
        });

        Route::get('get-tds-tax-data/{tdsRate}', 'getTdsRate')->name('get-tds-tax-data');
        Route::get('get-tcs-tax-data/{tcsRate}', 'getTcsRate')->name('get-tcs-tax-data');

        Route::get('check-gst-number-unique-validation/{gstNumber}/{ledgerId?}', 'checkGstUniqueNumberValidation')
            ->name('check-gst-number-unique-validation');
        Route::get('check-pan-number-unique-validation/{panNumber}/{ledgerId?}', 'checkPanUniqueNumberValidation')
            ->name('check-pan-number-unique-validation');

        Route::get('get-ledger-closing-balance/{ledger}', 'getClosingBalanceAndType')
            ->name('get-ledger-closing-balance');
        Route::get('check-ledger-closing-balance/{ledger}', 'checkClosingBalanceAndType')
            ->name('check-ledger-closing-balance');

        Route::post('item-income-store', 'incomeLedger')->name('item-income-store');
        Route::post('item-expense-store', 'expenseLedger')->name('item-expense-store');
        Route::post('bulk-delete-ledger-master', 'bulkDeleteLedger')->name('bulk-delete-ledger-master');
    });

    //Broker Master
    Route::controller(BrokerMasterController::class)->group(function () {
        Route::get('broker-master', 'index')->name('broker-master.index')
            ->middleware('permission:company_view_broker_masters|company_summery_broker_masters');
        Route::get('broker-master/create', 'create')->name('broker-master.create')
            ->middleware('permission:company_add_new_broker_masters');
        Route::post('broker-master', 'store')->name('broker-master.store')
            ->middleware('permission:company_add_new_broker_masters');
        Route::get('broker-master/{broker_master}/edit', 'edit')->name('broker-master.edit')
            ->middleware('permission:company_edit_broker_masters');
        Route::put('broker-master/{broker_master}', 'update')->name('broker-master.update')
            ->middleware('permission:company_edit_broker_masters');
        /*Route::get('broker-master/{broker_master}','show')->name('broker-master.show')
            ->middleware('permission:company_view_broker_masters');*/
        Route::delete('broker-master/{broker_master} ', 'destroy')->name('broker-master.destroy')
            ->middleware('permission:company_delete_broker_masters');
        Route::get('get-broker-details/{brokerId}', 'getBrokerDetails')->name('get-broker-details');
        Route::get('check-unique-gst-pan/{gstPan}/{type}/{brokerId?}', 'checkUniqueGstPan')
            ->name('broker-check-unique-gst-pan');
    });

    //Item Master
    Route::controller(ItemMasterController::class)->group(function () {
        Route::get('item-masters', 'index')->name('item-masters.index')
            ->middleware('permission:company_view_item_masters|company_summery_item_masters');
        Route::get('item-masters/create', function () {
            return view('company.react-transaction');
        })->name('item-masters.create')->middleware('permission:company_add_new_item_masters');
        // Route::get('item-masters/create', 'create')->name('item-masters.create')
        //     ->middleware('permission:company_add_new_item_masters');
        Route::post('item-masters', 'store')->name('item-masters.store')
            ->middleware('permission:company_add_new_item_masters');
        // Route::get('item-masters/{item_master}/edit', 'edit')->name('item-masters.edit')
        //     ->middleware('permission:company_edit_item_masters');
        Route::get('item-masters/{item_master}/edit', function () {
            return view('company.react-transaction');
        })->name('item-masters.edit')->middleware('permission:company_edit_item_masters');
        Route::put('item-masters/{item_master}', 'update')->name('item-masters.update')
            ->middleware('permission:company_edit_item_masters');
        /*Route::get('item-masters/{item_master}','show')->name('item-masters.show')
            ->middleware('permission:company_view_item_masters');*/
        Route::delete('item-masters/{item_master} ', 'destroy')->name('item-masters.destroy')
            ->middleware('permission:company_delete_item_masters');
        Route::get('get-item-type/{itemType}', 'getItemTypeName')->name('get-item-type');
        Route::get('get-edit-item-type/{itemType}/{itemMaster}', 'getEditItemTypeName')->name('get-edit-item-type');
        Route::get('get-item-master-groups', 'getCompanyItemMasterGroups')->name('get-item-master-groups');
        Route::get('get-item-good-quantity/{itemId}', 'getItemGoodQuantity')->name('get-item-good-quantity');
        Route::get('gst-tax-value/{gstTaxId}', 'getGstTaxValue')->name('gst-tax-value');
        Route::post('import-item', 'importItem')->name('import-item');
        Route::get('export-items/{companyId}', 'exportItems')->name('export-item');
        Route::get('get-item-closing-stock/{itemMaster}', 'getItemClosingStock')->name('get-item-closing-stock');
        Route::post('bulk-delete-item-master', 'bulkDeleteItemMaster')->name('bulk-delete-item-master');
        // ->middleware('permission:company_import_export_income');
        // Route::get('print-barcode1', 'printBarcodeIndex')->name('print-barcode.index');
        Route::get('get-items-details-for-barcode', 'getItemsDetailsForBarcode')->name('get-items-details-for-barcode');
        Route::post('get-items-barcode', 'getItemsBarcode')->name('get-items-barcode');
        Route::get('get-item-from-item-sku/{itemSku}', 'getItemFromItemSku')->name('get-item-from-item-sku');

        Route::get('/print-barcode', function () {
            return view('company.react-transaction');
        })->name('print-barcode.index');
    });

    Route::controller(PriceListController::class)->group(function () {
        Route::get('price-list', 'index')->name('price-list.index');
        Route::get('price-list/create', 'create')->name('price-list.create');
        Route::post('price-list', 'store')->name('price-list.store');
        Route::get('price-list/{price_list}/edit', 'edit')->name('price-list.edit');
        Route::put('price-list/{price_list}/update', 'update')->name('price-list.update');
        Route::delete('price-list/{priceList}', 'destroy')->name('price-list.delete');
        Route::post('price-list/import', 'importPriceList')->name('price-list.import');
    });

    Route::controller(UnitOfMeasurementController::class)->group(function () {
        Route::get('unit-of-measurements', 'index')->name('unit-of-measurement.index');
        Route::post('unit-of-measurements', 'store')->name('unit-of-measurement.store');
        Route::get('unit-of-measurements/{unit_of_measurement}/edit', 'edit')->name('unit-of-measurement.edit');
        Route::put('unit-of-measurements/{unit_of_measurement}', 'update')->name('unit-of-measurement.update');
        Route::delete('unit-of-measurements/{unit_of_measurement}', 'destroy')->name('unit-of-measurement.delete');
    });

    Route::controller(PaymentModeController::class)->group(function () {
        Route::post('payment-mode', 'store')->name('payment-mode.store');
        Route::delete('payment-mode/{paymentMode})', 'destroy')->name('payment-mode.delete');
    });

    //Transport Master
    Route::controller(TransportMasterController::class)->group(function () {
        Route::get('transport-master', 'index')->name('transport-master.index')
            ->middleware('permission:company_view_transport_masters|company_summery_transport_masters');
        Route::get('transport-master/create', 'create')->name('transport-master.create')
            ->middleware('permission:company_add_new_transport_masters');
        Route::post('transport-master', 'store')->name('transport-master.store')
            ->middleware('permission:company_add_new_transport_masters');
        Route::get('transport-master/{transport_master}/edit', 'edit')->name('transport-master.edit')
            ->middleware('permission:company_edit_transport_masters');
        Route::put('transport-master/{transport_master}', 'update')->name('transport-master.update')
            ->middleware('permission:company_edit_transport_masters');
        /* Route::get('transport-master/{transport_master}','show')->name('transport-master.show')
             ->middleware('permission:company_view_transport_masters'); */
        Route::delete('transport-master/{transport_master} ', 'destroy')->name('transport-master.destroy')
            ->middleware('permission:company_delete_transport_masters');
        Route::get('check-unique-gst/{gst}/{transportId}', 'checkUniqueGst')->name('transport-check-unique-gst');
    });

    //Expense Transaction
    Route::controller(ExpenseTransactionController::class)->group(function () {
        Route::get('expense-transaction', 'index')->name('expense-transaction.index')
            ->middleware('permission:company_view_transaction_masters|company_summery_transaction_masters');
        Route::post('expense-transaction', 'purchaseUpdate')->name('expense-transaction.update')
            ->middleware('permission:company_edit_transaction_masters');
        Route::post('expense-return-transaction', 'purchaseReturnUpdate')->name('expense-return-transaction.update')
            ->middleware('permission:company_edit_transaction_masters');
        Route::post('expense-debit-note-transaction', 'purchaseDebitNoteUpdate')
            ->name('expense-debit-note-transaction.update')
            ->middleware('permission:company_edit_transaction_masters');
        Route::post('expense-credit-note-transaction', 'purchaseCreditNoteUpdate')
            ->name('expense-credit-note-transaction.update')
            ->middleware('permission:company_edit_transaction_masters');
        Route::post('expense-purchase-order-transaction', 'purchaseOrderUpdate')
            ->name('purchase-order-transaction.update')
            ->middleware('permission:company_edit_transaction_masters');
    });
    //Receipt Transaction
    Route::controller(ReceiptTransactionController::class)->group(function () {
        Route::get('receipt-transaction', 'index')->name('receipt-transaction.index')
            ->middleware('permission:company_view_transaction_masters|company_summery_transaction_masters');
        Route::post('receipt-transaction', 'update')->name('receipt-transaction.update')
            ->middleware('permission:company_edit_transaction_masters');
        Route::get('receipt-transaction-preview/{receiptTransactionPreviewId}', 'getReceiptTransactionPreviewDetail')
            ->name('receipt-transaction-preview');
    });
    //Payment Transaction
    Route::controller(PaymentTransactionController::class)->group(function () {
        Route::get('payment-transaction', 'index')->name('payment-transaction.index')
            ->middleware('permission:company_view_transaction_masters|company_summery_transaction_masters');
        Route::post('payment-transaction', 'update')->name('payment-transaction.update')
            ->middleware('permission:company_edit_transaction_masters');
        Route::get('payment-transaction-preview/{paymentTransactionPreviewId}', 'getPaymentTransactionPreviewDetail')
            ->name('payment-transaction-preview');
        Route::get('payment-transaction/check-advance-payment-settle/{transactionId}', 'advancePaymentSettle')->name('payment-transaction.check-advance-payment-settle');
    });
    //Journal Transaction
    Route::controller(JournalTransactionController::class)->group(function () {
        Route::get('journal-transaction', 'index')->name('journal-transaction.index')
            ->middleware('permission:company_view_transaction_masters|company_summery_transaction_masters');
        Route::post('journal-transaction', 'update')->name('journal-transaction.update')
            ->middleware('permission:company_edit_transaction_masters');
    });
    //Income Transaction
    Route::controller(IncomeTransactionController::class)->group(function () {
        Route::get('income-transaction', 'index')->name('income-transaction.index')
            ->middleware('permission:company_view_transaction_masters|company_summery_transaction_masters');
        Route::post('income-transaction', 'incomeUpdate')->name('income-transaction.update')
            ->middleware('permission:company_edit_transaction_masters');
        Route::post('income-return-transaction', 'incomeReturnUpdate')->name('income-return-transaction.update')
            ->middleware('permission:company_edit_transaction_masters');
        Route::post('income-debit-note-transaction', 'incomeDebitNoteUpdate')->name('income-debit-note-transaction.update')
            ->middleware('permission:company_edit_transaction_masters');
        Route::post('income-credit-note-transaction', 'incomeCreditNoteUpdate')->name('income-credit-note-transaction.update')
            ->middleware('permission:company_edit_transaction_masters');
        Route::post('income-estimate-quote-transaction', 'incomeEstimateQuoteUpdate')->name('income-estimate-quote-transaction.update')
            ->middleware('permission:company_edit_transaction_masters');
        Route::post('delivery-challan', 'deliveryChallanUpdate')->name('delivery-challan.update')
            ->middleware('permission:company_edit_transaction_masters');
    });
    //Cess rate
    Route::controller(CessRateController::class)->group(function () {
        // Route::get('cess-rates', 'index')->name('cess-rates.index')->middleware('permission:company_view_cess_rate|company_summery_cess_rate');
        /* Route::get('cess-rates/create','create')->name('cess-rates.create')
             ->middleware('permission:company_add_new_cess_rate'); */
        Route::post('cess-rates', 'store')->name('cess-rates.store')
            ->middleware('permission:company_add_new_cess_rate');
        Route::get('cess-rates/{cess_rate}/edit', 'edit')->name('cess-rates.edit')
            ->middleware('permission:company_edit_cess_rate');
        Route::put('cess-rates/{cess_rate}', 'update')->name('cess-rates.update')
            ->middleware('permission:company_edit_cess_rate');
        /* Route::get('cess-rates/{cess_rate}','show')->name('cess-rates.show')
            ->middleware('permission:company_view_cess_rate'); */
        Route::delete('cess-rates/{cess_rate} ', 'destroy')->name('cess-rates.destroy')
            ->middleware('permission:company_delete_cess_rate');
    });

    //      Journal Transaction
    Route::controller(TransactionJournalController::class)->group(function () {
        Route::get('transaction-journal', 'index')
            ->name('transaction-journal.index')
            ->middleware(['permission:company_view_journal|company_summery_journal_transactions', 'userLastUsedAt']);
        Route::get('transaction-journal/create', 'create')
            ->name('transaction-journal.create')
            ->middleware('permission:company_add_new_journal');
        Route::post('transaction-journal', 'store')
            ->name('transaction-journal.store')
            ->middleware('permission:company_add_new_journal');
        Route::get('transaction-journal/{transaction_journal}/edit', 'edit')
            ->name('transaction-journal.edit')
            ->middleware('permission:company_edit_journal');
        Route::put('transaction-journal/{transaction_journal}', 'update')
            ->name('transaction-journal.update')
            ->middleware('permission:company_edit_journal');
        Route::get('transaction-journal/{transaction_journal}', 'show')
            ->name('transaction-journal.show')
            ->middleware('permission:company_view_journal');
        Route::delete('transaction-journal/{transaction_journal}', 'destroy')
            ->name('transaction-journal.destroy')
            ->middleware('permission:company_delete_journal');
        Route::post('transaction-journal/{transaction_journal}', 'update')
            ->name('company.transaction-journal.update')
            ->middleware('permission:company_edit_journal');
        Route::get('transaction-journal-pdf-preview/{journalPdfPreviewId}', 'getJournalPdfPreview')
            ->name('transaction-journal-pdf-preview');
        Route::get('transaction-journal-preview-pdf/{journalPdfPreviewId}', 'getJournalPdfPreviewDownload')
            ->name('transaction-journal-preview-pdf');
        Route::get('transaction-journal-pdf/{journalPdfDownloadId}/{isView?}', 'exportJournalTransactionPdf')
            ->name('transaction-journal-pdf-download')
            ->middleware('permission:company_import_export_journal');
        Route::get('export-journal-transaction', 'export')->name('export-journal-transaction')
            ->middleware('permission:company_import_export_journal');
        Route::post('import-journal-transaction', 'importJournalTransaction')
            ->name('import-journal-transaction')
            ->middleware('permission:company_import_export_journal');
        Route::post('journal-delete', 'bulkDelete')->name('journal-delete');
        Route::get('journal-download/{journal}/download', 'downloadAttachment')
            ->name('journal.download-attachment');
        Route::post('journal-bulk-download', 'journalBulkDownload')->name('journal-bulk-download');
    });
    Route::get('get-ledger-model-details-journal/{ledgerId}', [TransactionJournalController::class, 'getCreditLedgerModelDetails'])
        ->name('transaction-journal.get-ledger-model-details-journal');
    Route::get('get-ledger-model-details-debit-journal/{ledgerId}', [TransactionJournalController::class, 'getDebitLedgerModelDetails'])
        ->name('transaction-journal.get-debit-ledger-model-details-journal');

    //Transaction Payment
    Route::controller(TransactionPaymentController::class)->group(function () {
        Route::get('transaction-payment', 'index')
            ->name('transaction-payment.index')
            ->middleware(['permission:company_view_payment|company_summery_payment_transactions', 'userLastUsedAt']);
        Route::get('transaction-payment/create', 'create')
            ->name('transaction-payment.create')
            ->middleware('permission:company_add_new_payment');
        Route::post('transaction-payment', 'store')
            ->name('transaction-payment.store')
            ->middleware('permission:company_add_new_payment');
        Route::get('transaction-payment/{transaction_payment}/edit', 'edit')
            ->name('transaction-payment.edit')
            ->middleware('permission:company_edit_payment');
        Route::get('transaction-payment/{transaction_payment}', 'show')
            ->name('transaction-payment.show')
            ->middleware('permission:company_view_payment');
        Route::delete('transaction-payment/{transaction_payment} ', 'destroy')
            ->name('transaction-payment.destroy')
            ->middleware('permission:company_delete_payment');
        Route::get('transaction-payment-pdf-preview/{paymentPdfPreviewId}', 'getPaymentPdfPreview')
            ->name('transaction-payment-pdf-preview');
        Route::get('transaction-payment-preview-pdf-preview/{paymentPdfPreviewId}', 'getPaymentPdfPreviewDownload')
            ->name('transaction-payment-preview-pdf-preview');
        Route::get('transaction-payment-pdf/{paymentPdfDownloadId}/{isView?}', 'exportPaymentTransactionPdf')
            ->name('transaction-payment-pdf-download')
            ->middleware('permission:company_import_export_payment');
        Route::post('transaction-payment/{transaction_payment}', 'update')
            ->name('transaction-payment.update')
            ->middleware('permission:company_edit_payment');
        Route::get('get-payment-ledger-model-details/{ledgerId}', 'getLedgerModelDetails')
            ->name('transaction-payment.get-ledger-model-details');
        Route::get('transaction-payment-email/{paymentMailId}', 'paymentMail')
            ->name('transaction-payment-email');
        Route::post('payment-send-email/{paymentSendMailId}', 'sendMail')
            ->name('payment-send-email');
        Route::get('export-payment-transaction', 'export')
            ->name('export-payment-transaction')
            ->middleware('permission:company_import_export_payment');
        Route::post('import-payment-transaction', 'importPaymentTransaction')
            ->name('import-payment-transaction')
            ->middleware('permission:company_import_export_payment');
        Route::post('payment-transaction-delete', 'bulkDelete')->name('payment-transaction-bulk-delete');
        Route::get('payment-download/{payment}/download', 'downloadAttachment')
            ->name('payment.download-attachment');
        Route::get('payment-transaction-send-whatsapp/{payment}', 'sendWhatsapp')
            ->name('payment-transaction-send-whatsapp');
        Route::get('send-payment-invoice-whatsapp/{payment}', 'sendInvoiceToWhatsapp')
            ->name('send-payment-invoice-whatsapp');
        Route::post('payment-bulk-download', 'paymentBulkDownload')->name('payment-bulk-download');
        Route::post('payment-send-bulk-email', 'paymentSendBulkEmail')->name('payment-send-bulk-email');
    });

    // cheque configuration
    Route::controller(ChequeConfigurationController::class)->group(function () {
        Route::get('cheque-configuration', 'index')->name('cheque-configuration.index');
        Route::post('cheque-configuration', 'store')->name('cheque-configuration.store');
        Route::get('get-cheque-configuration', 'getChequeConfiguration')->name('get-cheque-configuration');
    });

    //Transaction Receipt
    Route::controller(TransactionReceiptController::class)->group(function () {
        Route::get('transaction-receipt', 'index')
            ->name('transaction-receipt.index')
            ->middleware(['permission:company_view_receipt|company_summery_receipt_transactions', 'userLastUsedAt']);
        Route::get('transaction-receipt/create', 'create')
            ->name('transaction-receipt.create')
            ->middleware('permission:company_add_new_receipt');
        Route::post('transaction-receipt', 'store')
            ->name('transaction-receipt.store')
            ->middleware('permission:company_add_new_receipt');
        Route::get('transaction-receipt/{transaction_receipt}/show', 'show')
            ->name('transaction-receipt.show')
            ->middleware('permission:company_view_receipt');
        Route::get('transaction-receipt/{transaction_receipt}/edit', 'edit')
            ->name('transaction-receipt.edit')
            ->middleware('permission:company_edit_receipt');
        Route::post('transaction-receipt/{transaction_receipt}', 'update')
            ->name('transaction-receipt.update')
            ->middleware('permission:company_edit_receipt');
        Route::delete('transaction-receipt/{transaction_receipt} ', 'destroy')
            ->name('transaction-receipt.destroy')
            ->middleware('permission:company_delete_receipt');
        Route::get('transaction-receipt-pdf-preview/{receiptPdfPreviewId}', 'getReceiptPdfPreview')
            ->name('transaction-receipt-pdf-preview');
        Route::get('transaction-receipt-preview-pdf-preview/{receiptPdfPreviewId}', 'getReceiptPdfPreviewDownload')
            ->name('transaction-receipt-preview-pdf-preview');
        Route::get('transaction-receipt-pdf/{receiptPdfDownloadId}/{isView?}', 'exportReceiptTransactionPdf')
            ->name('transaction-receipt-pdf-download')
            ->middleware('permission:company_import_export_receipt');
        Route::get('transaction-receipt-email/{receiptMailId}', 'receiveMail')
            ->name('transaction-receipt-email');
        Route::post('receive-send-email/{receiptSendMailId}', 'sendMail')
            ->name('receive-send-email');
        Route::get('get-ledger-model-details/{ledgerId}', 'getLedgerModelDetails')
            ->name('transaction-receipt.get-ledger-model-details');
        Route::get('export-receipt-transactions', 'export')
            ->name('export-receipt-transaction')
            ->middleware('permission:company_import_export_receipt');
        Route::post('import-receipt-transaction', 'importReceiptTransaction')
            ->name('import-receipt-transaction')
            ->middleware('permission:company_import_export_receipt');
        Route::post('receipt-transaction-delete', 'bulkDelete')->name('receipt-transaction-bulk-delete');
        Route::get('receipt-download/{receipt}/download', 'downloadAttachment')
            ->name('receipt.download-attachment');
        Route::get('receipt-transaction-send-whatsapp/{receipt}', 'sendWhatsapp')
            ->name('receipt-transaction-send-whatsapp');
        Route::get('send-receipt-invoice-whatsapp/{receiptId}', 'sendInvoiceToWhatsapp')->name('send-receipt-invoice-whatsapp');
        Route::post('receipt-bulk-download', 'receiptBulkDownload')->name('receipt-bulk-download');
        Route::post('receipt-send-bulk-email', 'receiptSendBulkEmail')->name('receipt-send-bulk-email');
        Route::get('receipt-transaction/check-advance-payment-settle/{transactionId}', 'advancePaymentSettle')->name('receipt-transaction.check-advance-payment-settle');
    });

    Route::get('third-party', function () {
        return view('company.react-transaction');
    })->name('integrations.index')->middleware('permission:company_view_integration_third_party');

    Route::get('third-party/vastra', function () {
        return view('company.react-transaction');
    })->middleware('permission:company_view_integration_third_party');
    Route::get('third-party/11za', function () {
        return view('company.react-transaction');
    })->name('integrations.11za')->middleware('permission:company_view_integration_third_party');

    //Sale Transaction
    Route::controller(SaleTransactionController::class)->group(function () {
        Route::get('sales', 'index')->name('sales.index')
            ->middleware(['permission:company_view_income|company_summery_income_transactions', 'userLastUsedAt']);
        // Route::get('sales/create', 'create')->name('sales.create')
        //     ->middleware('permission:company_add_new_income');

        Route::get('sales/create', function () {
            return view('company.react-transaction');
        })->name('sales.create');

        Route::get('purchase-sale/{purchase}/create', function () {
            return view('company.react-transaction');
        })->name('purchase-sale.create');

        Route::post('sales', 'store')->name('sales.store')
            ->middleware('permission:company_add_new_income');

        // Route::get('sales/{sale}/edit', 'edit')->name('sales.edit')
        //     ->middleware('permission:company_edit_income');

        Route::get('sales/{sale}/edit', function () {
            return view('company.react-transaction');
        })->name('sales.edit')->middleware('permission:company_edit_income');

        Route::match(['put', 'post'], 'sales/{sale}', 'update')->name('sales.update')
            ->middleware('permission:company_edit_income');
        Route::get('sales/{sale}', 'show')->name('sales.show')
            ->middleware('permission:company_view_income');
        Route::delete('sales/{sale} ', 'destroy')->name('sales.destroy')
            ->middleware('permission:company_delete_income');
        Route::get('export-sales-transactions', 'export')->name('export-sales-transaction')
            ->middleware('permission:company_import_export_income');
        Route::post('import-sale', 'importSale')->name('import-sale')
            ->middleware('permission:company_import_export_income');
        Route::get('gst-broker-transaction/{brokerId}', 'getBrokerDetail')->name('gst-broker-transaction');
        Route::get('gst-item-transaction/{itemId}', 'getItemDetail')->name('gst-item-transaction');
        Route::get('get-item-unit-price/{itemId}/{itemUnitId}/{customer?}', 'getItemUnitPrice')->name('get-item-unit-price');
        Route::get('gst-customer-detail-transaction/{customer}', 'getCustomerDetail')->name('gst-customer-detail-transaction');
        Route::post('check-customer-limit-amount', 'checkCustomerLimitAmount')->name('check-customer-limit-amount');
        Route::get('append-screen/{itemType}', 'getSalesTransactionItemType')->name('sales.append-screen');
        Route::get('get-tcs-tax-rate/{tcsTaxId}', 'getTcsTaxValue')->name('sales.get-tcs-tax-rate');
        // Route::get('sales-create/{sale}/duplicate', 'saleCreateDuplicate')->name('sales.create.duplicate')
        //     ->middleware('permission:company_add_new_income');
        Route::get('sales-create/{sale}/duplicate', function () {
            return view('company.react-transaction');
        })->name('sales.create.duplicate')->middleware('permission:company_add_new_income');
        Route::get('sales-email/{sale}', 'saleEmail')->name('sales-email');
        Route::post('sales-send-email/{sale}', 'sendEmail')->name('sales-send-email');
        Route::get('check-item-quantity-stock', 'checkItemQuantityStock')->name('check-item-quantity-stock');
        Route::get('check-sale-transaction-exists/{sale}', 'checkSaleTransactionExists')->name('check-sale-transaction-exists');
        Route::get('sale-send-whatsapp/{sale}', 'sendWhatsapp')->name('sale-send-whatsapp');
        Route::get('export-transaction-error', 'exportTransactionError')->name('export-transaction-error');
        Route::post('delete-sale', 'bulkDelete')->name('sale-bulk-delete');
        Route::post('get-income-accounting-ledger-list', 'getIncomeAccountingLedgerList')->name('get-income-accounting-ledger-list');
        Route::post('get-income-item-list', 'getIncomeItemList')->name('get-income-item-list');
        Route::get('send-invoice-whatsapp/{saleId}', 'sendInvoiceToWhatsapp')->name('send-sale-invoice-whatsapp');
        Route::post('sale-bulk-download', 'saleBulkDownload')->name('sale-bulk-download');
        // Route::get('sales-create/{estimate_quote}/estimate-quote', 'saleCreateEstimateQuote')->name('sales.create.estimate-quote')
        //     ->middleware('permission:company_add_new_income');
        Route::get('sales-create/{estimate_quote}/estimate-quote', function () {
            return view('company.react-transaction');
        })->name('sales.create.estimate-quote')->middleware('permission:company_add_new_income');
        Route::get('get-estimate-quote-transaction/{estimate_quote}', 'getEstimateQuoteTransaction')->name('sales.get-estimate-quote-transaction');
        Route::get('get-party-estimate-quote-document-numbers/{party}', 'getPartyEstimateQuoteDocumentNumbers')->name('sales.get-party-estimate-quote-document-numbers');
        Route::get('sale-download/{sale}/download', 'downloadAttachment')->name('sale.download-attachment');
        Route::post('manage-multiple-estimate-quote-transaction', 'manageMultipleEstimateQuoteTransaction')->name('sales.manage-multiple-estimate-quote-transaction');

        // Route::get('sales/create', function () {
        //     return view('company.react-test');
        // });

        Route::post('manage-multiple-delivery-challan-transaction', 'manageMultipleDeliveryChallanTransaction')->name('sales.manage-multiple-delivery-challan-transaction');
        Route::post('sale-thermal-print', 'saleThermalPrint')->name('sales.print-thermal');
        Route::post('send-sale-bulk-email', 'sendSaleBulkEmail')->name('send-sale-bulk-email');
        // Route::get('sales-create/{delivery_challan}/challan', 'saleCreateDeliveryChallan')->name('delivery-challan.create-invoice');
        Route::get('sales-create/{delivery_challan}/challan', function () {
            return view('company.react-transaction');
        })->name('delivery-challan.create-invoice')->middleware('permission:company_add_new_income');
        Route::get('edit-customer-details-price-list/{customer}', 'editCustomerDetailsPriceList')->name('edit-customer-details-price-list');
        Route::post('sale-dispatch-report', 'saleDispatchReport')->name('sale-dispatch-report');
    });

    // Delivery Challan Transaction
    Route::controller(DeliveryChallanTransactionController::class)->group(function () {
        Route::get('delivery-challan', 'index')->name('delivery-challan.index')->middleware(['permission:company_view_income|company_summery_income_transactions', 'userLastUsedAt']);
        // Route::get('delivery-challan/create', 'create')->name('delivery-challan.create')->middleware('permission:company_add_new_income');
        Route::get('delivery-challan/create', function () {
            return view('company.react-transaction');
        })->name('delivery-challan.create')->middleware('permission:company_add_new_income');
        Route::post('delivery-challan/create', 'store')->name('delivery-challan.store')->middleware('permission:company_add_new_income');
        Route::get('get-party-invoices/{party}', 'getPartyInvoices')->name('delivery-challan.get-party-invoices');
        Route::post('get-delivery-challan-list', 'getDeliveryChallanList')->name('get-delivery-challan-list');
        // Route::get('delivery-challan/{delivery_challan}/edit', 'edit')->name('deilvery-challan.edit')->middleware('permission:company_edit_income');
        Route::get('delivery-challan/{delivery_challan}/edit', function () {
            return view('company.react-transaction');
        })->name('deilvery-challan.edit')->middleware('permission:company_edit_income');
        Route::match(['put', 'post'], 'delivery-challan/{delivery_challan}/edit', 'update')->name('deilvery-challan-transaction.update')->middleware('permission:company_edit_income');
        Route::delete('delivery-challan/{delivery_challan}', 'destroy')->name('delivery-challan.destroy')->middleware('permission:company_delete_income');
        // Route::get('delivery-challan/{delivery_challan}/duplicate', 'createDuplicate')->name('delivery-challan.create.duplicate')->middleware('permission:company_add_new_income');
        Route::get('delivery-challan/{delivery_challan}/duplicate', function () {
            return view('company.react-transaction');
        })->name('delivery-challan.create.duplicate')->middleware('permission:company_add_new_income');
        Route::get('delivery-challan/{delivery_challan}/show', 'show')->name('delivery-challan.show')->middleware('permission:company_view_income');
        Route::get('delivery-challan/{delivery_challan}', 'deliveryMail')->name('delivery-challan-email');
        Route::get('delivery-challan/{delivery_challan}/{isView?}', 'getPdfDownload')->name('delivery-challan-pdf-download');
        Route::post('delivery-challan/{delivery_challan}', 'sendEmail')->name('delivery-challan.send-email');
        Route::get('send-delivery-challan-invoice-whatsapp/{delivery_challan}', 'sendInvoiceToWhatsapp')->name('send-delivery-challan-invoice-whatsapp');
        Route::get('delivery-challan-transaction-send-whatsapp/{delivery_challan}', 'sendWhatsapp')->name('delivery-challan-transaction-send-whatsapp');
        Route::get('export-delivery-challan', 'export')->name('export-delivery-challan-transaction')->middleware('permission:company_import_export_income');
        Route::get('delivery-challan-pdf-preview/{delivery_challan}/{printType?}', 'getPdfPreview')->name('delivery-challan.pdf-preview');
        Route::get('delivery-challan-pdf-download/{delivery_challan}/{isView?}', 'getPdfDownload')->name('delivery-challan.pdf-download');
        Route::get('delivery-challan-preiview-pdf-download/{deliveryChallanId}/{printType?}', 'getDeliveryChallanPdfDownload')->name('delivery-challan-preiview.pdf-download');
        Route::get('delivery-challan-send-whatsapp/{delivery_challan}', 'sendWhatsapp')->name('delivery-challan.send-whatsapp');
        Route::post('delete-delivery-challan-transaction', 'bulkDelete')->name('delivery-challan-bulk-delete');
        Route::post('delivery-challan-bulk-download', 'deliveryChallanBulkDownload')->name('delivery-challan-bulk-download');
        Route::get('delivery-challan-append-sale-transaction-items/{saleTransactionId}', 'getSaleTransactionItems')
            ->name('delivery-challan.append-sale-transaction-items');
        // Route::get('create-sale-delivery-challan/{sale}', 'createSaleDeliveryChallan')->name('sales.create.delivery-challan');
        Route::get('create-sale-delivery-challan/{sale}', function () {
            return view('company.react-transaction');
        })->name('sales.create.delivery-challan')->middleware('permission:company_add_new_income');
        Route::get('delivery-challan-download/{delivery_challan}/download', 'downloadAttachment')->name('delivery-challan.download-attachment');
        Route::get('check-delivery-challan-exists/{delivery_challan}', 'checkDeliveryChallanExists')->name('check-delivery-challan-exists');
        Route::post('import-delivery-challan', 'importDeliveryChallan')->name('import-delivery-challan');
        // ->middleware('permission:company_import_export_income');
    });

    Route::controller(IncomeEstimateQuoteTransactionController::class)->group(function () {
        Route::get('income-estimate-quote', 'index')->name('income-estimate-quote.index')
            ->middleware('permission:company_view_income|company_summery_income_transactions', 'userLastUsedAt');
        // Route::get('income-estimate-quote/create', 'create')->name('income-estimate-quote.create')
        //     ->middleware('permission:company_add_new_income');
        Route::get('income-estimate-quote/create', function () {
            return view('company.react-transaction');
        })->name('income-estimate-quote.create')->middleware('permission:company_add_new_income');
        Route::post('income-estimate-quote', 'store')->name('income-estimate-quote.store')
            ->middleware('permission:company_add_new_income');
        // Route::get('income-estimate-quote/{estimate_quote}/edit', 'edit')->name('income-estimate-quote.edit')
        //     ->middleware('permission:company_edit_income');
        Route::get('income-estimate-quote/{estimate_quote}/edit', function () {
            return view('company.react-transaction');
        })->name('income-estimate-quote.edit')->middleware('permission:company_edit_income');
        Route::match(['put', 'post'], 'income-estimate-quote/{estimate_quote}', 'update')->name('income-estimate-quote.update')
            ->middleware('permission:company_edit_income');
        Route::delete('income-estimate-quote/{estimate_quote}', 'destroy')->name('income-estimate-quote.destroy')
            ->middleware('permission:company_delete_income');
        // Route::get('income-estimate-quote-create/{estimate_quote}/duplicate', 'createDuplicate')->name('income-estimate-quote.create.duplicate')
        //     ->middleware('permission:company_add_new_income');
        Route::get('income-estimate-quote-create/{estimate_quote}/duplicate', function () {
            return view('company.react-transaction');
        })->name('income-estimate-quote.create.duplicate')->middleware('permission:company_add_new_income');
        Route::get('export-income-estimate-quote', 'export')->name('export-income-estimate-quote-transaction')
            ->middleware('permission:company_import_export_income');
        Route::post('import-income-estimate-quote', 'importEstimateQuote')->name('import-income-estimate-quote')->middleware('permission:company_import_export_income');
        Route::post('get-income-estimate-quote-item-list', 'getIncomeEstimateQuoteItemList')->name('get-income-estimate-quote-item-list');
        Route::post('get-income-estimate-quote-ledger-list', 'getIncomeEstimateQuoteLedgerList')->name('get-income-estimate-quote-ledger-list');
        Route::get('income-estimate-quote-append-screen/{itemType}', 'getItemType')->name('income-estimate-quote.append-screen');
        Route::post('check-party-limit-amount', 'checkCustomerLimitAmount')->name('check-party-limit-amount');
        Route::get('income-estimate-quote/{estimate_quote}', 'show')->name('income-estimate-quote.show')
            ->middleware('permission:company_view_income');
        Route::post('income-estimate-quote-bulk-delete', 'bulkDelete')->name('income-estimate-quote.bulk-delete');
        Route::get('estimate-quote-email/{estimate_quote}', 'estimateQuoteEmail')->name('estimate-quote-email');
        Route::post('estimate-quote-send-email/{estimate_quote}', 'estimateQuoteSendEmail')->name('estimate-quote-send-email');
        Route::get('income-estimate-quote-pdf-preview/{estimate_quote}/{printType?}', 'getPdfPreview')->name('income-estimate-quote.pdf-preview');
        Route::get('income-estimate-quote-pdf-download/{estimate_quote}/{isView?}', 'getPdfDownload')->name('income-estimate-quote.pdf-download');
        Route::get('income-estimate-preview-quote-pdf-download/{estimateQuoteId}/{printType?}', 'getEstimatePreviewPdfDownload')->name('income-estimate-preview-quote.pdf-download');
        Route::get('income-estimate-quote-send-email/{estimate_quote}', 'getSendEmailData')->name('income-estimate-quote.send-email');
        Route::post('income-estimate-quote-send-email/{estimate_quote}', 'sendEmail')->name('income-estimate-quote.send-email');
        Route::get('income-estimate-quote-pdf-download/{estimateQuotePdfDownloadId}/{isView?}', 'getPdfDownload')->name('income-estimate-quote-pdf-download');
        Route::get('income-estimate-quote-send-whatsapp/{estimate_quote}', 'sendWhatsapp')->name('income-estimate-quote.send-whatsapp');
        Route::get('send-income-estimate-quote-whatsapp/{estimate_quote}', 'sendInvoiceToWhatsapp')->name('send-income-estimate-quote-invoice-whatsapp');
        Route::post('income-estimate-quote-bulk-download', 'incomeEstimateQuoteBulkDownload')->name('income-estimate-quote-bulk-download');
        Route::post('income-estimate-quote-bulk-delete', 'bulkDelete')->name('income-estimate-quote-bulk-delete');
        Route::get('check-estimate-transaction-exists/{estimate_quote}', 'checkEstimateTransactionExists')->name('check-estimate-transaction-exists');
        Route::get('income-estimate-quote-download/{estimate_quote}/download', 'downloadAttachment')->name('income-estimate-quote.download-attachment');
        Route::post('income-estimate-quote-send-bulk-email', 'sendEstimateQuoteBulkEmail')->name('income-estimate-quote-send-bulk-email');
    });

    //Eway Bill
    Route::controller(EWayBillController::class)->group(function () {
        Route::post('generate-eway-bill/{transactionId}', 'generateEWayBill')->name('generate-eway-bill');
        Route::get('eway-bill/{transactionId}/{transactionType}', 'index')->name('eway-bill.index')
            ->middleware('permission:company_view_eway_bill_report');
        Route::get('eway-bill', 'eWayBillTableIndex')->name('e-way-bill-table-index')
            ->middleware('permission:company_view_eway_bill_report');
        Route::post('eway-bill/{ewayBillId}', 'eWayBillCancel')->name('eway-bill-cancel');
        Route::post('get-distance-from-pincode', 'getDistanceFromPincode')->name('get-distance-from-pincode');
        Route::get('get-ewaybill-pdf/{ewayBillId}', 'getEwaybillPdf')->name('get-ewaybill-pdf')
            ->middleware('permission:company_import_export_eway_bill_report');
        Route::get('get-ewaybill-details-pdf/{ewayBillId}', 'getEwaybillDeatilsPdf')->name('get-ewaybill-details-pdf')
            ->middleware('permission:company_import_export_eway_bill_report');
    });

    //E-Invoice
    Route::controller(EInvoiceController::class)->group(function () {
        // Route::get('generate-e-invoice/{transactionId}/{transactionType}', 'eInvoice')->name('generate-e-invoice');
        Route::post('generate-e-invoice/{transactionId}/{transactionType}', 'eInvoice')->name('generate-e-invoice');
        Route::get('e-invoice', 'eInvoiceTableIndex')->name('e-invoice-table-index')
            ->middleware('permission:company_view_einvoice_report');
        Route::post('e-invoice/{eInvoiceId}', 'eInvoiceCancel')->name('e-invoice-cancel');
        Route::get('get-einvoice-pdf/{eInvoiceId}', 'getEinvoicePdf')->name('get-einvoice-pdf')
            ->middleware('permission:company_import_export_einvoice_report');
    });

    //Configurations
    Route::controller(ConfigurationController::class)->group(function () {
        //Income Configuration
        Route::post('update-create-income-transaction-configuration', 'updateOrCreateIncomeTransactionConfiguration')
            ->name('update-create-income-transaction-configuration');

        //Sales-return Configuration
        Route::post('create-update-sales-return-configuration', 'createOrUpdateSalesReturnConfiguration')
            ->name('create-update-sales-return-configuration');

        //Income-Debit-Note Configuration
        Route::post('create-update-income-debit-note-configuration', 'createOrUpdateIncomeDebitNoteConfiguration')
            ->name('create-update-income-debit-note-configuration');

        //Income-Debit-Note Configuration
        Route::post('create-update-income-credit-note-configuration', 'createOrUpdateIncomeCreditNoteConfiguration')
            ->name('create-update-income-credit-note-configuration');

        //Income Transaction Configuration Form Data Render
        Route::get('update-income-configuration-form-render', 'updateIncomeConfigurationFormRender')
            ->name('update-income-configuration-form-render');

        //Expense Configuration
        Route::post('create-update-expense-configuration', 'createOrUpdateExpenseConfiguration')
            ->name('create-update-expense-configuration');

        //Expense-Debit-Note Configuration
        Route::post('create-update-expense-debit-note-configuration', 'createOrUpdateExpenseDebitNoteConfiguration')
            ->name('create-update-expense-debit-note-configuration');

        //Expense-Purchase Configuration
        Route::post('create-update-expense-purchase-configuration', 'createOrUpdateExpensePurchaseConfiguration')
            ->name('create-update-expense-purchase-configuration');

        //Expense-Credit-Note Configuration
        Route::post('create-update-expense-credit-note-configuration', 'createOrUpdateExpenseCreditNoteConfiguration')
            ->name('create-update-expense-credit-note-configuration');

        //Expense Transaction Configuration Form Data Render
        Route::get('update-expense-configuration-form-render', 'updateExpenseConfigurationFormRender')
            ->name('update-expense-configuration-form-render');

        //Receipt-Transaction Configuration
        Route::post('create-update-receipt-transaction-configuration', 'createOrUpdateReceiptTransactionConfiguration')
            ->name('create-update-receipt-transaction-configuration');

        //Payment-Transaction Configuration
        Route::post('create-update-payment-transaction-configuration', 'createOrUpdatePaymentTransactionConfiguration')
            ->name('create-update-payment-transaction-configuration');

        //Journal-Transaction Configuration
        Route::post('create-update-journal-transaction-configuration', 'createOrUpdateJournalTransactionConfiguration')
            ->name('create-update-journal-transaction-configuration');

        //Income Estimate Quote Configuration
        Route::post('update-create-income-estimate-quote-configuration', 'updateOrCreateIncomeEstimateQuoteConfiguration')
            ->name('update-create-income-estimate-quote-configuration');

        //Delivery Challan Configuration
        Route::post('update-create-delivery-challan-configuration', 'updateOrCreateDeliveryChallanConfiguration')
            ->name('update-create-delivery-challan-configuration');

        //Purchase Order Configuration
        Route::post('update-create-purchase-order-configuration', 'updateOrCreatePurchaseOrderConfiguration')
            ->name('update-create-purchase-order-configuration');
    });

    //Sale Return
    Route::controller(SalesReturnTransactionController::class)->group(function () {
        Route::get('sale-returns', 'index')->name('sale-returns.index')
            ->middleware(['permission:company_view_income|company_summery_income_transactions', 'userLastUsedAt']);
        // Route::get('sale-returns/create', 'create')->name('sale-returns.create')
        //     ->middleware('permission:company_add_new_income');
        Route::get('sale-returns/create', function () {
            return view('company.react-transaction');
        })->name('sale-returns.create')->middleware('permission:company_add_new_income');
        Route::post('sale-returns', 'store')->name('sale-returns.store')
            ->middleware('permission:company_add_new_income');
        // Route::get(uri: 'sale-returns/{sale_return}/edit', 'edit')->name('sale-returns.edit')
        //     ->middleware('permission:company_edit_income');
        Route::get('sale-returns/{sale_return}/edit', function () {
            return view('company.react-transaction');
        })->name('sale-returns.edit')->middleware('permission:company_edit_income');
        Route::match(['put', 'post'], 'sale-returns/{sale_return}', 'update')->name('sale-returns.update')
            ->middleware('permission:company_edit_income');
        Route::get('sale-returns/{sale_return}', 'show')->name('sale-returns.show')
            ->middleware('permission:company_view_income');
        Route::delete('sale-returns/{sale_return} ', 'destroy')->name('sale-returns.destroy')
            ->middleware('permission:company_delete_income');
        Route::get('check-sale-return-transaction-exists/{sale_return}', 'checkSaleReturnExists')
            ->name('check-sale-return-transaction-exists');
        Route::get('export-sale-returns', 'export')->name('export-sale-returns')
            ->middleware('permission:company_import_export_income');
        Route::get('sale-returns-invoice-type/{invoiceType}', 'getSaleReturnInvoiceType')
            ->name('sale-returns-invoice-type');
        Route::get('get-sales-transaction/{saleTransactionId}', 'getSaleTransaction')
            ->name('sales-return.get-sales-transaction');
        Route::get('sale-return-append-sale-transaction-items/{saleTransactionId}', 'getSaleTransactionItems')
            ->name('sales-return.append-sale-transaction-items');
        // Route::get('sale-returns-create/{sale_return}/duplicate', 'saleReturnCreateDuplicate')
        //     ->name('sale-returns.create.duplicate')
        //     ->middleware('permission:company_add_new_income');
        Route::get('sale-returns-create/{sale_return}/duplicate', function () {
            return view('company.react-transaction');
        })->name('sale-returns.create.duplicate')->middleware('permission:company_add_new_income');
        Route::get('sale-returns-email/{sale_return}', 'saleReturnEmail')
            ->name('sale-returns-email');
        Route::post('sale-returns-send-email/{sale_return}', 'sendEmail')
            ->name('sale-returns-send-email');
        Route::get('get-customer-invoice-number/{customerId}', 'getCustomerInvoiceNumber')
            ->name('sale-return.get-customer-invoice-number');
        Route::get('sale-return-send-whatsapp/{sale_return}', 'sendWhatsapp')
            ->name('sale-return-send-whatsapp');
        Route::post('import-sale-return', 'importSaleReturn')->name('import-sale-return')
            ->middleware('permission:company_import_export_income');
        Route::post('delete-sale-return', 'bulkDelete')->name('sale-return-bulk-delete');
        Route::post('get-sale-return-item-list', 'getSaleReturnItemList')->name('get-sale-return-item-list');
        Route::post('get-sale-return-accounting-ledger-list', 'getSaleReturnAccountingLedgerList')->name('get-sale-return-accounting-ledger-list');
        Route::get('send-sale-return-invoice-whatsapp/{saleReturnId}', 'sendInvoiceToWhatsapp')->name('send-sale-return-invoice-whatsapp');
        Route::post('sale-return-bulk-download', 'saleReturnBulkDownload')->name('sale-return-bulk-download');
        Route::post('send-sale-return-bulk-email', 'sendSaleReturnBulkEmail')->name('send-sale-return-bulk-email');
        Route::get('sale-return-download/{sale_return}/download', 'downloadAttachment')->name('sale-return.download-attachment');
        Route::get('create-sale-return/{sale_return}', function () {
            return view('company.react-transaction');
        })->name('sales.create.sale-return')->middleware('permission:company_add_new_income');
    });

    //Recurring Invoice
    Route::get('recurring-invoices', function () {
        return view('company.react-transaction');
    })->name('recurring-invoices.index');
    Route::get('recurring-invoices/create', function () {
        return view('company.react-transaction');
    })->name('recurring-invoices.create');
    Route::get('recurring-invoices/{id}/edit', function () {
        return view('company.react-transaction');
    })->name('recurring-invoices.edit');

    //Income Debit Note
    Route::controller(IncomeDebitNoteTransactionController::class)->group(function () {
        Route::get('income-debit-notes', 'index')
            ->name('income-debit-notes.index')
            ->middleware(['permission:company_view_income|company_summery_income_transactions', 'userLastUsedAt']);
        // Route::get('income-debit-notes/create', 'create')
        //     ->name('income-debit-notes.create')
        //     ->middleware('permission:company_add_new_income');
        Route::get('income-debit-notes/create', function () {
            return view('company.react-transaction');
        })->name('income-debit-notes.create')->middleware('permission:company_add_new_income');
        Route::post('income-debit-notes', 'store')
            ->name('income-debit-notes.store')
            ->middleware('permission:company_add_new_income');
        // Route::get('income-debit-notes/{income_debit_note}/edit', 'edit')
        //     ->name('income-debit-notes.edit')
        //     ->middleware('permission:company_edit_income');
        Route::get('income-debit-notes/{income_debit_note}/edit', function () {
            return view('company.react-transaction');
        })->name('income-debit-notes.edit')->middleware('permission:company_edit_income');
        Route::match(['put', 'post'], 'income-debit-notes/{income_debit_note}', 'update')
            ->name('income-debit-notes.update')
            ->middleware('permission:company_edit_income');
        Route::get('income-debit-notes/{income_debit_note}', 'show')
            ->name('income-debit-notes.show')
            ->middleware('permission:company_view_income');
        Route::delete('income-debit-notes/{income_debit_note}', 'destroy')
            ->name('income-debit-notes.destroy')
            ->middleware('permission:company_delete_income');
        Route::get('check-income-debit-note-transaction-exists/{income_debit_note}', 'checkIncomeDebitNoteExists')
            ->name('check-income-debit-note-transaction-exists');
        Route::get('export-income-debit-note-transaction', 'export')
            ->name('export-income-debit-note-transaction')
            ->middleware('permission:company_import_export_income');
        Route::post('import-income-debit-note', 'importDebitNote')
            ->name('import-income-debit-note')
            ->middleware('permission:company_import_export_income');
        Route::get('income-debit-notes-item-type/{itemType}', 'getDebitItemType')
            ->name('income-debit-notes-item-type');
        Route::get('debit-not-append-sale-transaction-items/{saleTransactionId}', 'getSaleTransactionItems')
            ->name('income-debit-notes.append-sale-transaction-items');
        // Route::get('income-debit-notes-create/{income_debit_note}/duplicate', 'IncomeDebitNoteCreateDuplicate')
        //     ->name('income-debit-notes.create.duplicate')
        //     ->middleware('permission:company_add_new_income');
        Route::get('income-debit-notes-create/{income_debit_note}/duplicate', function () {
            return view('company.react-transaction');
        })->name('income-debit-notes.create.duplicate')->middleware('permission:company_add_new_income');
        Route::get('income-debit-notes-email/{income_debit_note}', 'incomeDebitNoteReturnEmail')
            ->name('income-debit-notes-email');
        Route::post('income-debit-notes-send-email/{income_debit_note}', 'sendEmail')
            ->name('income-debit-notes-send-email');
        Route::get('income-dr-note-send-whatsapp/{income_debit_note}', 'sendWhatsapp')
            ->name('income-dr-note-send-whatsapp');
        Route::post('income-dr-note-delete', 'bulkDelete')->name('income-dr-note-bulk-delete');
        Route::post('get-income-debit-item-list', 'getIncomeDebitItemList')->name('get-income-debit-item-list');
        Route::post('get-income-debit-ledger-list', 'getIncomeDebitLedgerList')->name('get-income-debit-ledger-list');
        Route::get('send-income-debit-invoice-whatsapp/{incomeDnId}', 'sendInvoiceToWhatsapp')->name('send-income-debit-invoice-whatsapp');
        Route::post('income-debit-bulk-download', 'incomeDebitBulkDownload')->name('income-dr-note-bulk-download');
        Route::post('income-debit-send-bulk-email', 'sendIncomeDebitBulkEmail')->name('income-debit-send-bulk-email');
        Route::get('debit-note-download/{income_debit_note}/download', 'downloadAttachment')->name('income-debit-note.download-attachment');
    });

    //Income Credit Note
    Route::controller(IncomeCreditNoteTransactionController::class)->group(function () {
        Route::get('income-credit-notes', 'index')
            ->name('income-credit-notes.index')
            ->middleware(['permission:company_view_income|company_summery_income_transactions', 'userLastUsedAt']);
        // Route::get('income-credit-notes/create', 'create')
        //     ->name('income-credit-notes.create')
        //     ->middleware('permission:company_add_new_income');
        Route::get('income-credit-notes/create', function () {
            return view('company.react-transaction');
        })->name('income-credit-notes.create')->middleware('permission:company_add_new_income');

        Route::post('income-credit-notes', 'store')
            ->name('income-credit-notes.store')
            ->middleware('permission:company_add_new_income');
        // Route::get('income-credit-notes/{income_credit_note}/edit', 'edit')
        //     ->name('income-credit-notes.edit')
        //     ->middleware('permission:company_edit_income');
        Route::get('income-credit-notes/{income_credit_note}/edit', function () {
            return view('company.react-transaction');
        })->name('income-credit-notes.edit')->middleware('permission:company_edit_income');
        Route::match(['put', 'post'], 'income-credit-notes/{income_credit_note}', 'update')
            ->name('income-credit-notes.update')
            ->middleware('permission:company_edit_income');
        Route::get('income-credit-notes/{income_credit_note}', 'show')
            ->name('income-credit-notes.show')
            ->middleware('permission:company_view_income');
        Route::delete('income-credit-notes/{income_credit_note}', 'destroy')
            ->name('income-credit-notes.destroy')
            ->middleware('permission:company_delete_income');
        Route::get('check-income-credit-note-transaction-exists/{income_credit_note}', 'checkIncomeCreditNoteExists')
            ->name('check-income-credit-note-transaction-exists');
        Route::get('export-income-credit-notes', 'export')
            ->name('export-income-credit-notes')
            ->middleware('permission:company_import_export_income');
        Route::get('income-credit-notes-item-type/{itemType}', 'getCreditItemType')
            ->name('income-credit-notes-item-type');
        Route::get('credit-not-append-sale-transaction-items/{saleTransactionId}', 'getSaleTransactionItems')
            ->name('income-credit.append-sale-transaction-items');
        // Route::get('income-credit-notes-create/{income_credit_note}/duplicate', 'IncomeCreditNoteCreateDuplicate')
        //     ->name('income-credit-notes.create.duplicate')
        //     ->middleware('permission:company_add_new_income');
        Route::get('income-credit-notes-create/{income_credit_note}/duplicate', function () {
            return view('company.react-transaction');
        })->name('income-credit-notes.create.duplicate')->middleware('permission:company_add_new_income');
        Route::get('income-credit-notes-email/{income_credit_note}', 'incomeCreditNoteReturnEmail')
            ->name('income-credit-notes-email');
        Route::post('income-credit-notes-send-email/{income_credit_note}', 'sendEmail')
            ->name('income-credit-notes-send-email');
        Route::get('income-cr-note-send-whatsapp/{income_credit_note}', 'sendWhatsapp')
            ->name('income-cr-note-send-whatsapp');
        Route::post('import-income-credit-note', 'importCreditNote')
            ->name('import-income-credit-note')
            ->middleware('permission:company_import_export_income');
        Route::post('income-credit-notes-delete', 'bulkDelete')->name('income-cr-note-bulk-delete');
        Route::post('get-income-credit-item-list', 'getIncomeCreditItemList')->name('get-income-credit-item-list');
        Route::post('get-income-credit-accounting-ledger-list', 'getIncomeCreditAccountingLedgerList')->name('get-income-credit-accounting-ledger-list');
        Route::get('send-income-credit-invoice-whatsapp/{incomeCnId}', 'sendInvoiceToWhatsapp')->name('send-income-credit-invoice-whatsapp');
        Route::post('income-credit-bulk-download', 'incomeCreditBulkDownload')->name('income-cr-note-bulk-download');
        Route::post('income-credit-send-bulk-email', 'sendIncomeCreditBulkEmail')->name('income-cr-note-send-bulk-email');
        Route::get('credit-note-download/{income_credit_note}/download', 'downloadAttachment')->name('income-credit-note.download-attachment');
    });

    //Purchase Transaction
    Route::controller(PurchaseTransactionController::class)->group(function () {
        Route::get('purchases', 'index')
            ->name('purchases.index')
            ->middleware(['permission:company_view_expense|company_summery_expense_transactions', 'userLastUsedAt']);
        // Route::get('purchases/create', 'create')
        //     ->name('purchases.create')
        //     ->middleware('permission:company_add_new_expense');
        Route::get('purchases/create', function () {
            return view('company.react-transaction');
        })->name('purchases.create')->middleware('permission:company_add_new_expense');
        Route::post('purchases', 'store')
            ->name('purchases.store')
            ->middleware('permission:company_add_new_expense');
        // Route::get('purchases/{purchase}/edit', 'edit')
        //     ->name('purchases.edit')
        //     ->middleware('permission:company_edit_expense');
        Route::get('purchases/{purchase}/edit', function () {
            return view('company.react-transaction');
        })->name('purchases.edit')->middleware('permission:company_edit_expense');
        Route::match(['put', 'post'], 'purchases/{purchase}', 'update')
            ->name('purchases.update')
            ->middleware('permission:company_edit_expense');
        Route::get('purchases/{purchase}', 'show')
            ->name('purchases.show')
            ->middleware('permission:company_view_expense');
        Route::delete('purchases/{purchase} ', 'destroy')
            ->name('purchases.destroy')
            ->middleware('permission:company_delete_expense');
        Route::get('check-purchase-transaction-exists/{purchase}', 'checkPurchaseExists')
            ->name('check-purchase-transaction-exists');
        Route::get('get-tds-ledger', 'getTdsLedger')
            ->name('purchase.get-tds-ledger');
        Route::post('store-purchase-tds-entry', 'storePurchaseTdsEntry')
            ->name('purchase.store-purchase-tds-entry');
        Route::get('purchase-append-screen/{itemType}', 'getPurchaseTransactionItemType')
            ->name('purchase.append-screen');
        Route::get('get-purchase-transaction/{purchaseTransactionId}', 'getPurchaseTransaction')
            ->name('purchase.get-purchase-transaction');
        Route::get('get-tds-tax-rate/{tdsTaxId}', 'getTdsTaxValue')
            ->name('purchase.get-tds-tax-rate');
        Route::get('gst-supplier-detail-transaction/{supplier}', 'getSupplierDetail')
            ->name('gst-supplier-detail-transaction');
        // Route::get('purchases-create/{purchase}/duplicate', 'purchaseCreateDuplicate')
        //     ->name('purchases.create.duplicate')
        //     ->middleware('permission:company_add_new_expense');
        Route::get('purchases-create/{purchase}/duplicate', function () {
            return view('company.react-transaction');
        })->name('purchases.create.duplicate')->middleware('permission:company_add_new_expense');
        Route::get('purchases-download/{purchase}/download', 'downloadAttachment')
            ->name('purchase.download-attachment');
        Route::get('purchases-email/{purchase}', 'purchaseEmail')
            ->name('purchases-email');
        Route::post('purchases-send-email/{purchase}', 'sendEmail')
            ->name('purchases-send-email');
        Route::get('export-purchase-transactions', 'export')
            ->name('export-purchase-transaction')
            ->middleware('permission:company_import_export_expense');
        Route::post('import-purchase', 'importPurchase')
            ->name('import-purchase')
            ->middleware('permission:company_import_export_expense');
        Route::post('purchases-delete', 'bulkDelete')->name('purchases-bulk-delete');
        Route::post('get-purchases-item-list', 'getPurchasesItemList')->name('get-purchases-item-list');
        Route::post('get-purchases-accounting-ledger-list', 'getPurchasesAccountingLedgerList')->name('get-purchases-accounting-ledger-list');
        Route::post('purchase-bulk-download', 'purchaseBulkDownload')->name('purchase-bulk-download');
        Route::get('get-party-purchase-order-numbers/{party}', 'getPartyPurchaseOrderNumbers')->name('purchase.get-party-purchase-order-numbers');
        Route::post('purchase-order-transaction', 'getPurchaseOrderTransaction')->name('purchase.get-purchase-order-transaction');
        Route::post('manage-multiple-purchase-order-transaction', 'manageMultiplePurchaseOrderTransaction')->name('purchase.manage-multiple-purchase-order-transaction');
        // Route::get('purchase-create/{order}', 'purchaseCreatePurchaseOrder')->name('purchase.create.purchase-order');
        Route::get('purchase-create/{order}', function () {
            return view('company.react-transaction');
        })->name('purchase.create.purchase-order')->middleware('permission:company_add_new_expense');
        Route::get('purchase-order-append-purchase-transaction-items/{order}', 'getPurchaseOrderTransactionItems')
            ->name('purchase.append-purchase-order-transaction-items');
    });

    //Purchase Return Transaction
    Route::controller(PurchaseReturnTransactionController::class)->group(function () {
        Route::get('purchase-returns', 'index')
            ->name('purchase-returns.index')
            ->middleware(['permission:company_view_expense|company_summery_expense_transactions', 'userLastUsedAt']);
        // Route::get('purchase-returns/create', 'create')
        //     ->name('purchase-returns.create')
        //     ->middleware('permission:company_add_new_expense');

        Route::get('purchase-returns/create', function () {
            return view('company.react-transaction');
        })->name('purchase-returns.create')->middleware('permission:company_add_new_expense');

        Route::post('purchase-returns', 'store')
            ->name('purchase-returns.store')
            ->middleware('permission:company_add_new_expense');
        // Route::get('purchase-returns/{purchase_return}/edit', 'edit')
        //     ->name('purchase-returns.edit')
        //     ->middleware('permission:company_edit_expense');
        Route::get('purchase-returns/{purchase_return}/edit', function () {
            return view('company.react-transaction');
        })->name('purchase-returns.edit')->middleware('permission:company_edit_expense');
        Route::match(['put', 'post'], 'purchase-returns/{purchase_return}', 'update')
            ->name('purchase-returns.update')
            ->middleware('permission:company_edit_expense');
        Route::get('purchase-returns/{purchase_return}', 'show')
            ->name('purchase-returns.show')
            ->middleware('permission:company_view_expense');
        Route::delete('purchase-returns/{purchase_return} ', 'destroy')
            ->name('purchase-returns.destroy')
            ->middleware('permission:company_delete_expense');
        Route::get('check-purchase-return-exists/{purchase_return} ', 'checkPurchaseReturnExists')
            ->name('check-purchase-return-exists');
        Route::post('store-purchase-returns-tds-entry', 'storePurchaseReturnTdsEntry')
            ->name('purchase-return.store-tds-entry')
            ->middleware('permission:company_import_export_expense');
        Route::get('purchase-return-append-screen/{itemType}', 'getPurchaseReturnTransactionItemType')
            ->name('purchase-return.append-screen');
        Route::get('get-supplier-purchase-number/{supplierId}', 'getSupplierPurchaseNumber')
            ->name('purchase-return.get-supplier-purchase-number');
        Route::get('purchase-return-append-purchase-transaction-items/{purchaseTransactionId}', 'getSaleTransactionItems')
            ->name('purchase-return.append-purchase-transaction-items');
        // Route::get('purchase-returns-create/{purchase_return}/duplicate', 'purchaseReturnCreateDuplicate')
        //     ->name('purchase-returns.create.duplicate')
        //     ->middleware('permission:company_add_new_expense');
        Route::get('purchase-returns-create/{purchase_return}/duplicate', function () {
            return view('company.react-transaction');
        })->name('purchase-returns.create.duplicate')->middleware('permission:company_add_new_expense');
        Route::get('purchase-returns-email/{purchase_return}', 'purchaseReturnEmail')
            ->name('purchase-returns-email');
        Route::post('purchase-returns-send-email/{purchase_return}', 'sendEmail')
            ->name('purchase-returns-send-email');
        Route::get('export-purchase-returns-transactions', 'export')
            ->name('export-purchase-return-transaction');
        Route::post('import-purchase-return', 'importPurchaseReturn')
            ->name('import-purchase-return')
            ->middleware('permission:company_import_export_expense');
        Route::post('purchases-returns-delete', 'bulkDelete')->name('purchase-returns-bulk-delete');
        Route::post('get-purchase-return-item-list', 'getPurchaseReturnItemList')->name('get-purchase-return-item-list');
        Route::post('get-purchase-return-ledger-list', 'getPurchaseReturnAccountingLedgerList')->name('get-purchase-return-ledger-list');
        Route::post('purchase-return-bulk-download', 'purchaseReturnBulkDownload')->name('purchase-return-bulk-download');
        Route::get('purchase-return-download/{purchase_return}/download', 'downloadAttachment')->name('purchase-return.download-attachment');
        Route::get('create-purchase-return/{purchase_return}', function () {
            return view(view: 'company.react-transaction');
        })->name('purchase-return.create')->middleware('permission:company_add_new_expense');
    });

    //Expense Debit Note Transaction
    Route::controller(ExpenseDebitNoteTransactionController::class)->group(function () {
        Route::get('expense-debit-notes', 'index')
            ->name('expense-debit-notes.index')
            ->middleware(['permission:company_view_expense|company_summery_expense_transactions', 'userLastUsedAt']);
        // Route::get('expense-debit-notes/create', 'create')
        //     ->name('expense-debit-notes.create')
        //     ->middleware('permission:company_add_new_expense');
        Route::get('expense-debit-notes/create', function () {
            return view('company.react-transaction');
        })->name('expense-debit-notes.create')->middleware('permission:company_add_new_expense');

        Route::post('expense-debit-notes', 'store')
            ->name('expense-debit-notes.store')
            ->middleware('permission:company_add_new_expense');
        // Route::get('expense-debit-notes/{expense_debit_note}/edit', 'edit')
        //     ->name('expense-debit-notes.edit')
        //     ->middleware('permission:company_edit_expense');
        Route::get('expense-debit-notes/{expense_debit_note}/edit', function () {
            return view('company.react-transaction');
        })->name('expense-debit-notes.edit')->middleware('permission:company_edit_expense');
        Route::match(['put', 'post'], 'expense-debit-notes/{expense_debit_note}', 'update')
            ->name('expense-debit-notes.update')
            ->middleware('permission:company_edit_expense');
        Route::get('expense-debit-notes/{expense_debit_note}', 'show')
            ->name('expense-debit-notes.show')
            ->middleware('permission:company_view_expense');
        Route::delete('expense-debit-notes/{expense_debit_note} ', 'destroy')
            ->name('expense-debit-notes.destroy')
            ->middleware('permission:company_delete_expense');
        Route::get('check-expense-debit-notes-exists/{expense_debit_note} ', 'checkExpenseDebitNoteExists')
            ->name('check-expense-debit-notes-exists');
        Route::post('store-expense-debit-notes-tds-entry', 'storeExpenseDebitNoteTdsEntry')
            ->name('expense-debit-notes.store-tds-entry')
            ->middleware('permission:company_import_export_expense');
        Route::get('expense-debit-note-append-purchase-transaction-items/{purchaseTransactionId}', 'getSaleTransactionItems')
            ->name('expense-debit-note.append-purchase-transaction-items');
        // Route::get('expense-debit-notes-create/{expense_debit_note}/duplicate', 'expenseDebitNoteCreateDuplicate')
        //     ->name('expense-debit-notes.create.duplicate')
        //     ->middleware('permission:company_add_new_expense');
        Route::get('expense-debit-notes-create/{expense_debit_note}/duplicate', function () {
            return view('company.react-transaction');
        })->name('expense-debit-notes.create.duplicate')->middleware('permission:company_add_new_expense');

        Route::get('expense-debit-notes-email/{expense_debit_note}', 'expenseDebitNoteReturnEmail')
            ->name('expense-debit-notes-email');
        Route::post('expense-debit-notes-send-email/{expense_debit_note}', 'sendEmail')
            ->name('expense-debit-notes-send-email');
        Route::get('export-expense-debit-notes', 'export')
            ->name('export-expense-debit-note')
            ->middleware('permission:company_import_export_expense');
        Route::post('import-expense-debit-note', 'importDebitNote')
            ->name('import-expense-debit-note')
            ->middleware('permission:company_import_export_expense');
        Route::post('expense-debit-notes-delete', 'bulkDelete')->name('expense-debit-notes-bulk-delete');
        Route::post('expense-debit-bulk-download', 'expenseDebitNoteBulkDownload')->name('expense-debit-bulk-download');
        Route::get('expense-debit-note-download/{expense_debit_note}/download', 'downloadAttachment')->name('expense-debit-note.download-attachment');
        Route::get('expense-debit-note/append-screen/{itemType}', 'getExpenseDebitNoteTransactionItemType')
            ->name('expense-debit-note.append-screen');
        Route::post('get-expense-debit-note-item-list', 'getExpenseDebitNoteItemList')->name('get-expense-debit-note-item-list');
        Route::post('get-expense-debit-note-ledger-list', 'getExpenseDebitNoteAccountingLedgerList')->name('get-expense-debit-note-ledger-list');
    });

    //Expense Credit Note Transaction
    Route::controller(ExpenseCreditNoteTransactionController::class)->group(function () {
        Route::get('expense-credit-notes', 'index')
            ->name('expense-credit-notes.index')
            ->middleware(['permission:company_view_expense|company_summery_expense_transactions', 'userLastUsedAt']);
        // Route::get('expense-credit-notes/create', 'create')
        //     ->name('expense-credit-notes.create')
        //     ->middleware('permission:company_add_new_expense');

        Route::get('expense-credit-notes/create', function () {
            return view('company.react-transaction');
        })->name('expense-credit-notes.create')->middleware('permission:company_add_new_expense');

        Route::post('expense-credit-notes', 'store')
            ->name('expense-credit-notes.store')
            ->middleware('permission:company_add_new_expense');
        // Route::get('expense-credit-notes/{expense_credit_note}/edit', 'edit')
        //     ->name('expense-credit-notes.edit')
        //     ->middleware('permission:company_edit_expense');
        Route::get('expense-credit-notes/{expense_credit_note}/edit', function () {
            return view('company.react-transaction');
        })->name('expense-credit-notes.edit')->middleware('permission:company_edit_expense');
        Route::match(['put', 'post'], 'expense-credit-notes/{expense_credit_note}', 'update')
            ->name('expense-credit-notes.update')
            ->middleware('permission:company_edit_expense');
        Route::get('expense-credit-notes/{expense_credit_note}', 'show')
            ->name('expense-credit-notes.show')
            ->middleware('permission:company_view_expense');
        Route::delete('expense-credit-notes/{expense_credit_note} ', 'destroy')
            ->name('expense-credit-notes.destroy')
            ->middleware('permission:company_delete_expense');
        Route::get('check-expense-credit-notes-exists/{expense_credit_note} ', 'checkExpenseCreditNoteExists')
            ->name('check-expense-credit-notes-exists');
        Route::post('store-expense-credit-notes-tds-entry', 'storeExpenseCreditNoteTdsEntry')
            ->name('expense-credit-notes.store-tds-entry');
        Route::get('expense-credit-notes.append-screen/{itemType}', 'getExpenseCreditNoteItemType')
            ->name('expense-credit-notes.append-screen');
        Route::get('expense-credit-note-append-purchase-transaction-items/{purchaseTransactionId}', 'getPurchaseTransactionItems')
            ->name('expense-credit-note.append-purchase-transaction-items');
        // Route::get('expense-credit-notes-create/{expense_credit_note}/duplicate', 'expenseCreditNoteCreateDuplicate')
        //     ->name('expense-credit-notes.create.duplicate')
        //     ->middleware('permission:company_add_new_expense');
        Route::get('expense-credit-notes-create/{expense_credit_note}/duplicate', function () {
            return view('company.react-transaction');
        })->name('expense-credit-notes.create.duplicate')->middleware('permission:company_add_new_expense');
        Route::get('expense-credit-notes-email/{expense_credit_note}', 'expenseCreditNoteReturnEmail')
            ->name('expense-credit-notes-email');
        Route::post('expense-credit-notes-send-email/{expense_credit_note}', 'sendEmail')
            ->name('expense-credit-notes-send-email');
        Route::get('export-expense-credit-notes', 'export')
            ->name('export-expense-credit-note')
            ->middleware('permission:company_import_export_expense');
        Route::post('import-expense-credit-note', 'importCreditNote')
            ->name('import-expense-credit-note')
            ->middleware('permission:company_import_export_expense');
        Route::post('expense-credit-notes-delete', 'bulkDelete')->name('expense-credit-notes-bulk-delete');
        Route::post('get-expense-credit-note-item-list', 'getExpenseCreditItemList')->name('get-expense-credit-note-item-list');
        Route::post('get-expense-credit-accounting-ledger-list', 'getExpenseCreditAccountingLedgerList')->name('get-expense-credit-ledger-list');
        Route::post('expense-credit-bulk-download', 'expenseCreditNoteBulkDownload')->name('expense-credit-bulk-download');
        Route::get('expense-credit-note-download/{expense_credit_note}/download', 'downloadAttachment')->name('expense-credit-note.download-attachment');
    });

    // Expense Purchase Order
    Route::controller(PurchaseOrderTransctionController::class)->group(function () {
        Route::get('purchase-order', 'index')->name('purchase-orders.index');
        // Route::get('purchase-order/create', 'create')->name('purchase-orders.create');
        Route::post('purchase-order/create', 'store')->name('purchase-orders.store');
        Route::get('purchase-order/create', function () {
            return view('company.react-transaction');
        })->name('purchase-orders.create')->middleware('permission:company_add_new_expense');
        Route::get('purchase-order-append-screen/{itemType}', 'getItemType')->name('purchase-order.append-screen');
        Route::post('get-purchase-order-item-list', 'getPurchaseOrderItemList')->name('get-purchase-order-item-list');
        Route::post('get-purchase-order-ledger-list', 'getPurchaseOrderLedgerList')->name('get-purchase-order-ledger-list');
        // Route::get('purchase-order/{order}/edit', 'edit')->name('purchase-order.edit');
        Route::get('purchase-order/{order}/edit', function () {
            return view('company.react-transaction');
        })->name('purchase-order.edit')->middleware('permission:company_edit_expense');
        Route::match(['put', 'post'], 'purchase-order/{order}', 'update')->name('purchase-order.update');
        Route::get('purchase-order-download/{order}/download', 'downloadAttachment')->name('purchse-order.download-attachment');
        // Route::get('purchase-order-create/{order}/duplicate', 'createDuplicate')->name('purchase-order.create.duplicate');
        Route::get('purchase-order-create/{order}/duplicate', function () {
            return view('company.react-transaction');
        })->name('purchase-order.create.duplicate')->middleware('permission:company_add_new_expense');
        Route::get('export-purchase-order', 'export')->name('export-purchase-order-transaction');
        Route::delete('purchase-order/{order}', 'destroy')->name('purchase-order.destroy');
        Route::post('purchase-order-bulk-delete', 'bulkDelete')->name('purchase-order.bulk-delete');
        Route::get('check-purchase-order-exists/{order}', 'checkPurchaseOrderTransactionExists')->name('check-purchase-order-exists');
        Route::get('purchase-order-email/{order}', 'purchaseOrderEmail')->name('purchase-order-email');
        Route::post('purchase-order-send-email/{order}', 'purchaseOrderSendEmail')->name('purchase-order-send-email');
        Route::get('purchase-order-pdf-preview/{order}', 'getPdfPreview')->name('purchase-order.pdf-preview');
        Route::get('purchase-order-pdf-download/{order}', 'getPurchaseOrderPreviewPdfDownload')->name('purchase-order.pdf-download');
        Route::get('purchase-order-pdf-download/{order}/{isView?}', 'getPdfDownload')->name('purchase-order.pdf-download');
        Route::get('purchase-order-email/{order}', 'purchaseOrderEmail')->name('purchase-order-email');
        Route::get('send-purchase-order-quote-whatsapp/{order}', 'sendInvoiceToWhatsapp')->name('send-purchase-order-invoice-whatsapp');
        Route::get('purchase-order-transaction-send-whatsapp/{order}', 'sendWhatsapp')->name('purchase-order-transaction-send-whatsapp');
        Route::get('purchase-order/{order}', 'show')->name('purchase-order.show');
        Route::post('purchase-order-bulk-download', 'purchaseOrderBulkDownload')->name('purchase-order-bulk-download');
        Route::post('send-purchase-order-bulk-email', 'sendPurchaseOrderBulkEmail')->name('send-purchase-order-bulk-email');
        Route::get('get-purchase-order-transaction/{order}', 'getPurchaseOrderTransaction')
            ->name('purchase.get-purchase-order-transaction');
        Route::post('import-purchase-order', 'importPurchaseOrder')->name('import-purchase-order')->middleware('permission:company_import_export_expense');
    });

    //PDF-PREVIEW & PDF-DOWNLOAD
    Route::controller(SaleTransactionController::class)->group(function () {
        Route::get('sales-pdf-preview/{salePdfPreviewId}/{printType?}', 'getSalePdfPreview')
            ->name('sales-pdf-preview');
        Route::get('sales-pdf-download/{salePdfDownloadId}/{isView?}', 'getSalePdfDownload')
            ->name('sales-pdf-download');
        Route::get('sale-preview-pdf-download/{saleId}/{printType?}', 'getSalePreviewPdfDownload')->name('sale-preview-pdf-download');
    });
    Route::controller(SalesReturnTransactionController::class)->group(function () {
        Route::get('sale-returns-pdf-preview/{saleReturnPdfPreviewId}', 'getSaleReturnPdfPreview')
            ->name('sale-return-pdf-preview');
        Route::get('sale-return-pdf-download/{saleReturnPdfDownloadId}/{isView?}', 'getSaleReturnPdfDownload')
            ->name('sale-return-pdf-download');
        Route::get('sale-return-preview-pdf-download/{saleReturnId}', 'getSaleReturnPreviewPdfDownload')
            ->name('sale-return-preview-pdf-download');
    });
    Route::controller(IncomeDebitNoteTransactionController::class)->group(function () {
        Route::get('income-dn-preview/{incomeDnPreviewId}', 'getIncomeDebitNotePdfPreview')
            ->name('income-dn-preview');
        Route::get('income-dn-pdf-download/{incomeDnDownloadPdfId}/{isView?}', 'getIncomeDebitNotePdfDownload')
            ->name('income-dn-pdf-download');
        Route::get('income-dn-preview-pdf-download/{incomeDebitNoteId}', 'getIncomeDebitNotePreviewPdfDownload')
            ->name('income-dn-preview-pdf-download');
    });
    Route::controller(IncomeCreditNoteTransactionController::class)->group(function () {
        Route::get('income-cn-preview/{incomeCnPreviewId}', 'getIncomeCreditNotePreview')
            ->name('income-cn-preview');
        Route::get('income-cn-pdf-download/{incomeCnDownloadPdfId}/{isView?}', 'getIncomeCreditNotePdfDownload')
            ->name('income-cn-pdf-download');
        Route::get('income-cn-preview-pdf-download/{incomeCnDownloadPdfId}', 'getIncomeCreditNotePreviewPdfDownload')
            ->name('income-cn-preview-pdf-download');
    });
    Route::controller(PurchaseTransactionController::class)->group(function () {
        Route::get('purchase-pdf-preview/{purchasePdfPreviewId}', 'getPurchasePdfPreview')
            ->name('purchase-pdf-preview');
        Route::get('purchase-pdf-download/{purchasePdfDownloadId}/{isView?}', 'getPurchasePdfDownload')
            ->name('purchase-pdf-download');
        Route::get('purchase-preview-pdf-download/{purchaseId}', 'getPurchasePreviewPdfDownload')->name('purchase-preview-pdf-download');
    });
    Route::controller(PurchaseReturnTransactionController::class)->group(function () {
        Route::get('purchase-returns-pdf-preview/{purchaseReturnPdfPreviewId}', 'getPurchaseReturnPdfPreview')
            ->name('purchase-return-pdf-preview');
        Route::get('purchase-return-pdf-download/{purchaseReturnPdfDownloadId}/{isView?}', 'getPurchaseReturnPdfDownload')
            ->name('purchase-return-pdf-download');
        Route::get('purchase-return-preview-pdf-download/{purchaseReturnId}', 'getPurchaseReturnPreviewPdfDownload')->name('purchase-return-preview-pdf-download');
    });
    Route::controller(ExpenseDebitNoteTransactionController::class)->group(function () {
        Route::get('expense-dn-preview/{expenseDnPreviewId}', 'getExpenseDebitNotePdfPreview')
            ->name('expense-dn-preview');
        Route::get('expense-dn-pdf-download/{expenseDnDownloadPdfId}/{isView?}', 'getExpenseDebitNotePdfDownload')
            ->name('expense-dn-pdf-download');
        Route::get('expense-dn-preview-pdf-download/{expenseDnDownloadPdfId}', 'getExpenseDebitNotePreviewPdfDownload')->name('expense-dn-preview-pdf-download');
    });
    Route::controller(ExpenseCreditNoteTransactionController::class)->group(function () {
        Route::get('expense-cn-preview/{expenseCnPreviewId}', 'getExpenseCreditNotePreview')
            ->name('expense-cn-preview');
        Route::get('expense-cn-pdf-download/{expenseCnDownloadPdfId}/{isView?}', 'getExpenseCreditNotePdfDownload')
            ->name('expense-cn-pdf-download');
        Route::get('expense-cn-preview-pdf-download/{expenseCnDownloadPdfId}', 'getExpenseCreditNotePreviewPdfDownload')->name('expense-cn-preview-pdf-download');
    });

    //Sale Report
    Route::controller(SaleReportController::class)->group(function () {
        Route::get('sale-report', 'index')->name('sale-report')
            ->middleware('permission:company_view_sale_report');
    });
    //Purchase Report
    Route::controller(PurchaseReportController::class)->group(function () {
        Route::get('purchase-report', 'index')->name('purchase-report')
            ->middleware('permission:company_view_purchase_report');
    });
    //Day Book Report
    Route::controller(DayBookReportController::class)->group(function () {
        Route::get('day-book-report', 'index')->name('day-book-report')
            ->middleware('permission:company_view_day_book_report');
    });
    //Outstanding Report
    Route::controller(OutstandingReportController::class)->group(function () {
        Route::get('outstanding-report', 'index')->name('outstanding-report')
            ->middleware('permission:company_view_outstanding_report');
        Route::get('outstanding-report-rate-update', 'rateOfInterestUpdate')
            ->name('outstanding-report-rate-update');
        Route::get('sales-reminder-email/{sale}', 'sendSaleReminderEmailForm')
            ->name('sales-reminder-email');
        Route::post('sales-reminder-email/{sale}', 'sendSaleTransactionEmail')
            ->name('sales-reminder-send-email');
        Route::get('send-income-debit-notes-email/{income_debit_note}', 'incomeDebitNoteReturnEmail')
            ->name('send-income-debit-notes-email');
        Route::post('send-income-debit-notes-send-email/{income_debit_note}', 'sendEmail')
            ->name('send-income-debit-notes-send-email');
    });
    //Ageing Report
    Route::controller(AgeingReportController::class)->group(function () {
        Route::get('ageing-report', 'index')->name('ageing-report')
            ->middleware('permission:company_view_ageing_report');
        //          Route::get('ageing-period-day-report/{ageingPeriodDay}', 'ageingPeriodDay')->name('ageing-period-day-report');
        Route::middleware('permission:company_import_export_ageing_report')->group(function () {
            Route::get('ageing-period', 'ageingPeriodIndex')->name('ageing-period.index');
            Route::post('ageing-period', 'ageingPeriodCreate')->name('ageing-period.store');
            Route::delete('ageing-period-delete/{id}', 'ageingPeriodDelete')->name('ageing-period.destroy');
            Route::get('ageing-period-edit/{id}', 'ageingPeriodEdit')->name('ageing-period.edit');
            Route::post('ageing-period-update/{id}', 'ageingPeriodUpdate')->name('ageing-period.update');
        });
    });
    //Output Tax Report
    Route::controller(OutputTaxRegisterReportController::class)->group(function () {
        Route::get('output-tax-register-report', 'index')->name('output-tax-register-report')
            ->middleware('permission:company_view_output_tax_report');
    });
    //HSN Inward Report
    Route::controller(HsnSummaryInwardReportController::class)->group(function () {
        Route::get('hsn-summary-inward-report', 'index')->name('hsn-summary-inward-report')
            ->middleware('permission:company_view_hsn_inward_report');
    });
    //HSN Outward Report
    Route::controller(HsnSummaryReportController::class)->group(function () {
        Route::get('hsn-summary-out-word-report', 'index')->name('hsn-summary-outward-report')
            ->middleware('permission:company_view_hsn_outward_report');
    });
    //Input Tax Report
    Route::controller(InputTaxRegisterReportController::class)->group(function () {
        Route::get('input-tax-register-report', 'index')->name('input-tax-register-report')
            ->middleware('permission:company_view_input_tax_report');
    });
    //Ledger Report
    Route::controller(LedgerReportController::class)->group(function () {
        Route::get('ledger-report', 'index')->name('ledger-report')
            ->middleware('permission:company_view_ledger_report');
        Route::post('ledger-report-pdf', 'downloadPDF')->name('ledger-report-pdf')
            ->middleware('permission:company_import_export_ledger_report|company_import_export_cash_bank_report');
        Route::post('ledger-report-export-excel', 'exportExcel')->name('ledger-report-export-excel')
            ->middleware('permission:company_import_export_ledger_report|company_import_export_cash_bank_report');
        Route::get('check-transaction-type', 'checkTransactionType')->name('check-transaction-type');
        Route::delete('check-transaction-type-delete', 'checkTransactionTypeDelete')->name('check-transaction-type-delete');
        Route::get('cash-bank-report', 'index')->name('cash-bank-report')
            ->middleware('permission:company_view_cash_bank_report');
    });
    //Broker Report
    Route::controller(BrokerReportController::class)->group(function () {
        Route::get('broker-report', 'index')->name('broker-report')
            ->middleware('permission:company_view_broker_report');
    });
    //TDS Report
    Route::controller(TdsStatementReportController::class)->group(function () {
        // TDS liability Report
        Route::get('tds-liability-report', 'index')->name('tds-liability-report')
            ->middleware('permission:company_view_tds_liability_report');
        // TDS Return Report
        Route::get('tds-return-report', 'tdsReturn')->name('tds-return-report')
            ->middleware('permission:company_view_tds_return_report');
    });
    //TCS Report
    Route::controller(TcsStatementController::class)->group(function () {
        // TCS liability Report
        Route::get('tcs-liability-report', 'index')->name('tcs-liability-report')
            ->middleware('permission:company_view_tcs_liability_report');
        // TCS Return Report
        Route::get('tcs-return-report', 'tcsReturn')->name('tcs-return-report')
            ->middleware('permission:company_view_tcs_return_report');
        //            Route::post('export-tcs-return-report', 'exportTcsReturnReport')->name('export-tcs-return-report');
        //            Route::post('tcs-return-report-pdf', 'tcsReturnReportPdf')->name('tcs-return-report-pdf');
    });
    //GSTR3B Report
    Route::controller(Gstr3BSummeryController::class)->group(function () {
        Route::get('reports/gstr-3b-summary', 'index')->name('reports.gstr-3b-summary')
            ->middleware('permission:company_view_gstr_3b_report');
    });
    Route::get('/gstr-2b', function () {
        return view('company.react-transaction');
    })->name('dashboard.gstr-2b');
    //Stock Report
    Route::controller(StockReportController::class)->group(function () {
        Route::get('stock-report', 'index')->name('report-stock')
            ->middleware('permission:company_view_stock_report');
        Route::post('stock-report-pdf', 'pdfDownload')->name('report-stock-pdf')
            ->middleware('permission:company_import_export_stock_report');
        Route::get('stock-report-excel', 'excelDownload')->name('report-stock-excel')
            ->middleware('permission:company_import_export_stock_report');
    });
    // Item wise details Report
    Route::controller(ItemWiseDetailsReportController::class)->group(function () {
        Route::get('item-wise-details/{itemMaster}', 'index')->name('item-details-report')
            ->middleware('permission:company_view_stock_report');
        Route::post('item-wise-details-report-pdf', 'pdfDownload')->name('item-wise-details-report')
            ->middleware('permission:company_import_export_stock_report');
        Route::get('item-wise-details-report-excel', 'excelDownload')->name('item-wise-details-excel')
            ->middleware('permission:company_import_export_stock_report');
    });
    //Party Wise Sales / Purchase Report
    Route::controller(PartyWiseSalesPurchaseReportController::class)->group(function () {
        Route::get('party-wise-sale-purchase-report', 'index')->name('party-wise-sales-purchase-report')
            ->middleware('permission:company_view_party_wise_sales_purchase_report');
        Route::get('export-party-wise-sale-purchase-report', 'export')->name('export-party-wise-sales-purchase-report')
            ->middleware('permission:company_import_export_party_wise_sales_purchase_report');
    });

    //Trial Balance Report
    Route::controller(TrialBalanceController::class)->group(function () {
        Route::get('trial-balance-report', 'index')->name('trial-balance-report')
            ->middleware('permission:company_view_trial_balance_report');
        Route::get('export-trial-balance', 'export')->name('export-trial-balance')
            ->middleware('permission:company_import_export_trial_balance_report');
    });
    //Bill Wise Profit Report
    Route::controller(BillWiseProfitReportController::class)->group(function () {
        Route::get('bill-wise-profit-report', 'index')->name('bill-wise-profit-report')->middleware('permission:company_view_bill_wise_profit_report');
        Route::get('export-bill-wise-profit-report', 'export')->name('export-bill-wise-profit-report')->middleware('permission:company_import_export_bill_wise_profit_report');
    });
    //Item Wise Profit Report
    Route::controller(ItemWiseProfitReportController::class)->group(function () {
        Route::get('item-wise-profit-report', 'index')->name('item-wise-profit-report')->middleware('permission:company_view_item_wise_profit_report');
        Route::get('export-item-wise-profit-report', 'export')->name('export-item-wise-profit-report')->middleware('permission:company_import_export_item_wise_profit_report');
    });
    //Customer Summary Report
    Route::controller(CustomerSummaryReportController::class)->group(function () {
        Route::get('customer-summary-report', 'index')->name('customer-summary-report')->middleware('permission:company_view_customer_summary_report');
        Route::get('export-customer-summary-report', 'export')->name('export-customer-summary-report')->middleware('permission:company_import_export_customer_summary_report');
        Route::get('customer-summary-report/download-ledger', 'downloadLedger')->name('customer-summary-report.download-ledger')->middleware('permission:company_import_export_customer_summary_report');
        Route::get('customer-summary-report/{ledgerPdfPreviewId}', 'getLedgerPdfPreview')->name('customer-summary-report.preview-ledger');
        Route::get('customer-summary-report/send-ledger-whatsapp/{ledgerId}', 'sendLedgerToWhatsapp')->name('customer-summary-report.send-ledger-whatsapp');
        Route::get('customer-summary-report/send-whatsapp-manually/{ledger}', 'sendWhatsappManually')->name('customer-summary-report.send-whatsapp-manually');
        // Route::get('customer-summary-report/email/{ledgerId}', 'customerSummaryReportMail')->name('customer-summary-report.email');
        Route::get('customer-summary-report/send-email/{ledgerId}', 'sendEmail')->name('customer-summary-report.send-email');
    });
    //Supplier Summary Report
    Route::controller(SupplierSummaryReportController::class)->group(function () {
        Route::get('supplier-summary-report', 'index')->name('supplier-summary-report')->middleware('permission:company_view_supplier_summary_report');
        Route::get('supplier-summary-report/preview/{ledgerPdfPreviewId}', 'getLedgerPdfPreview')->name('supplier-summary-report.preview-ledger');
        Route::get('export-supplier-summary-report', 'export')->name('export-supplier-summary-report')->middleware('permission:company_import_export_supplier_summary_report');
        Route::get('supplier-summary-report/ledger-export', 'exportLedger')->name('supplier-summary-report.ledger-export')->middleware('permission:company_import_export_supplier_summary_report');
        Route::get('supplier-summary-report/send-whatsapp/{ledgerId}', 'sendAutoWhatsapp')->name('supplier-summary-report.send-whatsapp');
        Route::get('supplier-summary-report/send-whatsapp-manually/{ledger}', 'sendWhatsappManually')->name('supplier-summary-report.send-whatsapp-manually');
        Route::get('supplier-summary-report/send-email/{ledgerId}', 'sendEmail')->name('supplier-summary-report.send-email');
    });
    //Balance Sheet Report
    Route::controller(BalanceSheetReportController::class)->group(function () {
        Route::get('balance-sheet-report', 'index')->name('balance-sheet-report')
            ->middleware('permission:company_view_balance_sheet_report');
        Route::get('balance-sheet-report-pdf', 'export')->name('balance-sheet-report-pdf')
            ->middleware('permission:company_import_export_balance_sheet_report');
    });
    //Cash Flow Report
    Route::controller(CashFlowStatementReportController::class)->group(function () {
        Route::get('cash-flow-report', 'index')->name('cash-flow-report')
            ->middleware('permission:company_view_cash_flow_report');
        Route::get('export-cash-flow-report', 'export')->name('export-cash-flow-report')
            ->middleware('permission:company_import_export_cash_flow_report');
    });
    //Item Stock Report
    Route::controller(ItemStockController::class, 'index')->group(function () {
        Route::get('item-stock', 'index')->name('item-stock.index');
        Route::post('item-stock', 'store')->name('item-stock.store');
        Route::put('item-stock/{id}', 'itemNameUpdate')->name('item-name.update');
        Route::delete('item-stock/{id} ', 'itemStockDestroy')->name('item-stock.destroy');
        Route::post('item-stock-closing-balance', 'itemStockClosingStore')->name('item-stock-closing-balance.store');
        Route::put('item-stock-closing-balance/{id}', 'itemStockClosingUpdate')->name('item-stock-closing-balance.update');
        Route::delete('item-stock-closing-balance/{id} ', 'itemStockClosingDestroy')->name('item-stock-closing-balance.destroy');
    });

    //Sale Report
    Route::controller(SaleReportController::class)->group(function () {
        Route::get('export-sales-report', 'export')->name('export-sales-report')
            ->middleware('permission:company_import_export_sale_report');
    });
    //Purchase Report
    Route::controller(PurchaseReportController::class)->group(function () {
        Route::get('export-purchase-report', 'export')->name('export-purchase-report')
            ->middleware('permission:company_import_export_purchase_report');
    });
    //Day Book Report
    Route::controller(DayBookReportController::class)->group(function () {
        Route::get('export-day-book-report', 'export')->name('export-day-book-report')
            ->middleware('permission:company_import_export_day_book_report');
    });
    //Outstanding Report
    Route::controller(OutstandingReportController::class)->group(function () {
        Route::get('outstanding-report-export', 'export')->name('outstanding-report-export')
            ->middleware('permission:company_import_export_outstanding_report');
    });
    //Ageing Report
    Route::controller(AgeingReportController::class)->group(function () {
        Route::get('ageing-report-export', 'export')->name('ageing-report-export')
            ->middleware('permission:company_import_export_ageing_report');
    });
    //Output Tax Report
    Route::controller(OutputTaxRegisterReportController::class)->group(function () {
        Route::get('reports/output-tax-register', 'export')->name('output-tax-register-report-export')
            ->middleware('permission:company_import_export_output_tax_report');
    });
    //HSN Inward Report
    Route::controller(HsnSummaryInwardReportController::class)->group(function () {
        Route::get('reports/hsn-in-ward', 'export')->name('hsn-in-ward-report')
            ->middleware('permission:company_import_export_hsn_inward_report');
    });
    //HSN Outward Report
    Route::controller(HsnSummaryReportController::class)->group(function () {
        Route::get('reports/hsn-out-ward', 'export')->name('hsn-out-ward-report')
            ->middleware('permission:company_import_export_hsn_outward_report');
    });
    //Input Tax Report
    Route::controller(InputTaxRegisterReportController::class)->group(function () {
        Route::get('reports/input-tax-register', 'export')->name('input-tax-register-report-export')
            ->middleware('permission:company_import_export_input_tax_report');
    });
    //Broker Report
    Route::controller(BrokerReportController::class)->group(function () {
        Route::get('export-broker-report', 'export')->name('export-broker-report')
            ->middleware('permission:company_import_export_broker_report');
    });
    //TDS Report
    Route::controller(TdsStatementReportController::class)->group(function () {
        Route::get('export-tds-liability-report', 'exportTdsLiabilityReport')->name('export-tds-liability-report')
            ->middleware('permission:company_import_export_tds_liability_report');
        Route::get('export-tds-return-report', 'exportTdsReturnReport')->name('export-tds-return-report')
            ->middleware('permission:company_import_export_tds_return_report');
    });
    //TCS Report
    Route::controller(TcsStatementController::class)->group(function () {
        Route::get('export-tcs-liability-report', 'exportTcsLiabilityReport')->name('export-tcs-liability-report')
            ->middleware('permission:company_import_export_tcs_liability_report');
        Route::get('export-tcs-return-report', 'exportTcsReturnReport')->name('export-tcs-return-report')
            ->middleware('permission:company_import_export_tcs_return_report');
    });
    //GSTR3B Report
    Route::controller(Gstr3BSummeryController::class)->group(function () {
        Route::get('reports/gstr-3b-summary-pdf', 'downloadPDF')->name('reports.gstr-3b-summary.pdf')
            ->middleware('permission:company_import_export_gstr_3b_report');
    });
    Route::get('reports/gstr-3b-detailed-excel', [Gstr3BDetailedController::class, 'gstr3bSummaryDetailedExcelExport'])
        ->name('reports.gstr-3b-detailed.excel')
        ->middleware('permission:company_import_export_gstr_3b_report');
    //GSTR1 Report
    Route::controller(Gstr1ReportController::class)->group(function () {
        Route::get('reports/gstr-1', 'index')->name('reports.gstr-1')
            ->middleware('permission:company_view_gstr_1_report');
        Route::get('reports/gstr-1-pdf', 'downloadPDF')->name('reports.gstr-1.pdf')
            ->middleware('permission:company_import_export_gstr_1_report');
        Route::get('reports/gstr-1-excel', 'gstr1ExcelReport')->name('reports.gstr-1-excel')
            ->middleware('permission:company_import_export_gstr_1_report');
        // Route::get('reports/prepare-gstr1-data', 'prepareGstr1Data')->name('reports.prepare-gstr1-data');
        Route::get('reports/gstr-1-json', 'jsonExport')->name('reports.gstr-1-json-export');
        Route::post('reports/file-gstr1', 'prepareGstr1Data')->name('reports.file-gstr1');
        Route::get('reports/file-gstr1-evc', 'sendEvcOtpForGSTR1Filing')->name('reports.file-gstr1-evc');
        Route::get('reports/file-gstr1-evc-otp', 'fileGstr1')->name('reports.file-gstr1-evc-otp');
        Route::get('reports/resend-verification-otp', 'resendVerificationOtp')->name('resend-verification-otp');
        Route::get('reports/generate-gstr1-summary', 'generateGstr1Summary')->name('reports.generate-gstr1-summary');

    });

    // GST Portal Login
    Route::controller(GstrLoginController::class)->group(function () {
        Route::post('gst-portal-login', 'login')->name('gst.portal.login');
        Route::get('gstr1-view-summary', 'viewGstr1Summary')->name('gstr1-view-summary');
        Route::get('gstr3b-view-summary', 'viewGstr3BSummary')->name('gstr3b-view-summary');
        Route::get('gstr1-view-detailed/{section}', 'viewGstr1Detailed')->name('gstr1-view-detailed');
        Route::get('reports/fetch-gst-data', 'fetchGstData')->name('reports.fetch-gst-data');
        Route::post('gstr-1-reload', 'reload')->name('reports.gstr-1-reload');
        Route::post('reset-gst-data-for-portal', 'resetGstDataForPortal')->name('reset-gst-data-for-portal');
        Route::get('get-gstr1-report-preview', 'getGstr1ReportPreview')->name('get-gstr1-report-preview');
        Route::get('reports/gstr1-error-report-excel', 'gstr1DetailedExcelExport')->name('reports.gstr1-error-report-excel');
    });

    //Trading P&L Report
    Route::controller(TradingProfitLossReportController::class)->group(function () {
        Route::get('trading-profit-loss', 'index')->name('trading-profit-loss')
            ->middleware('permission:company_view_trading_profit_loss_report');
        Route::get('export-trading-profit-loss', 'export')->name('export-trading-profit-loss')
            ->middleware('permission:company_import_export_trading_profit_loss_report');
    });

    // Gst Dashboard
    Route::controller(GstDashboardController::class)->group(function () {
        Route::get('/gst-dashboard', 'index')->name('gst-dashboard');
        Route::get('export-gstr1-report', 'export')->name('export-gstr1-report');
        Route::get('export-gstr1-detail-report', 'exportDetail')->name('export-gstr1-detail-report');
        Route::get('download-filed-gstr1-report', 'downloadFiledGstr1Report')->name('download-filed-gstr1-report');
        Route::get('download-filed-gstr3b-report', 'downloadFiledGstr3bReport')->name('download-filed-gstr3b-report');
        Route::get('get-gst-data', 'getGstData')->name('get-gst-data');
    });

    Route::controller(ColumnSelectorFieldController::class)->group(function () {
        Route::get('column-selector-field/{type}', 'getColumnSelectorFields')->name('column-selector-field');
        Route::post('column-selector-field/{type}', 'setColumnSelectorFields')->name('column-selector-field.update');
    });

    Route::controller(CompanyShortcutKeyController::class)->group(function () {
        Route::get('shortcut-key', 'index')->name('shortcut-key.index');
    });

    Route::controller(CompanySupportController::class)->group(function () {
        Route::get('support', 'index')->name('support.index');
    });

    Route::controller(CompanyFilterController::class)->group(function () {
        Route::post('company-filters', 'index')->name('company-filters');
    });

    Route::controller(FinancialYearOpeningBalanceController::class)->group(function () {
        Route::get('transfer-closing-balance', 'index')->name('transfer-closing-balance.index')->withoutMiddleware('trClosingBal');
        Route::post('transfer-closing-balance', 'transferClosingBalance')->name('transfer-closing-balance')->withoutMiddleware('trClosingBal');
    });

    Route::post('delete-notification', [NotificationToSendController::class, 'bulkDeleteNotification'])->name('bulk-delete-notification');

    Route::get('reports-menu', [CompanyController::class, 'reportsMenu'])
        ->middleware('permission:company_view_ledger_report|company_view_outstanding_report|company_view_ageing_report|company_view_sale_report|company_view_purchase_report|
        company_view_day_book_report|company_view_cash_bank_report|company_view_stock_report|company_view_broker_report|company_view_trial_balance_report|
        company_view_trading_profit_loss_report|company_view_balance_sheet_report|company_view_cash_flow_report|company_view_bill_wise_profit_report|
        company_view_item_wise_profit_report|company_view_customer_summary_report|company_view_item_stock|company_view_einvoice_report|company_view_eway_bill_report|
        company_view_gstr_1_report|company_view_gstr_3b_report|company_view_input_tax_report|company_view_output_tax_report|
        company_view_hsn_outward_report|company_view_hsn_inward_report|company_view_tds_liability_report|company_view_tds_return_report|
        company_view_tcs_liability_report|company_view_tcs_return_report|company_view_supplier_summary_report|company_view_party_wise_sales_purchase_report')
        ->name('reports-menu');

    Route::controller(TrashController::class)->group(function () {
        Route::get('trash', 'index')->name('trash.index')->middleware('permission:company_view_recycle_bin');
        Route::post('trash/hard-delete', 'hardDeleteTransaction')->name('trash.hard-delete')->middleware('permission:company_delete_recycle_bin');
        Route::post('trash/transaction-restore', 'restoreTransaction')->name('trash.restore-transaction')->middleware('permission:company_edit_recycle_bin');
        Route::get('trash/empty-whole-bin', 'emptyWholeBin')->name('trash.empty-whole-bin')->middleware('permission:company_delete_recycle_bin');
        Route::post('trash/empty-transaction', 'emptyTransaction')->name('trash.empty-transaction')->middleware('permission:company_delete_recycle_bin');
        Route::post('trash/bulk-delete', 'bulkDelete')->name('trash.bulk-delete')->middleware('permission:company_delete_recycle_bin');
        Route::post('trash/bulk-restore', 'bulkRestore')->name('trash.bulk-restore')->middleware('permission:company_edit_recycle_bin');
        Route::post('trash/check-transaction-exists', 'checkTransactionExists')->name('trash.check-transaction-exists')->middleware('permission:company_delete_recycle_bin');
    });

    Route::controller(FreezeTransactionController::class)->group(function () {
        Route::get('freeze-transaction', 'index')->name('freeze-transaction.index')->middleware('permission:company_view_freeze_transaction');
        Route::get('freeze-transaction/sent-otp-password', 'sentOtpAsPassword')->name('freeze-transaction.sent-otp-password')->middleware('permission:company_edit_freeze_transaction');
        Route::post('freeze-transaction/lock-transaction', 'lockTransaction')->name('freeze-transaction.lock-transaction')->middleware('permission:company_edit_freeze_transaction');
        Route::post('freeze-transaction/unlock-transaction', 'unlockTransaction')->name('freeze-transaction.unlock-transaction')->middleware('permission:company_edit_freeze_transaction');
    });

    Route::controller(AuditTrailController::class)->group(function () {
        Route::get('audit-trail', 'index')->name('audit-trail.index')
            ->middleware('permission:company_view_audit_trail');
    });

    Route::get('api-keys', [ThirdPartyApiKeyController::class, 'index'])->name('api-keys.index')->middleware('permission:company_view_developer_access_api_key');
    Route::post('api-keys/store', [ThirdPartyApiKeyController::class, 'store'])->name('api-keys.store')->middleware('permission:company_add_developer_access_api_key|company_edit_developer_access_api_key');
    Route::delete('api-keys/{id}/delete', [ThirdPartyApiKeyController::class, 'destroy'])->name('api-keys.delete')->middleware('permission:company_delete_developer_access_api_key');

    Route::get('customer-master', function () {
        return view('company.react-transaction');
    })->name('customer-master-report')->middleware('permission:company_view_customer_master_report');
    Route::get('supplier-master', function () {
        return view('company.react-transaction');
    })->name('supplier-master-report')->middleware('permission:company_view_supplier_master_report');

    Route::get('gstr2b-report-send-mail/{id}/{type}', [Gstr2BReconciliationAPIController::class, 'sendMail'])->name('gstr2b-report.send-mail');
    Route::get('download-excle', [Gstr2BReconciliationAPIController::class, 'downloadExcel'])->name('download-excel');
});

Route::controller(TutorialController::class)->group(function () {
    Route::get('tutorial', 'companyTutorialIndex')->name('company.tutorial.index')->middleware('auth');
});

Route::controller(EstimateQuoteTitleController::class)->group(function () {
    Route::post('estimate-quote-transaction-title', 'storeDefaultEstimateQuoteTitle')->name('estimate-quote-title.store')
        ->middleware('permission:company_edit_transaction_masters');
    Route::delete('estimate-quote-title/{title}/delete', 'destroy')->name('estimate-quote-title.delete')
        ->middleware('permission:company_edit_transaction_masters');
});

Route::controller(PurchaseOrderTitleController::class)->group(function () {
    Route::post('purchase-order-transaction-title', 'store')->name('purchase-order-title.store');
    Route::delete('purchase-order-transaction-title/{title}/delete', 'destroy')->name('purchase-order-title.delete');
});

Route::get('razorpay-onboard', [RazorpayController::class, 'onBoard'])->name('razorpay.init');
Route::post('razorpay-payment-success', [RazorpayController::class, 'paymentSuccess'])->name('razorpay.success');
Route::post('razorpay-payment-failed', [RazorpayController::class, 'paymentFailed'])->name('razorpay.failed');

Route::get('sale-payment-show', [SaleTransactionController::class, 'paymentShow'])->name('sale-payment-show');
Route::get('income-debit-note-payment-show', [IncomeDebitNoteTransactionController::class, 'paymentShow'])
    ->name('income-debit-note-payment-show');

Route::post('reports-menu/update-order', [CompanyController::class, 'reportsUpdateOrder'])->name('company.reports-update-order');
Route::post('reports-menu/add-favorite', [CompanyController::class, 'addFavoriteReport'])->name('company.add-favorite-report');

// Global Search
Route::post('/global-search', [GlobalSearchController::class, 'retrieveSearchData'])->name('company.global-search');
Route::get('/generate-pdf-with-print', function (Request $request) {
    $pdfUrl = $request->get('pdfViewRoute');

    return view('pdf', compact('pdfUrl'));
})->name('company.generate-pdf-with-print');
