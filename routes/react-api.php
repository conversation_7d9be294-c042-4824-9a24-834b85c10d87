<?php

use App\Http\Controllers\API\v2\GstLoginAPI\GstLoginApiController;
use App\Http\Controllers\API\v2\Gstr2BReconciliation\Gstr2BReconciliationAPIController;
use App\Http\Controllers\EInvoiceController;
use App\Http\Controllers\ReactAPI\AdvancePaymentAPIController;
use App\Http\Controllers\ReactAPI\Auth11zaAPIController;
use App\Http\Controllers\ReactAPI\BarcodeItem\BarcodeItemAPIController;
use App\Http\Controllers\ReactAPI\BrokerMasterAPIController;
use App\Http\Controllers\ReactAPI\CessRate\CessRateAPIController;
use App\Http\Controllers\ReactAPI\CommonAPIController;
use App\Http\Controllers\ReactAPI\CustomerMasterReport\CustomerMasterReportAPIController;
use App\Http\Controllers\ReactAPI\CustomFields\CustomFieldItemMasterAPIController;
use App\Http\Controllers\ReactAPI\CustomFields\CustomFieldsAPIController;
use App\Http\Controllers\ReactAPI\CustomFields\CustomFieldsItemAPIController;
use App\Http\Controllers\ReactAPI\DashboardAPIController;
use App\Http\Controllers\ReactAPI\DeliveryChallan\DeliveryChallanConfigurationAPIController;
use App\Http\Controllers\ReactAPI\DeliveryChallan\DeliveryChallanTransactionAPIController;
use App\Http\Controllers\ReactAPI\DispatchAddressAPIController;
use App\Http\Controllers\ReactAPI\ExpenseCreditNote\ExpenseCNConfigurationAPIController;
use App\Http\Controllers\ReactAPI\ExpenseCreditNote\ExpenseCNTransactionAPIController;
use App\Http\Controllers\ReactAPI\ExpenseDebitNote\ExpenseDebitNoteConfigurationAPIController;
use App\Http\Controllers\ReactAPI\ExpenseDebitNote\ExpenseDebitNoteTransactionAPIController;
use App\Http\Controllers\ReactAPI\GeneralSettingAPIController;
use App\Http\Controllers\ReactAPI\IncomeCreditNote\IncomeCNConfigurationAPIController;
use App\Http\Controllers\ReactAPI\IncomeCreditNote\IncomeCNTransactionAPIController;
use App\Http\Controllers\ReactAPI\IncomeDebitNote\IncomeDebitNoteConfigurationAPIController;
use App\Http\Controllers\ReactAPI\IncomeDebitNote\IncomeDebitNoteTransactionAPIController;
use App\Http\Controllers\ReactAPI\IncomeEstimateQuote\EstimateQuoteConfigurationAPIController;
use App\Http\Controllers\ReactAPI\IncomeEstimateQuote\EstimateQuoteTitleAPIController;
use App\Http\Controllers\ReactAPI\IncomeEstimateQuote\EstimateQuoteTransactionAPIController;
use App\Http\Controllers\ReactAPI\ItemGroupAPIController;
use App\Http\Controllers\ReactAPI\ItemMasterAPIController;
use App\Http\Controllers\ReactAPI\ItemStockAPIController;
use App\Http\Controllers\ReactAPI\LedgerAPIController;
use App\Http\Controllers\ReactAPI\PaymentMode\PaymentModeAPIController;
use App\Http\Controllers\ReactAPI\PdfConfiguration\PdfConfigurationAPIController;
use App\Http\Controllers\ReactAPI\Purchase\PurchaseTransactionAPIController;
use App\Http\Controllers\ReactAPI\Purchase\PurchaseTransactionConfigurationAPIController;
use App\Http\Controllers\ReactAPI\PurchaseOCR\PurchaseOCRAPIController;
use App\Http\Controllers\ReactAPI\PurchaseOrder\PurchaseOrderConfigurationAPIController;
use App\Http\Controllers\ReactAPI\PurchaseOrder\PurchaseOrderTitleAPIController;
use App\Http\Controllers\ReactAPI\PurchaseOrder\PurchaseOrderTransactionAPIController;
use App\Http\Controllers\ReactAPI\PurchaseReturn\PurchaseReturnConfigurationAPIController;
use App\Http\Controllers\ReactAPI\PurchaseReturn\PurchaseReturnTransactionAPIController;
use App\Http\Controllers\ReactAPI\RearrangeItems\RearrangeItemsAPIController;
use App\Http\Controllers\ReactAPI\RecurringInvoice\RecurringInvoiceAPIController;
use App\Http\Controllers\ReactAPI\RecurringInvoice\RecurringInvoiceConfigurationAPIController;
use App\Http\Controllers\ReactAPI\Sale\SaleTransactionAPIController;
use App\Http\Controllers\ReactAPI\Sale\TransactionsConfigurationAPIController;
use App\Http\Controllers\ReactAPI\SaleReturn\SaleReturnConfigurationAPIController;
use App\Http\Controllers\ReactAPI\SaleReturn\SaleReturnTransactionAPIController;
use App\Http\Controllers\ReactAPI\ShippingAddressAPIController;
use App\Http\Controllers\ReactAPI\SupplierMasterReport\SupplierMasterReportAPIController;
use App\Http\Controllers\ReactAPI\TopAndLeastSellingItemReportAPIController;
use App\Http\Controllers\ReactAPI\TransportMasterAPIController;
use App\Http\Controllers\ReactAPI\UnitOfMeasurement\UnitOfMeasurementAPIController;
use App\Http\Controllers\UserSuggestionController;
use App\Http\Controllers\Vastra\VastraAPIController;
use App\Http\Controllers\WalkthroughController;
use Illuminate\Support\Facades\Route;

Route::middleware('auth', 'role:franchises_admin|franchises_junior_account_executive|franchises_senior_account_executive|franchises_manager|franchises_senior_manager|client_admin|client_user|consultant|consultant_user', 'checkCompanyIsSetInSession', 'checkPlanIsExpire')->group(function () {

    /* Common Routes */
    Route::get('company-details', [CommonAPIController::class, 'companyDetails'])->name('get-company-details');
    Route::post('party-list', [CommonAPIController::class, 'partyList'])->name('get-party-list');
    Route::get('party-details/{party}', [CommonAPIController::class, 'partyDetails'])->name('get-party-details');
    Route::get('broker-list', [CommonAPIController::class, 'brokerList'])->name('get-broker-list');
    Route::get('transport-list', [CommonAPIController::class, 'transportList'])->name('get-transport-list');
    Route::get('classification-nature-type/{type}', [CommonAPIController::class, 'classificationNatureType'])->name('get-classification-nature-type');
    Route::post('item-list', [CommonAPIController::class, 'itemList'])->name('get-item-list');
    Route::get('item-details/{item}', [CommonAPIController::class, 'itemDetails'])->name('get-item-details');
    Route::post('item-details-ledger-list', [CommonAPIController::class, 'itemDetailsLedgerList'])->name('get-item-details-ledger-list');
    Route::get('item-details-unit-list', [CommonAPIController::class, 'itemDetailsUnitList'])->name('get-item-details-unit-list');
    Route::get('item-details-gst-rate-list', [CommonAPIController::class, 'itemDetailsGstRateList'])->name('get-item-details-gst-rate-list');
    Route::get('tcs-group-list/{type}', [CommonAPIController::class, 'tcsGroupList'])->name('get-tcs-group-list');
    Route::get('tds-group-list/{type}', [CommonAPIController::class, 'tdsGroupList'])->name('get-tds-group-list');
    Route::post('price-lists', [CommonAPIController::class, 'priceListItemPrice'])->name('price-list-item-price');
    Route::post('tcs-rate-list/{type}', [CommonAPIController::class, 'tcsRateList'])->name('get-tcs-rate');
    Route::post('tds-rate-list/{type}', [CommonAPIController::class, 'tdsRateList'])->name('get-tds-rate-list');
    Route::get('tds-tcs-details/{ledger_id}/{party_id?}', [CommonAPIController::class, 'tdsTcsDetails'])->name('get-tds-tcs-rate');
    Route::post('payment-ledger-list', [CommonAPIController::class, 'paymentLedgerList'])->name('get-payment-ledger-list');
    Route::get('payment-mode-list/{type}', [CommonAPIController::class, 'paymentModeList'])->name('payment-mode-list');
    Route::post('additional-charges-ledger-list', [CommonAPIController::class, 'additionalChargesLedgerList'])->name('get-additional-charges-ledger-list');
    Route::post('add-less-ledger-list', [CommonAPIController::class, 'addLessLedgerList'])->name('get-add-less-ledger-list');
    Route::post('bank-ledger-list', [CommonAPIController::class, 'bankLedgerList'])->name('get-bank-ledger-list');
    Route::post('is-warn-on-negative-stock/{item_id}', [CommonAPIController::class, 'isWarnOnNegativeStock'])->name('is-warn-on-negative-stock');
    Route::get('estimate-quote-titles-list', [CommonAPIController::class, 'estimateQuoteTitlesList'])->name('estimate-quote-titles-list');
    Route::get('get-income-or-expense-type/{type}', [CommonAPIController::class, 'getIncomeOrExpenseType'])->name('get-income-or-expense-type');
    Route::get('purchase-order-titles-list', [CommonAPIController::class, 'purchaseOrderTitlesList'])->name('purchase-order-titles-list');
    Route::get('get-previous-next-transactions/{type}/{id?}', [CommonAPIController::class, 'getPreviousNextTransactions'])->name('get-previous-next-transactions');
    Route::get('check-gst-number-exists/{gst_number}/{ledger_id?}', [CommonAPIController::class, 'checkGstNumber'])->name('check-gst-number');
    Route::post('check-customer-limit-amount', [CommonAPIController::class, 'checkCustomerLimitAmount'])->name('check-customer-limit-amount');
    Route::get('get-tds-tax-data/{tds_tax_type_id}', [CommonAPIController::class, 'getTdsTaxData'])->name('get-tds-tax-data');
    Route::get('get-tcs-tax-data/{tcs_tax_type_id}', [CommonAPIController::class, 'getTcsTaxData'])->name('get-tcs-tax-data');
    Route::get('states/{countryId}', [CommonAPIController::class, 'getStates'])->name('get-states');
    Route::get('cities/{stateId}', [CommonAPIController::class, 'getCities'])->name('get-cities');
    Route::get('user-permissions', [CommonAPIController::class, 'userPermissions'])->name('user-permissions');

    /* Dashboard */
    Route::group(['middleware' => 'permission:company_view_dashboard'], function () {
        Route::get('dashboard', [DashboardAPIController::class, 'index'])->name('dashboard');
        Route::get('dashboard/data', [DashboardAPIController::class, 'dashboardData'])->name('dashboard-data');
        Route::get('dashboard/performance-data', [DashboardAPIController::class, 'performanceData'])->name('dashboard-performance-data');
        Route::post('dashboard/cash-flow/{frequency?}', [DashboardAPIController::class, 'cashFlowData'])->name('cash-flow-data');
        Route::get('dashboard/company-setting/{type}', [DashboardAPIController::class, 'companyLogoAndSignature'])->name('company-logo-and-signature');
        Route::post('dashboard/company-setting/{type}', [DashboardAPIController::class, 'changeCompanyLogoAndSignature'])->name('change-company-logo-and-signature');
        Route::get('get-report-button', [DashboardAPIController::class, 'getReportButton'])->name('get-report-button');
        Route::get('add-report-button-list', [DashboardAPIController::class, 'addReportButtonList'])->name('add-report-button-list');
        Route::post('add-report-button', [DashboardAPIController::class, 'addReportButton'])->name('add-report-button');
        Route::delete('remove-report-button/{id}', [DashboardAPIController::class, 'removeReportButton'])->name('remove-report-button');
        Route::get('dashboard/sale-purchase-data/{frequency}', [DashboardAPIController::class, 'salePurchaseData'])->name('sale-purchase-data');
        Route::get('dashboard/sales-collection-data/{frequency}', [DashboardAPIController::class, 'saleCollectionData'])->name('sale-collection-data');
        Route::get('dashboard/purchases-payment-data/{frequency}', [DashboardAPIController::class, 'purchasePaymentData'])->name('purchase-payment-data');
        Route::post('dashboard/suggestion', [UserSuggestionController::class, 'store'])->name('suggestion');
        Route::get('dashboard/whats-new-data', [DashboardAPIController::class, 'whatsNewFullData'])->name('whats-new-data');
        Route::post('dashboard/book-demo', [DashboardAPIController::class, 'bookDemo'])->name('book-demo');
        Route::post('dashboard/walkthrough/select', [WalkthroughController::class, 'selectOption'])->name('walkthrough.select');
        Route::get('dashboard/walkthrough/status/{user_id}', [WalkthroughController::class, 'getStatus'])->name('walkthrough.status');
        Route::post('dashboard/walkthrough/complete', [WalkthroughController::class, 'markAsCompleted'])->name('walkthrough.complete');
    });
    /* Broker Master */
    Route::get('broker-masters-details', [BrokerMasterAPIController::class, 'getDetails'])->name('broker-masters-details');
    Route::get('get-broker-details/{id}', [BrokerMasterAPIController::class, 'getBrokerDetails'])->name('get-broker-details');
    Route::post('broker-masters', [BrokerMasterAPIController::class, 'create'])->name('broker-masters')->middleware('permission:company_add_new_broker_masters');
    Route::get('broker-masters/{id}', [BrokerMasterAPIController::class, 'edit'])->name('broker-masters-edit')->middleware('permission:company_edit_broker_masters');
    Route::post('broker-masters/{id}', [BrokerMasterAPIController::class, 'update'])->name('broker-masters-update')->middleware('permission:company_edit_broker_masters');

    /* Transport Master */
    Route::post('transport-masters', [TransportMasterAPIController::class, 'create'])->name('transport-masters')->middleware('permission:company_add_new_transport_masters');
    Route::get('transport-masters/{id}', [TransportMasterAPIController::class, 'edit'])->name('transport-masters-edit')->middleware('permission:company_edit_transport_masters');
    Route::post('transport-masters/{id}', [TransportMasterAPIController::class, 'update'])->name('transport-masters-update')->middleware('permission:company_edit_transport_masters');

    /* Item Master */
    Route::get('item-masters-details', [ItemMasterAPIController::class, 'getDetails'])->name('item-masters-details');
    Route::get('item-masters-configuration', [ItemMasterAPIController::class, 'getItemMasterConfiguration'])->name('get-item-masters-configuration');
    Route::post('item-masters-configuration', [ItemMasterAPIController::class, 'storeUpdateItemConfiguration'])->name('update-item-masters-configuration');
    Route::post('item-masters', [ItemMasterAPIController::class, 'create'])->name('item-masters')->middleware('permission:company_add_new_item_masters');
    Route::get('item-masters/{id}', [ItemMasterAPIController::class, 'edit'])->name('item-masters-edit')->middleware('permission:company_edit_item_masters');
    Route::post('item-masters/{id}', [ItemMasterAPIController::class, 'update'])->name('item-masters-update')->middleware('permission:company_edit_item_masters');

    /* Item Group */
    Route::get('get-item-groups', [ItemGroupAPIController::class, 'getItemGroup'])->name('get-item-group');
    Route::get('get-gst-cess-rate', [ItemGroupAPIController::class, 'getGstCessRate'])->name('get-gst-cess-rate');
    Route::post('item-group', [ItemGroupAPIController::class, 'create'])->name('create-item-group');

    /* Item Unit of Measurement */
    Route::get('get-unit-of-measurement', [UnitOfMeasurementAPIController::class, 'getUnitOfMeasurement'])->name('get-unit-of-measurement');
    Route::post('unit-of-measurement', [UnitOfMeasurementAPIController::class, 'create'])->name('create-unit-of-measurement');
    Route::get('unit-of-measurement/{id}', [UnitOfMeasurementAPIController::class, 'edit'])->name('edit-unit-of-measurement');
    Route::post('unit-of-measurement/{id}', [UnitOfMeasurementAPIController::class, 'update'])->name('update-unit-of-measurement');
    Route::get('unit-of-measurement/{id}/destroy', [UnitOfMeasurementAPIController::class, 'destroy'])->name('destroy-unit-of-measurement');

    /* Cess Rate */
    Route::get('cess-rate', [CessRateAPIController::class, 'index'])->name('cess-rate');
    Route::post('cess-rate', [CessRateAPIController::class, 'create'])->name('create-cess-rate');
    Route::get('cess-rate/{id}', [CessRateAPIController::class, 'edit'])->name('edit-cess-rate');
    Route::post('cess-rate/{id}', [CessRateAPIController::class, 'update'])->name('update-cess-rate');
    Route::get('cess-rate/{id}/destroy', [CessRateAPIController::class, 'destroy'])->name('destroy-cess-rate');

    /* Ledger */
    Route::get('ledgers-group-details/{type}', [LedgerAPIController::class, 'ledgerGroupDetails'])->name('ledger-group-details');
    Route::get('ledgers-details/{group_id}', [LedgerAPIController::class, 'ledgerDetails'])->name('ledger-details');
    Route::post('ledgers', [LedgerAPIController::class, 'create'])->name('ledgers')->middleware('permission:company_add_new_ledgers');
    Route::get('ledgers/{id}', [LedgerAPIController::class, 'edit'])->name('ledgers-edit')->middleware('permission:company_edit_ledgers');
    Route::post('ledgers/{id}', [LedgerAPIController::class, 'update'])->name('ledgers-update')->middleware('permission:company_edit_ledgers');
    Route::get('delete-ledger-media/{id}', [LedgerAPIController::class, 'deleteLedgerMedia'])->name('delete-ledger-media');
    Route::get('get-holding-profit-ratio', [LedgerAPIController::class, 'getHoldingProfitRatio'])->name('get-holding-profit-ratio');
    Route::post('/ledger-bill-wish-excel', [LedgerAPIController::class, 'ledgerBillWishExcel'])->name('ledger-bill-wish-excel');

    /* Ledger Group */
    Route::get('get-ledger-groups', [LedgerAPIController::class, 'getLedgerGroup'])->name('get-ledger-group');
    Route::post('ledgers-group', [LedgerAPIController::class, 'createLedgerGroup'])->name('create-ledgers-group')->middleware('permission:company_add_new_ledgers|company_edit_ledgers');

    /* Location of Assets */
    Route::post('store-location-of-assets', [LedgerAPIController::class, 'storeLocationOfAssets'])->name('store-location-of-assets')->middleware('permission:company_add_new_ledgers|company_edit_ledgers');

    /* Dispatch Address */
    Route::get('dispatch-address-list', [DispatchAddressAPIController::class, 'dispatchAddressList'])->name('get-dispatch-address-list');
    Route::post('dispatch-address', [DispatchAddressAPIController::class, 'create'])->name('dispatch-address');
    Route::get('dispatch-address/{id}', [DispatchAddressAPIController::class, 'edit'])->name('dispatch-address-edit');
    Route::post('dispatch-address/{id}', [DispatchAddressAPIController::class, 'update'])->name('dispatch-address-update');
    Route::post('dispatch-address/{id}/delete', [DispatchAddressAPIController::class, 'destroy'])->name('dispatch-address-delete');

    /* Shipping Address */
    Route::get('shipping-address-list/{id}', [ShippingAddressAPIController::class, 'shippingAddressList'])->name('get-shipping-address-list');
    Route::post('shipping-address', [ShippingAddressAPIController::class, 'create'])->name('shipping-address');
    Route::get('shipping-address/{id}', [ShippingAddressAPIController::class, 'edit'])->name('shipping-address-edit');
    Route::post('shipping-address/{id}', [ShippingAddressAPIController::class, 'update'])->name('shipping-address-update');
    Route::post('shipping-address/{id}/delete', [ShippingAddressAPIController::class, 'destroy'])->name('shipping-address-delete');
    Route::get('update-selected-shipping-address/{address_id}/{ledger_id}', [ShippingAddressAPIController::class, 'updateSelectedShippingAddress'])->name('update-selected-shipping-address');

    /* Income Estimate Quote Transaction Routes */
    Route::get('income-estimate-quote-invoice-number', [EstimateQuoteTransactionAPIController::class, 'getEstimateQuoteInvoiceNumber'])->name('get-income-estimate-quote-invoice-number')->middleware('permission:company_add_new_income');
    Route::post('income-estimate-quote-transactions', [EstimateQuoteTransactionAPIController::class, 'create'])->name('income-estimate-quote-transaction')->middleware('permission:company_add_new_income');
    Route::get('income-estimate-quote-transactions/{id}', [EstimateQuoteTransactionAPIController::class, 'edit'])->name('income-estimate-quote-transaction-edit')->middleware('permission:company_edit_income|company_add_new_income');
    Route::post('income-estimate-quote-transactions/{id}', [EstimateQuoteTransactionAPIController::class, 'update'])->name('income-estimate-quote-transaction-update')->middleware('permission:company_edit_income');
    Route::get('income-estimate-quote-transactions/{id}/duplicate', [EstimateQuoteTransactionAPIController::class, 'duplicate'])->name('income-estimate-quote-transaction-duplicate')->middleware('permission:company_add_new_income');
    Route::get('income-estimate-quote-transactions/{id}/delete', [EstimateQuoteTransactionAPIController::class, 'destroy'])->name('income-estimate-quote-transaction-delete')->middleware('permission:company_delete_income');
    Route::get('income-estimate-quote-transactions/{id}/create-sale', [EstimateQuoteTransactionAPIController::class, 'createSaleFromEstimate'])->name('create-sale-from-estimate-transaction');

    /* Income Estimate Quote Transaction Configuration Routes */
    Route::get('income-estimate-quote-configuration', [EstimateQuoteConfigurationAPIController::class, 'estimateQuoteConfiguration'])->name('get-income-estimate-quote-configuration')->middleware('permission:company_add_new_income|company_edit_income');
    Route::post('income-estimate-quote-configuration/{type?}', [EstimateQuoteConfigurationAPIController::class, 'updateEstimateQuoteConfiguration'])->name('update-income-estimate-quote-configuration')->middleware('permission:company_add_new_income|company_edit_income');

    /* Sale Transaction Routes */
    Route::get('sale-invoice-number', [SaleTransactionAPIController::class, 'getSaleInvoiceNumber'])->name('get-sale-invoice-number')->middleware('permission:company_add_new_income');
    Route::post('sale-transactions', [SaleTransactionAPIController::class, 'create'])->name('sale-transaction')->middleware('permission:company_add_new_income');
    Route::get('sale-transactions/{id}', [SaleTransactionAPIController::class, 'edit'])->name('sale-transaction-edit')->middleware('permission:company_edit_income|company_add_new_income');
    Route::get('sale-transactions-details/{id}', [SaleTransactionAPIController::class, 'saleTransactionDetails'])->name('sale-transaction-details')->middleware('permission:company_edit_income|company_add_new_income');
    Route::post('sale-transactions/{id}', [SaleTransactionAPIController::class, 'update'])->name('sale-transaction-update')->middleware('permission:company_edit_income');
    Route::get('sale-transactions/{id}/duplicate', [SaleTransactionAPIController::class, 'duplicate'])->name('sale-transaction-duplicate')->middleware('permission:company_add_new_income');
    Route::post('sale-transaction/estimate', [SaleTransactionAPIController::class, 'estimate'])->name('sale-transaction-estimate')->middleware('permission:company_add_new_income|company_edit_income');
    Route::post('sale-transaction/challan', [SaleTransactionAPIController::class, 'deliveryChallan'])->name('sale-transaction-delivery-challan')->middleware('permission:company_add_new_income|company_edit_income');
    Route::get('sale-transactions/{id}/sale-return', [SaleTransactionAPIController::class, 'getSaleForSaleReturn'])->name('get-sale-for-sale-return')->middleware('permission:company_add_new_income');
    Route::get('sale-transactions/{id}/delete', [SaleTransactionAPIController::class, 'destroy'])->name('sale-transaction-delete')->middleware('permission:company_delete_income');

    /* Sale Transaction Configuration Routes */
    Route::get('sale-configuration', [TransactionsConfigurationAPIController::class, 'saleConfiguration'])->name('get-sale-configuration')->middleware('permission:company_add_new_income|company_edit_income');
    Route::post('sale-configuration/{type?}', [TransactionsConfigurationAPIController::class, 'updateSaleConfiguration'])->name('update-sale-configuration')->middleware('permission:company_add_new_income|company_edit_income');

    /* Delivery Challan Transaction Routes */
    Route::get('delivery-challan-invoice-number', [DeliveryChallanTransactionAPIController::class, 'getDeliveryChallanInvoiceNumber'])->name('get-delivery-challan-invoice-number')->middleware('permission:company_add_new_income');
    Route::post('delivery-challan-transactions', [DeliveryChallanTransactionAPIController::class, 'create'])->name('delivery-challan-transaction')->middleware('permission:company_add_new_income');
    Route::get('delivery-challan-transactions/{id}', [DeliveryChallanTransactionAPIController::class, 'edit'])->name('delivery-challan-transaction-edit')->middleware('permission:company_edit_income|company_add_new_income');
    Route::post('delivery-challan-transactions/{id}', [DeliveryChallanTransactionAPIController::class, 'update'])->name('delivery-challan-transaction-update')->middleware('permission:company_edit_income');
    Route::get('delivery-challan-transactions/{id}/duplicate', [DeliveryChallanTransactionAPIController::class, 'duplicate'])->name('delivery-challan-transaction-duplicate')->middleware('permission:company_add_new_income');
    Route::get('delivery-challan-transactions/{id}/sale', [DeliveryChallanTransactionAPIController::class, 'getSaleTransaction'])->name('delivery-challan-transaction-sale')->middleware('permission:company_add_new_income');
    Route::get('delivery-challan-transactions/{id}/create-sale', [DeliveryChallanTransactionAPIController::class, 'createSaleFromDelivery'])->name('create-sale-from-delivery');
    Route::get('delivery-challan-transactions/{id}/delete', [DeliveryChallanTransactionAPIController::class, 'destroy'])->name('delivery-challan-transaction-delete')->middleware('permission:company_delete_income');

    /* Delivery Challan Transaction Configuration Routes */
    Route::get('delivery-challan-configuration', [DeliveryChallanConfigurationAPIController::class, 'deliveryChallanConfiguration'])->name('get-delivery-challan-configuration')->middleware('permission:company_add_new_income|company_edit_income');
    Route::post('delivery-challan-configuration/{type?}', [DeliveryChallanConfigurationAPIController::class, 'updateDeliveryChallanConfiguration'])->name('update-delivery-challan-configuration')->middleware('permission:company_add_new_income|company_edit_income');

    /* Sale Return Transaction Routes */
    Route::get('sale-return-invoice-number', [SaleReturnTransactionAPIController::class, 'getSaleReturnInvoiceNumber'])->name('get-sale-return-invoice-number')->middleware('permission:company_add_new_income');
    Route::post('sale-return-transactions', [SaleReturnTransactionAPIController::class, 'create'])->name('sale-return-transaction')->middleware('permission:company_add_new_income');
    Route::get('sale-return-transactions/{id}', [SaleReturnTransactionAPIController::class, 'edit'])->name('sale-return-transaction-edit')->middleware('permission:company_edit_income');
    Route::post('sale-return-transactions/{id}', [SaleReturnTransactionAPIController::class, 'update'])->name('sale-return-transaction-update')->middleware('permission:company_edit_income');
    Route::get('sale-return-transactions/{id}/duplicate', [SaleReturnTransactionAPIController::class, 'duplicate'])->name('sale-return-transaction-duplicate')->middleware('permission:company_add_new_income');
    Route::get('sale-return-transactions/{id}/delete', [SaleReturnTransactionAPIController::class, 'destroy'])->name('sale-return-transaction-delete')->middleware('permission:company_delete_income');

    /* Sale Return Transaction Configuration Routes */
    Route::get('sale-return-configuration', [SaleReturnConfigurationAPIController::class, 'saleReturnConfiguration'])->name('get-sale-return-configuration')->middleware('permission:company_add_new_income|company_edit_income');
    Route::post('sale-return-configuration/{type?}', [SaleReturnConfigurationAPIController::class, 'updateSaleReturnConfiguration'])->name('update-sale-return-configuration')->middleware('permission:company_add_new_income|company_edit_income');

    /* Income Debit Note Routes */
    Route::get('debit-note-invoice-number', [IncomeDebitNoteTransactionAPIController::class, 'getDebitNoteInvoiceNumber'])->name('get-debit-note-invoice-number')->middleware('permission:company_add_new_income');
    Route::post('income-debit-notes', [IncomeDebitNoteTransactionAPIController::class, 'create'])->name('income-debit-note')->middleware('permission:company_add_new_income');
    Route::get('income-debit-notes/{id}', [IncomeDebitNoteTransactionAPIController::class, 'edit'])->name('income-debit-note-edit')->middleware('permission:company_edit_income');
    Route::post('income-debit-notes/{id}', [IncomeDebitNoteTransactionAPIController::class, 'update'])->name('income-debit-note-update')->middleware('permission:company_edit_income');
    Route::get('income-debit-notes/{id}/duplicate', [IncomeDebitNoteTransactionAPIController::class, 'duplicate'])->name('income-debit-note-duplicate')->middleware('permission:company_add_new_income');
    Route::get('income-debit-notes/{id}/delete', [IncomeDebitNoteTransactionAPIController::class, 'destroy'])->name('income-debit-note-delete')->middleware('permission:company_delete_income');

    /* Income Debit Note Transaction Configuration Routes */
    Route::get('income-debit-note-configuration', [IncomeDebitNoteConfigurationAPIController::class, 'incomeDebitNoteConfiguration'])->name('income-debit-note-configuration')->middleware('permission:company_add_new_income|company_edit_income');
    Route::post('income-debit-note-configuration/{type?}', [IncomeDebitNoteConfigurationAPIController::class, 'updateIncomeDebitNoteConfiguration'])->name('update-income-debit-note-configuration')->middleware('permission:company_add_new_income|company_edit_income');

    /* Income Credit Note Transaction Routes */
    Route::get('income-credit-note-invoice-number', [IncomeCNTransactionAPIController::class, 'getIncomeCNInvoiceNumber'])->name('get-income-credit-note-invoice-number')->middleware('permission:company_add_new_income');
    Route::post('income-credit-note-transactions', [IncomeCNTransactionAPIController::class, 'create'])->name('income-credit-note-transaction')->middleware('permission:company_add_new_income');
    Route::get('income-credit-note-transactions/{id}', [IncomeCNTransactionAPIController::class, 'edit'])->name('income-credit-note-transaction-edit')->middleware('permission:company_edit_income');
    Route::post('income-credit-note-transactions/{id}', [IncomeCNTransactionAPIController::class, 'update'])->name('income-credit-note-transaction-update')->middleware('permission:company_edit_income');
    Route::get('income-credit-note-transactions/{id}/duplicate', [IncomeCNTransactionAPIController::class, 'duplicate'])->name('income-credit-note-transaction-duplicate')->middleware('permission:company_add_new_income');
    Route::get('income-credit-note-transactions/{id}/delete', [IncomeCNTransactionAPIController::class, 'destroy'])->name('income-credit-note-transaction-delete')->middleware('permission:company_delete_income');

    /* Income Credit Note Transaction Configuration Routes */
    Route::get('income-credit-note-configuration', [IncomeCNConfigurationAPIController::class, 'incomeCreditNoteConfiguration'])->name('get-income-credit-note-configuration')->middleware('permission:company_add_new_income|company_edit_income');
    Route::post('income-credit-note-configuration/{type?}', [IncomeCNConfigurationAPIController::class, 'updateIncomeCreditNoteConfiguration'])->name('update-income-credit-note-configuration')->middleware('permission:company_add_new_income|company_edit_income');

    /* Purchase Transaction Routes */
    Route::get('purchase-invoice-number', [PurchaseTransactionAPIController::class, 'getPurchaseInvoiceNumber'])->name('get-purchase-invoice-number')->middleware('permission:company_add_new_expense');
    Route::get('purchase-order-number-list/{party_id}', [PurchaseTransactionAPIController::class, 'getPurchaseOrderNumberList'])->name('get-purchase-order-number-list')->middleware('permission:company_add_new_expense|company_edit_expense');
    Route::post('purchase-transactions', [PurchaseTransactionAPIController::class, 'create'])->name('purchase-transaction')->middleware('permission:company_add_new_expense');
    Route::get('purchase-transactions/{id}', [PurchaseTransactionAPIController::class, 'edit'])->name('purchase-transaction-edit')->middleware('permission:company_edit_expense|company_add_new_expense');
    Route::get('purchase-transactions-details/{id}', [PurchaseTransactionAPIController::class, 'purchaseTransactionDetails'])->name('purchase-transaction-details')->middleware('permission:company_edit_expense|company_add_new_expense');
    Route::post('purchase-transactions/{id}', [PurchaseTransactionAPIController::class, 'update'])->name('purchase-transaction-update')->middleware('permission:company_edit_expense');
    Route::get('purchase-transactions/{id}/duplicate', [PurchaseTransactionAPIController::class, 'duplicate'])->name('purchase-transaction-duplicate')->middleware('permission:company_add_new_expense');
    Route::post('purchase-transaction/purchase-order', [PurchaseTransactionAPIController::class, 'multiPurchaseOrder'])->name('purchase-transaction-purchase-order')->middleware('permission:company_add_new_expense');
    Route::get('purchase-transactions/{id}/delete', [PurchaseTransactionAPIController::class, 'destroy'])->name('purchase-transaction-delete')->middleware('permission:company_delete_expense');
    Route::get('purchase-transactions/{id}/purchase-return', [PurchaseTransactionAPIController::class, 'getPurchaseForPurchaseReturn'])->name('get-purchase-for-purchase-return')->middleware('permission:company_add_new_expense');

    /* Purchase Transaction Configuration Routes */
    Route::get('purchase-configuration', [PurchaseTransactionConfigurationAPIController::class, 'purchaseConfiguration'])->name('get-purchase-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');
    Route::post('purchase-configuration/{type?}', [PurchaseTransactionConfigurationAPIController::class, 'updatePurchaseConfiguration'])->name('update-purchase-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');

    /* Purchase Order Transaction Routes */
    Route::get('purchase-order-invoice-number', [PurchaseOrderTransactionAPIController::class, 'getPurchaseOrderInvoiceNumber'])->name('get-purchase-order-invoice-number')->middleware('permission:company_add_new_expense');
    Route::post('purchase-order-transactions', [PurchaseOrderTransactionAPIController::class, 'create'])->name('purchase-order-transaction')->middleware('permission:company_add_new_expense');
    Route::get('purchase-order-transactions/{id}', [PurchaseOrderTransactionAPIController::class, 'edit'])->name('purchase-order-transaction-edit')->middleware('permission:company_edit_expense|company_add_new_expense');
    Route::post('purchase-order-transactions/{id}', [PurchaseOrderTransactionAPIController::class, 'update'])->name('purchase-order-transaction-update')->middleware('permission:company_edit_expense');
    Route::get('purchase-order-transactions/{id}/duplicate', [PurchaseOrderTransactionAPIController::class, 'duplicate'])->name('purchase-order-transaction-duplicate')->middleware('permission:company_add_new_expense');
    Route::get('purchase-order-transactions/{id}/purchase-create-purchase-order', [PurchaseOrderTransactionAPIController::class, 'purchaseCreatePurchaseOrder'])->name('purchase-order-transaction-book')->middleware('permission:company_add_new_expense');
    Route::get('purchase-order-transactions/{id}/delete', [PurchaseOrderTransactionAPIController::class, 'destroy'])->name('purchase-order-transaction-delete')->middleware('permission:company_delete_expense');
    Route::get('purchase-order-transactions/{id}/create-purchase', [PurchaseOrderTransactionAPIController::class, 'createPurchaseFromPurchaseOrder'])->name('create-purchase-from-purchase-order');

    /* Purchase Order Transaction Routes */
    Route::get('purchase-order-configuration', [PurchaseOrderConfigurationAPIController::class, 'purchaseOrderConfiguration'])->name('get-purchase-order-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');
    Route::post('purchase-order-configuration/{type?}', [PurchaseOrderConfigurationAPIController::class, 'updatePurchaseOrderConfiguration'])->name('update-purchase-order-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');

    /* Purchase return Transaction Routes */
    Route::get('purchase-return-invoice-number', [PurchaseReturnTransactionAPIController::class, 'getPurchaseReturnInvoiceNumber'])->name('get-purchase-return-invoice-number')->middleware('permission:company_add_new_expense');
    Route::post('purchase-return-transactions', [PurchaseReturnTransactionAPIController::class, 'create'])->name('purchase-return-transaction')->middleware('permission:company_add_new_expense');
    Route::get('purchase-return-transactions/{id}', [PurchaseReturnTransactionAPIController::class, 'edit'])->name('purchase-return-transaction-edit')->middleware('permission:company_edit_expense');
    Route::post('purchase-return-transactions/{id}', [PurchaseReturnTransactionAPIController::class, 'update'])->name('purchase-return-transaction-update')->middleware('permission:company_edit_expense');
    Route::get('purchase-return-transactions/{id}/duplicate', [PurchaseReturnTransactionAPIController::class, 'duplicate'])->name('purchase-return-transaction-duplicate')->middleware('permission:company_add_new_expense');
    Route::get('purchase-return-transactions/{id}/delete', [PurchaseReturnTransactionAPIController::class, 'destroy'])->name('purchase-return-transaction-delete')->middleware('permission:company_delete_expense');

    /* Purchase Return Transaction Configuration Routes */
    Route::get('purchase-return-configuration', [PurchaseReturnConfigurationAPIController::class, 'purchaseReturnConfiguration'])->name('get-purchase-return-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');
    Route::post('purchase-return-configuration/{type?}', [PurchaseReturnConfigurationAPIController::class, 'updatePurchaseReturnConfiguration'])->name('update-purchase-return-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');

    /* Expense Debit Note Transaction Routes */
    Route::get('expense-debit-note-invoice-number', [ExpenseDebitNoteTransactionAPIController::class, 'getExpenseDebitNoteInvoiceNumber'])->name('get-expense-debit-note-invoice-number')->middleware('permission:company_add_new_expense');
    Route::post('expense-debit-note-transactions', [ExpenseDebitNoteTransactionAPIController::class, 'create'])->name('expense-debit-note-transaction-create')->middleware('permission:company_add_new_expense');
    Route::get('expense-debit-note-transactions/{id}', [ExpenseDebitNoteTransactionAPIController::class, 'edit'])->name('expense-debit-note-transaction-edit')->middleware('permission:company_edit_expense');
    Route::post('expense-debit-note-transactions/{id}', [ExpenseDebitNoteTransactionAPIController::class, 'update'])->name('expense-debit-note-transaction-update')->middleware('permission:company_edit_expense');
    Route::get('expense-debit-note-transactions/{id}/duplicate', [ExpenseDebitNoteTransactionAPIController::class, 'duplicate'])->name('expense-debit-note-transaction-duplicate')->middleware('permission:company_add_new_expense');
    Route::get('expense-debit-note-transactions/{id}/delete', [ExpenseDebitNoteTransactionAPIController::class, 'destroy'])->name('expense-debit-note-transaction-delete')->middleware('permission:company_delete_expense');

    /* Expense Debit Note Transaction Configuration Routes */
    Route::get('expense-debit-note-configuration', [ExpenseDebitNoteConfigurationAPIController::class, 'expenseDebitNoteConfiguration'])->name('get-expense-debit-note-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');
    Route::post('expense-debit-note-configuration/{type?}', [ExpenseDebitNoteConfigurationAPIController::class, 'updateExpenseDebitNoteConfiguration'])->name('update-expense-debit-note-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');

    /* Expense Credit Note Transaction Routes */
    Route::get('expense-credit-note-invoice-number', [ExpenseCNTransactionAPIController::class, 'getExpenseCreditNoteInvoiceNumber'])->name('get-expense-credit-note-invoice-number')->middleware('permission:company_add_new_expense');
    Route::post('expense-credit-note-transactions', [ExpenseCNTransactionAPIController::class, 'create'])->name('expense-credit-note-transaction')->middleware('permission:company_add_new_expense');
    Route::get('expense-credit-note-transactions/{id}', [ExpenseCNTransactionAPIController::class, 'edit'])->name('expense-credit-note-transaction-edit')->middleware('permission:company_edit_expense');
    Route::post('expense-credit-note-transactions/{id}', [ExpenseCNTransactionAPIController::class, 'update'])->name('expense-credit-note-transaction-update')->middleware('permission:company_edit_expense');
    Route::get('expense-credit-note-transactions/{id}/duplicate', [ExpenseCNTransactionAPIController::class, 'duplicate'])->name('expense-credit-note-transaction-duplicate')->middleware('permission:company_add_new_expense');
    Route::get('expense-credit-note-transactions/{id}/delete', [ExpenseCNTransactionAPIController::class, 'destroy'])->name('expense-credit-note-transaction-delete')->middleware('permission:company_delete_expense');

    /* Expense Credit Note Transaction Configuration Routes */
    Route::get('expense-credit-note-configuration', [ExpenseCNConfigurationAPIController::class, 'expenseCreditNoteConfiguration'])->name('get-expense-credit-note-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');
    Route::post('expense-credit-note-configuration/{type?}', [ExpenseCNConfigurationAPIController::class, 'updateExpenseCreditNoteConfiguration'])->name('update-expense-credit-note-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');

    /* Advance Payment Routes */
    Route::get('get-unsettled-invoices/{ledger_id}/{type}/{transaction_id?}', [AdvancePaymentAPIController::class, 'getUnsettledInvoices'])->name('get-unsettled-invoices')->middleware('permission:company_add_new_income|company_edit_income|company_add_new_expense|company_edit_expense');

    /* E-invoice */
    Route::post('generate-e-invoice/{transactionId}/{transactionType}', [EInvoiceController::class, 'eInvoice'])->name('generate-e-invoice');

    /* Item Stock Routes */
    Route::get('item-stock-list/{ledgerId}', [ItemStockAPIController::class, 'getItemStockList'])->name('get-item-stock-list');
    Route::post('item-stock', [ItemStockAPIController::class, 'itemStockStore'])->name('item-stock-store')->middleware('permission:company_add_new_ledgers');
    Route::get('item-stock/{id}', [ItemStockAPIController::class, 'itemStockEdit'])->name('item-stock-edit')->middleware('permission:company_edit_ledgers');
    Route::post('item-stock/{id}', [ItemStockAPIController::class, 'itemStockUpdate'])->name('item-stock-update')->middleware('permission:company_edit_ledgers');
    Route::get('item-stock/{id}/delete', [ItemStockAPIController::class, 'itemStockDestroy'])->name('item-stock-destroy')->middleware('permission:company_delete_ledgers');

    /* Custom Fields Routes */
    Route::get('custom-fields/types', [CustomFieldsAPIController::class, 'getInputTypes'])->name('get-input-type');
    Route::post('custom-fields', [CustomFieldsAPIController::class, 'store'])->name('store-custom-fields');
    Route::post('custom-fields/update-status', [CustomFieldsAPIController::class, 'updateStatus'])->name('update-status-custom-fields');
    Route::get('custom-fields/{id}/edit', [CustomFieldsAPIController::class, 'edit'])->name('edit-custom-fields');
    Route::post('custom-fields/{id}/update', [CustomFieldsAPIController::class, 'update'])->name('update-custom-fields');
    Route::get('custom-fields/{id}/delete', [CustomFieldsAPIController::class, 'delete'])->name('delete-custom-fields');

    /* rearrange items */
    Route::get('rearrange-items/{type}/{invoiceType}', [RearrangeItemsAPIController::class, 'getRearrangeItems'])->name('get-rearrange-items');
    Route::post('rearrange-items', [RearrangeItemsAPIController::class, 'rearrangeItems'])->name('rearrange-items');

    // pdf configuration api
    Route::get('get-print-settings', [PdfConfigurationAPIController::class, 'getPrintSettings'])->name('get-print-settings')->middleware('permission:company_view_print_setting|company_edit_print_setting');
    Route::get('get-pdf-template/{type}/{pdfFormat}', [PdfConfigurationAPIController::class, 'getPdfTemplate'])->name('get-pdf-template')->middleware('permission:company_view_print_setting|company_edit_print_setting');
    Route::post('save-pdf-template', [PdfConfigurationAPIController::class, 'savePdfTemplate'])->name('save-pdf-template')->middleware('permission:company_edit_print_setting');
    Route::post('update-pdf-format', [PdfConfigurationAPIController::class, 'updatePdfFormat'])->name('update-pdf-format')->middleware('permission:company_edit_print_setting');
    Route::get('pdf-header-setting/{type}', [PdfConfigurationAPIController::class, 'getPdfHeaderSetting'])->name('get-pdf-header-setting')->middleware('permission:company_view_print_setting|company_edit_print_setting');
    Route::post('pdf-header-setting/{type}', [PdfConfigurationAPIController::class, 'savePdfHeaderSetting'])->name('change-pdf-header-setting')->middleware('permission:company_edit_print_setting');
    Route::get('pdf-details-setting/{type}', [PdfConfigurationAPIController::class, 'getPdfDetailsSetting'])->name('get-pdf-details-setting')->middleware('permission:company_view_print_setting|company_edit_print_setting');
    Route::post('pdf-details-setting/{type}', [PdfConfigurationAPIController::class, 'savePdfDetailsSetting'])->name('change-pdf-details-setting')->middleware('permission:company_edit_print_setting');
    Route::get('pdf-footer-setting/{type}', [PdfConfigurationAPIController::class, 'getPdfFooterSetting'])->name('get-pdf-footer-setting')->middleware('permission:company_view_print_setting|company_edit_print_setting');
    Route::post('pdf-footer-setting/{type}', [PdfConfigurationAPIController::class, 'savePdfFooterSetting'])->name('change-pdf-footer-setting')->middleware('permission:company_edit_print_setting');
    Route::post('update-print-show-hide-setting/{type}', [PdfConfigurationAPIController::class, 'updatePrintShowHideSetting'])->name('update-print-show-hide-setting');
    Route::get('pdf-company-setting/{key}', [PdfConfigurationAPIController::class, 'pdfCompanySetting'])->name('pdf-company-setting')->middleware('permission:company_view_print_setting|company_edit_print_setting');
    Route::post('update-pdf-slogan', [PdfConfigurationAPIController::class, 'updatePdfSloganSetting'])->name('update-pdf-slogan')->middleware('permission:company_edit_print_setting');
    Route::post('update-pdf-logo', [PdfConfigurationAPIController::class, 'updatePdfLogoSetting'])->name('update-pdf-logo')->middleware('permission:company_edit_print_setting');
    Route::post('update-pdf-signature', [PdfConfigurationAPIController::class, 'updatePdfSignatureSetting'])->name('update-pdf-signature')->middleware('permission:company_edit_print_setting');
    Route::post('update-pdf-email', [PdfConfigurationAPIController::class, 'updatePdfEmailSetting'])->name('update-pdf-email')->middleware('permission:company_edit_print_setting');
    Route::post('update-pdf-mobile-number', [PdfConfigurationAPIController::class, 'updatePdfMobileNumberSetting'])->name('update-pdf-mobile-number')->middleware('permission:company_edit_print_setting');
    Route::delete('remove-company-logo', [PdfConfigurationAPIController::class, 'removeCompanyLogo'])->name('remove-company-logo')->middleware('permission:company_edit_print_setting');
    Route::delete('remove-company-signature', [PdfConfigurationAPIController::class, 'removeCompanySignature'])->name('remove-company-signature')->middleware('permission:company_edit_print_setting');
    Route::get('get-duplicate-invoice-label', [PdfConfigurationAPIController::class, 'duplicateInvoiceLabel'])->name('get-duplicate-invoice-label')->middleware('permission:company_view_print_setting|company_edit_print_setting');
    Route::post('update-duplicate-invoice-label', [PdfConfigurationAPIController::class, 'storeDuplicateInvoiceLabel'])->name('update-duplicate-invoice-label')->middleware('permission:company_edit_print_setting');
    Route::get('get-triplicate-invoice-label', [PdfConfigurationAPIController::class, 'getTriplicateInvoiceLabel'])->name('get-triplicate-invoice-label')->middleware('permission:company_view_print_setting|company_edit_print_setting');
    Route::post('update-triplicate-invoice-label', [PdfConfigurationAPIController::class, 'storeTriplicateInvoiceLabel'])->name('update-triplicate-invoice-label')->middleware('permission:company_edit_print_setting');
    Route::get('get-prop-name', [PdfConfigurationAPIController::class, 'getPropName'])->name('get-prop-name')->middleware('permission:company_view_print_setting|company_edit_print_setting');
    Route::post('update-prop-name', [PdfConfigurationAPIController::class, 'updatePropName'])->name('update-prop-name')->middleware('permission:company_edit_print_setting');

    // pdf-adjustments
    Route::get('pdf-adjustments/{pdfFormat}', [PdfConfigurationAPIController::class, 'pdfAdjustments'])->name('pdf-adjustments');
    Route::post('pdf-adjustments', [PdfConfigurationAPIController::class, 'pdfAdjustmentsCreateAndUpdate'])->name('pdf-adjustments-store-and-update');
    Route::delete('reset-pdf-adjustments/{pdfFormat}', [PdfConfigurationAPIController::class, 'resetPdfAdjustments'])->name('reset-pdf-adjustments');

    // General Setting
    Route::get('get-currency-list', [GeneralSettingAPIController::class, 'getCurrencyList'])->name('get-currency-list');
    Route::get('general-setting', [GeneralSettingAPIController::class, 'getGeneralSetting'])->name('get-general-setting')->middleware('permission:company_view_general_setting');
    Route::post('general-setting', [GeneralSettingAPIController::class, 'storeAndUpdateGeneralSetting'])->name('store-and-update-general-setting')->middleware('permission:company_edit_general_setting');

    // E-way bill and E-invoice Setting API
    Route::get('e-way-bill-setting', [GeneralSettingAPIController::class, 'getEWayBillSetting'])->name('get-e-way-bill-setting')->middleware('permission:company_view_ewaybill_and_einvoice_setting');
    Route::post('e-way-bill-setting', [GeneralSettingAPIController::class, 'storeAndUpdateEWayBillSetting'])->name('store-and-update-e-way-bill-setting')->middleware('permission:company_edit_ewaybill_and_einvoice_setting');

    // Cheque Printing Setting API
    Route::get('cheque-printing-setting', [GeneralSettingAPIController::class, 'getChequePrintingSetting'])->name('get-cheque-printing-setting')->middleware('permission:company_view_cheque_printing');
    Route::post('cheque-printing-setting', [GeneralSettingAPIController::class, 'storeAndUpdateChequePrintingSetting'])->name('store-and-update-cheque-printing-setting')->middleware('permission:company_edit_cheque_printing');

    // Email Setting API
    Route::get('mail-configuration', [GeneralSettingAPIController::class, 'getMailConfiguration'])->name('get-mail-configuration')->middleware('permission:company_view_email_and_whatsapp_configuration');
    Route::post('mail-configuration/{type?}', [GeneralSettingAPIController::class, 'storeAndUpdateMailConfiguration'])->name('store-and-update-mail-configuration')->middleware('permission:company_edit_email_and_whatsapp_configuration');
    Route::post('auto-send-email/{id}', [GeneralSettingAPIController::class, 'storeAndUpdateAutoSendEmail'])->name('store-and-update-auto-send-email')->middleware('permission:company_edit_email_and_whatsapp_configuration');

    // WhatsApp Setting API
    Route::get('whatsapp-configuration', [GeneralSettingAPIController::class, 'getWhatsappConfiguration'])->name('get-whatsapp-configuration')->middleware('permission:company_view_email_and_whatsapp_configuration');
    Route::post('whatsapp-auto-payment-reminder-status', [GeneralSettingAPIController::class, 'channgeWhatsappAutoPaymentReminderStatus'])->name('')->middleware('permission:company_edit_email_and_whatsapp_configuration');
    Route::post('add-whatsapp-device', [GeneralSettingAPIController::class, 'addWhatsappDevice'])->name('add-whatsapp-device')->middleware('permission:company_edit_email_and_whatsapp_configuration');
    Route::get('connect-device-wise-configuration/{instanceId}', [GeneralSettingAPIController::class, 'connectDeviceWiseConfiguration'])->name('connect-device-wise-configuration')->middleware('permission:company_view_email_and_whatsapp_configuration');
    Route::post('whatsapp-connected-devices', [GeneralSettingAPIController::class, 'whatsappConnectedDevices'])->name('whatsapp-connected-devices')->middleware('permission:company_edit_email_and_whatsapp_configuration');
    Route::post('update-device-name/{id}', [GeneralSettingAPIController::class, 'updateInstanceDeviceName'])->name('update-device-name')->middleware('permission:company_edit_email_and_whatsapp_configuration');
    Route::delete('remove-whatsapp-device/{id}', [GeneralSettingAPIController::class, 'deleteWhatsappDevice'])->name('whatsapp-device-destroy')->middleware('permission:company_edit_email_and_whatsapp_configuration');
    Route::post('auto-send-whatsapp/{id}', [GeneralSettingAPIController::class, 'storeAndUpdateAutoSendWhatsapp'])->name('store-and-update-auto-send-whatsapp')->middleware('permission:company_edit_email_and_whatsapp_configuration');

    /* Purchase OCR Result */
    Route::get('ocr/{id}/purchase', [PurchaseOCRAPIController::class, 'getPurchaseOCRTransaction'])->name('get-purchase-ocr-transaction');
    Route::post('ocr/purchase', [PurchaseOCRAPIController::class, 'storePurchaseOCRTransaction'])->name('store-purchase-ocr-transaction');

    /* Bank OCR Result */
    Route::get('ocr/{id}/bank', [PurchaseOCRAPIController::class, 'getBankOCRTransaction'])->name('get-bank-ocr-transaction');
    // Route::get('bank-ledgers-list', [PurchaseOCRAPIController::class, 'getBankLedgers'])->name('get-bank-ledgers');
    // Bank OCR Statement Notes Update
    Route::post('ocr-bank-note-update/{id}', [PurchaseOCRAPIController::class, 'ocrBankNotesUpdate'])->name('ocr-bank-notes-update');
    // Bank OCR Statement Ledger Update
    Route::post('update-ocr-bank-statement', [PurchaseOCRAPIController::class, 'updateOcrBankStatement'])->name('update-ocr-bank-statement');
    // Bank OCR Statement Ledger Bulk Update
    Route::post('ocr-statement-ledgers/update-bulk', [PurchaseOCRAPIController::class, 'updateBulkStatementForLedger'])->name('ocr-statement-ledgers-update-bulk');
    // Bank OCR Statement Destroy
    Route::post('ocr-statement-destroy/{id}', [PurchaseOCRAPIController::class, 'ocrStatementDestroy'])->name('ocr-statement-destroy');
    // Bank OCR Statement settle
    Route::get('ocr-statement-receipt-settle/{ledger}/{id}', [PurchaseOCRAPIController::class, 'ocrStatementReceiptSettle'])->name('ocr-statement-receipt-settle');
    Route::get('ocr-statement-payment-settle/{ledger}/{id}', [PurchaseOCRAPIController::class, 'ocrStatementPaymentSettle'])->name('ocr-statement-payment-settle');
    Route::post('ocr-statement-settle', [PurchaseOCRAPIController::class, 'ocrStatementSettle'])->name('ocr-statement-settle');
    // Bank OCR Statement posted trsancation
    Route::post('ocr-statement-bank-posted', [PurchaseOCRAPIController::class, 'ocrStatementBankPosted'])->name('ocr-statement-bank-posted');
    Route::post('ocr-statement-bank/bulk-posted', [PurchaseOCRAPIController::class, 'ocrStatementBankBulkPosted'])->name('ocr-statement-bank-bulk-posted');

    // PDF preview API
    Route::get('pdf-preview/{transaction}/{layout}', [GeneralSettingAPIController::class, 'pdfPreview'])->name('pdf-preview');

    /* Customer Master Report */
    Route::get('customer-master-report', [CustomerMasterReportAPIController::class, 'index'])->name('customer-master-report');
    Route::delete('customer-master-report/{id}', [CustomerMasterReportAPIController::class, 'destroy'])->name('customer-master-report-destroy');
    Route::get('column-selector/customer-master-report', [CustomerMasterReportAPIController::class, 'getColumnSelector'])->name('customer-master-report-column-selector');
    Route::post('column-selector/customer-master-report', [CustomerMasterReportAPIController::class, 'updateColumnSelector'])->name('customer-master-report-update-column-selector');
    Route::get('export/customer-master-report', [CustomerMasterReportAPIController::class, 'export'])->name('customer-master-report-export');

    /* Supplier Master Report */
    Route::get('supplier-master-report', [SupplierMasterReportAPIController::class, 'index'])->name('supplier-master-report');
    Route::delete('supplier-master-report/{id}', [SupplierMasterReportAPIController::class, 'destroy'])->name('supplier-master-report-destroy');
    Route::get('column-selector/supplier-master-report', [SupplierMasterReportAPIController::class, 'columnSelector'])->name('supplier-master-report-column-selector');
    Route::post('column-selector/supplier-master-report', [SupplierMasterReportAPIController::class, 'updateColumnSelector'])->name('supplier-master-report-update-column-selector');
    Route::get('export/supplier-master-report', [SupplierMasterReportAPIController::class, 'export'])->name('supplier-master-report-export');

    Route::post('estimate-quote-title', [EstimateQuoteTitleAPIController::class, 'store'])->name('estimate-quote-title-store');
    Route::get('estimate-quote-title/{title}', [EstimateQuoteTitleAPIController::class, 'edit'])->name('estimate-quote-title-edit');
    Route::post('estimate-quote-title/{id}', [EstimateQuoteTitleAPIController::class, 'update'])->name('estimate-quote-title-update');
    Route::delete('estimate-quote-title/{title}/delete', [EstimateQuoteTitleAPIController::class, 'destroy'])->name('estimate-quote-title-destroy');

    Route::post('purchase-order-title', [PurchaseOrderTitleAPIController::class, 'store'])->name('purchase-order-title-store');
    Route::get('purchase-order-title/{title}', [PurchaseOrderTitleAPIController::class, 'edit'])->name('purchase-order-title-edit');
    Route::post('purchase-order-title/{id}', [PurchaseOrderTitleAPIController::class, 'update'])->name('purchase-order-title-update');
    Route::delete('purchase-order-title/{title}/delete', [PurchaseOrderTitleAPIController::class, 'destroy'])->name('purchase-order-title-destroy');

    /* Barcode Item API */
    Route::get('barcode-item-list', [BarcodeItemAPIController::class, 'getBarcodeItemList'])->name('get-barcode-item-list');
    Route::get('barcode-item-group-list', [BarcodeItemAPIController::class, 'getBarcodeItemGroupList'])->name('get-barcode-item-group-list');
    Route::get('barcode-item-details', [BarcodeItemAPIController::class, 'getBarcodeItemDetails'])->name('get-barcode-item-details');
    Route::post('download-barcode-pdf', [BarcodeItemAPIController::class, 'downloadBarcodePdf'])->name('download-barcode-pdf');
    Route::get('barcode-setting/{type}', [BarcodeItemAPIController::class, 'getBarcodeSetting'])->name('get-barcode-setting');
    Route::post('barcode-setting/{type}', [BarcodeItemAPIController::class, 'saveAndUpdateBarcodeSetting'])->name('change-barcode-setting');
    Route::get('printer-size', [BarcodeItemAPIController::class, 'printerSize'])->name('get-printer-size');
    Route::post('printer-size', [BarcodeItemAPIController::class, 'updatePrinterSize'])->name('update-printer-size');

    /* Recurring Invoice */
    Route::get('recurring-invoices', [RecurringInvoiceAPIController::class, 'index'])->name('get-recurring-invoice');
    Route::post('recurring-invoices', [RecurringInvoiceAPIController::class, 'store'])->name('store-recurring-invoice');
    Route::get('recurring-invoices/{id}', [RecurringInvoiceAPIController::class, 'edit'])->name('edit-recurring-invoice');
    Route::post('recurring-invoices/{id}', [RecurringInvoiceAPIController::class, 'update'])->name('update-recurring-invoice');
    Route::delete('recurring-invoices/{id}', [RecurringInvoiceAPIController::class, 'destroy'])->name('destroy-recurring-invoice');
    Route::get('check-recurring-invoice-exists/{id}', [RecurringInvoiceAPIController::class, 'checkRecurringInvoiceExists'])->name('check-recurring-invoice-exists');
    Route::get('recurring-invoices/{id}/update-status', [RecurringInvoiceAPIController::class, 'updateStatus'])->name('update-recurring-invoice-status');
    Route::get('recurring-invoices-templates', [RecurringInvoiceAPIController::class, 'getDescriptionTemplate'])->name('get-recurring-invoice-description-template');

    Route::get('recurring-invoices-party-list', [RecurringInvoiceAPIController::class, 'getPartyList'])->name('get-recurring-party-list');
    Route::get('recurring-invoices-party-group-list', [RecurringInvoiceAPIController::class, 'getPartyGroupList'])->name('get-recurring-party-group-list');
    Route::get('recurring-invoices-parties', [RecurringInvoiceAPIController::class, 'getPartyDetail'])->name('get-recurring-party-detail');
    Route::post('recurring-invoices/{id}/handle-approval', [RecurringInvoiceAPIController::class, 'handleApproveRejectInvoice'])->name('approve-or-reject-recurring-invoice');
    Route::get('recurring-invoices/{id}/undo-rejection', [RecurringInvoiceAPIController::class, 'undoRejection'])->name('undo-rejection-recurring-invoice');
    Route::get('recurring-invoices-pdf-preview/{id}/{printType?}', [RecurringInvoiceAPIController::class, 'getPdfPreview'])->name('get-recurring-invoice-pdf-preview');
    Route::get('recurring-invoices-pdf-download/{id}/{printType?}', [RecurringInvoiceAPIController::class, 'getPdfDownload'])->name('get-recurring-invoice-pdf-download');
    Route::get('recurring-invoices-dynamic-variables', [RecurringInvoiceAPIController::class, 'getDynamicVariables'])->name('get-recurring-invoice-dynamic-variables');

    /* Recurring Invoice Configuration */
    Route::get('recurring-invoice-configuration', [RecurringInvoiceConfigurationAPIController::class, 'getConfiguration'])->name('get-recurring-invoice-configuration');
    Route::post('recurring-invoice-configuration/{type?}', [RecurringInvoiceConfigurationAPIController::class, 'updateConfiguration'])->name('update-recurring-invoice-configuration');

    Route::prefix('vastra')->group(function () {
        Route::get('configuration', [VastraAPIController::class, 'getVastraConfiguration']);
        Route::post('configuration', [VastraAPIController::class, 'storeAndUpdateVastraConfiguration']);
        Route::get('sync', [VastraAPIController::class, 'syncMasterDataFromVastra']);
        // Route::get('sync-sale-order', [VastraAPIController::class, 'syncSaleOrderFromVastra']);
        // Route::get('sync-sale', [VastraAPIController::class, 'syncSaleFromVastra']);
        Route::post('fetch-delivery-challans', [VastraAPIController::class, 'fetchDeliveyChallansData']);

        Route::get('delivery-challans-columns', [VastraAPIController::class, 'getDeliveryChallansColumns']);
        Route::post('delivery-challans-columns', [VastraAPIController::class, 'updateDeliveryChallansColumns']);
        Route::get('delivery-challans', [VastraAPIController::class, 'index']);
        Route::post('check-transaction-exists', [VastraAPIController::class, 'checkTransactionExists']);
        Route::post('delivery-challans-destroy', [VastraAPIController::class, 'destroy']);
        Route::post('create-sale-from-delivery', [VastraAPIController::class, 'createSaleFromVastraDelivery']);
    });

    /* Top & Least Selling Items */
    Route::get('top-selling-items', [TopAndLeastSellingItemReportAPIController::class, 'getTopSellingItems'])->name('get-top-selling-items');
    Route::get('least-selling-items', [TopAndLeastSellingItemReportAPIController::class, 'getLeastSellingItems'])->name('get-least-selling-items');
    Route::get('export/top-least-selling-items-report', [TopAndLeastSellingItemReportAPIController::class, 'export'])->name('export-top-least-selling-items');

    /* Custom fields item */
    // Route::get('custom-fields-item/types', [CustomFieldsItemAPIController::class, 'getInputTypes']);
    // Route::post('custom-fields-item', [CustomFieldsItemAPIController::class, 'store']);
    // Route::post('custom-fields-item/update-status', [CustomFieldsItemAPIController::class, 'updateStatus']);
    // Route::get('custom-fields-item/{id}/edit', [CustomFieldsItemAPIController::class, 'edit']);
    // Route::post('custom-fields-item/{id}/update', [CustomFieldsItemAPIController::class, 'update']);
    // Route::get('custom-fields-item/{id}/delete', [CustomFieldsItemAPIController::class, 'delete']);

    // Route::post('custom-fields-item/formula', [CustomFieldsItemAPIController::class, 'storeUpdateFormula']);
    // Route::get('custom-fields-item/delete-formula/{id}', [CustomFieldsItemAPIController::class, 'deleteFormula']);

    /* Low Stock Report Item Level */
    Route::get('low-stock-report-items', [ItemMasterAPIController::class, 'getReOrderItems']);
    Route::get('export/low-stock-report-items', [ItemMasterAPIController::class, 'export']);

    // Auth 11za API
    Route::get('11za-configurations', [Auth11zaAPIController::class, 'authToken11za']);
    Route::post('11za-configurations', [Auth11zaAPIController::class, 'saveConfiguration']);
    Route::get('11za-templates', [Auth11zaAPIController::class, 'templates']);
    Route::post('11za-templates/{id}/update-status', [Auth11zaAPIController::class, 'updateStatus']);
    Route::get('11za-templates-name-list', [Auth11zaAPIController::class, 'templatesNameList']);
    Route::post('11za-templates-body', [Auth11zaAPIController::class, 'templatesBody']);
    Route::get('11za-template/{id}/edit', [Auth11zaAPIController::class, 'edit']);
    Route::post('11za-template/{id}/update', [Auth11zaAPIController::class, 'update']);

    // Payment Mode
    Route::get('payment-modes/{type}', [PaymentModeAPIController::class, 'index']);
    Route::post('payment-modes', [PaymentModeAPIController::class, 'create'])->name('payment-mode-store');
    Route::get('payment-modes/{id}/edit', [PaymentModeAPIController::class, 'edit'])->name('payment-mode-edit');
    Route::post('payment-modes/{id}/update', [PaymentModeAPIController::class, 'update'])->name('payment-mode-update');
    Route::delete('payment-modes/{id}/delete', [PaymentModeAPIController::class, 'destroy'])->name('payment-mode-destroy');

    /* Custom fields item master */
    Route::get('custom-field-item-master/types', [CustomFieldItemMasterAPIController::class, 'getCustomFieldTypes']);
    Route::get('custom-field-item-master', [CustomFieldItemMasterAPIController::class, 'index']);
    Route::post('custom-field-item-master', [CustomFieldItemMasterAPIController::class, 'store']);
    Route::get('custom-field-item-master/{id}/edit', [CustomFieldItemMasterAPIController::class, 'edit']);
    Route::post('custom-field-item-master/{id}/update', [CustomFieldItemMasterAPIController::class, 'update']);
    Route::post('custom-field-item-master/update-status', [CustomFieldItemMasterAPIController::class, 'updateStatus']);
    Route::get('custom-field-item-master/{id}/delete', [CustomFieldItemMasterAPIController::class, 'delete']);
    Route::post('custom-field-item-master/update-transaction-status', [CustomFieldItemMasterAPIController::class, 'updateTransactionStatus']);
    Route::post('custom-field-item-master/formula', [CustomFieldItemMasterAPIController::class, 'storeUpdateFormula']);
    Route::get('custom-field-item-master/delete-formula/{id}', [CustomFieldItemMasterAPIController::class, 'deleteFormula']);

    // GST Login
    Route::post('gst-login', [GstLoginApiController::class, 'login']);

    // fetch Data
    Route::get('fetch-data', [Gstr2BReconciliationAPIController::class, 'fetchData']);

    // Gstr2b Summary
    Route::get('gstr-2b-summary', [Gstr2BReconciliationAPIController::class, 'index']);
    Route::get('gstr-2b-details', [Gstr2BReconciliationAPIController::class, 'reconciliation']);

    Route::get('check-gst-login',[Gstr2BReconciliationAPIController::class, 'checkGstLogin']);
});
/* Broker Master */
Route::get('broker-masters-details', [BrokerMasterAPIController::class, 'getDetails'])->name('broker-masters-details');
Route::get('get-broker-details/{id}', [BrokerMasterAPIController::class, 'getBrokerDetails'])->name('get-broker-details');
Route::post('broker-masters', [BrokerMasterAPIController::class, 'create'])->name('broker-masters')->middleware('permission:company_add_new_broker_masters');
Route::get('broker-masters/{id}', [BrokerMasterAPIController::class, 'edit'])->name('broker-masters-edit')->middleware('permission:company_edit_broker_masters');
Route::post('broker-masters/{id}', [BrokerMasterAPIController::class, 'update'])->name('broker-masters-update')->middleware('permission:company_edit_broker_masters');

/* Transport Master */
Route::post('transport-masters', [TransportMasterAPIController::class, 'create'])->name('transport-masters')->middleware('permission:company_add_new_transport_masters');
Route::get('transport-masters/{id}', [TransportMasterAPIController::class, 'edit'])->name('transport-masters-edit')->middleware('permission:company_edit_transport_masters');
Route::post('transport-masters/{id}', [TransportMasterAPIController::class, 'update'])->name('transport-masters-update')->middleware('permission:company_edit_transport_masters');

/* Item Master */
Route::get('item-masters-details', [ItemMasterAPIController::class, 'getDetails'])->name('item-masters-details');
Route::get('item-masters-configuration', [ItemMasterAPIController::class, 'getItemMasterConfiguration'])->name('get-item-masters-configuration');
Route::post('item-masters-configuration', [ItemMasterAPIController::class, 'storeUpdateItemConfiguration'])->name('update-item-masters-configuration');
Route::post('item-masters', [ItemMasterAPIController::class, 'create'])->name('item-masters')->middleware('permission:company_add_new_item_masters');
Route::get('item-masters/{id}', [ItemMasterAPIController::class, 'edit'])->name('item-masters-edit')->middleware('permission:company_edit_item_masters');
Route::post('item-masters/{id}', [ItemMasterAPIController::class, 'update'])->name('item-masters-update')->middleware('permission:company_edit_item_masters');

/* Item Group */
Route::get('get-item-groups', [ItemGroupAPIController::class, 'getItemGroup'])->name('get-item-group');
Route::get('get-gst-cess-rate', [ItemGroupAPIController::class, 'getGstCessRate'])->name('get-gst-cess-rate');
Route::post('item-group', [ItemGroupAPIController::class, 'create'])->name('create-item-group');

/* Item Unit of Measurement */
Route::get('get-unit-of-measurement', [UnitOfMeasurementAPIController::class, 'getUnitOfMeasurement'])->name('get-unit-of-measurement');
Route::post('unit-of-measurement', [UnitOfMeasurementAPIController::class, 'create'])->name('create-unit-of-measurement');
Route::get('unit-of-measurement/{id}', [UnitOfMeasurementAPIController::class, 'edit'])->name('edit-unit-of-measurement');
Route::post('unit-of-measurement/{id}', [UnitOfMeasurementAPIController::class, 'update'])->name('update-unit-of-measurement');
Route::get('unit-of-measurement/{id}/destroy', [UnitOfMeasurementAPIController::class, 'destroy'])->name('destroy-unit-of-measurement');

/* Cess Rate */
Route::get('cess-rate', [CessRateAPIController::class, 'index'])->name('cess-rate');
Route::post('cess-rate', [CessRateAPIController::class, 'create'])->name('create-cess-rate');
Route::get('cess-rate/{id}', [CessRateAPIController::class, 'edit'])->name('edit-cess-rate');
Route::post('cess-rate/{id}', [CessRateAPIController::class, 'update'])->name('update-cess-rate');
Route::get('cess-rate/{id}/destroy', [CessRateAPIController::class, 'destroy'])->name('destroy-cess-rate');

/* Ledger */
Route::get('ledgers-group-details/{type}', [LedgerAPIController::class, 'ledgerGroupDetails'])->name('ledger-group-details');
Route::get('ledgers-details/{group_id}', [LedgerAPIController::class, 'ledgerDetails'])->name('ledger-details');
Route::post('ledgers', [LedgerAPIController::class, 'create'])->name('ledgers')->middleware('permission:company_add_new_ledgers');
Route::get('ledgers/{id}', [LedgerAPIController::class, 'edit'])->name('ledgers-edit')->middleware('permission:company_edit_ledgers');
Route::post('ledgers/{id}', [LedgerAPIController::class, 'update'])->name('ledgers-update')->middleware('permission:company_edit_ledgers');
Route::get('delete-ledger-media/{id}', [LedgerAPIController::class, 'deleteLedgerMedia'])->name('delete-ledger-media');
Route::get('get-holding-profit-ratio', [LedgerAPIController::class, 'getHoldingProfitRatio'])->name('get-holding-profit-ratio');
Route::post('/ledger-bill-wish-excel', [LedgerAPIController::class, 'ledgerBillWishExcel'])->name('ledger-bill-wish-excel');

/* Ledger Group */
Route::get('get-ledger-groups', [LedgerAPIController::class, 'getLedgerGroup'])->name('get-ledger-group');
Route::post('ledgers-group', [LedgerAPIController::class, 'createLedgerGroup'])->name('create-ledgers-group')->middleware('permission:company_add_new_ledgers|company_edit_ledgers');

/* Location of Assets */
Route::post('store-location-of-assets', [LedgerAPIController::class, 'storeLocationOfAssets'])->name('store-location-of-assets')->middleware('permission:company_add_new_ledgers|company_edit_ledgers');

/* Dispatch Address */
Route::get('dispatch-address-list', [DispatchAddressAPIController::class, 'dispatchAddressList'])->name('get-dispatch-address-list');
Route::post('dispatch-address', [DispatchAddressAPIController::class, 'create'])->name('dispatch-address');
Route::get('dispatch-address/{id}', [DispatchAddressAPIController::class, 'edit'])->name('dispatch-address-edit');
Route::post('dispatch-address/{id}', [DispatchAddressAPIController::class, 'update'])->name('dispatch-address-update');
Route::post('dispatch-address/{id}/delete', [DispatchAddressAPIController::class, 'destroy'])->name('dispatch-address-delete');

/* Shipping Address */
Route::get('shipping-address-list/{id}', [ShippingAddressAPIController::class, 'shippingAddressList'])->name('get-shipping-address-list');
Route::post('shipping-address', [ShippingAddressAPIController::class, 'create'])->name('shipping-address');
Route::get('shipping-address/{id}', [ShippingAddressAPIController::class, 'edit'])->name('shipping-address-edit');
Route::post('shipping-address/{id}', [ShippingAddressAPIController::class, 'update'])->name('shipping-address-update');
Route::post('shipping-address/{id}/delete', [ShippingAddressAPIController::class, 'destroy'])->name('shipping-address-delete');
Route::get('update-selected-shipping-address/{address_id}/{ledger_id}', [ShippingAddressAPIController::class, 'updateSelectedShippingAddress'])->name('update-selected-shipping-address');

/* Income Estimate Quote Transaction Routes */
Route::get('income-estimate-quote-invoice-number', [EstimateQuoteTransactionAPIController::class, 'getEstimateQuoteInvoiceNumber'])->name('get-income-estimate-quote-invoice-number')->middleware('permission:company_add_new_income');
Route::post('income-estimate-quote-transactions', [EstimateQuoteTransactionAPIController::class, 'create'])->name('income-estimate-quote-transaction')->middleware('permission:company_add_new_income');
Route::get('income-estimate-quote-transactions/{id}', [EstimateQuoteTransactionAPIController::class, 'edit'])->name('income-estimate-quote-transaction-edit')->middleware('permission:company_edit_income|company_add_new_income');
Route::post('income-estimate-quote-transactions/{id}', [EstimateQuoteTransactionAPIController::class, 'update'])->name('income-estimate-quote-transaction-update')->middleware('permission:company_edit_income');
Route::get('income-estimate-quote-transactions/{id}/duplicate', [EstimateQuoteTransactionAPIController::class, 'duplicate'])->name('income-estimate-quote-transaction-duplicate')->middleware('permission:company_add_new_income');
Route::get('income-estimate-quote-transactions/{id}/delete', [EstimateQuoteTransactionAPIController::class, 'destroy'])->name('income-estimate-quote-transaction-delete')->middleware('permission:company_delete_income');
Route::get('income-estimate-quote-transactions/{id}/create-sale', [EstimateQuoteTransactionAPIController::class, 'createSaleFromEstimate'])->name('create-sale-from-estimate-transaction');

/* Income Estimate Quote Transaction Configuration Routes */
Route::get('income-estimate-quote-configuration', [EstimateQuoteConfigurationAPIController::class, 'estimateQuoteConfiguration'])->name('get-income-estimate-quote-configuration')->middleware('permission:company_add_new_income|company_edit_income');
Route::post('income-estimate-quote-configuration/{type?}', [EstimateQuoteConfigurationAPIController::class, 'updateEstimateQuoteConfiguration'])->name('update-income-estimate-quote-configuration')->middleware('permission:company_add_new_income|company_edit_income');

/* Sale Transaction Routes */
Route::get('sale-invoice-number', [SaleTransactionAPIController::class, 'getSaleInvoiceNumber'])->name('get-sale-invoice-number')->middleware('permission:company_add_new_income');
Route::post('sale-transactions', [SaleTransactionAPIController::class, 'create'])->name('sale-transaction')->middleware('permission:company_add_new_income');
Route::get('sale-transactions/{id}', [SaleTransactionAPIController::class, 'edit'])->name('sale-transaction-edit')->middleware('permission:company_edit_income|company_add_new_income');
Route::get('sale-transactions-details/{id}', [SaleTransactionAPIController::class, 'saleTransactionDetails'])->name('sale-transaction-details')->middleware('permission:company_edit_income|company_add_new_income');
Route::post('sale-transactions/{id}', [SaleTransactionAPIController::class, 'update'])->name('sale-transaction-update')->middleware('permission:company_edit_income');
Route::get('sale-transactions/{id}/duplicate', [SaleTransactionAPIController::class, 'duplicate'])->name('sale-transaction-duplicate')->middleware('permission:company_add_new_income');
Route::post('sale-transaction/estimate', [SaleTransactionAPIController::class, 'estimate'])->name('sale-transaction-estimate')->middleware('permission:company_add_new_income|company_edit_income');
Route::post('sale-transaction/challan', [SaleTransactionAPIController::class, 'deliveryChallan'])->name('sale-transaction-delivery-challan')->middleware('permission:company_add_new_income|company_edit_income');
Route::get('sale-transactions/{id}/sale-return', [SaleTransactionAPIController::class, 'getSaleForSaleReturn'])->name('get-sale-for-sale-return')->middleware('permission:company_add_new_income');
Route::get('sale-transactions/{id}/delete', [SaleTransactionAPIController::class, 'destroy'])->name('sale-transaction-delete')->middleware('permission:company_delete_income');

/* Sale Transaction Configuration Routes */
Route::get('sale-configuration', [TransactionsConfigurationAPIController::class, 'saleConfiguration'])->name('get-sale-configuration')->middleware('permission:company_add_new_income|company_edit_income');
Route::post('sale-configuration/{type?}', [TransactionsConfigurationAPIController::class, 'updateSaleConfiguration'])->name('update-sale-configuration')->middleware('permission:company_add_new_income|company_edit_income');

/* Delivery Challan Transaction Routes */
Route::get('delivery-challan-invoice-number', [DeliveryChallanTransactionAPIController::class, 'getDeliveryChallanInvoiceNumber'])->name('get-delivery-challan-invoice-number')->middleware('permission:company_add_new_income');
Route::post('delivery-challan-transactions', [DeliveryChallanTransactionAPIController::class, 'create'])->name('delivery-challan-transaction')->middleware('permission:company_add_new_income');
Route::get('delivery-challan-transactions/{id}', [DeliveryChallanTransactionAPIController::class, 'edit'])->name('delivery-challan-transaction-edit')->middleware('permission:company_edit_income|company_add_new_income');
Route::post('delivery-challan-transactions/{id}', [DeliveryChallanTransactionAPIController::class, 'update'])->name('delivery-challan-transaction-update')->middleware('permission:company_edit_income');
Route::get('delivery-challan-transactions/{id}/duplicate', [DeliveryChallanTransactionAPIController::class, 'duplicate'])->name('delivery-challan-transaction-duplicate')->middleware('permission:company_add_new_income');
Route::get('delivery-challan-transactions/{id}/sale', [DeliveryChallanTransactionAPIController::class, 'getSaleTransaction'])->name('delivery-challan-transaction-sale')->middleware('permission:company_add_new_income');
Route::get('delivery-challan-transactions/{id}/create-sale', [DeliveryChallanTransactionAPIController::class, 'createSaleFromDelivery'])->name('create-sale-from-delivery');
Route::get('delivery-challan-transactions/{id}/delete', [DeliveryChallanTransactionAPIController::class, 'destroy'])->name('delivery-challan-transaction-delete')->middleware('permission:company_delete_income');

/* Delivery Challan Transaction Configuration Routes */
Route::get('delivery-challan-configuration', [DeliveryChallanConfigurationAPIController::class, 'deliveryChallanConfiguration'])->name('get-delivery-challan-configuration')->middleware('permission:company_add_new_income|company_edit_income');
Route::post('delivery-challan-configuration/{type?}', [DeliveryChallanConfigurationAPIController::class, 'updateDeliveryChallanConfiguration'])->name('update-delivery-challan-configuration')->middleware('permission:company_add_new_income|company_edit_income');

/* Sale Return Transaction Routes */
Route::get('sale-return-invoice-number', [SaleReturnTransactionAPIController::class, 'getSaleReturnInvoiceNumber'])->name('get-sale-return-invoice-number')->middleware('permission:company_add_new_income');
Route::post('sale-return-transactions', [SaleReturnTransactionAPIController::class, 'create'])->name('sale-return-transaction')->middleware('permission:company_add_new_income');
Route::get('sale-return-transactions/{id}', [SaleReturnTransactionAPIController::class, 'edit'])->name('sale-return-transaction-edit')->middleware('permission:company_edit_income');
Route::post('sale-return-transactions/{id}', [SaleReturnTransactionAPIController::class, 'update'])->name('sale-return-transaction-update')->middleware('permission:company_edit_income');
Route::get('sale-return-transactions/{id}/duplicate', [SaleReturnTransactionAPIController::class, 'duplicate'])->name('sale-return-transaction-duplicate')->middleware('permission:company_add_new_income');
Route::get('sale-return-transactions/{id}/delete', [SaleReturnTransactionAPIController::class, 'destroy'])->name('sale-return-transaction-delete')->middleware('permission:company_delete_income');

/* Sale Return Transaction Configuration Routes */
Route::get('sale-return-configuration', [SaleReturnConfigurationAPIController::class, 'saleReturnConfiguration'])->name('get-sale-return-configuration')->middleware('permission:company_add_new_income|company_edit_income');
Route::post('sale-return-configuration/{type?}', [SaleReturnConfigurationAPIController::class, 'updateSaleReturnConfiguration'])->name('update-sale-return-configuration')->middleware('permission:company_add_new_income|company_edit_income');

/* Income Debit Note Routes */
Route::get('debit-note-invoice-number', [IncomeDebitNoteTransactionAPIController::class, 'getDebitNoteInvoiceNumber'])->name('get-debit-note-invoice-number')->middleware('permission:company_add_new_income');
Route::post('income-debit-notes', [IncomeDebitNoteTransactionAPIController::class, 'create'])->name('income-debit-note')->middleware('permission:company_add_new_income');
Route::get('income-debit-notes/{id}', [IncomeDebitNoteTransactionAPIController::class, 'edit'])->name('income-debit-note-edit')->middleware('permission:company_edit_income');
Route::post('income-debit-notes/{id}', [IncomeDebitNoteTransactionAPIController::class, 'update'])->name('income-debit-note-update')->middleware('permission:company_edit_income');
Route::get('income-debit-notes/{id}/duplicate', [IncomeDebitNoteTransactionAPIController::class, 'duplicate'])->name('income-debit-note-duplicate')->middleware('permission:company_add_new_income');
Route::get('income-debit-notes/{id}/delete', [IncomeDebitNoteTransactionAPIController::class, 'destroy'])->name('income-debit-note-delete')->middleware('permission:company_delete_income');

/* Income Debit Note Transaction Configuration Routes */
Route::get('income-debit-note-configuration', [IncomeDebitNoteConfigurationAPIController::class, 'incomeDebitNoteConfiguration'])->name('income-debit-note-configuration')->middleware('permission:company_add_new_income|company_edit_income');
Route::post('income-debit-note-configuration/{type?}', [IncomeDebitNoteConfigurationAPIController::class, 'updateIncomeDebitNoteConfiguration'])->name('update-income-debit-note-configuration')->middleware('permission:company_add_new_income|company_edit_income');

/* Income Credit Note Transaction Routes */
Route::get('income-credit-note-invoice-number', [IncomeCNTransactionAPIController::class, 'getIncomeCNInvoiceNumber'])->name('get-income-credit-note-invoice-number')->middleware('permission:company_add_new_income');
Route::post('income-credit-note-transactions', [IncomeCNTransactionAPIController::class, 'create'])->name('income-credit-note-transaction')->middleware('permission:company_add_new_income');
Route::get('income-credit-note-transactions/{id}', [IncomeCNTransactionAPIController::class, 'edit'])->name('income-credit-note-transaction-edit')->middleware('permission:company_edit_income');
Route::post('income-credit-note-transactions/{id}', [IncomeCNTransactionAPIController::class, 'update'])->name('income-credit-note-transaction-update')->middleware('permission:company_edit_income');
Route::get('income-credit-note-transactions/{id}/duplicate', [IncomeCNTransactionAPIController::class, 'duplicate'])->name('income-credit-note-transaction-duplicate')->middleware('permission:company_add_new_income');
Route::get('income-credit-note-transactions/{id}/delete', [IncomeCNTransactionAPIController::class, 'destroy'])->name('income-credit-note-transaction-delete')->middleware('permission:company_delete_income');

/* Income Credit Note Transaction Configuration Routes */
Route::get('income-credit-note-configuration', [IncomeCNConfigurationAPIController::class, 'incomeCreditNoteConfiguration'])->name('get-income-credit-note-configuration')->middleware('permission:company_add_new_income|company_edit_income');
Route::post('income-credit-note-configuration/{type?}', [IncomeCNConfigurationAPIController::class, 'updateIncomeCreditNoteConfiguration'])->name('update-income-credit-note-configuration')->middleware('permission:company_add_new_income|company_edit_income');

/* Purchase Transaction Routes */
Route::get('purchase-invoice-number', [PurchaseTransactionAPIController::class, 'getPurchaseInvoiceNumber'])->name('get-purchase-invoice-number')->middleware('permission:company_add_new_expense');
Route::get('purchase-order-number-list/{party_id}', [PurchaseTransactionAPIController::class, 'getPurchaseOrderNumberList'])->name('get-purchase-order-number-list')->middleware('permission:company_add_new_expense|company_edit_expense');
Route::post('purchase-transactions', [PurchaseTransactionAPIController::class, 'create'])->name('purchase-transaction')->middleware('permission:company_add_new_expense');
Route::get('purchase-transactions/{id}', [PurchaseTransactionAPIController::class, 'edit'])->name('purchase-transaction-edit')->middleware('permission:company_edit_expense|company_add_new_expense');
Route::get('purchase-transactions-details/{id}', [PurchaseTransactionAPIController::class, 'purchaseTransactionDetails'])->name('purchase-transaction-details')->middleware('permission:company_edit_expense|company_add_new_expense');
Route::post('purchase-transactions/{id}', [PurchaseTransactionAPIController::class, 'update'])->name('purchase-transaction-update')->middleware('permission:company_edit_expense');
Route::get('purchase-transactions/{id}/duplicate', [PurchaseTransactionAPIController::class, 'duplicate'])->name('purchase-transaction-duplicate')->middleware('permission:company_add_new_expense');
Route::post('purchase-transaction/purchase-order', [PurchaseTransactionAPIController::class, 'multiPurchaseOrder'])->name('purchase-transaction-purchase-order')->middleware('permission:company_add_new_expense');
Route::get('purchase-transactions/{id}/delete', [PurchaseTransactionAPIController::class, 'destroy'])->name('purchase-transaction-delete')->middleware('permission:company_delete_expense');
Route::get('purchase-transactions/{id}/purchase-return', [PurchaseTransactionAPIController::class, 'getPurchaseForPurchaseReturn'])->name('get-purchase-for-purchase-return')->middleware('permission:company_add_new_expense');

/* Purchase Transaction Configuration Routes */
Route::get('purchase-configuration', [PurchaseTransactionConfigurationAPIController::class, 'purchaseConfiguration'])->name('get-purchase-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');
Route::post('purchase-configuration/{type?}', [PurchaseTransactionConfigurationAPIController::class, 'updatePurchaseConfiguration'])->name('update-purchase-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');

/* Purchase Order Transaction Routes */
Route::get('purchase-order-invoice-number', [PurchaseOrderTransactionAPIController::class, 'getPurchaseOrderInvoiceNumber'])->name('get-purchase-order-invoice-number')->middleware('permission:company_add_new_expense');
Route::post('purchase-order-transactions', [PurchaseOrderTransactionAPIController::class, 'create'])->name('purchase-order-transaction')->middleware('permission:company_add_new_expense');
Route::get('purchase-order-transactions/{id}', [PurchaseOrderTransactionAPIController::class, 'edit'])->name('purchase-order-transaction-edit')->middleware('permission:company_edit_expense|company_add_new_expense');
Route::post('purchase-order-transactions/{id}', [PurchaseOrderTransactionAPIController::class, 'update'])->name('purchase-order-transaction-update')->middleware('permission:company_edit_expense');
Route::get('purchase-order-transactions/{id}/duplicate', [PurchaseOrderTransactionAPIController::class, 'duplicate'])->name('purchase-order-transaction-duplicate')->middleware('permission:company_add_new_expense');
Route::get('purchase-order-transactions/{id}/purchase-create-purchase-order', [PurchaseOrderTransactionAPIController::class, 'purchaseCreatePurchaseOrder'])->name('purchase-order-transaction-book')->middleware('permission:company_add_new_expense');
Route::get('purchase-order-transactions/{id}/delete', [PurchaseOrderTransactionAPIController::class, 'destroy'])->name('purchase-order-transaction-delete')->middleware('permission:company_delete_expense');
Route::get('purchase-order-transactions/{id}/create-purchase', [PurchaseOrderTransactionAPIController::class, 'createPurchaseFromPurchaseOrder'])->name('create-purchase-from-purchase-order');

/* Purchase Order Transaction Routes */
Route::get('purchase-order-configuration', [PurchaseOrderConfigurationAPIController::class, 'purchaseOrderConfiguration'])->name('get-purchase-order-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');
Route::post('purchase-order-configuration/{type?}', [PurchaseOrderConfigurationAPIController::class, 'updatePurchaseOrderConfiguration'])->name('update-purchase-order-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');

/* Purchase return Transaction Routes */
Route::get('purchase-return-invoice-number', [PurchaseReturnTransactionAPIController::class, 'getPurchaseReturnInvoiceNumber'])->name('get-purchase-return-invoice-number')->middleware('permission:company_add_new_expense');
Route::post('purchase-return-transactions', [PurchaseReturnTransactionAPIController::class, 'create'])->name('purchase-return-transaction')->middleware('permission:company_add_new_expense');
Route::get('purchase-return-transactions/{id}', [PurchaseReturnTransactionAPIController::class, 'edit'])->name('purchase-return-transaction-edit')->middleware('permission:company_edit_expense');
Route::post('purchase-return-transactions/{id}', [PurchaseReturnTransactionAPIController::class, 'update'])->name('purchase-return-transaction-update')->middleware('permission:company_edit_expense');
Route::get('purchase-return-transactions/{id}/duplicate', [PurchaseReturnTransactionAPIController::class, 'duplicate'])->name('purchase-return-transaction-duplicate')->middleware('permission:company_add_new_expense');
Route::get('purchase-return-transactions/{id}/delete', [PurchaseReturnTransactionAPIController::class, 'destroy'])->name('purchase-return-transaction-delete')->middleware('permission:company_delete_expense');

/* Purchase Return Transaction Configuration Routes */
Route::get('purchase-return-configuration', [PurchaseReturnConfigurationAPIController::class, 'purchaseReturnConfiguration'])->name('get-purchase-return-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');
Route::post('purchase-return-configuration/{type?}', [PurchaseReturnConfigurationAPIController::class, 'updatePurchaseReturnConfiguration'])->name('update-purchase-return-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');

/* Expense Debit Note Transaction Routes */
Route::get('expense-debit-note-invoice-number', [ExpenseDebitNoteTransactionAPIController::class, 'getExpenseDebitNoteInvoiceNumber'])->name('get-expense-debit-note-invoice-number')->middleware('permission:company_add_new_expense');
Route::post('expense-debit-note-transactions', [ExpenseDebitNoteTransactionAPIController::class, 'create'])->name('expense-debit-note-transaction-create')->middleware('permission:company_add_new_expense');
Route::get('expense-debit-note-transactions/{id}', [ExpenseDebitNoteTransactionAPIController::class, 'edit'])->name('expense-debit-note-transaction-edit')->middleware('permission:company_edit_expense');
Route::post('expense-debit-note-transactions/{id}', [ExpenseDebitNoteTransactionAPIController::class, 'update'])->name('expense-debit-note-transaction-update')->middleware('permission:company_edit_expense');
Route::get('expense-debit-note-transactions/{id}/duplicate', [ExpenseDebitNoteTransactionAPIController::class, 'duplicate'])->name('expense-debit-note-transaction-duplicate')->middleware('permission:company_add_new_expense');
Route::get('expense-debit-note-transactions/{id}/delete', [ExpenseDebitNoteTransactionAPIController::class, 'destroy'])->name('expense-debit-note-transaction-delete')->middleware('permission:company_delete_expense');

/* Expense Debit Note Transaction Configuration Routes */
Route::get('expense-debit-note-configuration', [ExpenseDebitNoteConfigurationAPIController::class, 'expenseDebitNoteConfiguration'])->name('get-expense-debit-note-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');
Route::post('expense-debit-note-configuration/{type?}', [ExpenseDebitNoteConfigurationAPIController::class, 'updateExpenseDebitNoteConfiguration'])->name('update-expense-debit-note-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');

/* Expense Credit Note Transaction Routes */
Route::get('expense-credit-note-invoice-number', [ExpenseCNTransactionAPIController::class, 'getExpenseCreditNoteInvoiceNumber'])->name('get-expense-credit-note-invoice-number')->middleware('permission:company_add_new_expense');
Route::post('expense-credit-note-transactions', [ExpenseCNTransactionAPIController::class, 'create'])->name('expense-credit-note-transaction')->middleware('permission:company_add_new_expense');
Route::get('expense-credit-note-transactions/{id}', [ExpenseCNTransactionAPIController::class, 'edit'])->name('expense-credit-note-transaction-edit')->middleware('permission:company_edit_expense');
Route::post('expense-credit-note-transactions/{id}', [ExpenseCNTransactionAPIController::class, 'update'])->name('expense-credit-note-transaction-update')->middleware('permission:company_edit_expense');
Route::get('expense-credit-note-transactions/{id}/duplicate', [ExpenseCNTransactionAPIController::class, 'duplicate'])->name('expense-credit-note-transaction-duplicate')->middleware('permission:company_add_new_expense');
Route::get('expense-credit-note-transactions/{id}/delete', [ExpenseCNTransactionAPIController::class, 'destroy'])->name('expense-credit-note-transaction-delete')->middleware('permission:company_delete_expense');

/* Expense Credit Note Transaction Configuration Routes */
Route::get('expense-credit-note-configuration', [ExpenseCNConfigurationAPIController::class, 'expenseCreditNoteConfiguration'])->name('get-expense-credit-note-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');
Route::post('expense-credit-note-configuration/{type?}', [ExpenseCNConfigurationAPIController::class, 'updateExpenseCreditNoteConfiguration'])->name('update-expense-credit-note-configuration')->middleware('permission:company_add_new_expense|company_edit_expense');

/* Advance Payment Routes */
Route::get('get-unsettled-invoices/{ledger_id}/{type}/{transaction_id?}', [AdvancePaymentAPIController::class, 'getUnsettledInvoices'])->name('get-unsettled-invoices')->middleware('permission:company_add_new_income|company_edit_income|company_add_new_expense|company_edit_expense');

/* E-invoice */
Route::post('generate-e-invoice/{transactionId}/{transactionType}', [EInvoiceController::class, 'eInvoice'])->name('generate-e-invoice');

/* Item Stock Routes */
Route::get('item-stock-list/{ledgerId}', [ItemStockAPIController::class, 'getItemStockList'])->name('get-item-stock-list');
Route::post('item-stock', [ItemStockAPIController::class, 'itemStockStore'])->name('item-stock-store')->middleware('permission:company_add_new_ledgers');
Route::get('item-stock/{id}', [ItemStockAPIController::class, 'itemStockEdit'])->name('item-stock-edit')->middleware('permission:company_edit_ledgers');
Route::post('item-stock/{id}', [ItemStockAPIController::class, 'itemStockUpdate'])->name('item-stock-update')->middleware('permission:company_edit_ledgers');
Route::get('item-stock/{id}/delete', [ItemStockAPIController::class, 'itemStockDestroy'])->name('item-stock-destroy')->middleware('permission:company_delete_ledgers');

/* Custom Fields Routes */
Route::get('custom-fields/types', [CustomFieldsAPIController::class, 'getInputTypes'])->name('get-input-type');
Route::post('custom-fields', [CustomFieldsAPIController::class, 'store'])->name('store-custom-fields');
Route::post('custom-fields/update-status', [CustomFieldsAPIController::class, 'updateStatus'])->name('update-status-custom-fields');
Route::get('custom-fields/{id}/edit', [CustomFieldsAPIController::class, 'edit'])->name('edit-custom-fields');
Route::post('custom-fields/{id}/update', [CustomFieldsAPIController::class, 'update'])->name('update-custom-fields');
Route::get('custom-fields/{id}/delete', [CustomFieldsAPIController::class, 'delete'])->name('delete-custom-fields');

/* rearrange items */
Route::get('rearrange-items/{type}/{invoiceType}', [RearrangeItemsAPIController::class, 'getRearrangeItems'])->name('get-rearrange-items');
Route::post('rearrange-items', [RearrangeItemsAPIController::class, 'rearrangeItems'])->name('rearrange-items');

// pdf configuration api
Route::get('get-print-settings', [PdfConfigurationAPIController::class, 'getPrintSettings'])->name('get-print-settings')->middleware('permission:company_view_print_setting|company_edit_print_setting');
Route::get('get-pdf-template/{type}/{pdfFormat}', [PdfConfigurationAPIController::class, 'getPdfTemplate'])->name('get-pdf-template')->middleware('permission:company_view_print_setting|company_edit_print_setting');
Route::post('save-pdf-template', [PdfConfigurationAPIController::class, 'savePdfTemplate'])->name('save-pdf-template')->middleware('permission:company_edit_print_setting');
Route::post('update-pdf-format', [PdfConfigurationAPIController::class, 'updatePdfFormat'])->name('update-pdf-format')->middleware('permission:company_edit_print_setting');
Route::get('pdf-header-setting/{type}', [PdfConfigurationAPIController::class, 'getPdfHeaderSetting'])->name('get-pdf-header-setting')->middleware('permission:company_view_print_setting|company_edit_print_setting');
Route::post('pdf-header-setting/{type}', [PdfConfigurationAPIController::class, 'savePdfHeaderSetting'])->name('change-pdf-header-setting')->middleware('permission:company_edit_print_setting');
Route::get('pdf-details-setting/{type}', [PdfConfigurationAPIController::class, 'getPdfDetailsSetting'])->name('get-pdf-details-setting')->middleware('permission:company_view_print_setting|company_edit_print_setting');
Route::post('pdf-details-setting/{type}', [PdfConfigurationAPIController::class, 'savePdfDetailsSetting'])->name('change-pdf-details-setting')->middleware('permission:company_edit_print_setting');
Route::get('pdf-footer-setting/{type}', [PdfConfigurationAPIController::class, 'getPdfFooterSetting'])->name('get-pdf-footer-setting')->middleware('permission:company_view_print_setting|company_edit_print_setting');
Route::post('pdf-footer-setting/{type}', [PdfConfigurationAPIController::class, 'savePdfFooterSetting'])->name('change-pdf-footer-setting')->middleware('permission:company_edit_print_setting');
Route::post('update-print-show-hide-setting/{type}', [PdfConfigurationAPIController::class, 'updatePrintShowHideSetting'])->name('update-print-show-hide-setting');
Route::get('pdf-company-setting/{key}', [PdfConfigurationAPIController::class, 'pdfCompanySetting'])->name('pdf-company-setting')->middleware('permission:company_view_print_setting|company_edit_print_setting');
Route::post('update-pdf-slogan', [PdfConfigurationAPIController::class, 'updatePdfSloganSetting'])->name('update-pdf-slogan')->middleware('permission:company_edit_print_setting');
Route::post('update-pdf-logo', [PdfConfigurationAPIController::class, 'updatePdfLogoSetting'])->name('update-pdf-logo')->middleware('permission:company_edit_print_setting');
Route::post('update-pdf-signature', [PdfConfigurationAPIController::class, 'updatePdfSignatureSetting'])->name('update-pdf-signature')->middleware('permission:company_edit_print_setting');
Route::post('update-pdf-email', [PdfConfigurationAPIController::class, 'updatePdfEmailSetting'])->name('update-pdf-email')->middleware('permission:company_edit_print_setting');
Route::post('update-pdf-mobile-number', [PdfConfigurationAPIController::class, 'updatePdfMobileNumberSetting'])->name('update-pdf-mobile-number')->middleware('permission:company_edit_print_setting');
Route::delete('remove-company-logo', [PdfConfigurationAPIController::class, 'removeCompanyLogo'])->name('remove-company-logo')->middleware('permission:company_edit_print_setting');
Route::delete('remove-company-signature', [PdfConfigurationAPIController::class, 'removeCompanySignature'])->name('remove-company-signature')->middleware('permission:company_edit_print_setting');
Route::get('get-duplicate-invoice-label', [PdfConfigurationAPIController::class, 'duplicateInvoiceLabel'])->name('get-duplicate-invoice-label')->middleware('permission:company_view_print_setting|company_edit_print_setting');
Route::post('update-duplicate-invoice-label', [PdfConfigurationAPIController::class, 'storeDuplicateInvoiceLabel'])->name('update-duplicate-invoice-label')->middleware('permission:company_edit_print_setting');
Route::get('get-triplicate-invoice-label', [PdfConfigurationAPIController::class, 'getTriplicateInvoiceLabel'])->name('get-triplicate-invoice-label')->middleware('permission:company_view_print_setting|company_edit_print_setting');
Route::post('update-triplicate-invoice-label', [PdfConfigurationAPIController::class, 'storeTriplicateInvoiceLabel'])->name('update-triplicate-invoice-label')->middleware('permission:company_edit_print_setting');
Route::get('get-prop-name', [PdfConfigurationAPIController::class, 'getPropName'])->name('get-prop-name')->middleware('permission:company_view_print_setting|company_edit_print_setting');
Route::post('update-prop-name', [PdfConfigurationAPIController::class, 'updatePropName'])->name('update-prop-name')->middleware('permission:company_edit_print_setting');

// pdf-adjustments
Route::get('pdf-adjustments/{pdfFormat}', [PdfConfigurationAPIController::class, 'pdfAdjustments'])->name('pdf-adjustments');
Route::post('pdf-adjustments', [PdfConfigurationAPIController::class, 'pdfAdjustmentsCreateAndUpdate'])->name('pdf-adjustments-store-and-update');
Route::delete('reset-pdf-adjustments/{pdfFormat}', [PdfConfigurationAPIController::class, 'resetPdfAdjustments'])->name('reset-pdf-adjustments');

// General Setting
Route::get('get-currency-list', [GeneralSettingAPIController::class, 'getCurrencyList'])->name('get-currency-list');
Route::get('general-setting', [GeneralSettingAPIController::class, 'getGeneralSetting'])->name('get-general-setting')->middleware('permission:company_view_general_setting');
Route::post('general-setting', [GeneralSettingAPIController::class, 'storeAndUpdateGeneralSetting'])->name('store-and-update-general-setting')->middleware('permission:company_edit_general_setting');

// E-way bill and E-invoice Setting API
Route::get('e-way-bill-setting', [GeneralSettingAPIController::class, 'getEWayBillSetting'])->name('get-e-way-bill-setting')->middleware('permission:company_view_ewaybill_and_einvoice_setting');
Route::post('e-way-bill-setting', [GeneralSettingAPIController::class, 'storeAndUpdateEWayBillSetting'])->name('store-and-update-e-way-bill-setting')->middleware('permission:company_edit_ewaybill_and_einvoice_setting');

// Cheque Printing Setting API
Route::get('cheque-printing-setting', [GeneralSettingAPIController::class, 'getChequePrintingSetting'])->name('get-cheque-printing-setting')->middleware('permission:company_view_cheque_printing');
Route::post('cheque-printing-setting', [GeneralSettingAPIController::class, 'storeAndUpdateChequePrintingSetting'])->name('store-and-update-cheque-printing-setting')->middleware('permission:company_edit_cheque_printing');

// Email Setting API
Route::get('mail-configuration', [GeneralSettingAPIController::class, 'getMailConfiguration'])->name('get-mail-configuration')->middleware('permission:company_view_email_and_whatsapp_configuration');
Route::post('mail-configuration/{type?}', [GeneralSettingAPIController::class, 'storeAndUpdateMailConfiguration'])->name('store-and-update-mail-configuration')->middleware('permission:company_edit_email_and_whatsapp_configuration');
Route::post('auto-send-email/{id}', [GeneralSettingAPIController::class, 'storeAndUpdateAutoSendEmail'])->name('store-and-update-auto-send-email')->middleware('permission:company_edit_email_and_whatsapp_configuration');

// WhatsApp Setting API
Route::get('whatsapp-configuration', [GeneralSettingAPIController::class, 'getWhatsappConfiguration'])->name('get-whatsapp-configuration')->middleware('permission:company_view_email_and_whatsapp_configuration');
Route::post('whatsapp-auto-payment-reminder-status', [GeneralSettingAPIController::class, 'channgeWhatsappAutoPaymentReminderStatus'])->name('')->middleware('permission:company_edit_email_and_whatsapp_configuration');
Route::post('add-whatsapp-device', [GeneralSettingAPIController::class, 'addWhatsappDevice'])->name('add-whatsapp-device')->middleware('permission:company_edit_email_and_whatsapp_configuration');
Route::get('connect-device-wise-configuration/{instanceId}', [GeneralSettingAPIController::class, 'connectDeviceWiseConfiguration'])->name('connect-device-wise-configuration')->middleware('permission:company_view_email_and_whatsapp_configuration');
Route::post('whatsapp-connected-devices', [GeneralSettingAPIController::class, 'whatsappConnectedDevices'])->name('whatsapp-connected-devices')->middleware('permission:company_edit_email_and_whatsapp_configuration');
Route::post('update-device-name/{id}', [GeneralSettingAPIController::class, 'updateInstanceDeviceName'])->name('update-device-name')->middleware('permission:company_edit_email_and_whatsapp_configuration');
Route::delete('remove-whatsapp-device/{id}', [GeneralSettingAPIController::class, 'deleteWhatsappDevice'])->name('whatsapp-device-destroy')->middleware('permission:company_edit_email_and_whatsapp_configuration');
Route::post('auto-send-whatsapp/{id}', [GeneralSettingAPIController::class, 'storeAndUpdateAutoSendWhatsapp'])->name('store-and-update-auto-send-whatsapp')->middleware('permission:company_edit_email_and_whatsapp_configuration');

/* Purchase OCR Result */
Route::get('ocr/{id}/purchase', [PurchaseOCRAPIController::class, 'getPurchaseOCRTransaction'])->name('get-purchase-ocr-transaction');
Route::post('ocr/purchase', [PurchaseOCRAPIController::class, 'storePurchaseOCRTransaction'])->name('store-purchase-ocr-transaction');

/* Bank OCR Result */
Route::get('ocr/{id}/bank', [PurchaseOCRAPIController::class, 'getBankOCRTransaction'])->name('get-bank-ocr-transaction');
// Route::get('bank-ledgers-list', [PurchaseOCRAPIController::class, 'getBankLedgers'])->name('get-bank-ledgers');
// Bank OCR Statement Notes Update
Route::post('ocr-bank-note-update/{id}', [PurchaseOCRAPIController::class, 'ocrBankNotesUpdate'])->name('ocr-bank-notes-update');
// Bank OCR Statement Ledger Update
Route::post('update-ocr-bank-statement', [PurchaseOCRAPIController::class, 'updateOcrBankStatement'])->name('update-ocr-bank-statement');
// Bank OCR Statement Ledger Bulk Update
Route::post('ocr-statement-ledgers/update-bulk', [PurchaseOCRAPIController::class, 'updateBulkStatementForLedger'])->name('ocr-statement-ledgers-update-bulk');
// Bank OCR Statement Destroy
Route::post('ocr-statement-destroy/{id}', [PurchaseOCRAPIController::class, 'ocrStatementDestroy'])->name('ocr-statement-destroy');
// Bank OCR Statement settle
Route::get('ocr-statement-receipt-settle/{ledger}/{id}', [PurchaseOCRAPIController::class, 'ocrStatementReceiptSettle'])->name('ocr-statement-receipt-settle');
Route::get('ocr-statement-payment-settle/{ledger}/{id}', [PurchaseOCRAPIController::class, 'ocrStatementPaymentSettle'])->name('ocr-statement-payment-settle');
Route::post('ocr-statement-settle', [PurchaseOCRAPIController::class, 'ocrStatementSettle'])->name('ocr-statement-settle');
// Bank OCR Statement posted trsancation
Route::post('ocr-statement-bank-posted', [PurchaseOCRAPIController::class, 'ocrStatementBankPosted'])->name('ocr-statement-bank-posted');
Route::post('ocr-statement-bank/bulk-posted', [PurchaseOCRAPIController::class, 'ocrStatementBankBulkPosted'])->name('ocr-statement-bank-bulk-posted');

// PDF preview API
Route::get('pdf-preview/{transaction}/{layout}', [GeneralSettingAPIController::class, 'pdfPreview'])->name('pdf-preview');

/* Customer Master Report */
Route::get('customer-master-report', [CustomerMasterReportAPIController::class, 'index'])->name('customer-master-report');
Route::delete('customer-master-report/{id}', [CustomerMasterReportAPIController::class, 'destroy'])->name('customer-master-report-destroy');
Route::get('column-selector/customer-master-report', [CustomerMasterReportAPIController::class, 'getColumnSelector'])->name('customer-master-report-column-selector');
Route::post('column-selector/customer-master-report', [CustomerMasterReportAPIController::class, 'updateColumnSelector'])->name('customer-master-report-update-column-selector');
Route::get('export/customer-master-report', [CustomerMasterReportAPIController::class, 'export'])->name('customer-master-report-export');

/* Supplier Master Report */
Route::get('supplier-master-report', [SupplierMasterReportAPIController::class, 'index'])->name('supplier-master-report');
Route::delete('supplier-master-report/{id}', [SupplierMasterReportAPIController::class, 'destroy'])->name('supplier-master-report-destroy');
Route::get('column-selector/supplier-master-report', [SupplierMasterReportAPIController::class, 'columnSelector'])->name('supplier-master-report-column-selector');
Route::post('column-selector/supplier-master-report', [SupplierMasterReportAPIController::class, 'updateColumnSelector'])->name('supplier-master-report-update-column-selector');
Route::get('export/supplier-master-report', [SupplierMasterReportAPIController::class, 'export'])->name('supplier-master-report-export');

Route::post('estimate-quote-title', [EstimateQuoteTitleAPIController::class, 'store'])->name('estimate-quote-title-store');
Route::get('estimate-quote-title/{title}', [EstimateQuoteTitleAPIController::class, 'edit'])->name('estimate-quote-title-edit');
Route::post('estimate-quote-title/{id}', [EstimateQuoteTitleAPIController::class, 'update'])->name('estimate-quote-title-update');
Route::delete('estimate-quote-title/{title}/delete', [EstimateQuoteTitleAPIController::class, 'destroy'])->name('estimate-quote-title-destroy');

Route::post('purchase-order-title', [PurchaseOrderTitleAPIController::class, 'store'])->name('purchase-order-title-store');
Route::get('purchase-order-title/{title}', [PurchaseOrderTitleAPIController::class, 'edit'])->name('purchase-order-title-edit');
Route::post('purchase-order-title/{id}', [PurchaseOrderTitleAPIController::class, 'update'])->name('purchase-order-title-update');
Route::delete('purchase-order-title/{title}/delete', [PurchaseOrderTitleAPIController::class, 'destroy'])->name('purchase-order-title-destroy');

/* Barcode Item API */
Route::get('barcode-item-list', [BarcodeItemAPIController::class, 'getBarcodeItemList'])->name('get-barcode-item-list');
Route::get('barcode-item-group-list', [BarcodeItemAPIController::class, 'getBarcodeItemGroupList'])->name('get-barcode-item-group-list');
Route::get('barcode-item-details', [BarcodeItemAPIController::class, 'getBarcodeItemDetails'])->name('get-barcode-item-details');
Route::post('download-barcode-pdf', [BarcodeItemAPIController::class, 'downloadBarcodePdf'])->name('download-barcode-pdf');
Route::get('barcode-setting/{type}', [BarcodeItemAPIController::class, 'getBarcodeSetting'])->name('get-barcode-setting');
Route::post('barcode-setting/{type}', [BarcodeItemAPIController::class, 'saveAndUpdateBarcodeSetting'])->name('change-barcode-setting');
Route::get('printer-size', [BarcodeItemAPIController::class, 'printerSize'])->name('get-printer-size');
Route::post('printer-size', [BarcodeItemAPIController::class, 'updatePrinterSize'])->name('update-printer-size');

/* Recurring Invoice */
Route::get('recurring-invoices', [RecurringInvoiceAPIController::class, 'index'])->name('get-recurring-invoice');
Route::post('recurring-invoices', [RecurringInvoiceAPIController::class, 'store'])->name('store-recurring-invoice');
Route::get('recurring-invoices/{id}', [RecurringInvoiceAPIController::class, 'edit'])->name('edit-recurring-invoice');
Route::post('recurring-invoices/{id}', [RecurringInvoiceAPIController::class, 'update'])->name('update-recurring-invoice');
Route::delete('recurring-invoices/{id}', [RecurringInvoiceAPIController::class, 'destroy'])->name('destroy-recurring-invoice');
Route::get('check-recurring-invoice-exists/{id}', [RecurringInvoiceAPIController::class, 'checkRecurringInvoiceExists'])->name('check-recurring-invoice-exists');
Route::get('recurring-invoices/{id}/update-status', [RecurringInvoiceAPIController::class, 'updateStatus'])->name('update-recurring-invoice-status');
Route::get('recurring-invoices-templates', [RecurringInvoiceAPIController::class, 'getDescriptionTemplate'])->name('get-recurring-invoice-description-template');

Route::get('recurring-invoices-party-list', [RecurringInvoiceAPIController::class, 'getPartyList'])->name('get-recurring-party-list');
Route::get('recurring-invoices-party-group-list', [RecurringInvoiceAPIController::class, 'getPartyGroupList'])->name('get-recurring-party-group-list');
Route::get('recurring-invoices-parties', [RecurringInvoiceAPIController::class, 'getPartyDetail'])->name('get-recurring-party-detail');
Route::post('recurring-invoices/{id}/handle-approval', [RecurringInvoiceAPIController::class, 'handleApproveRejectInvoice'])->name('approve-or-reject-recurring-invoice');
Route::get('recurring-invoices/{id}/undo-rejection', [RecurringInvoiceAPIController::class, 'undoRejection'])->name('undo-rejection-recurring-invoice');
Route::get('recurring-invoices-pdf-preview/{id}/{printType?}', [RecurringInvoiceAPIController::class, 'getPdfPreview'])->name('get-recurring-invoice-pdf-preview');
Route::get('recurring-invoices-pdf-download/{id}/{printType?}', [RecurringInvoiceAPIController::class, 'getPdfDownload'])->name('get-recurring-invoice-pdf-download');
Route::get('recurring-invoices-dynamic-variables', [RecurringInvoiceAPIController::class, 'getDynamicVariables'])->name('get-recurring-invoice-dynamic-variables');

/* Recurring Invoice Configuration */
Route::get('recurring-invoice-configuration', [RecurringInvoiceConfigurationAPIController::class, 'getConfiguration'])->name('get-recurring-invoice-configuration');
Route::post('recurring-invoice-configuration/{type?}', [RecurringInvoiceConfigurationAPIController::class, 'updateConfiguration'])->name('update-recurring-invoice-configuration');

Route::prefix('vastra')->group(function () {
    Route::get('configuration', [VastraAPIController::class, 'getVastraConfiguration']);
    Route::post('configuration', [VastraAPIController::class, 'storeAndUpdateVastraConfiguration']);
    Route::get('sync', [VastraAPIController::class, 'syncMasterDataFromVastra']);
    // Route::get('sync-sale-order', [VastraAPIController::class, 'syncSaleOrderFromVastra']);
    // Route::get('sync-sale', [VastraAPIController::class, 'syncSaleFromVastra']);
    Route::post('fetch-delivery-challans', [VastraAPIController::class, 'fetchDeliveyChallansData']);

    Route::get('delivery-challans-columns', [VastraAPIController::class, 'getDeliveryChallansColumns']);
    Route::post('delivery-challans-columns', [VastraAPIController::class, 'updateDeliveryChallansColumns']);
    Route::get('delivery-challans', [VastraAPIController::class, 'index']);
    Route::post('check-transaction-exists', [VastraAPIController::class, 'checkTransactionExists']);
    Route::post('delivery-challans-destroy', [VastraAPIController::class, 'destroy']);
    Route::post('create-sale-from-delivery', [VastraAPIController::class, 'createSaleFromVastraDelivery']);
});

/* Top & Least Selling Items */
Route::get('top-selling-items', [TopAndLeastSellingItemReportAPIController::class, 'getTopSellingItems'])->name('get-top-selling-items');
Route::get('least-selling-items', [TopAndLeastSellingItemReportAPIController::class, 'getLeastSellingItems'])->name('get-least-selling-items');
Route::get('export/top-least-selling-items-report', [TopAndLeastSellingItemReportAPIController::class, 'export'])->name('export-top-least-selling-items');

/* Custom fields item */
// Route::get('custom-fields-item/types', [CustomFieldsItemAPIController::class, 'getInputTypes']);
// Route::post('custom-fields-item', [CustomFieldsItemAPIController::class, 'store']);
// Route::post('custom-fields-item/update-status', [CustomFieldsItemAPIController::class, 'updateStatus']);
// Route::get('custom-fields-item/{id}/edit', [CustomFieldsItemAPIController::class, 'edit']);
// Route::post('custom-fields-item/{id}/update', [CustomFieldsItemAPIController::class, 'update']);
// Route::get('custom-fields-item/{id}/delete', [CustomFieldsItemAPIController::class, 'delete']);

// Route::post('custom-fields-item/formula', [CustomFieldsItemAPIController::class, 'storeUpdateFormula']);
// Route::get('custom-fields-item/delete-formula/{id}', [CustomFieldsItemAPIController::class, 'deleteFormula']);

/* Low Stock Report Item Level */
Route::get('low-stock-report-items', [ItemMasterAPIController::class, 'getReOrderItems']);
Route::get('export/low-stock-report-items', [ItemMasterAPIController::class, 'export']);

// Auth 11za API
Route::get('11za-configurations', [Auth11zaAPIController::class, 'authToken11za']);
Route::post('11za-configurations', [Auth11zaAPIController::class, 'saveConfiguration']);
Route::get('11za-templates', [Auth11zaAPIController::class, 'templates']);
Route::post('11za-templates/{id}/update-status', [Auth11zaAPIController::class, 'updateStatus']);
Route::get('11za-templates-name-list', [Auth11zaAPIController::class, 'templatesNameList']);
Route::post('11za-templates-body', [Auth11zaAPIController::class, 'templatesBody']);
Route::get('11za-template/{id}/edit', [Auth11zaAPIController::class, 'edit']);
Route::post('11za-template/{id}/update', [Auth11zaAPIController::class, 'update']);

// Payment Mode
Route::get('payment-modes/{type}', [PaymentModeAPIController::class, 'index']);
Route::post('payment-modes', [PaymentModeAPIController::class, 'create'])->name('payment-mode-store');
Route::get('payment-modes/{id}/edit', [PaymentModeAPIController::class, 'edit'])->name('payment-mode-edit');
Route::post('payment-modes/{id}/update', [PaymentModeAPIController::class, 'update'])->name('payment-mode-update');
Route::delete('payment-modes/{id}/delete', [PaymentModeAPIController::class, 'destroy'])->name('payment-mode-destroy');

/* Custom fields item master */
Route::get('custom-field-item-master/types', [CustomFieldItemMasterAPIController::class, 'getCustomFieldTypes']);
Route::get('custom-field-item-master', [CustomFieldItemMasterAPIController::class, 'index']);
Route::post('custom-field-item-master', [CustomFieldItemMasterAPIController::class, 'store']);
Route::get('custom-field-item-master/{id}/edit', [CustomFieldItemMasterAPIController::class, 'edit']);
Route::post('custom-field-item-master/{id}/update', [CustomFieldItemMasterAPIController::class, 'update']);
Route::post('custom-field-item-master/update-status', [CustomFieldItemMasterAPIController::class, 'updateStatus']);
Route::get('custom-field-item-master/{id}/delete', [CustomFieldItemMasterAPIController::class, 'delete']);
Route::post('custom-field-item-master/update-transaction-status', [CustomFieldItemMasterAPIController::class, 'updateTransactionStatus']);
Route::post('custom-field-item-master/formula', [CustomFieldItemMasterAPIController::class, 'storeUpdateFormula']);
Route::get('custom-field-item-master/delete-formula/{id}', [CustomFieldItemMasterAPIController::class, 'deleteFormula']);

// GST Login
Route::post('gst-login', [GstLoginApiController::class, 'login']);

    // fetch Data
    Route::get('fetch-data', [Gstr2BReconciliationAPIController::class, 'fetchData']);

    // Gstr2b Summary
    Route::get('gstr-2b-summary', [Gstr2BReconciliationAPIController::class, 'index']);
    Route::get('gstr-2b-details', [Gstr2BReconciliationAPIController::class, 'reconciliation']);
// });
