<?php

use App\Http\Controllers\API\v2\CessModule\CessModuleAPIController;
use App\Http\Controllers\API\v2\CommonAPIController;
use App\Http\Controllers\API\v2\CompanyDashboard\CompanyDashboardAPIController;
use App\Http\Controllers\API\v2\CustomField\CustomFieldsAPIController;
use App\Http\Controllers\API\v2\DeliveryChallan\DeliveryChallanConfigurationAPIController;
use App\Http\Controllers\API\v2\DeliveryChallan\DeliveryChallanTransactionAPIController;
use App\Http\Controllers\API\v2\DispatchAddressAPIController;
use App\Http\Controllers\API\v2\ExpenseCreditNote\ExpenseCreditNoteConfigurationAPIController;
use App\Http\Controllers\API\v2\ExpenseCreditNote\ExpenseCreditNoteTransactionAPIController;
use App\Http\Controllers\API\v2\ExpenseDebitNote\ExpenseDebitNoteConfigurationAPIController;
use App\Http\Controllers\API\v2\ExpenseDebitNote\ExpenseDebitNoteTransactionAPIController;
use App\Http\Controllers\API\v2\IncomeCreditNote\IncomeCreditNoteConfigurationAPIController;
use App\Http\Controllers\API\v2\IncomeCreditNote\IncomeCreditNoteTransactionAPIController;
use App\Http\Controllers\API\v2\IncomeDebitNote\IncomeDebitNoteConfigurationAPIController;
use App\Http\Controllers\API\v2\IncomeDebitNote\IncomeDebitNoteTransactionAPIController;
use App\Http\Controllers\API\v2\IncomeEstimateQuote\IncomeEstimateQuoteConfigurationAPIController;
use App\Http\Controllers\API\v2\IncomeEstimateQuote\IncomeEstimateQuoteTransactionAPIController;
use App\Http\Controllers\API\v2\ItemMaster\ItemMasterAPIController;
use App\Http\Controllers\API\v2\Ledger\LedgerAPIController;
use App\Http\Controllers\API\v2\PaymentMode\PaymentModeAPIController;
use App\Http\Controllers\API\v2\PrintSetting\PrintSettingAPIController;
use App\Http\Controllers\API\v2\Purchase\PurchaseConfigurationAPIController;
use App\Http\Controllers\API\v2\Purchase\PurchaseTransactionAPIController;
use App\Http\Controllers\API\v2\PurchaseOrder\PurchaseOrderConfigurationAPIController;
use App\Http\Controllers\API\v2\PurchaseOrder\PurchaseOrderTransactionAPIController;
use App\Http\Controllers\API\v2\PurchaseReturn\PurchaseReturnConfigurationAPIController;
use App\Http\Controllers\API\v2\PurchaseReturn\PurchaseReturnTransactionAPIController;
use App\Http\Controllers\API\v2\RecycleBin\RecycleBinAPIController;
use App\Http\Controllers\API\v2\Sale\SaleConfigurationAPIController;
use App\Http\Controllers\API\v2\Sale\SaleTransactionAPIController;
use App\Http\Controllers\API\v2\SaleReturn\SaleReturnConfigurationAPIController;
use App\Http\Controllers\API\v2\SaleReturn\SaleReturnTransactionAPIController;
use App\Http\Controllers\API\v2\ShippingAddressAPIController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth:sanctum', 'companyValidate'])->group(function () {

    Route::controller(CompanyDashboardAPIController::class)->group(function () {
        Route::get('company-dashboard-data', 'companyDashboardData')->middleware('userLastUsedAt');
    });

    Route::controller(CommonAPIController::class)->group(function () {
        // Route::get('party-details/{party}', 'partyDetails');
        Route::get('payment-mode-list/{type}', 'paymentModeList');
        Route::get('additional-charges-ledger-list', 'additionalChargesLedgerList');
        Route::get('add-less-ledger-list', 'addLessLedgerList');
        Route::get('tcs-rate-list/{type}', 'tcsRateList');
        Route::get('tds-rate-list/{type}', 'tdsRateList');
        Route::get('tds-tcs-details/{ledger_id}/{party_id?}', 'tdsTcsDetails');
        Route::get('price-lists', 'priceListItemPrice');
        Route::get('lock-transaction-date', 'lockTransactionDate');
        Route::get('e-way-details/{id}', 'eWayDetails');
        Route::get('transaction-pdf-preview/{transaction_type}/{transaction_id}', 'transactionPdfPreview');
        Route::get('favorite-reports', 'favoriteReports');
        Route::post('favorite-reports', 'markAsFavorite');
    });

    /* Dispatch Address */
    Route::get('dispatch-address-list', [DispatchAddressAPIController::class, 'index']);
    Route::post('dispatch-address', [DispatchAddressAPIController::class, 'create']);
    Route::get('dispatch-address/{id}', [DispatchAddressAPIController::class, 'edit']);
    Route::post('dispatch-address/{id}', [DispatchAddressAPIController::class, 'update']);
    Route::post('dispatch-address/{id}/delete', [DispatchAddressAPIController::class, 'destroy']);

    /* Shipping Address */
    Route::get('shipping-address-list/{id}', [ShippingAddressAPIController::class, 'index']);
    Route::post('shipping-address', [ShippingAddressAPIController::class, 'store']);
    Route::get('shipping-address/{id}', [ShippingAddressAPIController::class, 'edit']);
    Route::post('shipping-address/{id}', [ShippingAddressAPIController::class, 'update']);
    Route::delete('shipping-address/{id}', [ShippingAddressAPIController::class, 'destroy']);
    Route::get('update-selected-shipping-address/{address_id}/{ledger_id}', [ShippingAddressAPIController::class, 'updateSelectedShippingAddress']);

    Route::controller(SaleTransactionAPIController::class)->group(function () {
        Route::get('sales/next-invoice-number', 'nextInvoiceNumber');
        Route::post('sales', 'store');
        Route::get('sales/{id}', 'edit');
        Route::get('sale-transaction-details/{id}', 'saleTransactionDetails');
        Route::post('sales/{id}', 'update');
        Route::get('sales/{id}/duplicate', 'duplicate');
        Route::get('sales-invoice-document-list/{partyId}', 'salesInvoiceDocumentList');
        Route::post('sales-multi-estimate', 'multiEstimate');
        Route::post('sales-multi-challan', 'multiDeliveryChallan');
        Route::get('sales/{id}/sale-return', 'getSaleForSaleReturn');
    });

    Route::controller(SaleConfigurationAPIController::class)->group(function () {
        Route::get('/sale-configuration', 'saleConfiguration');
        Route::post('/sale-configuration/{type?}', 'updateSaleConfiguration');
    });

    Route::controller(PurchaseTransactionAPIController::class)->group(function () {
        Route::get('purchase/next-invoice-number', 'nextInvoiceNumber');
        Route::post('purchase', 'store');
        Route::get('purchase/{id}', 'edit');
        Route::get('purchase-transaction-details/{id}', 'purchaseTransactionDetails');
        Route::post('purchase/{id}', 'update');
        Route::post('purchase-multi-purchase-order', 'multiPurchaseOrder');
        Route::get('purchase/{id}/duplicate', 'duplicate');
        Route::get('purchase/{id}/purchase-return', 'getPurchaseForPurchaseReturn');
    });

    Route::controller(PurchaseConfigurationAPIController::class)->group(function () {
        Route::get('/purchase-configuration', 'purchaseConfiguration');
        Route::post('/purchase-configuration/{type?}', 'updatePurchaseConfiguration');
    });

    Route::controller(SaleReturnTransactionAPIController::class)->group(function () {
        Route::get('sale-returns/next-invoice-number', 'nextInvoiceNumber');
        Route::post('sale-returns', 'store');
        Route::get('sale-returns/{id}', 'edit');
        Route::post('sale-returns/{id}', 'update');
        Route::get('sale-returns/{id}/duplicate', 'duplicate');
    });

    Route::controller(SaleReturnConfigurationAPIController::class)->group(function () {
        Route::get('/sale-returns-configuration', 'saleReturnConfiguration');
        Route::post('/sale-returns-configuration/{type?}', 'updateSaleReturnConfiguration');
    });

    Route::controller(IncomeEstimateQuoteTransactionAPIController::class)->group(function () {
        Route::get('income-estimate-quote/next-invoice-number', 'nextInvoiceNumber');
        Route::post('income-estimate-quote', 'store');
        Route::get('income-estimate-quote/{id}', 'edit');
        Route::post('income-estimate-quote/{id}', 'update');
        Route::get('income-estimate-quote/{id}/duplicate', 'duplicate');
    });

    Route::controller(IncomeEstimateQuoteConfigurationAPIController::class)->group(function () {
        Route::get('/income-estimate-quote-configuration', 'estimateQuoteConfiguration');
        Route::post('/income-estimate-quote-configuration/{type?}', 'updateEstimateQuoteConfiguration');
    });

    // Income Credit Note
    Route::controller(IncomeCreditNoteTransactionAPIController::class)->group(function () {
        Route::get('income-credit-notes/next-invoice-number', 'nextInvoiceNumber');
        Route::post('income-credit-notes', 'store');
        Route::get('income-credit-notes/{id}', 'edit');
        Route::post('income-credit-notes/{id}', 'update');
        Route::get('income-credit-notes/{id}/duplicate', 'duplicate');
    });

    Route::controller(IncomeCreditNoteConfigurationAPIController::class)->group(function () {
        Route::get('/income-credit-note-configuration', 'incomeCreditNoteConfiguration');
        Route::post('/income-credit-note-configuration/{type?}', 'updateIncomeCreditNoteConfiguration');
    });

    // Income Debit Note
    Route::controller(IncomeDebitNoteTransactionAPIController::class)->group(function () {
        Route::get('income-debit-note/next-invoice-number', 'nextInvoiceNumber');
        Route::post('income-debit-notes', 'store');
        Route::get('income-debit-notes/{id}', 'edit');
        Route::post('income-debit-notes/{id}', 'update');
        Route::get('income-debit-notes/{id}/duplicate', 'duplicate');
    });

    Route::controller(IncomeDebitNoteConfigurationAPIController::class)->group(function () {
        Route::get('/income-debit-note-configuration', 'incomeDebitNoteConfiguration');
        Route::post('/income-debit-note-configuration/{type?}', 'updateIncomeDebitNoteConfiguration');
    });

    // Expense Debit Note
    Route::controller(ExpenseDebitNoteTransactionAPIController::class)->group(function () {
        Route::get('expense-debit-note/next-invoice-number', 'nextInvoiceNumber');
        Route::post('expense-debit-notes', 'store');
        Route::get('expense-debit-notes/{id}', 'edit');
        Route::post('expense-debit-notes/{id}', 'update');
        Route::get('expense-debit-notes/{id}/duplicate', 'duplicate');
    });

    Route::controller(ExpenseDebitNoteConfigurationAPIController::class)->group(function () {
        Route::get('/expense-debit-note-configuration', 'expenseDebitNoteConfiguration');
        Route::post('/expense-debit-note-configuration/{type?}', 'updateExpenseDebitNoteConfiguration');
    });

    // Purchase Order
    Route::controller(PurchaseOrderTransactionAPIController::class)->group(function () {
        Route::get('purchase-order/next-invoice-number', 'nextInvoiceNumber');
        Route::post('purchase-order', 'store');
        Route::get('purchase-order/{id}', 'edit');
        Route::post('purchase-order/{id}', 'update');
        Route::get('purchase-order-document-list/{partyId}', 'purchaseOrderDocumentList');
        Route::get('purchase-order/{id}/purchase-create-purchase-order', 'purchaseCreatePurchaseOrder');
        Route::get('purchase-order/{id}/duplicate', 'duplicate');
    });

    Route::controller(PurchaseOrderConfigurationAPIController::class)->group(function () {
        Route::get('/purchase-order-configuration', 'purchaseOrderConfiguration');
        Route::post('/purchase-order-configuration/{type?}', 'updatePurchaseOrderConfiguration');
    });

    // Purchase Return
    Route::controller(PurchaseReturnTransactionAPIController::class)->group(function () {
        Route::get('purchase-return/next-invoice-number', 'nextInvoiceNumber');
        Route::post('purchase-return', 'store');
        Route::get('purchase-return/{id}', 'edit');
        Route::post('purchase-return/{id}', 'update');
        Route::get('purchase-return/{id}/duplicate', 'duplicate');
    });

    Route::controller(PurchaseReturnConfigurationAPIController::class)->group(function () {
        Route::get('/purchase-return-configuration', 'purchaseReturnConfiguration');
        Route::post('/purchase-return-configuration/{type?}', 'updatePurchaseReturnConfiguration');
    });

    // Delivery Challan Transaction
    Route::controller(DeliveryChallanTransactionAPIController::class)->group(function () {
        Route::get('delivery-challan/next-invoice-number', 'nextInvoiceNumber');
        Route::post('delivery-challan', 'store');
        Route::get('delivery-challan/{id}', 'edit');
        Route::post('delivery-challan/{id}', 'update');
        Route::get('delivery-challan/{id}/get-sale-transaction', 'getSaleTransaction');
        Route::get('delivery-challan-document-numbers-list/{patyId}', 'getDocumentNumbersList');
        Route::get('delivery-challan/{id}/duplicate', 'duplicate');
    });

    Route::controller(DeliveryChallanConfigurationAPIController::class)->group(function () {
        Route::get('/delivery-challan-configuration', 'deliveryChallanConfiguration');
        Route::post('/delivery-challan-configuration/{type?}', 'updateDeliveryChallanConfiguration');
    });

    // Expense Credit Note
    Route::controller(ExpenseCreditNoteTransactionAPIController::class)->group(function () {
        Route::get('expense-credit-notes/next-invoice-number', 'nextInvoiceNumber');
        Route::post('expense-credit-notes', 'store');
        Route::get('expense-credit-notes/{id}', 'edit');
        Route::post('expense-credit-notes/{id}', 'update');
        Route::get('expense-credit-notes/{id}/duplicate', 'duplicate');
    });

    Route::controller(ExpenseCreditNoteConfigurationAPIController::class)->group(function () {
        Route::get('/expense-credit-note-configuration', 'expenseCreditNoteConfiguration');
        Route::post('/expense-credit-note-configuration/{type?}', 'updateExpenseCreditNoteConfiguration');
    });

    // Payment Mode
    Route::controller(PaymentModeAPIController::class)->group(function () {
        Route::get('payment-modes/{type}', 'index');
        Route::post('payment-mode', 'create');
        Route::get('payment-mode/{id}', 'edit');
        Route::post('payment-mode/{id}', 'update');
        Route::delete('payment-mode/{id}/delete', 'destroy');
    });

    // Item Master
    Route::controller(ItemMasterAPIController::class)->group(function () {
        Route::get('item-masters', 'index');
        Route::post('item-masters', 'create');
        Route::get('item-masters/{id}', 'edit');
        Route::post('item-masters/{id}', 'update');
    });

    // Cess Module
    Route::controller(CessModuleAPIController::class)->group(function () {
        Route::get('cess-rates', 'index');
        Route::post('cess-rates', 'create');
        Route::get('cess-rates/{id}', 'edit');
        Route::post('cess-rates/{id}', 'update');
        Route::delete('cess-rates/{id}', 'destroy');
    });

    // Recycle Bin
    Route::controller(RecycleBinAPIController::class)->group(function () {
        Route::get('recycle-bin/{type}', 'index');
        Route::post('recycle-bin/restore-or-delete/{event}', 'restoreAndDelete');
        Route::delete('recycle-bin/empty', 'emptyRecycleBin');
    });

    Route::post('ledgers/create', [LedgerAPIController::class, 'create']);
    Route::get('ledgers/{id}/edit', [LedgerAPIController::class, 'edit']);
    Route::post('ledgers/{id}/update', [LedgerAPIController::class, 'update']);

    // Print Setting
    Route::controller(PrintSettingAPIController::class)->group(function () {
        Route::get('general-print-settings', 'getGeneralPrintSettings');
        Route::get('pdf-template/{type}/{pdfFormat}', 'getPdfTemplate');
        Route::post('pdf-template', 'savePdfTemplate');
        Route::post('pdf-format', 'updatePdfFormat');
        Route::get('pdf-header-setting/{type}', 'getPdfHeaderSetting');
        Route::post('pdf-header-setting/{type}', 'savePdfHeaderSetting');
        Route::get('pdf-details-setting/{type}', 'getPdfDetailsSetting');
        Route::post('pdf-details-setting/{type}', 'savePdfDetailsSetting');
        Route::get('pdf-footer-setting/{type}', 'getPdfFooterSetting');
        Route::post('pdf-footer-setting/{type}', 'savePdfFooterSetting');
        Route::get('pdf-company-setting/{type}', 'pdfCompanySetting');
        Route::post('update-pdf-slogan', 'updatePdfSloganSetting');
        Route::post('update-pdf-email', 'updatePdfEmailSetting');
        Route::post('update-pdf-mobile-number', 'updatePdfMobileNumberSetting');
        Route::post('update-pdf-logo', 'updatePdfLogoSetting');
        Route::post('update-pdf-signature', 'updatePdfSignatureSetting');
        Route::delete('remove-company-logo', 'removeCompanyLogo');
        Route::delete('remove-company-signature', 'removeCompanySignature');
        Route::post('update-print-show-hide-setting/{type}', 'updatePrintShowHideSetting');
        Route::get('pdf-adjustments/{pdfFormat}', 'pdfAdjustments');
        Route::post('pdf-adjustments', 'pdfAdjustmentsCreateAndUpdate');
        Route::delete('reset-pdf-adjustments/{pdfFormat}', 'resetPdfAdjustments');
        Route::get('pdf-preview/{transaction}/{layout}', 'pdfPreview');
        Route::get('get-prop-name', 'getPropName');
        Route::post('update-prop-name', 'updatePropName');
        Route::get('get-duplicate-invoice-label', 'duplicateInvoiceLabel');
        Route::post('update-duplicate-invoice-label', 'storeDuplicateInvoiceLabel');
        Route::get('get-triplicate-invoice-label', 'getTriplicateInvoiceLabel');
        Route::post('update-triplicate-invoice-label', 'storeTriplicateInvoiceLabel');

        Route::get('custom-fields/types', [CustomFieldsAPIController::class, 'getInputTypes']);
        Route::post('custom-fields', [CustomFieldsAPIController::class, 'store']);
        Route::post('custom-fields/update-status', [CustomFieldsAPIController::class, 'updateStatus']);
        Route::get('custom-fields/{id}/edit', [CustomFieldsAPIController::class, 'edit']);
        Route::post('custom-fields/{id}/update', [CustomFieldsAPIController::class, 'update']);
        Route::get('custom-fields/{id}/delete', [CustomFieldsAPIController::class, 'delete']);
    });
});
