<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\NotificationTemplate;
use Illuminate\Database\Seeder;

class CreateGstr2bReconciliationNotificationTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        disableCompanyScope();

        $companies = Company::all();

        foreach ($companies as $company) {
            $template = [
                'company_id' => $company->id,
                'template_name' => NotificationTemplate::GSTR2B_RECONCILIATION,
                'subject' => 'Request to Upload Pending Invoice(s) to GSTN for  { month_period }',
                'body' => '<p>Hello <strong>{ party_name }</strong>, </p><p><br></p><p>Kindly report Invoice No.<strong>{ invoice_number }</strong> dated <strong>{ invoice_date }</strong> in your GSTR-1 so that we can claim the ITC for this supply.</p><p><br></p>',
                'whatsapp_body' => 'Hello { party_name },'."\n".'Kindly report Invoice No. { invoice_number } dated { invoice_date }  in your GSTR-1 so that we can claim the ITC for this supply.',
                'dynamic_variable' => '{ month_period },{ party_name },{ our_company_name },{ invoice_number },{ invoice_date }',
                'regards' => 'We appreciate your cooperation.'."\n".'Regards'."\n".'{ our_company_name }',
                'is_attachment' => true,
                'use_for_whatsapp' => false,
                'use_for_email' => false,
            ];

            NotificationTemplate::create($template);
        }

        enableCompanyScope();
    }
}
