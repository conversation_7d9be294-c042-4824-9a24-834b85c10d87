<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gstr2b_summary_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->foreign('company_id')->references('id')->on('companies');
            $table->string('return_period');
            $table->integer('transaction_type');
            $table->string('gstin');
            $table->string('invoice_number');
            $table->decimal('taxable_value', 15, 2);
            $table->decimal('igst', 15, 2);
            $table->decimal('cgst', 15, 2);
            $table->decimal('sgst', 15, 2);
            $table->decimal('cess', 15, 2);
            $table->decimal('total_tax', 15, 2);
            $table->decimal('invoice_amount', 15, 2);
            $table->date('invoice_date');
            $table->timestamps();

            $table->unique(['company_id', 'gstin', 'invoice_number', 'invoice_date'], 'unique_invoice_per_company');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gstr2b_summary_transactions');
    }
};
